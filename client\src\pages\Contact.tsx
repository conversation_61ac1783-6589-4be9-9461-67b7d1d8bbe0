import React, { useState, useEffect } from 'react';
import { useLocation, Link } from 'wouter';
import { toast } from 'react-toastify';
import { Button, TextField, Typography, Paper, Grid, Container, Box, CircularProgress, IconButton } from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SendIcon from '@mui/icons-material/Send';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp, Facebook, Twitter, Linkedin, Instagram, MapPin } from 'lucide-react';
import { Helmet } from 'react-helmet-async';
import SharedNavbar from '@/components/shared-navbar';

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

// Particle component for background effects
interface ParticleProps {
  className?: string;
  style?: React.CSSProperties;
}

const Particle = ({ className, style }: ParticleProps) => {
  const randomDelay = Math.random() * 5;
  const randomDuration = 15 + Math.random() * 15;

  return (
    <motion.div
      className={`absolute rounded-full bg-white/20 ${className}`}
      style={style}
      animate={{
        y: [0, -30, 0],
        opacity: [0, 0.4, 0],
        scale: [0, 1, 0]
      }}
      transition={{
        duration: randomDuration,
        repeat: Infinity,
        delay: randomDelay,
        ease: "easeInOut"
      }}
    />
  );
};

const Contact: React.FC = () => {
  const [location, setLocation] = useLocation();
  const [formData, setFormData] = useState<ContactFormData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [errors, setErrors] = useState<Partial<ContactFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);

  // Show scroll-to-top button when scrolled down
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    // Clear error when user types
    if (errors[name as keyof ContactFormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<ContactFormData> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Message sent successfully! We will get back to you soon.');
        // Reset form
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
      } else {
        toast.error(data.message || 'Failed to send message. Please try again later.');
      }
    } catch (error) {
      console.error('Error sending contact form:', error);
      toast.error('An unexpected error occurred. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Contact Us | PathLink</title>
      </Helmet>

      {/* Navigation Bar */}
      <SharedNavbar />

      {/* Main Content */}
      <div className="min-h-screen bg-[#EAE6E1]/30 relative overflow-hidden">
        {/* Animated particles */}
        {[...Array(15)].map((_, i) => {
          const size = Math.floor(Math.random() * 4) + 2;
          const left = Math.floor(Math.random() * 100);
          const top = Math.floor(Math.random() * 100);
          return (
            <Particle
              key={i}
              className={`w-${size} h-${size} absolute`}
              style={{ left: `${left}%`, top: `${top}%` }}
            />
          );
        })}

        <Container maxWidth="lg" sx={{ py: 8, position: 'relative', zIndex: 1 }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7 }}
          >
            <Typography variant="h2" component="h1" align="center" gutterBottom
              sx={{
                color: '#1C2A42',
                fontWeight: 'bold',
                mb: 4
              }}>
              Contact Us
            </Typography>
          </motion.div>

          <Grid container spacing={6}>
            {/* Contact Information */}
            <Grid item xs={12} md={5}>
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                <Paper
                  elevation={3}
                  sx={{
                    p: 4,
                    height: '100%',
                    backgroundColor: '#E5DEFF30',
                    transition: 'all 0.4s ease-in-out',
                    borderRadius: '16px',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '6px',
                      background: 'linear-gradient(90deg, #1C2A42, #6366F1)',
                      borderTopLeftRadius: '16px',
                      borderTopRightRadius: '16px'
                    }
                  }}
                >
                  <Typography variant="h5" component="h2" gutterBottom sx={{ color: '#1C2A42', fontWeight: 'bold' }}>
                    Get In Touch
                  </Typography>

                  <Typography paragraph sx={{ mb: 4 }}>
                    Have questions about PathLink? We're here to help! Fill out the form and our team will get back to you as soon as possible.
                  </Typography>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, p: 2, borderRadius: '8px', backgroundColor: 'rgba(255, 255, 255, 0.5)' }}>
                      <EmailIcon sx={{ mr: 2, color: '#1C2A42' }} />
                      <Typography>
                        <strong>Email:</strong> <EMAIL>
                      </Typography>
                    </Box>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, p: 2, borderRadius: '8px', backgroundColor: 'rgba(255, 255, 255, 0.5)' }}>
                      <PhoneIcon sx={{ mr: 2, color: '#1C2A42' }} />
                      <Typography>
                        <strong>Phone:</strong> +****************
                      </Typography>
                    </Box>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', p: 2, borderRadius: '8px', backgroundColor: 'rgba(255, 255, 255, 0.5)' }}>
                      <LocationOnIcon sx={{ mr: 2, color: '#1C2A42' }} />
                      <Typography>
                        <strong>Address:</strong> 123 Career Avenue, Suite 200<br />
                        San Francisco, CA 94105
                      </Typography>
                    </Box>
                  </motion.div>

                  {/* Social Media Links */}
                  <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, gap: 2 }}>
                    <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                      <IconButton sx={{ color: '#1C2A42' }}>
                        <Facebook size={20} />
                      </IconButton>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                      <IconButton sx={{ color: '#1C2A42' }}>
                        <Twitter size={20} />
                      </IconButton>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                      <IconButton sx={{ color: '#1C2A42' }}>
                        <Linkedin size={20} />
                      </IconButton>
                    </motion.div>
                    <motion.div whileHover={{ scale: 1.2 }} whileTap={{ scale: 0.9 }}>
                      <IconButton sx={{ color: '#1C2A42' }}>
                        <Instagram size={20} />
                      </IconButton>
                    </motion.div>
                  </Box>
                </Paper>
              </motion.div>
            </Grid>

            {/* Contact Form */}
            <Grid item xs={12} md={7}>
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.7, delay: 0.4 }}
              >
                <Paper
                  elevation={3}
                  sx={{
                    p: 4,
                    backgroundColor: 'white',
                    transition: 'all 0.4s ease-in-out',
                    borderRadius: '16px',
                    '&:hover': {
                      transform: 'translateY(-8px)',
                      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
                    }
                  }}
                >
                  <Typography variant="h5" component="h2" gutterBottom sx={{ color: '#1C2A42', fontWeight: 'bold' }}>
                    Send Us a Message
                  </Typography>

                  <form onSubmit={handleSubmit}>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Your Name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          error={!!errors.name}
                          helperText={errors.name}
                          required
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              '&:hover fieldset': {
                                borderColor: '#1C2A42',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#1C2A42',
                              },
                            },
                            '& label.Mui-focused': {
                              color: '#1C2A42',
                            },
                          }}
                        />
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <TextField
                          fullWidth
                          label="Your Email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleChange}
                          error={!!errors.email}
                          helperText={errors.email}
                          required
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              '&:hover fieldset': {
                                borderColor: '#1C2A42',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#1C2A42',
                              },
                            },
                            '& label.Mui-focused': {
                              color: '#1C2A42',
                            },
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleChange}
                          error={!!errors.subject}
                          helperText={errors.subject}
                          required
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              '&:hover fieldset': {
                                borderColor: '#1C2A42',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#1C2A42',
                              },
                            },
                            '& label.Mui-focused': {
                              color: '#1C2A42',
                            },
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Your Message"
                          name="message"
                          multiline
                          rows={6}
                          value={formData.message}
                          onChange={handleChange}
                          error={!!errors.message}
                          helperText={errors.message}
                          required
                          sx={{
                            '& .MuiOutlinedInput-root': {
                              '&:hover fieldset': {
                                borderColor: '#1C2A42',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#1C2A42',
                              },
                            },
                            '& label.Mui-focused': {
                              color: '#1C2A42',
                            },
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            type="submit"
                            variant="contained"
                            size="large"
                            disabled={isSubmitting}
                            endIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
                            sx={{
                              backgroundColor: '#1C2A42',
                              '&:hover': {
                                backgroundColor: '#2a3f61'
                              },
                              px: 4,
                              py: 1.5,
                              borderRadius: '8px',
                              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                              transition: 'all 0.3s ease'
                            }}
                          >
                            {isSubmitting ? 'Sending...' : 'Send Message'}
                          </Button>
                        </motion.div>
                      </Grid>
                    </Grid>
                  </form>
                </Paper>
              </motion.div>
            </Grid>
          </Grid>

          {/* Map Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.6 }}
            className="mt-12"
          >
            <Paper
              elevation={3}
              sx={{
                p: 0,
                borderRadius: '16px',
                overflow: 'hidden',
                height: '300px',
                position: 'relative'
              }}
            >
              <Box
                sx={{
                  height: '100%',
                  width: '100%',
                  backgroundColor: '#E5DEFF50',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  flexDirection: 'column'
                }}
              >
                <MapPin size={40} color="#1C2A42" />
                <Typography variant="h6" sx={{ mt: 2, color: '#1C2A42' }}>
                  123 Career Avenue, San Francisco, CA 94105
                </Typography>
                <Typography variant="body2" sx={{ mt: 1, color: '#666' }}>
                  Interactive map would be displayed here
                </Typography>
              </Box>
            </Paper>
          </motion.div>
        </Container>

        {/* Scroll to top button */}
        <AnimatePresence>
          {showScrollTop && (
            <motion.button
              onClick={scrollToTop}
              className="fixed bottom-8 right-8 bg-[#1C2A42] text-white p-3 rounded-full shadow-lg z-50"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              type="button"
              aria-label="Scroll to top"
            >
              <ChevronUp className="h-6 w-6" />
            </motion.button>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default Contact;
