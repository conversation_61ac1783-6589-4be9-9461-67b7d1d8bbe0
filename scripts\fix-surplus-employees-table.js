import { Pool } from 'pg';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const connectionString = process.env.DATABASE_URL;

async function fixSurplusEmployeesTable() {
  const pool = new Pool({ connectionString });
  try {
    console.log('=== FIXING SURPLUS EMPLOYEES TABLE ===');
    
    // Check current columns
    const currentColumns = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'surplus_employees'
    `);
    
    const existingColumns = currentColumns.rows.map(row => row.column_name);
    console.log('Existing columns:', existingColumns);
    
    // Define required columns with their types and defaults
    const requiredColumns = [
      { name: 'notes', type: 'TEXT', default: "''" },
      { name: 'transfer_reason', type: 'TEXT', default: "''" },
      { name: 'potential_positions', type: 'TEXT', default: "''" },
      { name: 'user_id', type: 'INTEGER', default: 'NULL' }
    ];
    
    // Add missing columns
    for (const column of requiredColumns) {
      if (!existingColumns.includes(column.name)) {
        console.log(`Adding missing column: ${column.name}`);
        
        const alterQuery = `
          ALTER TABLE surplus_employees 
          ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}
        `;
        
        await pool.query(alterQuery);
        console.log(`✅ Added column: ${column.name}`);
      } else {
        console.log(`✓ Column already exists: ${column.name}`);
      }
    }
    
    // Add foreign key constraint for user_id if it doesn't exist
    if (!existingColumns.includes('user_id')) {
      console.log('Adding foreign key constraint for user_id...');
      try {
        await pool.query(`
          ALTER TABLE surplus_employees 
          ADD CONSTRAINT fk_surplus_employees_user_id 
          FOREIGN KEY (user_id) REFERENCES users(id)
        `);
        console.log('✅ Added foreign key constraint for user_id');
      } catch (error) {
        console.log('⚠️ Foreign key constraint may already exist or failed:', error.message);
      }
    }
    
    // Update skills column to allow NULL and set default
    console.log('Updating skills column to allow NULL...');
    try {
      await pool.query(`
        ALTER TABLE surplus_employees 
        ALTER COLUMN skills DROP NOT NULL,
        ALTER COLUMN skills SET DEFAULT ''
      `);
      console.log('✅ Updated skills column');
    } catch (error) {
      console.log('⚠️ Skills column update failed:', error.message);
    }
    
    // Update status column to have proper default
    console.log('Updating status column default...');
    try {
      await pool.query(`
        ALTER TABLE surplus_employees 
        ALTER COLUMN status SET DEFAULT 'available'
      `);
      console.log('✅ Updated status column default');
    } catch (error) {
      console.log('⚠️ Status column update failed:', error.message);
    }
    
    // Verify the final structure
    console.log('\n=== FINAL TABLE STRUCTURE ===');
    const finalStructure = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default 
      FROM information_schema.columns 
      WHERE table_name = 'surplus_employees' 
      ORDER BY ordinal_position
    `);
    
    finalStructure.rows.forEach(col => {
      console.log(`- ${col.column_name}: ${col.data_type}, Nullable: ${col.is_nullable}, Default: ${col.column_default}`);
    });
    
    console.log('\n✅ Surplus employees table migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Error details:', error);
  } finally {
    await pool.end();
  }
}

fixSurplusEmployeesTable();
