const { neon } = require('@neondatabase/serverless');
const dotenv = require('dotenv');
const { initializeDatabase } = require('./init-database');
const db = require('./db-connection');

// Load environment variables
dotenv.config();

const connectionString = process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

async function testDatabaseConnection() {
  console.log('🔄 Testing Neon database connection...');
  console.log('Connection string:', connectionString.replace(/:[^:@]*@/, ':****@'));

  try {
    // Test basic connection
    const sql = neon(connectionString);
    const result = await sql`SELECT NOW() as current_time, version() as db_version`;
    console.log('✅ Basic connection successful');
    console.log('Database time:', result[0].current_time);
    console.log('Database version:', result[0].db_version.substring(0, 50) + '...');

    // Initialize database tables
    console.log('\n🔄 Initializing database tables...');
    await initializeDatabase();
    console.log('✅ Database tables initialized');

    // Test database helper functions
    console.log('\n🔄 Testing database helper functions...');
    
    // Test connection function
    const connected = await db.testConnection();
    console.log('✅ Database helper connection:', connected ? 'SUCCESS' : 'FAILED');

    // Test getting users
    try {
      const testUser = await db.getUserByEmail('<EMAIL>');
      console.log('✅ Get user by email:', testUser ? 'SUCCESS' : 'NO USER FOUND');
      if (testUser) {
        console.log('   User:', testUser.name, '(' + testUser.role + ')');
      }
    } catch (error) {
      console.log('❌ Get user by email failed:', error.message);
    }

    // Test getting jobs
    try {
      const jobs = await db.getJobs();
      console.log('✅ Get jobs:', jobs.length, 'jobs found');
      if (jobs.length > 0) {
        console.log('   Sample job:', jobs[0].title, 'at', jobs[0].company);
      }
    } catch (error) {
      console.log('❌ Get jobs failed:', error.message);
    }

    // Test getting resumes
    try {
      const resumes = await db.getResumesWithWorkers();
      console.log('✅ Get resumes:', resumes.length, 'resumes found');
    } catch (error) {
      console.log('❌ Get resumes failed:', error.message);
    }

    // Test table existence
    console.log('\n🔄 Checking table structure...');
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    
    console.log('✅ Tables found:', tables.map(t => t.table_name).join(', '));

    // Check users table structure
    const userColumns = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'users' 
      ORDER BY ordinal_position
    `;
    
    console.log('\n📋 Users table structure:');
    userColumns.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    // Check jobs table structure
    const jobColumns = await sql`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'jobs' 
      ORDER BY ordinal_position
    `;
    
    console.log('\n📋 Jobs table structure:');
    jobColumns.forEach(col => {
      console.log(`   ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
    });

    console.log('\n✅ All database tests completed successfully!');
    console.log('\n🎉 Your Netlify functions should now work properly with the Neon database.');
    
    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
    return false;
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testDatabaseConnection()
    .then((success) => {
      if (success) {
        console.log('\n🎯 Database is ready for production!');
        process.exit(0);
      } else {
        console.log('\n💥 Database test failed. Please check the connection and try again.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnection };
