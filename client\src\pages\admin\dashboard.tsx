import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { Loader2, Users, Briefcase, FileText, AlertTriangle, CheckCircle, UserCheck, UserX } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLocation } from 'wouter';

// Define the statistics data types
interface PlatformStats {
  total_users: number;
  employers: number;
  workers: number;
  admin_users: number;
  total_jobs: number;
  total_applications: number;
  total_resumes: number;
  active_users_last_30_days: number;
  new_users_last_30_days: number;
  verified_domains: number;
}

interface UserStats {
  total: number;
  verified: number;
  unverified: number;
  active_last_30_days: number;
  inactive: number;
  employers: number;
  workers: number;
  admins: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const AdminDashboard: React.FC = () => {
  const [, setLocation] = useLocation();
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch platform statistics
  const { data: platformStats, isLoading: isLoadingPlatformStats } = useQuery({
    queryKey: ['/api/admin/stats/platform'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/stats/platform');
      if (!res.ok) throw new Error('Failed to fetch platform statistics');
      return res.json() as Promise<PlatformStats>;
    },
  });

  // Fetch user statistics
  const { data: userStats, isLoading: isLoadingUserStats } = useQuery({
    queryKey: ['/api/admin/stats/users'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/stats/users');
      if (!res.ok) throw new Error('Failed to fetch user statistics');
      return res.json() as Promise<UserStats>;
    },
  });

  // Prepare data for user type pie chart
  const userTypePieData = userStats
    ? [
        { name: 'Employers', value: userStats.employers },
        { name: 'Workers', value: userStats.workers },
        { name: 'Admins', value: userStats.admins },
      ]
    : [];

  // Prepare data for user status pie chart
  const userStatusPieData = userStats
    ? [
        { name: 'Verified', value: userStats.verified },
        { name: 'Unverified', value: userStats.unverified },
        { name: 'Active (30d)', value: userStats.active_last_30_days },
        { name: 'Inactive', value: userStats.inactive },
      ]
    : [];

  // Loading state
  if (isLoadingPlatformStats || isLoadingUserStats) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading admin dashboard...</span>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">Admin Dashboard</h1>
          <div className="space-x-2">
            <Button onClick={() => setLocation('/admin/users')}>
              <Users className="h-4 w-4 mr-2" />
              Manage Users
            </Button>
            <Button onClick={() => setLocation('/admin/domains')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Approved Domains
            </Button>
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Platform Overview</TabsTrigger>
            <TabsTrigger value="users">User Statistics</TabsTrigger>
            <TabsTrigger value="activity">Recent Activity</TabsTrigger>
            <TabsTrigger value="issues">System Issues</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {platformStats && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Total Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <Users className="h-8 w-8 text-blue-500 mr-3" />
                        <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{platformStats.total_users}</p>
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        {platformStats.new_users_last_30_days} new in last 30 days
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Total Jobs</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <Briefcase className="h-8 w-8 text-green-500 mr-3" />
                        <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{platformStats.total_jobs}</p>
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        {platformStats.total_applications} total applications
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Resumes</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <FileText className="h-8 w-8 text-purple-500 mr-3" />
                        <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{platformStats.total_resumes}</p>
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        Uploaded by workers
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Active Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="flex items-center">
                        <UserCheck className="h-8 w-8 text-amber-500 mr-3" />
                        <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{platformStats.active_users_last_30_days}</p>
                      </div>
                      <p className="text-sm text-gray-500 mt-2">
                        In the last 30 days
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader>
                      <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">User Distribution</CardTitle>
                      <CardDescription>Breakdown of users by role</CardDescription>
                    </CardHeader>
                    <CardContent className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={[
                              { name: 'Employers', value: platformStats.employers },
                              { name: 'Workers', value: platformStats.workers },
                              { name: 'Admins', value: platformStats.admin_users },
                            ]}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {userTypePieData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader>
                      <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Platform Growth</CardTitle>
                      <CardDescription>New users over time</CardDescription>
                    </CardHeader>
                    <CardContent className="h-80">
                      <div className="flex flex-col items-center justify-center h-full">
                        <p className="text-gray-500 dark:text-gray-400 text-center">
                          Historical data will be displayed here as it becomes available.
                        </p>
                        <Button variant="outline" className="mt-4">
                          Generate Report
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <Card className="bg-white dark:bg-gray-800 shadow-md mb-8">
                  <CardHeader>
                    <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Quick Actions</CardTitle>
                    <CardDescription>Common administrative tasks</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => setLocation('/admin/users/create')}>
                        <Users className="h-6 w-6 mb-2" />
                        <span>Create New User</span>
                      </Button>
                      <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => setLocation('/admin/domains')}>
                        <CheckCircle className="h-6 w-6 mb-2" />
                        <span>Manage Approved Domains</span>
                      </Button>
                      <Button variant="outline" className="h-auto py-4 flex flex-col items-center justify-center" onClick={() => setLocation('/admin/system')}>
                        <AlertTriangle className="h-6 w-6 mb-2" />
                        <span>System Settings</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          <TabsContent value="users">
            {userStats && (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Total Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{userStats.total}</p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Verified Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-green-600 dark:text-green-400">{userStats.verified}</p>
                      <p className="text-sm text-gray-500 mt-2">
                        {((userStats.verified / userStats.total) * 100).toFixed(1)}% of total users
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Unverified Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-amber-600 dark:text-amber-400">{userStats.unverified}</p>
                      <p className="text-sm text-gray-500 mt-2">
                        {((userStats.unverified / userStats.total) * 100).toFixed(1)}% of total users
                      </p>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Inactive Users</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-3xl font-bold text-red-600 dark:text-red-400">{userStats.inactive}</p>
                      <p className="text-sm text-gray-500 mt-2">
                        {((userStats.inactive / userStats.total) * 100).toFixed(1)}% of total users
                      </p>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader>
                      <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">User Types</CardTitle>
                      <CardDescription>Distribution by role</CardDescription>
                    </CardHeader>
                    <CardContent className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={userTypePieData}
                            cx="50%"
                            cy="50%"
                            labelLine={true}
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {userTypePieData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>

                  <Card className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader>
                      <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">User Status</CardTitle>
                      <CardDescription>Activity and verification status</CardDescription>
                    </CardHeader>
                    <CardContent className="h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={[
                            { name: 'Verified', value: userStats.verified },
                            { name: 'Unverified', value: userStats.unverified },
                            { name: 'Active (30d)', value: userStats.active_last_30_days },
                            { name: 'Inactive', value: userStats.inactive },
                          ]}
                          margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="name" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="value" name="Users" fill="#8884d8" />
                        </BarChart>
                      </ResponsiveContainer>
                    </CardContent>
                  </Card>
                </div>

                <div className="flex justify-end mb-8">
                  <Button onClick={() => setLocation('/admin/users')}>
                    <Users className="h-4 w-4 mr-2" />
                    View All Users
                  </Button>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="activity">
            <Card className="bg-white dark:bg-gray-800 shadow-md mb-8">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Recent Activity</CardTitle>
                <CardDescription>Latest actions on the platform</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <p className="text-gray-500 dark:text-gray-400 text-center">
                    Activity log will be displayed here once implemented.
                  </p>
                  <Button variant="outline" className="mt-4">
                    View Full Activity Log
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="issues">
            <Card className="bg-white dark:bg-gray-800 shadow-md mb-8">
              <CardHeader>
                <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">System Issues</CardTitle>
                <CardDescription>Errors and warnings that need attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center justify-center h-40">
                  <p className="text-gray-500 dark:text-gray-400 text-center">
                    No system issues detected at this time.
                  </p>
                  <Button variant="outline" className="mt-4">
                    Run System Diagnostics
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
