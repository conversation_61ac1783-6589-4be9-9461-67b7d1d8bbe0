import OpenAI from "openai";

// Initialize OpenAI client with API key from environment
// Verify API key exists
if (!process.env.OPENAI_API_KEY) {
  console.error("OPENAI_API_KEY is not set in environment variables");
}

const openai = new OpenAI({ 
  apiKey: process.env.OPENAI_API_KEY || "" // Provide empty string as fallback to avoid null
});

// Validate response format
interface ReskillingRecommendation {
  title: string;
  description: string;
  relevance: 'High' | 'Medium' | 'Low';
  timeCommitment: string;
  transferableSkills: string[];
}

interface SkillsAssessment {
  currentSkills: string[];
  skillGaps: string[];
  suggestions: ReskillingRecommendation[];
}

/**
 * Analyzes resume text to extract skills and provide reskilling recommendations
 * @param resumeText - Plain text extracted from resume
 * @param jobTitle - Optional job title to target recommendations
 */
export async function analyzeResume(resumeText: string, jobTitle?: string): Promise<SkillsAssessment> {
  try {
    // Define the prompt for GPT-4o
    const prompt = `
    Analyze the following resume and extract key information:
    1. Identify all technical and soft skills present
    2. Identify skill gaps for ${jobTitle || "modern workforce needs"}
    3. Provide 3-5 personalized reskilling recommendations

    For each reskilling recommendation, please include:
    - A clear title
    - A brief description (1-2 sentences)
    - Relevance level (High/Medium/Low)
    - Time commitment estimate (e.g., "2-3 months part-time")
    - Transferable skills from current resume that would help

    Return the result as a valid JSON object with the following structure:
    {
      "currentSkills": ["skill1", "skill2"...],
      "skillGaps": ["gap1", "gap2"...],
      "suggestions": [
        {
          "title": "string",
          "description": "string",
          "relevance": "High|Medium|Low",
          "timeCommitment": "string",
          "transferableSkills": ["skill1", "skill2"]
        }
      ]
    }

    Resume to analyze:
    ${resumeText}
    `;

    // Call OpenAI API with GPT-4o
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: "You are an expert career coach and skills analyst." },
        { role: "user", content: prompt as string }
      ],
      response_format: { type: "json_object" }
    });

    // Parse the response
    const content = response.choices[0].message.content || "{}";
    const result = JSON.parse(content);
    return result as SkillsAssessment;
  } catch (error) {
    console.error("OpenAI API error:", error);
    
    // Return a fallback response for when API fails
    return {
      currentSkills: ["Communication", "Problem Solving", "Adaptability"],
      skillGaps: ["Data Analysis", "Automation", "Digital Marketing"],
      suggestions: [
        {
          title: "Data Analysis Fundamentals",
          description: "Learn the basics of data analysis with tools like Excel, SQL and Python. This course provides practical skills for any industry.",
          relevance: "High",
          timeCommitment: "2-3 months part-time",
          transferableSkills: ["Problem Solving", "Attention to Detail"]
        },
        {
          title: "Digital Marketing Essentials",
          description: "Master the fundamentals of digital marketing including social media, SEO, and content creation strategies.",
          relevance: "Medium",
          timeCommitment: "4-6 weeks part-time",
          transferableSkills: ["Communication", "Creativity"]
        },
        {
          title: "Project Management Certification",
          description: "Gain credentials in project management to enhance organizational and leadership capabilities.",
          relevance: "Medium",
          timeCommitment: "2-4 months part-time",
          transferableSkills: ["Organization", "Leadership", "Communication"]
        }
      ]
    };
  }
}

/**
 * Generates personalized reskilling suggestions based on worker profile
 * @param skills - List of current skills
 * @param desiredRole - Target role or industry
 */
export async function generateReskillingPath(skills: string[], desiredRole: string): Promise<ReskillingRecommendation[]> {
  try {
    const prompt = `
    Based on the following current skills and desired role, provide 3-5 personalized reskilling recommendations.
    
    Current skills: ${skills.join(", ")}
    Desired role: ${desiredRole}
    
    For each reskilling recommendation, include:
    - A clear title
    - A brief description (1-2 sentences)
    - Relevance level (High/Medium/Low)
    - Time commitment estimate
    - Transferable skills from current skillset that would help
    
    Return the result as a valid JSON array of recommendation objects with this structure:
    [
      {
        "title": "string",
        "description": "string",
        "relevance": "High|Medium|Low",
        "timeCommitment": "string",
        "transferableSkills": ["skill1", "skill2"]
      }
    ]
    `;
    
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: "You are an expert career coach and skills analyst." },
        { role: "user", content: prompt as string }
      ],
      response_format: { type: "json_object" }
    });
    
    const content = response.choices[0].message.content || "[]";
    const result = JSON.parse(content);
    return result as ReskillingRecommendation[];
  } catch (error) {
    console.error("OpenAI API error:", error);
    
    // Return fallback recommendations if API fails
    return [
      {
        title: "Web Development Fundamentals",
        description: "Learn the basics of web development with HTML, CSS and JavaScript. This provides a foundation for many tech roles.",
        relevance: "High",
        timeCommitment: "2-3 months part-time",
        transferableSkills: skills.filter(s => ["Analytical Thinking", "Problem Solving", "Attention to Detail"].includes(s))
      },
      {
        title: "Data Science Essentials",
        description: "Learn the fundamentals of data analysis, statistics, and machine learning applications.",
        relevance: "Medium",
        timeCommitment: "3-4 months part-time",
        transferableSkills: skills.filter(s => ["Mathematics", "Problem Solving", "Critical Thinking"].includes(s))
      }
    ];
  }
}