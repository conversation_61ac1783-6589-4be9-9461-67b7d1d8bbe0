import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function fixResumePaths() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Get all resumes
    const result = await client.query('SELECT * FROM resumes ORDER BY id');
    const resumes = result.rows;
    console.log(`Found ${resumes.length} resumes`);

    // Check uploads/resumes directory
    const uploadsDir = path.join(__dirname, '..', 'uploads', 'resumes');
    console.log(`Checking uploads directory: ${uploadsDir}`);
    
    if (!fs.existsSync(uploadsDir)) {
      console.error(`Uploads directory does not exist: ${uploadsDir}`);
      return;
    }

    const files = fs.readdirSync(uploadsDir);
    console.log(`Found ${files.length} files in uploads directory`);

    // Process each resume
    for (const resume of resumes) {
      console.log(`\nProcessing resume ID ${resume.id}:`);
      console.log(`  Filename: ${resume.filename}`);
      console.log(`  Current path: ${resume.file_path}`);
      console.log(`  Worker ID: ${resume.worker_id}`);

      // Check if current file path exists
      if (fs.existsSync(resume.file_path)) {
        console.log(`  ✓ File exists at current path`);
        continue;
      }

      // Try to find the file in uploads directory
      const expectedPath = path.join(uploadsDir, resume.filename);
      if (fs.existsSync(expectedPath)) {
        console.log(`  ✓ Found file in uploads directory: ${expectedPath}`);
        
        // Update the database with correct path
        await client.query(
          'UPDATE resumes SET file_path = $1 WHERE id = $2',
          [expectedPath, resume.id]
        );
        console.log(`  ✓ Updated database with correct path`);
      } else {
        console.log(`  ✗ File not found anywhere`);
        console.log(`  Available files in uploads:`, files.slice(0, 5).join(', '), files.length > 5 ? '...' : '');
        
        // Delete invalid resume record
        await client.query('DELETE FROM resumes WHERE id = $1', [resume.id]);
        console.log(`  ✓ Deleted invalid resume record`);
      }
    }

    console.log('\nResume path fixing completed');
  } catch (error) {
    console.error('Error fixing resume paths:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

fixResumePaths();
