import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';
import { RoleSwitcher } from './RoleSwitcher';
import { VerificationStatus } from './VerificationStatus';
import { Badge } from '@/components/ui/badge';

export function UserProfile() {
  const { user } = useAuth();

  if (!user) {
    return null;
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {user.name}
          <Badge variant={getRoleBadgeVariant(user.role)}>
            {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
          </Badge>
        </CardTitle>
        <CardDescription>{user.email}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Show verification status for workers */}
        <VerificationStatus />

        {/* Show original role if user has switched */}
        {user.original_role && user.original_role !== user.role && (
          <div className="text-sm text-muted-foreground">
            Originally registered as: {user.original_role.charAt(0).toUpperCase() + user.original_role.slice(1)}
          </div>
        )}

        {/* Role switcher for employers */}
        <RoleSwitcher />
      </CardContent>
    </Card>
  );
}

// Helper function to get badge variant based on role
function getRoleBadgeVariant(role: string): "default" | "secondary" | "destructive" | "outline" {
  switch (role) {
    case 'admin':
      return 'destructive';
    case 'employer':
      return 'default';
    case 'worker':
      return 'secondary';
    default:
      return 'outline';
  }
}
