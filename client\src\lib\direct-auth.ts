/**
 * Direct authentication functions that bypass the Supabase client
 * and directly call the Supabase API endpoints.
 *
 * This is useful as a fallback when the Supabase client fails due to network issues.
 */

import { withRetry, shouldRateLimit, getWaitTime, recordAttempt } from './retry-utils';

// Get the Supabase URL and key from environment variables
const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL || 'https://najfrlqsmmuikqovrumc.supabase.co';
const SUPABASE_KEY = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.MfrcoZ3qD5JytVuD_ZzVE6oWFJI2QrqgxiYulEeoxTc';

// Social login providers
export type SocialProvider = 'google' | 'facebook' | 'apple' | 'linkedin';

// OAuth configuration
const OAUTH_REDIRECT_URL = window.location.origin + '/auth/callback';

/**
 * Sign in with email and password using direct API call
 */
// Track the last registration time
let lastRegistrationTime = 0;
const COOLDOWN_AFTER_REGISTRATION = 3000; // 3 seconds cooldown after registration

export async function directSignIn(email: string, password: string) {
  // Check if we should rate limit login attempts
  if (shouldRateLimit('login')) {
    const waitTime = getWaitTime('login');
    throw new Error(`Too many login attempts. Please wait ${Math.ceil(waitTime/1000)} seconds before trying again.`);
  }

  // Check if we need to wait after registration
  const timeSinceRegistration = Date.now() - lastRegistrationTime;
  if (lastRegistrationTime > 0 && timeSinceRegistration < COOLDOWN_AFTER_REGISTRATION) {
    const remainingTime = Math.ceil((COOLDOWN_AFTER_REGISTRATION - timeSinceRegistration) / 1000);
    console.log(`Waiting ${remainingTime}s after registration before login`);
    await new Promise(resolve => setTimeout(resolve, COOLDOWN_AFTER_REGISTRATION - timeSinceRegistration));
  }

  try {
    // Use withRetry to handle rate limiting and retries
    return await withRetry('login', async () => {
      console.log('Attempting direct sign in to Supabase API');

      // Add a small delay before login to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 500));

      const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_KEY,
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check for rate limiting errors
        if (response.status === 429) {
          console.warn('Rate limit exceeded when signing in');
          throw new Error('Too many login attempts. Please wait a moment and try again later.');
        }

        // Handle other errors
        throw new Error(errorData.error_description || 'Failed to sign in');
      }

      const data = await response.json();
      console.log('Direct sign in successful');

      // Store the session in localStorage to mimic Supabase client behavior
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        expires_at: Date.now() + data.expires_in * 1000,
      }));

      return data;
    });
  } catch (error) {
    console.error('Direct sign in failed:', error);
    throw error;
  }
}

/**
 * Sign up with email and password using direct API call
 */
export async function directSignUp(email: string, password: string, userData: any) {
  // Check if we should rate limit signup attempts
  if (shouldRateLimit('signup')) {
    const waitTime = getWaitTime('signup');
    throw new Error(`Too many registration attempts. Please wait ${Math.ceil(waitTime/1000)} seconds before trying again.`);
  }

  try {
    // Use withRetry to handle rate limiting and retries
    return await withRetry('signup', async () => {
      console.log('Attempting direct sign up to Supabase API');

      const response = await fetch(`${SUPABASE_URL}/auth/v1/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_KEY,
        },
        body: JSON.stringify({
          email,
          password,
          data: userData
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check for rate limiting errors
        if (response.status === 429) {
          console.warn('Rate limit exceeded when signing up');
          throw new Error('Too many registration attempts. Please wait a moment and try again later.');
        }

        // Handle other errors
        throw new Error(errorData.error_description || 'Failed to sign up');
      }

      const data = await response.json();
      console.log('Direct sign up successful');

      // Record the registration time to enforce cooldown before login
      lastRegistrationTime = Date.now();
      console.log('Registration time recorded:', new Date(lastRegistrationTime).toISOString());

      return data;
    });
  } catch (error) {
    console.error('Direct sign up failed:', error);
    throw error;
  }
}

/**
 * Create a profile for a user using direct API call
 */
export async function directCreateProfile(userId: string, profileData: any) {
  try {
    console.log('Attempting to create profile via direct API call');

    const response = await fetch(`${SUPABASE_URL}/rest/v1/profiles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': SUPABASE_KEY,
        'Prefer': 'return=representation',
      },
      body: JSON.stringify({
        id: userId,
        ...profileData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create profile');
    }

    const data = await response.json();
    console.log('Direct profile creation successful');

    return data;
  } catch (error) {
    console.error('Direct profile creation failed:', error);
    throw error;
  }
}

/**
 * Sign in with a social provider (OAuth)
 */
export function directSocialSignIn(provider: SocialProvider) {
  // Check if we should rate limit login attempts
  if (shouldRateLimit('social-login')) {
    const waitTime = getWaitTime('social-login');
    throw new Error(`Too many login attempts. Please wait ${Math.ceil(waitTime/1000)} seconds before trying again.`);
  }

  // Record this attempt
  recordAttempt('social-login', false);

  if (provider === 'facebook') {
    // For Facebook, we'll use the Facebook SDK
    initFacebookSDK().then(() => {
      // @ts-ignore - FB is added by the SDK
      FB.login(function(response) {
        if (response.authResponse) {
          console.log('Facebook login successful');
          const accessToken = response.authResponse.accessToken;

          // Send the token to our server for verification
          fetch('/api/auth/social', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              token: accessToken,
              provider: 'facebook'
            }),
            credentials: 'include'
          })
          .then(response => {
            if (response.ok) {
              // Redirect to dashboard on success
              window.location.href = '/dashboard';
            } else {
              throw new Error('Failed to authenticate with server');
            }
          })
          .catch(error => {
            console.error('Facebook authentication error:', error);
            alert('Facebook login failed. Please try again.');
          });
        } else {
          console.log('User cancelled login or did not fully authorize.');
        }
      }, {scope: 'email,public_profile'});
    }).catch(error => {
      console.error('Failed to initialize Facebook SDK:', error);
      alert('Facebook login is not available at the moment. Please try again later.');
    });
  } else {
    // For other providers, use Supabase OAuth
    // Construct the OAuth URL
    const url = new URL(`${SUPABASE_URL}/auth/v1/authorize`);

    url.searchParams.append('provider', provider);
    url.searchParams.append('redirect_to', OAUTH_REDIRECT_URL);

    // Open the OAuth login window
    window.location.href = url.toString();
  }
}

/**
 * Initialize the Facebook SDK
 */
function initFacebookSDK(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if the SDK is already loaded
    if (window.FB) {
      resolve();
      return;
    }

    // Load the Facebook SDK asynchronously
    (function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s) as HTMLScriptElement;
      js.id = id;
      js.src = "https://connect.facebook.net/en_US/sdk.js";
      fjs.parentNode?.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));

    // Initialize the SDK once it's loaded
    window.fbAsyncInit = function() {
      // @ts-ignore - FB is added by the SDK
      FB.init({
        appId: import.meta.env.VITE_FACEBOOK_APP_ID || '123456789012345', // Replace with your app ID
        cookie: true,
        xfbml: true,
        version: 'v18.0' // Use the latest version
      });

      resolve();
    };

    // Set a timeout in case the SDK fails to load
    setTimeout(() => {
      if (!window.FB) {
        reject(new Error('Facebook SDK failed to load'));
      }
    }, 5000);
  });
}

/**
 * Sign in with phone number
 */
export async function directPhoneSignIn(phone: string, verificationCode: string) {
  // Check if we should rate limit login attempts
  if (shouldRateLimit('phone-login')) {
    const waitTime = getWaitTime('phone-login');
    throw new Error(`Too many login attempts. Please wait ${Math.ceil(waitTime/1000)} seconds before trying again.`);
  }

  try {
    // Use withRetry to handle rate limiting and retries
    return await withRetry('phone-login', async () => {
      console.log('Attempting phone sign in to Supabase API');

      // First verify the phone number with the code
      const verifyResponse = await fetch(`${SUPABASE_URL}/auth/v1/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_KEY,
        },
        body: JSON.stringify({
          phone,
          token: verificationCode,
          type: 'sms'
        }),
      });

      if (!verifyResponse.ok) {
        const errorData = await verifyResponse.json();

        // Check for rate limiting errors
        if (verifyResponse.status === 429) {
          console.warn('Rate limit exceeded when verifying phone');
          throw new Error('Too many verification attempts. Please wait a moment and try again later.');
        }

        // Handle other errors
        throw new Error(errorData.error_description || 'Failed to verify phone number');
      }

      // Now sign in with the verified phone
      const response = await fetch(`${SUPABASE_URL}/auth/v1/token?grant_type=password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_KEY,
        },
        body: JSON.stringify({
          phone,
          password: verificationCode // Use the verification code as the password
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check for rate limiting errors
        if (response.status === 429) {
          console.warn('Rate limit exceeded when signing in with phone');
          throw new Error('Too many login attempts. Please wait a moment and try again later.');
        }

        // Handle other errors
        throw new Error(errorData.error_description || 'Failed to sign in with phone');
      }

      const data = await response.json();
      console.log('Direct phone sign in successful');

      // Store the session in localStorage to mimic Supabase client behavior
      localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: data.access_token,
        refresh_token: data.refresh_token,
        expires_at: Date.now() + data.expires_in * 1000,
      }));

      return data;
    });
  } catch (error) {
    console.error('Direct phone sign in failed:', error);
    throw error;
  }
}

/**
 * Send a verification code to a phone number
 */
export async function directSendPhoneVerification(phone: string) {
  // Check if we should rate limit verification attempts
  if (shouldRateLimit('phone-verification')) {
    const waitTime = getWaitTime('phone-verification');
    throw new Error(`Too many verification attempts. Please wait ${Math.ceil(waitTime/1000)} seconds before trying again.`);
  }

  try {
    // Use withRetry to handle rate limiting and retries
    return await withRetry('phone-verification', async () => {
      console.log('Sending verification code to phone');

      const response = await fetch(`${SUPABASE_URL}/auth/v1/otp`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'apikey': SUPABASE_KEY,
        },
        body: JSON.stringify({
          phone,
          channel: 'sms'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check for rate limiting errors
        if (response.status === 429) {
          console.warn('Rate limit exceeded when sending verification code');
          throw new Error('Too many verification attempts. Please wait a moment and try again later.');
        }

        // Handle other errors
        throw new Error(errorData.error_description || 'Failed to send verification code');
      }

      const data = await response.json();
      console.log('Verification code sent successfully');

      return data;
    });
  } catch (error) {
    console.error('Failed to send verification code:', error);
    throw error;
  }
}
