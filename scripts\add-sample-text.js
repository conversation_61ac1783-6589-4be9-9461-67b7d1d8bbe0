import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

async function addSampleText() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Get all resumes
    const result = await client.query('SELECT * FROM resumes');
    const resumes = [];
    console.log(`Found ${result.rows.length} existing resumes, but creating new ones anyway.`);

    // Sample resume data with different skills and backgrounds
    const sampleResumes = [
      {
        name: "<PERSON>",
        skills: ["JavaScript", "React", "Node.js", "TypeScript", "MongoDB"],
        experience: "5 years of frontend development experience at tech startups",
        education: "Bachelor's in Computer Science, Stanford University",
        industry: "Technology"
      },
      {
        name: "<PERSON>",
        skills: ["Python", "Django", "PostgreSQL", "AWS", "Docker"],
        experience: "7 years as a backend developer with focus on scalable systems",
        education: "Master's in Software Engineering, MIT",
        industry: "Technology"
      },
      {
        name: "Michael Chen",
        skills: ["Java", "Spring Boot", "Kubernetes", "Microservices", "CI/CD"],
        experience: "10 years in enterprise software development",
        education: "PhD in Computer Science, UC Berkeley",
        industry: "Technology"
      },
      {
        name: "Sarah Williams",
        skills: ["Data Analysis", "R", "Python", "Tableau", "SQL"],
        experience: "4 years as a data analyst in the healthcare sector",
        education: "Master's in Data Science, University of Washington",
        industry: "Healthcare"
      },
      {
        name: "David Rodriguez",
        skills: ["C++", "CUDA", "Machine Learning", "Computer Vision", "TensorFlow"],
        experience: "6 years developing AI solutions for autonomous vehicles",
        education: "Master's in Artificial Intelligence, Carnegie Mellon",
        industry: "Automotive"
      },
      {
        name: "Jessica Lee",
        skills: ["UX Design", "Figma", "Adobe XD", "User Research", "Prototyping"],
        experience: "8 years as a UX designer for mobile applications",
        education: "Bachelor's in Graphic Design, RISD",
        industry: "Design"
      },
      {
        name: "Robert Taylor",
        skills: ["DevOps", "AWS", "Terraform", "Jenkins", "Ansible"],
        experience: "9 years in infrastructure and cloud architecture",
        education: "Bachelor's in Information Technology, Georgia Tech",
        industry: "Technology"
      },
      {
        name: "Amanda Martinez",
        skills: ["Product Management", "Agile", "Scrum", "Market Research", "Roadmapping"],
        experience: "7 years as a product manager for SaaS products",
        education: "MBA, Harvard Business School",
        industry: "Technology"
      },
      {
        name: "James Wilson",
        skills: ["iOS Development", "Swift", "Objective-C", "UIKit", "SwiftUI"],
        experience: "6 years developing iOS applications",
        education: "Bachelor's in Computer Science, University of Michigan",
        industry: "Technology"
      },
      {
        name: "Sophia Garcia",
        skills: ["Android Development", "Kotlin", "Java", "Jetpack Compose", "Firebase"],
        experience: "5 years as an Android developer",
        education: "Master's in Mobile Computing, University of Toronto",
        industry: "Technology"
      },
      {
        name: "Daniel Kim",
        skills: ["Blockchain", "Solidity", "Ethereum", "Smart Contracts", "Web3"],
        experience: "4 years developing blockchain applications",
        education: "Bachelor's in Computer Engineering, Cornell University",
        industry: "Finance"
      },
      {
        name: "Olivia Brown",
        skills: ["Digital Marketing", "SEO", "Content Strategy", "Google Analytics", "Social Media"],
        experience: "8 years in digital marketing for e-commerce",
        education: "Bachelor's in Marketing, NYU",
        industry: "Marketing"
      },
      {
        name: "Ethan Davis",
        skills: ["Cybersecurity", "Penetration Testing", "Network Security", "CISSP", "Ethical Hacking"],
        experience: "10 years in information security",
        education: "Master's in Cybersecurity, Johns Hopkins University",
        industry: "Security"
      },
      {
        name: "Ava Thompson",
        skills: ["Data Engineering", "Apache Spark", "Hadoop", "ETL", "Big Data"],
        experience: "7 years building data pipelines for analytics",
        education: "Master's in Computer Science, University of Illinois",
        industry: "Technology"
      },
      {
        name: "Noah Martin",
        skills: ["Game Development", "Unity", "C#", "3D Modeling", "Animation"],
        experience: "6 years developing mobile and console games",
        education: "Bachelor's in Game Design, DigiPen Institute",
        industry: "Gaming"
      }
    ];

    // Function to generate resume content
    function generateResumeContent(resume) {
      return `
# ${resume.name}
Email: ${resume.name.toLowerCase().replace(/\s+/g, '.')}@example.com

## Skills
${resume.skills.join(', ')}

## Experience
${resume.experience}

## Education
${resume.education}

## Industry
${resume.industry}

## Additional Information
Passionate professional with a strong background in ${resume.industry.toLowerCase()}.
Experienced in ${resume.skills.slice(0, 3).join(', ')}, and other technologies.
Looking for opportunities to leverage my expertise in ${resume.skills[0]} and ${resume.skills[1]}.

References available upon request.
`;
    }

    // Process each resume
    for (let i = 0; i < resumes.length; i++) {
      try {
        const resume = resumes[i];
        const sampleIndex = i % sampleResumes.length; // Cycle through sample resumes
        const sampleResume = sampleResumes[sampleIndex];

        console.log(`Processing resume: ${resume.filename} (ID: ${resume.id})`);

        // Generate resume content
        const resumeContent = generateResumeContent(sampleResume);

        // Update the resume record with sample text
        await client.query(
          'UPDATE resumes SET extracted_text = $1, last_indexed = NOW() WHERE id = $2',
          [resumeContent, resume.id]
        );

        console.log(`Updated resume ID ${resume.id} with sample text for ${sampleResume.name}`);
      } catch (error) {
        console.error(`Error processing resume ID ${resumes[i].id}:`, error);
      }
    }

    // If no resumes exist, create 15 sample resumes
    if (resumes.length === 0) {
      console.log('No existing resumes found. Creating sample resumes...');

      for (let i = 0; i < sampleResumes.length; i++) {
        try {
          const sampleResume = sampleResumes[i];
          const workerId = 100 + i; // Start from ID 100 to avoid conflicts

          // Generate resume content
          const resumeContent = generateResumeContent(sampleResume);

          // Create a filename
          const filename = `${sampleResume.name.replace(/\s+/g, '_')}_Resume.pdf`;

          // Create a user for this resume first
          try {
            await client.query(
              `INSERT INTO users (id, name, email, password, role, is_verified, created_at)
               VALUES ($1, $2, $3, $4, $5, $6, $7)
               ON CONFLICT (id) DO NOTHING`,
              [
                workerId,
                sampleResume.name,
                `${sampleResume.name.toLowerCase().replace(/\s+/g, '.')}@example.com`,
                '$2b$10$dummyhashedpassword', // Dummy hashed password
                'worker',
                true,
                new Date()
              ]
            );

            console.log(`Created user for ${sampleResume.name} with ID ${workerId}`);

            // Now insert the resume
            const result = await client.query(
              `INSERT INTO resumes (worker_id, filename, file_path, file_size, file_type, upload_date, extracted_text, last_indexed)
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
               RETURNING id`,
              [
                workerId,
                filename,
                `uploads/resumes/sample_${workerId}.pdf`,
                Math.floor(Math.random() * 500000) + 100000, // Random file size between 100KB and 600KB
                'application/pdf',
                new Date(),
                resumeContent,
                new Date()
              ]
            );

            console.log(`Created sample resume for ${sampleResume.name} with ID ${result.rows[0].id}`);
          } catch (userError) {
            console.error(`Error creating user for ${sampleResume.name}:`, userError);
          }
        } catch (resumeError) {
          console.error(`Error creating sample resume for ${sampleResumes[i].name}:`, resumeError);
        }
      }
    }

    console.log('Resume text update completed');
  } catch (error) {
    console.error('Error updating resume text:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

addSampleText();
