/**
 * This script imports existing resume files from the uploads/resumes directory
 * into the database for search functionality testing.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import pg from 'pg';
import { promisify } from 'util';
// Import mammoth for DOCX parsing
import mammoth from 'mammoth';
import dotenv from 'dotenv';

// Skip pdf-parse import as it's causing issues
// import pdf from 'pdf-parse';

// Load environment variables
dotenv.config();

const { Pool } = pg;
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/pathlink',
});

// Sample resume data to associate with the PDFs
const sampleResumes = [
  {
    name: "<PERSON>",
    skills: ["JavaScript", "React", "Node.js", "TypeScript", "MongoDB"],
    experience: "5 years of frontend development experience at tech startups",
    education: "Bachelor's in Computer Science, Stanford University",
    industry: "Technology"
  },
  {
    name: "<PERSON>",
    skills: ["Python", "Django", "PostgreSQL", "AWS", "Docker"],
    experience: "7 years as a backend developer with focus on scalable systems",
    education: "Master's in Software Engineering, MIT",
    industry: "Technology"
  },
  {
    name: "Michael Brown",
    skills: ["Java", "Spring Boot", "Microservices", "Kubernetes", "CI/CD"],
    experience: "10 years in enterprise software development",
    education: "Bachelor's in Computer Engineering, UC Berkeley",
    industry: "Technology"
  },
  {
    name: "Sarah Davis",
    skills: ["Data Analysis", "SQL", "Python", "Tableau", "Excel"],
    experience: "6 years as a data analyst in the finance sector",
    education: "Master's in Business Analytics, NYU",
    industry: "Finance"
  },
  {
    name: "David Wilson",
    skills: ["UX/UI Design", "Figma", "Adobe XD", "HTML/CSS", "User Research"],
    experience: "8 years designing user interfaces for web and mobile applications",
    education: "Bachelor's in Graphic Design, RISD",
    industry: "Design"
  }
];

// Function to generate resume content
function generateResumeContent(resume) {
  return `
# ${resume.name}
Email: ${resume.name.toLowerCase().replace(/\s+/g, '.')}@example.com

## Skills
${resume.skills.join(', ')}

## Experience
${resume.experience}

## Education
${resume.education}

## Industry
${resume.industry}

## Additional Information
Passionate professional with a strong background in ${resume.industry.toLowerCase()}.
Experienced in ${resume.skills.slice(0, 3).join(', ')}, and other technologies.
Looking for opportunities to leverage my expertise in ${resume.skills[0]} and ${resume.skills[1]}.

References available upon request.
`;
}

// Function to extract text from PDF files
async function extractTextFromPdf(filePath) {
  // Since pdf-parse is causing issues, we'll return null and use the sample text instead
  console.log(`PDF extraction not available for: ${filePath}`);
  return null;
}

// Function to extract text from DOCX files
async function extractTextFromDocx(filePath) {
  try {
    const result = await mammoth.extractRawText({ path: filePath });
    return result.value;
  } catch (error) {
    console.error(`Error extracting text from DOCX: ${filePath}`, error);
    return null;
  }
}

// Function to extract text from TXT files
function extractTextFromTxt(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error extracting text from TXT: ${filePath}`, error);
    return null;
  }
}

// Main function to import resumes
async function importResumes() {
  const client = await pool.connect();

  try {
    // Path to the uploads/resumes directory
    const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');

    // Check if directory exists
    if (!fs.existsSync(resumesDir)) {
      console.error(`Directory not found: ${resumesDir}`);
      return;
    }

    // Get all files in the directory
    const files = fs.readdirSync(resumesDir);
    console.log(`Found ${files.length} files in ${resumesDir}`);

    // Process each file
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const filePath = path.join(resumesDir, file);

      // Skip directories
      if (fs.statSync(filePath).isDirectory()) {
        continue;
      }

      console.log(`Processing file: ${file}`);

      // Get file extension
      const fileExt = path.extname(file).toLowerCase();
      let fileType;

      if (fileExt === '.pdf') {
        fileType = 'pdf';
      } else if (fileExt === '.docx') {
        fileType = 'docx';
      } else if (fileExt === '.txt') {
        fileType = 'txt';
      } else {
        console.log(`Skipping unsupported file type: ${fileExt}`);
        continue;
      }

      // Get file stats
      const stats = fs.statSync(filePath);
      const fileSize = stats.size;

      // Use a sample resume data for the extracted text
      const sampleIndex = i % sampleResumes.length;
      const sampleResume = sampleResumes[sampleIndex];

      // Try to extract text from the file
      let extractedText;
      if (fileType === 'pdf') {
        extractedText = await extractTextFromPdf(filePath);
      } else if (fileType === 'docx') {
        extractedText = await extractTextFromDocx(filePath);
      } else if (fileType === 'txt') {
        extractedText = extractTextFromTxt(filePath);
      }

      // If extraction failed, use the sample text
      if (!extractedText) {
        extractedText = generateResumeContent(sampleResume);
      }

      // Create a user ID for this resume (starting from 1000 to avoid conflicts)
      const workerId = 1000 + i;

      // Create a user for this resume
      console.log(`Creating user for ${file} with ID ${workerId}`);
      await client.query(
        `INSERT INTO users (id, name, email, password, role, is_verified, created_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         ON CONFLICT (id) DO NOTHING`,
        [
          workerId,
          sampleResume.name,
          `${sampleResume.name.toLowerCase().replace(/\s+/g, '.')}.${workerId}@example.com`,
          '$2b$10$dummyhashedpassword', // Dummy hashed password
          'worker',
          true,
          new Date()
        ]
      );

      // Check if a resume already exists for this worker
      const existingResume = await client.query(
        'SELECT id FROM resumes WHERE worker_id = $1',
        [workerId]
      );

      if (existingResume.rows.length > 0) {
        // Update existing resume
        const resumeId = existingResume.rows[0].id;
        console.log(`Updating existing resume ID ${resumeId} for worker ${workerId}`);

        await client.query(
          `UPDATE resumes
           SET filename = $1, file_path = $2, file_size = $3, file_type = $4,
               extracted_text = $5, last_indexed = $6
           WHERE id = $7`,
          [
            file,
            filePath,
            fileSize,
            fileType,
            extractedText,
            new Date(),
            resumeId
          ]
        );
      } else {
        // Create new resume
        console.log(`Creating new resume for worker ${workerId}`);

        await client.query(
          `INSERT INTO resumes (worker_id, filename, file_path, file_size, file_type, upload_date, extracted_text, last_indexed)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            workerId,
            file,
            filePath,
            fileSize,
            fileType,
            new Date(),
            extractedText,
            new Date()
          ]
        );
      }

      console.log(`Successfully processed ${file}`);
    }

    console.log('Resume import completed successfully');
  } catch (error) {
    console.error('Error importing resumes:', error);
  } finally {
    client.release();
  }
}

// Run the import function
try {
  await importResumes();
  console.log('Script completed');
  process.exit(0);
} catch (error) {
  console.error('Script failed:', error);
  process.exit(1);
}
