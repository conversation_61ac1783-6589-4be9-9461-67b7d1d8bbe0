service: pathlink

provider:
  name: aws
  runtime: nodejs18.x
  region: us-east-1
  environment:
    NODE_ENV: production
    DATABASE_URL: ${env:DATABASE_URL}
    SESSION_SECRET: ${env:SESSION_SECRET}
    OPENAI_API_KEY: ${env:OPENAI_API_KEY}
    SENDGRID_API_KEY: ${env:SENDGRID_API_KEY}

functions:
  api:
    handler: dist/index.handler
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /
          method: ANY
          cors: true

plugins:
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3001