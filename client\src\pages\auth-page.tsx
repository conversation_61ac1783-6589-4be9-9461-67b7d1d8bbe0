import React, { useState, useRef, useEffect } from "react";
import { useLocation, <PERSON> } from "wouter";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuth } from "@/hooks/use-auth";
import { Logo } from "@/components/logo";
import ForgotPasswordModal from "@/components/forgot-password-modal";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTheme } from "@/components/ui/theme-provider";
import { <PERSON><PERSON>, AlertDes<PERSON> } from "@/components/ui/alert";
import { <PERSON>, <PERSON> } from "lucide-react";
import { SocialLoginButton, type SocialProvider } from "@/components/social-login-button";
import { PhoneInput } from "@/components/phone-input";
import { directSocialSignIn, directPhoneSignIn } from "@/lib/direct-auth";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { motion, AnimatePresence } from "framer-motion";

// List of approved employer domains
const APPROVED_EMPLOYER_DOMAINS = ["pathlink.com", "google.com", "microsoft.com", "apple.com", "amazon.com", "meta.com"];

const loginSchema = z.object({
  email: z.string().min(1, "Email is required"),
  password: z.string().min(1, "Password is required"),
});

const registerSchema = z.object({
  name: z.string().optional(), // We're using nameValue state instead
  email: z.string().min(1, "Email is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  role: z.enum(["worker", "employer"], {
    required_error: "Please select a role",
  }),
});

type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

export default function AuthPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showForgotPassword, setShowForgotPassword] = useState(false);
  const { user, loginMutation, registerMutation } = useAuth();
  const [_, setLocation] = useLocation();
  const { theme, setTheme } = useTheme();
  const [nameValue, setNameValue] = useState("");
  const fullNameRef = useRef<HTMLInputElement>(null);

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
      role: "worker", // Default role to avoid undefined value
    },
    mode: "onChange",
  });

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      setLocation("/dashboard");
    }
  }, [user, setLocation]);

  // Track login attempts
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [lastLoginAttempt, setLastLoginAttempt] = useState(0);

  const onLoginSubmit = async (data: LoginFormValues) => {
    setErrorMessage(null);

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!data.email || !emailRegex.test(data.email)) {
      setErrorMessage("Please enter a valid email address");
      return;
    }

    try {
      await loginMutation.mutateAsync(data);
    } catch (error: any) {
      setErrorMessage(error.message || "Invalid email or password");
    }
  };

  const onRegisterSubmit = async (data: RegisterFormValues) => {
    setErrorMessage(null);

    // Make sure the name from the standalone input is included
    if (!nameValue.trim()) {
      setErrorMessage("Full name is required");
      return;
    }

    // Get the email value directly from the input field
    const email = registerForm.getValues().email?.trim() || '';

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!email || !emailRegex.test(email)) {
      setErrorMessage("Please enter a valid email address");
      return;
    }

    // For employer role, validate domain
    if (data.role === "employer") {
      const domain = email.split('@')[1];
      if (!domain || !APPROVED_EMPLOYER_DOMAINS.includes(domain)) {
        setErrorMessage(`Employer email must use an approved company domain (e.g., @pathlink.com). Approved domains: ${APPROVED_EMPLOYER_DOMAINS.join(', ')}`);
        return;
      }
    }

    try {
      await registerMutation.mutateAsync({
        ...data,
        email, // ensure trimmed, non-empty value is sent
        name: nameValue,
      });
    } catch (error: any) {
      setErrorMessage(error.message || "Registration failed");
    }
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        when: "beforeChildren",
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <div className="min-h-screen flex flex-col md:flex-row bg-[#EAE6E1] dark:bg-gray-900 text-[#1C2A42] dark:text-white transition-colors relative overflow-hidden">
      {/* Left side: Auth form */}
      <div className="flex-1 flex flex-col justify-center items-center p-4 sm:p-6 md:p-10 relative z-10">
        <div className="w-full max-w-md">
          <div className="text-center mb-6 sm:mb-8">
            <Logo className="mx-auto h-16 sm:h-20 md:h-24 w-auto" />
            <h1 className="mt-4 sm:mt-6 text-xl sm:text-2xl md:text-3xl font-bold">
              {isLogin ? "Sign in to your account" : "Create your account"}
            </h1>
            <p className="mt-2 text-sm sm:text-base text-gray-600 dark:text-gray-400 px-2">
              {isLogin
                ? "Enter your credentials to access your account"
                : "Join PathLink to connect with opportunities"}
            </p>
          </div>

          {errorMessage && (
            <Alert
              variant="destructive"
              className="mb-4"
            >
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          {isLogin ? (
            <Card className="border-0 sm:border shadow-none sm:shadow-md">
              <CardHeader className="px-4 sm:px-6 pt-4 sm:pt-6 pb-4">
                <CardTitle className="text-lg sm:text-xl">Sign In</CardTitle>
                <CardDescription className="text-sm">Access your PathLink account</CardDescription>
              </CardHeader>
              <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
                <Tabs defaultValue="email" className="w-full">
                  <TabsList className="grid grid-cols-2 mb-4">
                    <TabsTrigger value="email">Email</TabsTrigger>
                    <TabsTrigger value="phone">Phone</TabsTrigger>
                  </TabsList>

                  <TabsContent value="email" className="space-y-4">
                    <Form {...loginForm}>
                      <form
                        onSubmit={loginForm.handleSubmit(onLoginSubmit)}
                        className="space-y-4"
                      >
                        <FormField
                          control={loginForm.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="<EMAIL>"
                                  {...field}
                                  disabled={loginMutation.isPending}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={loginForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <div className="flex justify-between items-center">
                                <FormLabel>Password</FormLabel>
                                <Button
                                  variant="link"
                                  className="p-0 h-auto text-sm text-blue-500 dark:text-blue-400"
                                  type="button"
                                  onClick={() => setShowForgotPassword(true)}
                                >
                                  Forgot password?
                                </Button>
                              </div>
                              <FormControl>
                                <Input
                                  type="password"
                                  {...field}
                                  disabled={loginMutation.isPending}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <Button
                          type="submit"
                          className="w-full"
                          disabled={loginMutation.isPending}
                        >
                          {loginMutation.isPending ? "Signing in..." : "Sign In"}
                        </Button>
                      </form>
                    </Form>

                    <div className="relative my-4">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-gray-300 dark:border-gray-600"></span>
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-white dark:bg-gray-800 px-2 text-gray-500 dark:text-gray-400">
                          Or continue with
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                      {[
                        { provider: "google" as SocialProvider, delay: 0 },
                        { provider: "facebook" as SocialProvider, delay: 0.1 },
                        { provider: "apple" as SocialProvider, delay: 0.2 },
                        { provider: "linkedin" as SocialProvider, delay: 0.3 }
                      ].map((item) => (
                        <motion.div
                          key={item.provider}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 0.7 + item.delay }}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="w-full"
                        >
                          <SocialLoginButton
                            provider={item.provider}
                            onClick={() => directSocialSignIn(item.provider)}
                            isLoading={loginMutation.isPending}
                            fullWidth
                            className="h-10 sm:h-auto text-xs sm:text-sm"
                          />
                        </motion.div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="phone">
                    <PhoneInput
                      onSubmit={async (phone, code) => {
                        try {
                          await directPhoneSignIn(phone, code);
                        } catch (error: any) {
                          setErrorMessage(error.message || "Phone verification failed");
                        }
                      }}
                      isLoading={loginMutation.isPending}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2 px-4 sm:px-6 pb-4 sm:pb-6">
                <Button
                  variant="link"
                  onClick={() => setIsLogin(false)}
                  className="text-sm"
                >
                  Don't have an account? Sign up
                </Button>
                <Link to="/" className="text-xs sm:text-sm text-gray-500 hover:text-[#1C2A42] dark:hover:text-gray-300 flex items-center justify-center gap-1">
                  <span className="text-xs">←</span> Return to Landing
                </Link>
              </CardFooter>
            </Card>
          ) : (
            <Card className="border-0 sm:border shadow-none sm:shadow-md">
              <CardHeader className="px-4 sm:px-6 pt-4 sm:pt-6 pb-4">
                <CardTitle className="text-lg sm:text-xl">Create Account</CardTitle>
                <CardDescription className="text-sm">Join PathLink today</CardDescription>
              </CardHeader>
              <CardContent className="px-4 sm:px-6 pb-4 sm:pb-6">
                <Tabs defaultValue="email" className="w-full">
                  <TabsList className="grid grid-cols-2 mb-4">
                    <TabsTrigger value="email">Email</TabsTrigger>
                    <TabsTrigger value="phone">Phone</TabsTrigger>
                  </TabsList>

                  <TabsContent value="email" className="space-y-4">
                    <Form {...registerForm}>
                      <form
                        onSubmit={registerForm.handleSubmit(onRegisterSubmit)}
                        className="space-y-4"
                      >
                        <div className="space-y-1">
                          <label className="font-medium text-sm">Full Name</label>
                          <Input
                            placeholder="John Doe"
                            value={nameValue}
                            onChange={(e) => setNameValue(e.target.value)}
                            disabled={registerMutation.isPending}
                            className={!nameValue.trim() && registerForm.formState.isSubmitted ? "border-red-500" : ""}
                          />
                          {!nameValue.trim() && registerForm.formState.isSubmitted && (
                            <p className="text-sm font-medium text-red-500 mt-1">Full name is required</p>
                          )}
                        </div>
                        <div className="space-y-1">
                          <label className="font-medium text-sm">Email Address</label>
                          <Input
                            type="email"
                            placeholder={registerForm.watch("role") === "employer"
                              ? "<EMAIL>"
                              : "<EMAIL>"}
                            value={registerForm.getValues().email || ""}
                            onChange={(e) => registerForm.setValue("email", e.target.value, { shouldValidate: true })}
                            disabled={registerMutation.isPending}
                            className={registerForm.formState.errors.email ? "border-red-500" : ""}
                          />
                          {registerForm.formState.errors.email && (
                            <p className="text-sm font-medium text-red-500 mt-1">
                              {registerForm.formState.errors.email.message || "Please enter a valid email address"}
                            </p>
                          )}
                          {registerForm.watch("role") === "employer" && (
                            <p className="text-xs text-muted-foreground mt-1">
                              Employer emails must use an approved domain (e.g., @pathlink.com)
                            </p>
                          )}
                        </div>
                        <FormField
                          control={registerForm.control}
                          name="password"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  {...field}
                                  disabled={registerMutation.isPending}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={registerForm.control}
                          name="role"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Role</FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                defaultValue={field.value}
                                disabled={registerMutation.isPending}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select your role" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="employer">Employer</SelectItem>
                                  <SelectItem value="worker">Worker</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <Button
                          type="submit"
                          className="w-full"
                          disabled={registerMutation.isPending}
                        >
                          {registerMutation.isPending
                            ? "Creating account..."
                            : "Create Account"}
                        </Button>
                      </form>
                    </Form>

                    <div className="relative my-4">
                      <div className="absolute inset-0 flex items-center">
                        <span className="w-full border-t border-gray-300 dark:border-gray-600"></span>
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-white dark:bg-gray-800 px-2 text-gray-500 dark:text-gray-400">
                          Or sign up with
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                      {[
                        { provider: "google" as SocialProvider, delay: 0 },
                        { provider: "facebook" as SocialProvider, delay: 0.1 },
                        { provider: "apple" as SocialProvider, delay: 0.2 },
                        { provider: "linkedin" as SocialProvider, delay: 0.3 }
                      ].map((item) => (
                        <motion.div
                          key={item.provider}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 0.7 + item.delay }}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className="w-full"
                        >
                          <SocialLoginButton
                            provider={item.provider}
                            onClick={() => directSocialSignIn(item.provider)}
                            isLoading={registerMutation.isPending}
                            fullWidth
                            className="h-10 sm:h-auto text-xs sm:text-sm"
                          />
                        </motion.div>
                      ))}
                    </div>
                  </TabsContent>

                  <TabsContent value="phone">
                    <PhoneInput
                      onSubmit={async (phone, code) => {
                        try {
                          await directPhoneSignIn(phone, code);
                        } catch (error: any) {
                          setErrorMessage(error.message || "Phone verification failed");
                        }
                      }}
                      isLoading={registerMutation.isPending}
                    />
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex flex-col space-y-2 px-4 sm:px-6 pb-4 sm:pb-6">
                <Button
                  variant="link"
                  onClick={() => setIsLogin(true)}
                  className="text-sm"
                >
                  Already have an account? Sign in
                </Button>
                <Link to="/" className="text-xs sm:text-sm text-gray-500 hover:text-[#1C2A42] dark:hover:text-gray-300 flex items-center justify-center gap-1">
                  <span className="text-xs">←</span> Return to Landing
                </Link>
              </CardFooter>
            </Card>
          )}
        </div>
      </div>

      {/* Forgot Password Modal */}
      <ForgotPasswordModal
        open={showForgotPassword}
        onClose={() => setShowForgotPassword(false)}
      />
    </div>
  );
}
