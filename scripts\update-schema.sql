-- Add new fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS original_role TEXT,
ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS verification_token TEXT,
ADD COLUMN IF NOT EXISTS verification_expires TIMESTAMP,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NOW();

-- Update the role enum to include admin
ALTER TABLE users 
DROP CONSTRAINT IF EXISTS users_role_check;

ALTER TABLE users
ADD CONSTRAINT users_role_check 
CHECK (role IN ('worker', 'employer', 'admin'));

-- <PERSON><PERSON> approved_domains table
CREATE TABLE IF NOT EXISTS approved_domains (
    id SERIAL PRIMARY KEY,
    domain TEXT NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    added_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Insert some common company domains as examples
INSERT INTO approved_domains (domain, description, is_active)
VALUES 
    ('google.com', 'Google Inc.', TRUE),
    ('microsoft.com', 'Microsoft Corporation', TRUE),
    ('apple.com', 'Apple Inc.', TRUE),
    ('amazon.com', 'Amazon', TRUE),
    ('meta.com', 'Meta (Facebook)', TRUE),
    ('ibm.com', 'IBM', TRUE),
    ('intel.com', 'Intel Corporation', TRUE),
    ('cisco.com', 'Cisco Systems', TRUE),
    ('oracle.com', 'Oracle Corporation', TRUE),
    ('salesforce.com', 'Salesforce', TRUE)
ON CONFLICT (domain) DO NOTHING;

-- Create an admin user if it doesn't exist
INSERT INTO users (email, password, name, role, is_verified)
VALUES (
    '<EMAIL>', 
    '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', -- 'password' hashed
    'System Administrator', 
    'admin',
    TRUE
)
ON CONFLICT (email) DO NOTHING;
