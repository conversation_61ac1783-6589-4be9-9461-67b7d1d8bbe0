-- Create todos table for testing Supabase connection
CREATE TABLE IF NOT EXISTS public.todos (
  id SERIAL PRIMARY KEY,
  task TEXT NOT NULL,
  is_complete BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.todos ENABLE ROW LEVEL SECURITY;

-- Create policy for anonymous access (for testing only)
CREATE POLICY "Allow anonymous access" ON public.todos
  FOR ALL USING (true);

-- Insert some sample data
INSERT INTO public.todos (task, is_complete) VALUES
  ('Learn Supabase', false),
  ('Build a React app with Supabase', false),
  ('Deploy to production', false),
  ('Add authentication', false),
  ('Implement real-time features', false);

-- Note: Run this SQL in the Supabase SQL Editor
-- 1. Go to https://app.supabase.com/
-- 2. Select your project
-- 3. Go to SQL Editor
-- 4. Paste this SQL and run it
