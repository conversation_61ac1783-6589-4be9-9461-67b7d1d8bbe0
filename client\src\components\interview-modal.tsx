import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Calendar } from "lucide-react";

// Custom Match interface
interface Match {
  id: number;
  status: "pending" | "matched" | "rejected" | "interview_scheduled" | "accepted" | "withdrawn";
  worker_id: number;
  job_id: number;
  match_score: number;
  match_date: string | Date;
  interview_date?: string | Date | null;
  worker_name?: string;
}

interface InterviewModalProps {
  open: boolean;
  onClose: () => void;
  match: Match | null;
  onSchedule: (date: string) => void;
}

export default function InterviewModal({
  open,
  onClose,
  match,
  onSchedule
}: InterviewModalProps) {
  const [interviewDate, setInterviewDate] = useState<string>('');

  const handleSchedule = () => {
    if (interviewDate) {
      onSchedule(interviewDate);
      setInterviewDate('');
    }
  };

  const handleClose = () => {
    setInterviewDate('');
    onClose();
  };

  if (!match) return null;

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-bold text-[#1C2A42] dark:text-white">
            <Calendar className="h-5 w-5" />
            Schedule Interview
          </DialogTitle>
          <DialogDescription>
            Schedule an interview with {match.worker_name || `Applicant ${match.worker_id}`} for the position.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="mb-4">
            <label htmlFor="interview-datetime" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Interview Date and Time
            </label>
            <input
              id="interview-datetime"
              type="datetime-local"
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              value={interviewDate}
              onChange={(e) => setInterviewDate(e.target.value)}
              min={new Date().toISOString().slice(0, 16)}
              aria-label="Interview date and time"
            />
          </div>
        </div>

        <DialogFooter className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={handleClose}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSchedule}
            disabled={!interviewDate}
          >
            Schedule Interview
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
