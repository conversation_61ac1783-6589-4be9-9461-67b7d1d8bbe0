// Run the migration to add new job fields
import 'dotenv/config';
import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Create a PostgreSQL pool
const { Pool } = pg;
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function runMigration() {
  console.log('Running migration to add new job fields...');

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../drizzle/migrations/add_job_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Connect to the database
    const client = await pool.connect();

    try {
      // Execute the migration
      await client.query(migrationSQL);
      console.log('Migration completed successfully!');
    } finally {
      // Release the client back to the pool
      client.release();
    }
  } catch (error) {
    console.error('Error running migration:', error);
    process.exit(1);
  }
}

runMigration();
