// Load environment variables from .env file
import 'dotenv/config';
import dotenv from 'dotenv'
dotenv.config()


import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { setupVite, serveStatic, log } from "./vite";
import { runMigrations } from "./migrations";
import { securityHeaders, sanitizeParams, simpleRateLimit } from "./security";
import path from "path";
import fs from "fs";
import { storage } from "./storage";
import { fileURLToPath } from "url";

// Log loaded environment variables (without showing actual values for security)
const envVars = [
  'DATABASE_URL',
  'SESSION_SECRET',
  'OPENAI_API_KEY',
  'SENDGRID_API_KEY'
];

log('Environment variables loaded:');
envVars.forEach(varName => {
  log(`- ${varName}: ${process.env[varName] ? 'Set' : 'Not set'}`);
});

const app = express();

// Apply security middleware
app.use(securityHeaders);
app.use(sanitizeParams);
// Temporarily disable rate limiting for development
// app.use('/api/login', simpleRateLimit(20, 15 * 60 * 1000)); // More lenient rate limit for login
// app.use('/api/register', simpleRateLimit(10, 15 * 60 * 1000)); // Rate limit for registration
// app.use('/api', simpleRateLimit(100, 15 * 60 * 1000)); // General API rate limit

// Body parsers
app.use(express.json({ limit: '1mb' })); // Limit JSON payload size
app.use(express.urlencoded({ extended: false, limit: '1mb' })); // Limit form data size

// Serve static files from uploads directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const uploadsPath = path.join(__dirname, '../uploads');

// Create uploads directory and subdirectories if they don't exist
try {
  if (!fs.existsSync(uploadsPath)) {
    fs.mkdirSync(uploadsPath, { recursive: true });
    fs.mkdirSync(path.join(uploadsPath, 'profile'), { recursive: true });
    fs.mkdirSync(path.join(uploadsPath, 'resumes'), { recursive: true });
    console.log('Created uploads directories');
  }
} catch (error) {
  console.error('Error creating uploads directories:', error);
}

// Serve the uploads directory
app.use('/uploads', express.static(uploadsPath));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  // Use the configured storage (Neon Database or in-memory fallback)
  log('Using configured storage');

  const server = await registerRoutes(app);

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    // Log the error but don't expose details to the client in production
    console.error('Server error:', err);

    // In production, don't send the stack trace to the client
    const responseBody = {
      message: process.env.NODE_ENV === 'production'
        ? (status === 500 ? 'Internal Server Error' : message)
        : message,
      error: process.env.NODE_ENV === 'production' ? undefined : err.stack
    };

    res.status(status).json(responseBody);
    // Don't rethrow the error - this would crash the server
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // Use the PORT environment variable provided by Render, or default to 5000
  // this serves both the API and the client.
  const port = process.env.PORT || 5000;
  server.listen({
    port,
    host: "0.0.0.0", // Bind to all network interfaces, not just localhost
  }, () => {
    log(`serving on port ${port}`);
  });
})();
