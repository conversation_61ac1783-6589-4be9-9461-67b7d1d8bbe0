import { Pool } from 'pg';

import dotenv from 'dotenv';
dotenv.config();

const connectionString = process.env.DATABASE_URL;

async function testReapplyDirect() {
  const pool = new Pool({ connectionString });
  try {
    console.log('=== TESTING REAPPLY FUNCTIONALITY (DIRECT DATABASE) ===');
    
    // Check current status of match 28
    console.log('1. Checking current match status...');
    const currentResult = await pool.query('SELECT * FROM matches WHERE id = $1', [28]);
    
    if (currentResult.rows.length > 0) {
      const match = currentResult.rows[0];
      console.log('Current match 28:', {
        id: match.id,
        worker_id: match.worker_id,
        job_id: match.job_id,
        status: match.status,
        match_score: match.match_score,
        match_date: match.match_date
      });
      
      // If it's withdrawn, let's simulate a reapply by updating it to pending
      if (match.status === 'withdrawn') {
        console.log('2. Simulating reapply (updating status to pending)...');
        
        const updateResult = await pool.query(
          'UPDATE matches SET status = $1, match_score = $2, match_date = $3 WHERE id = $4 RETURNING *',
          ['pending', 85, new Date(), 28]
        );
        
        if (updateResult.rows.length > 0) {
          const updatedMatch = updateResult.rows[0];
          console.log('✅ REAPPLY SUCCESS: Match updated:', {
            id: updatedMatch.id,
            status: updatedMatch.status,
            match_score: updatedMatch.match_score,
            match_date: updatedMatch.match_date
          });
        } else {
          console.log('❌ Update failed');
        }
      } else {
        console.log(`Match is currently ${match.status}, not withdrawn. Cannot test reapply.`);
      }
    } else {
      console.log('❌ Match 28 not found');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  } finally {
    await pool.end();
  }
}

testReapplyDirect();
