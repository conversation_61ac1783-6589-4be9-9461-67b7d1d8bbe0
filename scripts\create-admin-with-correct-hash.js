import pg from 'pg';
import crypto from 'crypto';
import { promisify } from 'util';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

dotenv.config();

const scryptAsync = promisify(crypto.scrypt);

// Same hashing function as in auth.ts
async function hashPassword(password) {
  const salt = crypto.randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createAdminUser() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new pg.Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Hash the password using the same method as the application
    const password = 'password';
    const hashedPassword = await hashPassword(password);
    console.log('Password hashed successfully');

    // Create the admin user
    const insertQuery = `
      INSERT INTO users (email, password, name, role, is_verified, original_role, created_at)
      VALUES ($1, $2, $3, $4, $5, $6, NOW())
      ON CONFLICT (email) DO UPDATE SET
        password = $2,
        name = $3,
        role = $4,
        is_verified = $5,
        original_role = $6
      RETURNING id, email, name, role;
    `;

    const values = [
      '<EMAIL>',
      hashedPassword,
      'System Administrator',
      'admin',
      true,
      'admin'
    ];

    const result = await client.query(insertQuery, values);
    console.log('Admin user created/updated successfully:', result.rows[0]);
    console.log('Hashed password:', hashedPassword);

    // Verify the admin user was created
    const verifyQuery = await client.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (verifyQuery.rows.length > 0) {
      console.log('Admin user verified in database:', {
        id: verifyQuery.rows[0].id,
        email: verifyQuery.rows[0].email,
        name: verifyQuery.rows[0].name,
        role: verifyQuery.rows[0].role,
        password_hash: verifyQuery.rows[0].password.substring(0, 20) + '...' // Show just the beginning for security
      });
    }

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

createAdminUser();
