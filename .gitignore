# Dependencies
node_modules/
.pnp/
.pnp.js

# Build outputs
dist/
build/
.next/
out/

# Environment variables - never commit these
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Secrets and keys
*.pem
*.key
*.cert
*.p12
*.pfx
*.crt
*.cer
*.der
*.priv
*.keystore
*.jks
*.p8
*.jwk

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
coverage/
.nyc_output/

# Editor directories and files
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store

# Temporary files
*.tmp
*.temp
.cache/

# Uploaded files
uploads/*
!uploads/.gitkeep

# Security and sensitive files
.npmrc
.yarnrc
.aws/
.config/
.ssh/
.bash_history
.zsh_history
.netrc
.pgpass
.htpasswd
.htaccess
config.json
secrets.json
credentials.json
*_rsa
*_dsa
*_ed25519
*_ecdsa

# Database files
*.sqlite
*.db

# Vercel
.vercel/

# Render
.render/

# Fly.io
.fly/

# Railway
.railway/

# Misc
Thumbs.db
server/public
vite.config.ts.*
*.tar.gz
# Local Netlify folder
.netlify
