/**
 * This script tests the connection to Supabase using the Supabase client
 * instead of direct PostgreSQL connection.
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testSupabaseClient() {
  console.log('Testing Supabase client connection...');
  
  const supabaseUrl = process.env.VITE_SUPABASE_URL;
  const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('ERROR: Supabase URL or key is not set in environment variables.');
    process.exit(1);
  }
  
  console.log('Using Supabase URL:', supabaseUrl);
  console.log('Using Supabase key:', supabaseKey.substring(0, 10) + '...');
  
  // Create Supabase client
  const supabase = createClient(supabaseUrl, supabaseKey);
  
  try {
    console.log('Testing Supabase connection...');
    
    // Test the connection by checking the Supabase auth
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      throw authError;
    }
    
    console.log('Successfully connected to Supabase Auth!');
    
    // Try to query a table
    console.log('Testing database query...');
    const { data: tablesData, error: tablesError } = await supabase
      .from('pg_catalog.pg_tables')
      .select('tablename')
      .eq('schemaname', 'public')
      .limit(10);
    
    if (tablesError) {
      console.warn('Warning: Could not query tables:', tablesError.message);
      console.log('This might be due to permissions, but the connection is working.');
    } else {
      console.log('Tables in the database:', tablesData);
    }
    
    // Try to get the database connection string
    console.log('\nTo connect directly to the PostgreSQL database:');
    console.log('1. Go to the Supabase dashboard: https://app.supabase.com/');
    console.log('2. Select your project');
    console.log('3. Go to Project Settings > Database');
    console.log('4. Find the "Connection string" section');
    console.log('5. Copy the "URI" connection string and replace [YOUR-PASSWORD] with your database password');
    
    return true;
  } catch (error) {
    console.error('Error connecting to Supabase:', error.message);
    return false;
  }
}

// Run the test
testSupabaseClient().then(success => {
  if (success) {
    console.log('✅ Supabase client connection test passed!');
    process.exit(0);
  } else {
    console.error('❌ Supabase client connection test failed!');
    process.exit(1);
  }
}).catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
