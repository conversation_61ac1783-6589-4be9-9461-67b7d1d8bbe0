import passport from "passport";
import { Strategy as LocalStrategy } from "passport-local";
import { Express } from "express";
import session from "express-session";
import { scrypt, randomBytes, timingSafeEqual } from "crypto";
import { promisify } from "util";
import { storage } from "./storage";
import { sendWelcomeEmail } from "./email";
import { User as SelectUser } from "@shared/schema";
import fetch from "node-fetch";
import bcrypt from "bcrypt";

declare global {
  namespace Express {
    interface User extends SelectUser {}
  }
}

const scryptAsync = promisify(scrypt);

export async function hashPassword(password: string) {
  const salt = randomBytes(16).toString("hex");
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString("hex")}.${salt}`;
}

export async function comparePasswords(supplied: string, stored: string) {
  try {
    console.log(`🔐 Comparing password for hash: ${stored.substring(0, 10)}...`);
    console.log(`🔐 Supplied password length: ${supplied.length}`);

    // Check if the stored password is a bcrypt hash (starts with $2b$)
    if (stored.startsWith('$2b$') || stored.startsWith('$2a$')) {
      // For backward compatibility with bcrypt hashes
      console.log("Using bcrypt comparison for legacy hash");
      const result = await bcrypt.compare(supplied, stored);
      console.log(`🔐 Bcrypt comparison result: ${result}`);
      return result;
    }

    // Regular scrypt comparison
    const [hashed, salt] = stored.split(".");
    if (!hashed || !salt) {
      console.error("Invalid password format");
      return false;
    }
    const hashedBuf = Buffer.from(hashed, "hex");
    const suppliedBuf = (await scryptAsync(supplied, salt, 64)) as Buffer;
    return timingSafeEqual(hashedBuf, suppliedBuf);
  } catch (error) {
    console.error("Password comparison error:", error);
    return false;
  }
}

export function setupAuth(app: Express) {
  // Ensure session secret is set
  if (!process.env.SESSION_SECRET) {
    console.error("WARNING: SESSION_SECRET is not set. Using a random secret for this session only.");
    console.error("This is insecure for production. Set SESSION_SECRET in your environment variables.");
  }

  const sessionSecret = process.env.SESSION_SECRET || randomBytes(32).toString('hex');

  const sessionSettings: session.SessionOptions = {
    secret: sessionSecret,
    resave: false,
    saveUninitialized: false,
    store: storage.sessionStore,
    cookie: {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      sameSite: 'lax' // Provides CSRF protection
    }
  };

  app.set("trust proxy", 1);
  app.use(session(sessionSettings));
  app.use(passport.initialize());
  app.use(passport.session());

  // Configure passport to use local strategy with email as username field
  passport.use(
    new LocalStrategy(
      { usernameField: "email" },
      async (email, password, done) => {
        try {
          console.log(`Login attempt for email: ${email}`);
          const user = await storage.getUserByEmail(email);
          if (!user) {
            console.log(`User not found for email: ${email}`);
            return done(null, false, { message: "Invalid email or password" });
          }

          const isPasswordValid = await comparePasswords(password, user.password);
          if (!isPasswordValid) {
            console.log(`Invalid password for email: ${email}`);
            return done(null, false, { message: "Invalid email or password" });
          }

          console.log(`Successful login for user: ${user.name}`);
          return done(null, user);
        } catch (error) {
          console.error("Authentication error:", error);
          return done(error);
        }
      }
    )
  );

  passport.serializeUser((user, done) => {
    console.log(`Serializing user: ${user.id}`);
    done(null, user.id);
  });

  passport.deserializeUser(async (id: number, done) => {
    try {
      // Removed console.log to reduce noise in logs
      const user = await storage.getUser(id);
      if (!user) {
        console.log(`User not found for id: ${id}`);
        return done(null, false);
      }
      done(null, user);
    } catch (error) {
      console.error("Deserialization error:", error);
      done(error);
    }
  });

  app.post("/api/register", async (req, res, next) => {
    try {
      console.log("Registration attempt:", req.body);
      const { email, password, name, role } = req.body;

      if (!email || !password || !name || !role) {
        console.log("Registration failed: Missing required fields");
        return res.status(400).json({ message: "All fields are required" });
      }

      const existingUser = await storage.getUserByEmail(email);
      if (existingUser) {
        console.log(`Registration failed: Email already exists: ${email}`);
        return res.status(400).json({ message: "Email already exists" });
      }

      // If registering as an employer, verify the email domain
      if (role === "employer") {
        // Extract domain from email
        const domain = email.split('@')[1];
        if (!domain) {
          return res.status(400).json({ message: "Invalid email format" });
        }

        // Check if domain is in approved list
        const approvedDomain = await storage.getApprovedDomainByName(domain);
        const isApproved = !!approvedDomain && approvedDomain.is_active;

        if (!isApproved) {
          console.log(`Registration failed: Email domain not approved: ${domain}`);
          return res.status(400).json({
            message: "Email domain not approved for employer registration, Please contact us to add your domain.",
            requiresVerification: true
          });
        }
      }

      const hashedPassword = await hashPassword(password);
      const user = await storage.createUser({
        email,
        password: hashedPassword,
        name,
        role,
        is_verified: role === "worker" // Workers are auto-verified, employers need domain verification
      });

      console.log(`User registered successfully: ${user.name}, ${user.email}, ${user.role}`);

      // Send welcome email
      try {
        const emailSuccess = await sendWelcomeEmail(user.email, user.name, user.role);
        if (emailSuccess) {
          console.log(`Welcome email sent to ${user.email}`);
        } else {
          console.warn(`Failed to send welcome email to ${user.email}`);
        }
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError);
        // Continue with registration even if email fails
      }

      req.login(user, (err) => {
        if (err) {
          console.error("Login after registration failed:", err);
          return next(err);
        }
        console.log(`Auto-login successful for: ${user.name}`);
        return res.status(201).json({
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          is_verified: user.is_verified
        });
      });
    } catch (error) {
      console.error("Registration error:", error);
      return res.status(500).json({ message: "Registration failed" });
    }
  });

  app.post("/api/login", (req, res, next) => {
    console.log("Login attempt:", { email: req.body.email });

    passport.authenticate("local", (err: any, user: Express.User | false, info: { message?: string }) => {
      if (err) {
        console.error("Login authentication error:", err);
        return next(err);
      }
      if (!user) {
        console.log("Login failed:", info?.message);
        return res.status(401).json({ message: info?.message || "Invalid email or password" });
      }

      req.login(user, (err) => {
        if (err) {
          console.error("Session creation error:", err);
          return next(err);
        }
        console.log(`Login successful for: ${user.name}`);
        return res.status(200).json({
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        });
      });
    })(req, res, next);
  });

  app.post("/api/logout", (req, res, next) => {
    console.log("Logout request received");
    if (!req.isAuthenticated()) {
      console.log("Logout: No authenticated user");
      return res.sendStatus(200);
    }

    const userId = req.user?.id;
    req.logout((err) => {
      if (err) {
        console.error("Logout error:", err);
        return next(err);
      }
      console.log(`User logged out: ${userId}`);
      res.sendStatus(200);
    });
  });

  app.get("/api/user", (req, res) => {
    if (!req.isAuthenticated()) {
      console.log("User data request: Not authenticated");
      return res.sendStatus(401);
    }
    const { id, email, name, role, is_verified, original_role } = req.user!;
    console.log(`User data request: ${name}, ${email}, ${role}`);
    res.json({ id, email, name, role, is_verified, original_role });
  });

  // Handle social login token verification
  app.post("/api/auth/social", async (req, res) => {
    try {
      const { token, provider } = req.body;

      if (!token || !provider) {
        return res.status(400).json({ message: "Token and provider are required" });
      }

      console.log(`Social login attempt with ${provider}`);

      // Verify the token with the provider
      let userData;

      switch (provider) {
        case 'google':
          // Verify Google token
          userData = await verifyGoogleToken(token);
          break;
        case 'facebook':
          // Verify Facebook token
          userData = await verifyFacebookToken(token);
          break;
        case 'apple':
          // Verify Apple token
          userData = await verifyAppleToken(token);
          break;
        case 'linkedin':
          // Verify LinkedIn token
          userData = await verifyLinkedInToken(token);
          break;
        default:
          return res.status(400).json({ message: "Invalid provider" });
      }

      if (!userData || !userData.email) {
        return res.status(401).json({ message: "Invalid token" });
      }

      // Check if user exists
      let user = await storage.getUserByEmail(userData.email);

      if (!user) {
        // Create a new user
        const hashedPassword = await hashPassword(randomBytes(16).toString('hex'));
        user = await storage.createUser({
          email: userData.email,
          password: hashedPassword,
          name: userData.name || userData.email.split('@')[0],
          role: 'worker', // Default role for social login
          is_verified: true // Social logins are auto-verified
        });

        console.log(`New user created via ${provider} login: ${user.email}`);

        // Send welcome email
        try {
          await sendWelcomeEmail(user.email, user.name, user.role);
        } catch (emailError) {
          console.error('Error sending welcome email:', emailError);
          // Continue with login even if email fails
        }
      }

      // Log the user in
      req.login(user, (err) => {
        if (err) {
          console.error("Session creation error:", err);
          return res.status(500).json({ message: "Login failed" });
        }

        console.log(`Social login successful for: ${user.name}`);
        return res.status(200).json({
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        });
      });
    } catch (error) {
      console.error("Social login error:", error);
      return res.status(500).json({ message: "Authentication failed" });
    }
  });

  // Handle phone authentication
  app.post("/api/auth/phone", async (req, res) => {
    try {
      const { phone, code } = req.body;

      if (!phone || !code) {
        return res.status(400).json({ message: "Phone number and verification code are required" });
      }

      console.log(`Phone login attempt: ${phone}`);

      // In a real implementation, you would verify the code with a service like Twilio
      // For now, we'll just check if the code is "123456" for testing
      if (code !== "123456" && process.env.NODE_ENV !== "development") {
        return res.status(401).json({ message: "Invalid verification code" });
      }

      // Check if user exists
      let user = await storage.getUserByEmail(phone); // Using phone as email

      if (!user) {
        // Create a new user
        const hashedPassword = await hashPassword(code);
        user = await storage.createUser({
          email: phone, // Using phone as email
          password: hashedPassword,
          name: phone, // Using phone as name initially
          role: 'worker', // Default role for phone login
          is_verified: true // Phone logins are auto-verified
        });

        console.log(`New user created via phone login: ${phone}`);
      }

      // Log the user in
      req.login(user, (err) => {
        if (err) {
          console.error("Session creation error:", err);
          return res.status(500).json({ message: "Login failed" });
        }

        console.log(`Phone login successful for: ${user.name}`);
        return res.status(200).json({
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role
        });
      });
    } catch (error) {
      console.error("Phone login error:", error);
      return res.status(500).json({ message: "Authentication failed" });
    }
  });
}

// Helper functions for social login token verification
async function verifyGoogleToken(token: string) {
  try {
    const response = await fetch(`https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=${token}`);
    if (!response.ok) {
      throw new Error('Failed to verify Google token');
    }
    const data = await response.json();
    return {
      email: data.email,
      name: data.name
    };
  } catch (error) {
    console.error('Google token verification error:', error);
    return null;
  }
}

async function verifyFacebookToken(token: string) {
  try {
    // First, verify the token is valid using the app secret
    const appId = process.env.FACEBOOK_APP_ID;
    const appSecret = process.env.FACEBOOK_APP_SECRET;

    if (!appId || !appSecret) {
      console.error('Facebook App ID or Secret not configured');
      throw new Error('Facebook authentication not properly configured');
    }

    // Verify the token with Facebook
    const appAccessTokenResponse = await fetch(
      `https://graph.facebook.com/oauth/access_token?client_id=${appId}&client_secret=${appSecret}&grant_type=client_credentials`
    );

    if (!appAccessTokenResponse.ok) {
      throw new Error('Failed to get app access token');
    }

    const appAccessTokenData = await appAccessTokenResponse.json();
    const appAccessToken = appAccessTokenData.access_token;

    // Debug token to verify it's valid
    const debugTokenResponse = await fetch(
      `https://graph.facebook.com/debug_token?input_token=${token}&access_token=${appAccessToken}`
    );

    if (!debugTokenResponse.ok) {
      throw new Error('Failed to debug token');
    }

    const debugTokenData = await debugTokenResponse.json();

    if (!debugTokenData.data.is_valid) {
      throw new Error('Invalid Facebook token');
    }

    // Get user data
    const response = await fetch(
      `https://graph.facebook.com/me?fields=name,email&access_token=${token}`
    );

    if (!response.ok) {
      throw new Error('Failed to get user data from Facebook');
    }

    const data = await response.json();

    if (!data.email) {
      throw new Error('Email not provided by Facebook. Please ensure your app has email permissions.');
    }

    return {
      email: data.email,
      name: data.name
    };
  } catch (error) {
    console.error('Facebook token verification error:', error);
    return null;
  }
}

async function verifyAppleToken(token: string) {
  // Apple token verification is more complex and requires additional setup
  // This is a placeholder implementation
  try {
    // In a real implementation, you would verify the token with Apple's servers
    // For now, we'll just return a placeholder user
    return {
      email: '<EMAIL>',
      name: 'Apple User'
    };
  } catch (error) {
    console.error('Apple token verification error:', error);
    return null;
  }
}

async function verifyLinkedInToken(token: string) {
  try {
    const response = await fetch('https://api.linkedin.com/v2/me', {
      headers: {
        Authorization: `Bearer ${token}`,
        'cache-control': 'no-cache',
      },
    });
    if (!response.ok) {
      throw new Error('Failed to verify LinkedIn token');
    }
    const userData = await response.json();

    // Get email address (requires separate API call)
    const emailResponse = await fetch('https://api.linkedin.com/v2/emailAddress?q=members&projection=(elements*(handle~))', {
      headers: {
        Authorization: `Bearer ${token}`,
        'cache-control': 'no-cache',
      },
    });
    if (!emailResponse.ok) {
      throw new Error('Failed to get LinkedIn email');
    }
    const emailData = await emailResponse.json();

    return {
      email: emailData.elements[0]['handle~'].emailAddress,
      name: `${userData.localizedFirstName} ${userData.localizedLastName}`
    };
  } catch (error) {
    console.error('LinkedIn token verification error:', error);
    return null;
  }
}
