#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 PathLink AWS Deployment Script');
console.log('==================================\n');

// Check if AWS CLI is installed
function checkAWSCLI() {
  try {
    // Try standard aws command first
    try {
      execSync('aws --version', { stdio: 'pipe' });
      console.log('✅ AWS CLI is installed');
      return true;
    } catch (e) {
      // Try full path if standard command fails
      execSync('"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe" --version', { stdio: 'pipe' });
      console.log('✅ AWS CLI is installed (found at full path)');
      return true;
    }
  } catch (error) {
    console.log('❌ AWS CLI is not installed');
    console.log('Please install AWS CLI: https://aws.amazon.com/cli/');
    console.log('Windows: Download installer from AWS website');
    console.log('Mac: brew install awscli');
    console.log('Linux: sudo apt install awscli');
    return false;
  }
}

// Check if Elastic Beanstalk CLI is installed
function checkEBCLI() {
  try {
    execSync('eb --version', { stdio: 'pipe' });
    console.log('✅ Elastic Beanstalk CLI is installed');
    return true;
  } catch (error) {
    console.log('❌ Elastic Beanstalk CLI is not installed');
    console.log('Install with: pip install awsebcli');
    console.log('Or: pip3 install awsebcli');
    return false;
  }
}

// Check AWS credentials
function checkAWSCredentials() {
  try {
    // Try standard aws command first
    try {
      execSync('aws sts get-caller-identity', { stdio: 'pipe' });
      console.log('✅ AWS credentials are configured');
      return true;
    } catch (e) {
      // Try full path if standard command fails
      execSync('"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe" sts get-caller-identity', { stdio: 'pipe' });
      console.log('✅ AWS credentials are configured (using full path)');
      return true;
    }
  } catch (error) {
    console.log('❌ AWS credentials not configured');
    console.log('Run: aws configure');
    console.log('You will need:');
    console.log('- AWS Access Key ID');
    console.log('- AWS Secret Access Key');
    console.log('- Default region (recommend: us-east-1)');
    return false;
  }
}

// Create necessary directories and files
function setupAWSFiles() {
  console.log('\n📁 Setting up AWS configuration files...');
  
  // Create .ebextensions directory
  if (!fs.existsSync('.ebextensions')) {
    fs.mkdirSync('.ebextensions');
    console.log('✅ Created .ebextensions directory');
  }

  // Create Procfile for Elastic Beanstalk
  const procfileContent = 'web: npm start';
  fs.writeFileSync('Procfile', procfileContent);
  console.log('✅ Created Procfile');

  // Create .ebignore file
  const ebignoreContent = `
node_modules/
.git/
.env
*.log
.DS_Store
screenshots/
test-*.json
*-test-report.json
.vscode/
.idea/
*.md
README.md
deploy-to-aws.cjs
AWS_DEPLOYMENT_GUIDE.md
`;
  fs.writeFileSync('.ebignore', ebignoreContent.trim());
  console.log('✅ Created .ebignore file');

  // Update package.json engines
  const packageJsonPath = 'package.json';
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  if (!packageJson.engines) {
    packageJson.engines = {};
  }
  packageJson.engines.node = '18.x';
  packageJson.engines.npm = '9.x';
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ Updated package.json with Node.js version');
}

// Build the application
function buildApplication() {
  console.log('\n🔨 Building application for AWS...');
  try {
    console.log('Running: npm run build:aws');
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed');
    console.log('Trying alternative build command...');
    try {
      execSync('npm run build', { stdio: 'inherit' });
      console.log('✅ Application built with standard build command');
      return true;
    } catch (error2) {
      console.log('❌ Both build commands failed');
      console.log('Please check your build configuration');
      return false;
    }
  }
}

// Initialize Elastic Beanstalk
function initializeEB() {
  console.log('\n🌐 Initializing Elastic Beanstalk...');
  try {
    // Check if already initialized
    if (fs.existsSync('.elasticbeanstalk')) {
      console.log('✅ Elastic Beanstalk already initialized');
      return true;
    }

    console.log('Running EB init...');
    execSync('eb init pathlink --region us-east-1 --platform "Node.js 18 running on 64bit Amazon Linux 2"', { 
      stdio: 'inherit' 
    });
    console.log('✅ Elastic Beanstalk initialized');
    return true;
  } catch (error) {
    console.log('❌ EB initialization failed');
    console.log('Error:', error.message);
    console.log('Try running manually: eb init pathlink --region us-east-1');
    return false;
  }
}

// Create Elastic Beanstalk environment
function createEBEnvironment() {
  console.log('\n🏗️ Creating Elastic Beanstalk environment...');
  try {
    // Check if environment already exists
    try {
      const status = execSync('eb status', { stdio: 'pipe' }).toString();
      if (status.includes('Environment details') || status.includes('Health:')) {
        console.log('✅ Environment already exists');
        return true;
      }
    } catch (e) {
      // Environment doesn't exist, create it
      console.log('No existing environment found, creating new one...');
    }

    console.log('Creating environment (this may take 5-10 minutes)...');
    execSync('eb create pathlink-production --instance-type t3.micro --region us-east-1', { 
      stdio: 'inherit' 
    });
    console.log('✅ Environment created successfully');
    return true;
  } catch (error) {
    console.log('❌ Environment creation failed');
    console.log('Error:', error.message);
    console.log('Try running manually: eb create pathlink-production --instance-type t3.micro');
    return false;
  }
}

// Deploy to Elastic Beanstalk
function deployToEB() {
  console.log('\n🚀 Deploying to Elastic Beanstalk...');
  try {
    console.log('Deploying application (this may take a few minutes)...');
    execSync('eb deploy', { stdio: 'inherit' });
    console.log('✅ Deployment successful');
    
    // Get the application URL
    try {
      const status = execSync('eb status', { stdio: 'pipe' }).toString();
      const urlMatch = status.match(/CNAME:\s*(.+)/);
      if (urlMatch) {
        const appUrl = urlMatch[1].trim();
        console.log(`\n🌍 Your application is live at: https://${appUrl}`);
        
        // Update netlify.toml with the correct URL
        updateNetlifyConfig(appUrl);
        return appUrl;
      }
    } catch (e) {
      console.log('⚠️ Could not get application URL, but deployment may have succeeded');
    }
    
    return true;
  } catch (error) {
    console.log('❌ Deployment failed');
    console.log('Error:', error.message);
    console.log('Check logs with: eb logs');
    return false;
  }
}

// Update Netlify configuration with AWS URL
function updateNetlifyConfig(awsUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    if (!fs.existsSync('netlify.toml')) {
      console.log('⚠️ netlify.toml not found, skipping update');
      return;
    }

    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace the placeholder AWS URL with the actual URL
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/pathlink-production\.us-east-1\.elasticbeanstalk\.com/g,
      `https://${awsUrl}`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated with AWS URL');
    console.log(`🔗 API calls will be redirected to: https://${awsUrl}`);
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log('Please manually update netlify.toml with your AWS URL');
  }
}

// Test the deployment
function testDeployment() {
  console.log('\n🧪 Testing deployment...');
  try {
    const status = execSync('eb status', { stdio: 'pipe' }).toString();
    console.log('Application Status:');
    console.log(status);
    
    const urlMatch = status.match(/CNAME:\s*(.+)/);
    
    if (urlMatch) {
      const appUrl = urlMatch[1].trim();
      console.log(`\n✅ Application URL: https://${appUrl}`);
      console.log(`🔍 Test your application at: https://${appUrl}/health`);
      console.log(`🌐 Frontend will be served via Netlify with API calls redirected to AWS`);
    }
  } catch (error) {
    console.log('⚠️ Could not get application status');
    console.log('Try running: eb status');
  }
}

// Main deployment function
async function deployToAWS() {
  console.log('Starting AWS deployment process...\n');

  // Step 1: Check prerequisites
  console.log('🔍 Checking prerequisites...');
  if (!checkAWSCLI()) {
    console.log('\n❌ AWS CLI not found. Please install it first.');
    return false;
  }

  if (!checkEBCLI()) {
    console.log('\n❌ Elastic Beanstalk CLI not found. Please install it first.');
    return false;
  }

  if (!checkAWSCredentials()) {
    console.log('\n❌ AWS credentials not configured. Please run: aws configure');
    return false;
  }

  // Step 2: Setup AWS files
  setupAWSFiles();

  // Step 3: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed with deployment.');
    return false;
  }

  // Step 4: Initialize EB
  if (!initializeEB()) {
    console.log('\n❌ EB initialization failed. You can try running manually:');
    console.log('eb init pathlink --region us-east-1');
    return false;
  }

  // Step 5: Create environment
  if (!createEBEnvironment()) {
    console.log('\n❌ Environment creation failed. You can try running manually:');
    console.log('eb create pathlink-production --instance-type t3.micro');
    return false;
  }

  // Step 6: Deploy
  const deployResult = deployToEB();
  if (!deployResult) {
    console.log('\n❌ Deployment failed. Check logs with: eb logs');
    return false;
  }

  // Step 7: Test deployment
  testDeployment();

  console.log('\n🎉 AWS Deployment Complete!');
  console.log('==========================');
  console.log('✅ Backend deployed to AWS Elastic Beanstalk');
  console.log('✅ Netlify configuration updated (if netlify.toml exists)');
  console.log('✅ API calls will be redirected from Netlify to AWS');
  console.log('\nNext steps:');
  console.log('1. Test your AWS application URL');
  console.log('2. Deploy your frontend to Netlify (if not already done)');
  console.log('3. Test the full application end-to-end');
  console.log('4. Configure your custom domain (optional)');
  console.log('\nUseful commands:');
  console.log('- eb status: Check application status');
  console.log('- eb logs: View application logs');
  console.log('- eb deploy: Deploy updates');
  console.log('- eb open: Open application in browser');
  console.log('- eb terminate: Terminate environment (careful!)');
  
  return true;
}

// Run the deployment
if (require.main === module) {
  deployToAWS().catch(console.error);
}

module.exports = { deployToAWS };
