import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { CareerTransitionPlan } from "@shared/schema";

export function useCareerTransitions() {
  const { toast } = useToast();
  
  const {
    data: careerPlans,
    isLoading,
    error,
  } = useQuery<CareerTransitionPlan[]>({
    queryKey: ["/api/pathcoach/career-transitions"],
    staleTime: 300000, // 5 minutes
  });
  
  const createCareerPlanMutation = useMutation({
    mutationFn: async (planData: { 
      current_role: string; 
      dream_role: string; 
      transition_plan: any;
    }) => {
      const res = await apiRequest("POST", "/api/pathcoach/career-transitions", planData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Your career transition plan has been saved.",
      });
      // Invalidate the career plans query to refetch data
      queryClient.invalidateQueries({ queryKey: ["/api/pathcoach/career-transitions"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to save career plan: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    careerPlans,
    isLoading,
    error,
    createCareerPlanMutation,
  };
}