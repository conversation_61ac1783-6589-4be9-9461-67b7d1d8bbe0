# 🚀 PathLink AWS Deployment Setup

## 📋 Overview
Deploy PathLink to AWS and set up redirect to Netlify for seamless hosting.

## 🏗️ AWS Deployment Strategy

### Option 1: AWS Elastic Beanstalk (Recommended)
- Easy deployment and management
- Auto-scaling capabilities
- Built-in load balancing
- Simple configuration

### Option 2: AWS EC2 + Application Load Balancer
- More control over infrastructure
- Custom server configuration
- Direct server access

### Option 3: AWS Lambda + API Gateway (Serverless)
- Cost-effective for low traffic
- Automatic scaling
- Pay-per-request pricing

## 🚀 Step 1: Prepare for AWS Deployment

### Install AWS CLI
```bash
# Install AWS CLI
npm install -g aws-cli

# Configure AWS credentials
aws configure
```

### Install Elastic Beanstalk CLI
```bash
# Install EB CLI
pip install awsebcli
```

## 📦 Step 2: Create AWS Deployment Configuration

### Create .ebextensions directory
```bash
mkdir .ebextensions
```

### Environment Configuration
Create `.ebextensions/01_environment.config`:
```yaml
option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: your-production-session-secret
    OPENAI_API_KEY: your-openai-api-key
    SENDGRID_API_KEY: your-sendgrid-api-key
```

### Node.js Configuration
Create `.ebextensions/02_nodejs.config`:
```yaml
option_settings:
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.17.0
  aws:elasticbeanstalk:container:nodejs:staticfiles:
    /static: dist
```

## 🔧 Step 3: Update Package.json for AWS

### Add AWS-specific scripts
```json
{
  "scripts": {
    "start": "node dist/index.js",
    "build:aws": "npm run build && npm run build:server",
    "build:server": "tsc server/index.ts --outDir dist --target es2020 --module commonjs",
    "deploy:aws": "npm run build:aws && eb deploy",
    "aws:init": "eb init",
    "aws:create": "eb create pathlink-production"
  }
}
```

## 🌐 Step 4: Create Netlify Redirect Setup

### Create Netlify Site
1. Go to Netlify dashboard
2. Create new site from Git
3. Connect your PathLink repository
4. Set build command: `npm run build`
5. Set publish directory: `dist`

### Configure Netlify Redirects
Create `public/_redirects`:
```
# Redirect all traffic to AWS when needed
/api/* https://pathlink-aws.elasticbeanstalk.com/api/:splat 200
/* /index.html 200
```

### Create Netlify Configuration
Create `netlify.toml`:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/api/*"
  to = "https://pathlink-aws.elasticbeanstalk.com/api/:splat"
  status = 200
  force = true

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
```

## 🚀 Step 5: AWS Deployment Commands

### Initialize Elastic Beanstalk
```bash
# Initialize EB application
eb init pathlink

# Create production environment
eb create pathlink-production

# Deploy application
eb deploy
```

### Alternative: Docker Deployment
Create `Dockerfile`:
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 8080

CMD ["npm", "start"]
```

Create `docker-compose.yml`:
```yaml
version: '3.8'
services:
  pathlink:
    build: .
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - SESSION_SECRET=${SESSION_SECRET}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
```

## 🔄 Step 6: Set Up AWS-Netlify Integration

### Option A: Frontend on Netlify, Backend on AWS
- Deploy frontend (React app) to Netlify
- Deploy backend (Node.js API) to AWS
- Configure CORS and API endpoints

### Option B: Full App on AWS with Netlify CDN
- Deploy full application to AWS
- Use Netlify as CDN/proxy
- Set up domain redirects

### Option C: Hybrid Deployment
- Primary deployment on AWS
- Netlify as backup/failover
- Load balancer routing

## 🌍 Step 7: Domain and SSL Configuration

### AWS Route 53 Setup
```bash
# Create hosted zone
aws route53 create-hosted-zone --name pathlink.com --caller-reference $(date +%s)

# Create SSL certificate
aws acm request-certificate --domain-name pathlink.com --domain-name *.pathlink.com
```

### Netlify Domain Setup
1. Add custom domain in Netlify
2. Configure DNS records
3. Enable HTTPS/SSL

## 📊 Step 8: Monitoring and Logging

### AWS CloudWatch Setup
```yaml
# .ebextensions/03_cloudwatch.config
option_settings:
  aws:elasticbeanstalk:cloudwatch:logs:
    StreamLogs: true
    DeleteOnTerminate: false
    RetentionInDays: 7
```

### Application Monitoring
```javascript
// Add to server/index.ts
import AWS from 'aws-sdk';

const cloudwatch = new AWS.CloudWatch();

// Custom metrics
const putMetric = (metricName, value) => {
  cloudwatch.putMetricData({
    Namespace: 'PathLink/Application',
    MetricData: [{
      MetricName: metricName,
      Value: value,
      Unit: 'Count'
    }]
  }).promise();
};
```

## 🔧 Step 9: Environment Variables Management

### AWS Systems Manager Parameter Store
```bash
# Store sensitive environment variables
aws ssm put-parameter --name "/pathlink/database-url" --value "your-db-url" --type "SecureString"
aws ssm put-parameter --name "/pathlink/openai-key" --value "your-openai-key" --type "SecureString"
```

### Update application to use Parameter Store
```javascript
// server/config.ts
import AWS from 'aws-sdk';

const ssm = new AWS.SSM();

export const getParameter = async (name) => {
  const result = await ssm.getParameter({
    Name: name,
    WithDecryption: true
  }).promise();
  return result.Parameter.Value;
};
```

## 🚀 Step 10: Deployment Script

Create `deploy-aws.sh`:
```bash
#!/bin/bash

echo "🚀 Starting PathLink AWS Deployment..."

# Build application
echo "📦 Building application..."
npm run build:aws

# Deploy to Elastic Beanstalk
echo "🌐 Deploying to AWS..."
eb deploy

# Update Netlify redirects
echo "🔄 Updating Netlify configuration..."
# Add any Netlify-specific updates here

echo "✅ Deployment complete!"
echo "🌍 AWS URL: $(eb status | grep CNAME | awk '{print $2}')"
echo "🌐 Netlify URL: https://pathlink.netlify.app"
```

## 📋 Step 11: Testing Deployment

### Health Check Endpoint
```javascript
// Add to server/routes.ts
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version
  });
});
```

### Deployment Verification
```bash
# Test AWS deployment
curl https://pathlink-aws.elasticbeanstalk.com/health

# Test Netlify deployment
curl https://pathlink.netlify.app/health
```

## 🎯 Benefits of This Setup

1. **Reliability**: Multiple deployment targets
2. **Performance**: AWS for backend, Netlify CDN for frontend
3. **Scalability**: AWS auto-scaling capabilities
4. **Cost-Effective**: Pay-per-use pricing
5. **Easy Management**: Simple deployment commands
6. **Backup Strategy**: Netlify as failover option

## 🔧 Next Steps

1. Choose deployment strategy (Elastic Beanstalk recommended)
2. Set up AWS credentials
3. Configure environment variables
4. Run deployment scripts
5. Test both AWS and Netlify endpoints
6. Configure domain and SSL
7. Set up monitoring and alerts

This setup gives you the best of both worlds - AWS's powerful infrastructure with Netlify's ease of use!
