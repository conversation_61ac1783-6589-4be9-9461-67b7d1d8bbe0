name: Repository Ruleset Enforcement

on:
  push:
    branches: [ main, master, staging, production ]
  pull_request:
    branches: [ main, master, staging, production ]
  workflow_dispatch:

jobs:
  ruleset-compliance:
    runs-on: ubuntu-latest
    name: Enforce Repository Rules
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for comprehensive checks
    
    - name: Check for sensitive files
      run: |
        echo "🔍 Checking for sensitive files that should not be committed..."
        
        # Check for environment files
        if find . -name ".env" -not -path "./.git/*" | grep -q .; then
          echo "❌ Found .env files in repository"
          find . -name ".env" -not -path "./.git/*"
          exit 1
        fi
        
        # Check for key files
        if find . -name "*.key" -o -name "*.pem" -o -name "*.p12" -o -name "*.pfx" | grep -q .; then
          echo "❌ Found key/certificate files in repository"
          find . -name "*.key" -o -name "*.pem" -o -name "*.p12" -o -name "*.pfx"
          exit 1
        fi
        
        # Check for credential files
        if find . -name "credentials.json" -o -name "secrets.json" -o -name "config/production.json" | grep -q .; then
          echo "❌ Found credential files in repository"
          find . -name "credentials.json" -o -name "secrets.json" -o -name "config/production.json"
          exit 1
        fi
        
        echo "✅ No sensitive files found"
    
    - name: Check file sizes
      run: |
        echo "📏 Checking for oversized files..."
        
        # Find files larger than 10MB
        large_files=$(find . -type f -size +10M -not -path "./.git/*" -not -path "./node_modules/*")
        
        if [ -n "$large_files" ]; then
          echo "❌ Found files larger than 10MB:"
          echo "$large_files"
          exit 1
        fi
        
        echo "✅ All files are within size limits"
    
    - name: Check for restricted file extensions
      run: |
        echo "🚫 Checking for restricted file extensions..."
        
        # Check for executable files
        if find . -name "*.exe" -o -name "*.bat" -o -name "*.cmd" -o -name "*.com" -o -name "*.scr" -o -name "*.msi" | grep -q .; then
          echo "❌ Found executable files in repository"
          find . -name "*.exe" -o -name "*.bat" -o -name "*.cmd" -o -name "*.com" -o -name "*.scr" -o -name "*.msi"
          exit 1
        fi
        
        # Check for archive files (potential source code extraction)
        if find . -name "*.zip" -o -name "*.tar" -o -name "*.tar.gz" -o -name "*.rar" -o -name "*.7z" -not -path "./node_modules/*" | grep -q .; then
          echo "❌ Found archive files in repository"
          find . -name "*.zip" -o -name "*.tar" -o -name "*.tar.gz" -o -name "*.rar" -o -name "*.7z" -not -path "./node_modules/*"
          exit 1
        fi
        
        # Check for database files
        if find . -name "*.db" -o -name "*.sqlite" -o -name "*.mdb" | grep -q .; then
          echo "❌ Found database files in repository"
          find . -name "*.db" -o -name "*.sqlite" -o -name "*.mdb"
          exit 1
        fi
        
        echo "✅ No restricted file extensions found"
    
    - name: Verify security configurations
      run: |
        echo "🔧 Verifying security configurations..."
        
        # Check if security files exist and haven't been tampered with
        required_files=(
          ".github/workflows/codespace-security.yml"
          ".devcontainer/devcontainer.json"
          ".devcontainer/Dockerfile"
          ".devcontainer/security-monitor.sh"
          ".github/CODESPACE_SECURITY_POLICY.md"
        )
        
        for file in "${required_files[@]}"; do
          if [ ! -f "$file" ]; then
            echo "❌ Required security file missing: $file"
            exit 1
          fi
        done
        
        # Check if security monitor script is executable
        if [ ! -x ".devcontainer/security-monitor.sh" ]; then
          echo "❌ Security monitor script is not executable"
          exit 1
        fi
        
        echo "✅ All security configurations verified"
    
    - name: Check commit signatures
      if: github.event_name == 'push'
      run: |
        echo "✍️ Checking commit signatures..."
        
        # Get the range of commits to check
        if [ "${{ github.event.before }}" != "0000000000000000000000000000000000000000" ]; then
          commit_range="${{ github.event.before }}..${{ github.event.after }}"
        else
          # For new branches, check the last commit
          commit_range="${{ github.event.after }}^..${{ github.event.after }}"
        fi
        
        # Check each commit in the range
        unsigned_commits=()
        for commit in $(git rev-list $commit_range); do
          if ! git verify-commit $commit 2>/dev/null; then
            unsigned_commits+=($commit)
          fi
        done
        
        if [ ${#unsigned_commits[@]} -gt 0 ]; then
          echo "⚠️ Found unsigned commits:"
          for commit in "${unsigned_commits[@]}"; do
            echo "  - $commit: $(git log --format='%s' -n 1 $commit)"
          done
          echo "ℹ️ Consider enabling commit signing for enhanced security"
        else
          echo "✅ All commits are signed"
        fi
    
    - name: Validate branch protection compliance
      run: |
        echo "🛡️ Validating branch protection compliance..."
        
        # Check if this is a direct push to protected branch (should not happen with proper rules)
        if [ "${{ github.event_name }}" == "push" ] && [ "${{ github.event.pull_request }}" == "" ]; then
          protected_branches=("main" "master" "production" "staging")
          current_branch="${{ github.ref_name }}"
          
          for branch in "${protected_branches[@]}"; do
            if [ "$current_branch" == "$branch" ]; then
              echo "❌ Direct push to protected branch '$branch' detected"
              echo "All changes to protected branches must go through pull requests"
              exit 1
            fi
          done
        fi
        
        echo "✅ Branch protection compliance verified"
    
    - name: Security audit summary
      run: |
        echo "📊 Security Audit Summary"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "✅ No sensitive files found in repository"
        echo "✅ All files within size limits (10MB max)"
        echo "✅ No restricted file extensions detected"
        echo "✅ Security configurations verified"
        echo "✅ Repository ruleset compliance confirmed"
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
        echo "🔒 PathLink repository security: COMPLIANT"
    
    - name: Notify security team on failure
      if: failure()
      run: |
        echo "🚨 Security compliance check failed!"
        echo "This indicates a potential security violation or policy breach."
        echo "The security team has been notified and will review this incident."
        
        # In a real implementation, you would send notifications here
        # Example: curl -X POST -H 'Content-type: application/json' \
        #   --data '{"text":"Security compliance failure in PathLink repository"}' \
        #   $SLACK_WEBHOOK_URL

  codespace-security-check:
    runs-on: ubuntu-latest
    name: Verify Codespace Security Configuration
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Validate Codespace security configuration
      run: |
        echo "🔍 Validating Codespace security configuration..."
        
        # Check devcontainer configuration
        if [ ! -f ".devcontainer/devcontainer.json" ]; then
          echo "❌ Missing devcontainer configuration"
          exit 1
        fi
        
        # Verify security restrictions are in place
        if ! grep -q "restrict-downloads" .devcontainer/Dockerfile; then
          echo "❌ Security restrictions not found in Dockerfile"
          exit 1
        fi
        
        # Check security monitor script
        if [ ! -f ".devcontainer/security-monitor.sh" ]; then
          echo "❌ Security monitor script missing"
          exit 1
        fi
        
        # Verify security policy documentation
        if [ ! -f ".github/CODESPACE_SECURITY_POLICY.md" ]; then
          echo "❌ Security policy documentation missing"
          exit 1
        fi
        
        echo "✅ Codespace security configuration validated"
    
    - name: Test security restrictions
      run: |
        echo "🧪 Testing security restriction patterns..."
        
        # Test that security monitor script has proper restrictions
        if ! grep -q "wget\|curl\|scp\|rsync" .devcontainer/security-monitor.sh; then
          echo "❌ Security monitor missing command restrictions"
          exit 1
        fi
        
        # Test that Dockerfile removes dangerous tools
        if ! grep -q "remove.*wget\|remove.*curl" .devcontainer/Dockerfile; then
          echo "❌ Dockerfile doesn't remove dangerous tools"
          exit 1
        fi
        
        echo "✅ Security restrictions properly configured"
