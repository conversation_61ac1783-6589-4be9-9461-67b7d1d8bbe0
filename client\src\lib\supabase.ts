// This file is kept for backward compatibility
// It now uses direct API calls instead of Supabase

import * as apiClient from './api-client';

console.log('Using API client instead of Supabase');

// Export all API client functions directly
export * from './api-client';

// Create a minimal compatibility layer for components that expect Supabase
export const supabase = {
  auth: {
    signUp: apiClient.signUp,
    signInWithPassword: apiClient.signIn,
    signOut: apiClient.signOut,
    resetPasswordForEmail: apiClient.resetPassword,
    getUser: apiClient.getCurrentUser
  }
};


