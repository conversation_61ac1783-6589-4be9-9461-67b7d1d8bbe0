#!/bin/bash

# PathLink Codespace Security Startup Script
# This script enforces security restrictions on container startup

echo "🔒 Initializing PathLink Security Environment..."

# Create security log directory
mkdir -p /var/log
touch /var/log/pathlink-security.log
chmod 666 /var/log/pathlink-security.log

# Log startup event
echo "[$(date '+%Y-%m-%d %H:%M:%S')] SYSTEM: Codespace security initialized for user: ${USER:-unknown}" >> /var/log/pathlink-security.log

# Set up command restrictions in user's shell profile
cat >> ~/.bashrc << 'EOF'

# PathLink Security Restrictions
export PATHLINK_SECURITY_ENABLED=true

# Override dangerous commands with security warnings
wget() { /usr/local/bin/restrict-downloads "wget" "$@"; }
curl() { /usr/local/bin/restrict-downloads "curl" "$@"; }
scp() { /usr/local/bin/restrict-downloads "scp" "$@"; }
rsync() { /usr/local/bin/restrict-downloads "rsync" "$@"; }
ftp() { /usr/local/bin/restrict-downloads "ftp" "$@"; }
sftp() { /usr/local/bin/restrict-downloads "sftp" "$@"; }
nc() { /usr/local/bin/restrict-downloads "nc" "$@"; }
netcat() { /usr/local/bin/restrict-downloads "netcat" "$@"; }
tar() { /usr/local/bin/restrict-downloads "tar" "$@"; }
zip() { /usr/local/bin/restrict-downloads "zip" "$@"; }
unzip() { /usr/local/bin/restrict-downloads "unzip" "$@"; }
gzip() { /usr/local/bin/restrict-downloads "gzip" "$@"; }
gunzip() { /usr/local/bin/restrict-downloads "gunzip" "$@"; }

# Export functions to make them available in subshells
export -f wget curl scp rsync ftp sftp nc netcat tar zip unzip gzip gunzip

# Security status command
pathlink-security() {
    case "$1" in
        "status")
            /usr/local/bin/security-monitor status
            ;;
        "log")
            /usr/local/bin/security-monitor log
            ;;
        "help")
            echo "PathLink Security Commands:"
            echo "  pathlink-security status  - Show security status"
            echo "  pathlink-security log     - Show security events"
            echo "  pathlink-security help    - Show this help"
            ;;
        *)
            echo "PathLink Codespace Security is ACTIVE"
            echo "Use 'pathlink-security help' for available commands"
            ;;
    esac
}

# Display security notice on terminal startup
if [[ -t 1 ]] && [[ "$PATHLINK_SECURITY_NOTICE_SHOWN" != "true" ]]; then
    export PATHLINK_SECURITY_NOTICE_SHOWN=true
    echo ""
    echo "🔒 PathLink Codespace Security Notice"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "✅ You can: Edit code, run npm commands, create PRs"
    echo "🚫 Restricted: Downloads, file transfers, code copying"
    echo "📋 Use 'pathlink-security status' for more information"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
fi

EOF

# Set up git restrictions
cat >> ~/.gitconfig << 'EOF'
[alias]
    # Override potentially dangerous git commands
    clone = "!echo '🚫 Git clone is restricted in this environment. You can only work with the current repository.' && false"
    archive = "!echo '🚫 Git archive is restricted in this environment.' && false"
    bundle = "!echo '🚫 Git bundle is restricted in this environment.' && false"
    
[core]
    # Prevent accidental credential exposure
    askpass = /usr/local/bin/restrict-downloads
    
[credential]
    # Use secure credential handling
    helper = cache --timeout=3600
EOF

# Create restricted environment indicators
echo "PATHLINK_RESTRICTED_ENVIRONMENT=true" >> ~/.profile
echo "CODESPACE_SECURITY_LEVEL=maximum" >> ~/.profile

# Set up file system restrictions
# Create symbolic links to security monitor for common commands
ln -sf /usr/local/bin/restrict-downloads /usr/local/bin/wget 2>/dev/null || true
ln -sf /usr/local/bin/restrict-downloads /usr/local/bin/curl 2>/dev/null || true

# Set restrictive permissions on sensitive directories
chmod 700 ~/.ssh 2>/dev/null || true
chmod 600 ~/.ssh/* 2>/dev/null || true

# Create security monitoring cron job (if cron is available)
if command -v crontab >/dev/null 2>&1; then
    echo "*/5 * * * * /usr/local/bin/security-monitor status >> /var/log/pathlink-security.log 2>&1" | crontab - 2>/dev/null || true
fi

# Final security check
echo "🔒 Security restrictions applied successfully"
echo "📋 Security log: /var/log/pathlink-security.log"
echo "🛡️  Environment is now secured for collaborative development"

# Log completion
echo "[$(date '+%Y-%m-%d %H:%M:%S')] SYSTEM: Security startup completed successfully" >> /var/log/pathlink-security.log
