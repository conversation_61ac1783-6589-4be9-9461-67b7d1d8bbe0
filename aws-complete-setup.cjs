#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 PathLink Complete AWS Setup & Deployment');
console.log('===========================================\n');

// Read AWS credentials from CSV file
function readAWSCredentials() {
  try {
    const csvPath = path.join(__dirname, 'pathlink-deployer_accessKeys.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.trim().split('\n');
    
    if (lines.length < 2) {
      throw new Error('Invalid CSV format');
    }
    
    const [accessKeyId, secretAccessKey] = lines[1].split(',');
    
    return {
      accessKeyId: accessKeyId.trim(),
      secretAccessKey: secretAccessKey.trim()
    };
  } catch (error) {
    console.error('❌ Error reading AWS credentials:', error.message);
    console.log('Please ensure pathlink-deployer_accessKeys.csv exists and has the correct format');
    process.exit(1);
  }
}

// Create AWS credentials directory and files
function setupAWSCredentials(credentials) {
  const homeDir = require('os').homedir();
  const awsDir = path.join(homeDir, '.aws');
  
  // Create .aws directory if it doesn't exist
  if (!fs.existsSync(awsDir)) {
    fs.mkdirSync(awsDir, { recursive: true });
    console.log('✅ Created .aws directory');
  }
  
  // Create credentials file
  const credentialsContent = `[default]
aws_access_key_id = ${credentials.accessKeyId}
aws_secret_access_key = ${credentials.secretAccessKey}
`;
  
  const credentialsPath = path.join(awsDir, 'credentials');
  fs.writeFileSync(credentialsPath, credentialsContent);
  console.log('✅ AWS credentials configured');
  
  // Create config file
  const configContent = `[default]
region = us-east-1
output = json
`;
  
  const configPath = path.join(awsDir, 'config');
  fs.writeFileSync(configPath, configContent);
  console.log('✅ AWS config configured');
}

// Check if a command exists
function commandExists(command) {
  try {
    execSync(`${command} --version`, { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

// Install AWS CLI on Windows
function installAWSCLI() {
  console.log('📦 Installing AWS CLI...');
  
  // Try different installation methods
  const installMethods = [
    {
      name: 'winget',
      command: 'winget install Amazon.AWSCLI --accept-package-agreements --accept-source-agreements',
      description: 'Windows Package Manager'
    },
    {
      name: 'chocolatey',
      command: 'choco install awscli -y',
      description: 'Chocolatey Package Manager'
    }
  ];
  
  for (const method of installMethods) {
    try {
      console.log(`Trying ${method.description}...`);
      execSync(method.command, { stdio: 'inherit' });
      console.log(`✅ AWS CLI installed via ${method.name}`);
      return true;
    } catch (error) {
      console.log(`⚠️ ${method.name} installation failed, trying next method...`);
    }
  }
  
  console.log('❌ Automatic installation failed.');
  console.log('Please install AWS CLI manually:');
  console.log('1. Download from: https://awscli.amazonaws.com/AWSCLIV2.msi');
  console.log('2. Run the installer');
  console.log('3. Restart your terminal');
  console.log('4. Run this script again');
  return false;
}

// Install EB CLI
function installEBCLI() {
  console.log('📦 Installing Elastic Beanstalk CLI...');
  
  const installMethods = [
    'pip install awsebcli --upgrade --user',
    'pip3 install awsebcli --upgrade --user',
    'python -m pip install awsebcli --upgrade --user',
    'python3 -m pip install awsebcli --upgrade --user'
  ];
  
  for (const command of installMethods) {
    try {
      console.log(`Trying: ${command}`);
      execSync(command, { stdio: 'inherit' });
      console.log('✅ EB CLI installed successfully');
      return true;
    } catch (error) {
      console.log(`⚠️ Command failed, trying next method...`);
    }
  }
  
  console.log('❌ EB CLI installation failed.');
  console.log('Please install Python and pip, then run: pip install awsebcli');
  return false;
}

// Setup AWS files for deployment
function setupAWSFiles() {
  console.log('\n📁 Setting up AWS deployment files...');
  
  // Create .ebextensions directory
  if (!fs.existsSync('.ebextensions')) {
    fs.mkdirSync('.ebextensions');
    console.log('✅ Created .ebextensions directory');
  }

  // Create environment configuration
  const envConfig = `option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: pathlink-production-secret-2024
    JWT_SECRET: pathlink-jwt-secret-2024
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.19.0
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.micro
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
`;

  fs.writeFileSync('.ebextensions/environment.config', envConfig);
  console.log('✅ Created environment configuration');

  // Update Procfile
  const procfileContent = 'web: npm start';
  fs.writeFileSync('Procfile', procfileContent);
  console.log('✅ Created/Updated Procfile');

  // Create .ebignore file
  const ebignoreContent = `node_modules/
.git/
.env
*.log
.DS_Store
screenshots/
test-*.json
*-test-report.json
.vscode/
.idea/
*.md
README.md
.ebextensions/
`;
  fs.writeFileSync('.ebignore', ebignoreContent);
  console.log('✅ Created .ebignore file');

  // Update package.json engines
  const packageJsonPath = path.join(__dirname, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  if (!packageJson.engines) {
    packageJson.engines = {};
  }
  packageJson.engines.node = '18.x';
  packageJson.engines.npm = '9.x';
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ Updated package.json with Node.js version');
}

// Build application for AWS
function buildApplication() {
  console.log('\n🔨 Building application for AWS...');
  try {
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

// Test AWS connection
function testAWSConnection() {
  try {
    console.log('\n🧪 Testing AWS connection...');
    const result = execSync('aws sts get-caller-identity', { encoding: 'utf8' });
    const identity = JSON.parse(result);
    console.log('✅ AWS connection successful');
    console.log(`   Account: ${identity.Account}`);
    console.log(`   User: ${identity.Arn}`);
    return true;
  } catch (error) {
    console.log('❌ AWS connection test failed:', error.message);
    return false;
  }
}

// Main setup function
async function main() {
  console.log('Starting complete AWS setup...\n');
  
  // Step 1: Read credentials
  const credentials = readAWSCredentials();
  console.log('✅ AWS credentials loaded from CSV');
  
  // Step 2: Setup credentials
  setupAWSCredentials(credentials);
  
  // Step 3: Check/Install AWS CLI
  if (!commandExists('aws')) {
    console.log('⚠️ AWS CLI not found. Installing...');
    if (!installAWSCLI()) {
      process.exit(1);
    }
    
    // Wait a moment for installation to complete
    console.log('⏳ Waiting for AWS CLI installation to complete...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  } else {
    console.log('✅ AWS CLI is available');
  }
  
  // Step 4: Check/Install EB CLI
  if (!commandExists('eb')) {
    console.log('⚠️ EB CLI not found. Installing...');
    if (!installEBCLI()) {
      process.exit(1);
    }
    
    // Wait a moment for installation to complete
    console.log('⏳ Waiting for EB CLI installation to complete...');
    await new Promise(resolve => setTimeout(resolve, 3000));
  } else {
    console.log('✅ EB CLI is available');
  }
  
  // Step 5: Test AWS connection
  if (!testAWSConnection()) {
    console.log('\n❌ AWS connection failed. Please restart your terminal and try again.');
    process.exit(1);
  }
  
  // Step 6: Setup AWS files
  setupAWSFiles();
  
  // Step 7: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed with deployment.');
    process.exit(1);
  }
  
  console.log('\n🎉 AWS Setup Complete!');
  console.log('======================');
  console.log('✅ AWS CLI installed and configured');
  console.log('✅ EB CLI installed');
  console.log('✅ AWS connection tested');
  console.log('✅ Deployment files created');
  console.log('✅ Application built for AWS');
  console.log('\n🚀 Ready for deployment!');
  console.log('Run: node deploy-to-aws.js');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
