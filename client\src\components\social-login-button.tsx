import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Import icons
import {
  FaGoogle,
  FaFacebook,
  FaApple,
  FaLinkedin,
  FaPhone
} from 'react-icons/fa';

export type SocialProvider = 'google' | 'facebook' | 'apple' | 'linkedin' | 'phone';

interface SocialLoginButtonProps {
  provider: SocialProvider;
  onClick: () => void;
  className?: string;
  fullWidth?: boolean;
  isLoading?: boolean;
}

const providerConfig = {
  google: {
    icon: FaGoogle,
    text: 'Google',
    bgColor: 'bg-white dark:bg-gray-800',
    hoverColor: 'hover:bg-gray-100 dark:hover:bg-gray-700',
    textColor: 'text-gray-800 dark:text-white',
    borderColor: 'border-gray-300 dark:border-gray-600',
  },
  facebook: {
    icon: FaFacebook,
    text: 'Facebook',
    bgColor: 'bg-[#1877F2]',
    hoverColor: 'hover:bg-[#166FE5]',
    textColor: 'text-white',
    borderColor: 'border-[#1877F2]',
  },
  apple: {
    icon: FaApple,
    text: 'Apple',
    bgColor: 'bg-black dark:bg-black',
    hoverColor: 'hover:bg-gray-900 dark:hover:bg-gray-900',
    textColor: 'text-white',
    borderColor: 'border-black dark:border-black',
  },
  linkedin: {
    icon: FaLinkedin,
    text: 'LinkedIn',
    bgColor: 'bg-[#0A66C2]',
    hoverColor: 'hover:bg-[#004182]',
    textColor: 'text-white',
    borderColor: 'border-[#0A66C2]',
  },
  phone: {
    icon: FaPhone,
    text: 'Phone',
    bgColor: 'bg-green-600',
    hoverColor: 'hover:bg-green-700',
    textColor: 'text-white',
    borderColor: 'border-green-600',
  },
};

export function SocialLoginButton({
  provider,
  onClick,
  className,
  fullWidth = false,
  isLoading = false,
}: SocialLoginButtonProps) {
  const config = providerConfig[provider];
  const Icon = config.icon;

  return (
    <Button
      type="button"
      variant="outline"
      onClick={onClick}
      disabled={isLoading}
      className={cn(
        'flex items-center gap-1 sm:gap-2 border transition-colors duration-200 px-2 sm:px-4 py-2 min-h-[40px]',
        config.bgColor,
        config.hoverColor,
        config.textColor,
        config.borderColor,
        fullWidth ? 'w-full justify-center' : '',
        className
      )}
    >
      {isLoading ? (
        <div className="h-3 w-3 sm:h-4 sm:w-4 animate-spin rounded-full border-2 border-current border-t-transparent flex-shrink-0" />
      ) : (
        <Icon className="h-3 w-3 sm:h-4 sm:w-4 flex-shrink-0" />
      )}
      <span className="text-xs sm:text-sm font-medium truncate">
        <span className="hidden sm:inline">Continue with </span>
        <span className="sm:hidden">Sign in with </span>
        {config.text}
      </span>
    </Button>
  );
}
