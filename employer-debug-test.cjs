const { chromium } = require('playwright');
const fs = require('fs');

class EmployerDebugTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5000';
  }

  async setup() {
    console.log('🚀 Setting up debug test for employer login...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 2000
    });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1280, height: 720 });
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async takeScreenshot(name) {
    const filename = `screenshots/debug-${name}-${Date.now()}.png`;
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    await this.page.screenshot({ path: filename, fullPage: true });
    console.log(`📸 Screenshot saved: ${filename}`);
    return filename;
  }

  async debugAuthPage() {
    console.log('\n🔍 DEBUGGING AUTH PAGE...');

    try {
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(3000);

      // Take screenshot of auth page
      await this.takeScreenshot('auth-page-debug');

      // Get page title
      const title = await this.page.title();
      console.log(`📄 Page title: ${title}`);

      // Get page URL
      const url = this.page.url();
      console.log(`🔗 Current URL: ${url}`);

      // Check all input elements
      const allInputs = await this.page.locator('input').count();
      console.log(`📝 Total inputs found: ${allInputs}`);

      // Check specific input types
      const emailInputs = await this.page.locator('input[type="email"]').count();
      const passwordInputs = await this.page.locator('input[type="password"]').count();
      const textInputs = await this.page.locator('input[type="text"]').count();

      console.log(`📧 Email inputs: ${emailInputs}`);
      console.log(`🔒 Password inputs: ${passwordInputs}`);
      console.log(`📝 Text inputs: ${textInputs}`);

      // Check all buttons
      const allButtons = await this.page.locator('button').count();
      console.log(`🔘 Total buttons found: ${allButtons}`);

      // List all button texts
      const buttons = await this.page.locator('button').all();
      for (let i = 0; i < buttons.length; i++) {
        try {
          const buttonText = await buttons[i].textContent();
          console.log(`  Button ${i + 1}: "${buttonText}"`);
        } catch (e) {
          console.log(`  Button ${i + 1}: [Could not read text]`);
        }
      }

      // Check for forms
      const forms = await this.page.locator('form').count();
      console.log(`📋 Forms found: ${forms}`);

      // Get page content to look for login-related text
      const pageContent = await this.page.textContent('body');
      const loginKeywords = ['login', 'sign in', 'email', 'password', 'auth'];
      const foundKeywords = loginKeywords.filter(keyword => 
        pageContent.toLowerCase().includes(keyword.toLowerCase())
      );
      console.log(`🔍 Login keywords found: ${foundKeywords.join(', ')}`);

      // Try to find any input that might be for email
      const possibleEmailInputs = await this.page.locator('input[placeholder*="email"], input[name*="email"], input[id*="email"]').count();
      console.log(`📧 Possible email inputs: ${possibleEmailInputs}`);

      // Try to find any input that might be for password
      const possiblePasswordInputs = await this.page.locator('input[placeholder*="password"], input[name*="password"], input[id*="password"]').count();
      console.log(`🔒 Possible password inputs: ${possiblePasswordInputs}`);

    } catch (error) {
      console.error('❌ Debug test failed:', error);
    }
  }

  async testEmployerLogin() {
    console.log('\n🔐 TESTING EMPLOYER LOGIN WITH MULTIPLE STRATEGIES...');

    try {
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(3000);

      // Strategy 1: Look for any input fields and try to use them
      const allInputs = await this.page.locator('input').all();
      
      if (allInputs.length >= 2) {
        console.log(`📝 Found ${allInputs.length} inputs, attempting login...`);
        
        // Assume first input is email, second is password
        await allInputs[0].fill('<EMAIL>');
        await allInputs[1].fill('password123');
        
        await this.takeScreenshot('employer-login-filled');
        
        // Look for any button that might be a submit button
        const buttons = await this.page.locator('button').all();
        
        for (const button of buttons) {
          const buttonText = await button.textContent();
          if (buttonText && (
            buttonText.toLowerCase().includes('sign in') ||
            buttonText.toLowerCase().includes('login') ||
            buttonText.toLowerCase().includes('submit')
          )) {
            console.log(`🔘 Clicking button: "${buttonText}"`);
            await button.click();
            await this.page.waitForTimeout(4000);
            break;
          }
        }

        // Check if login was successful
        const currentUrl = this.page.url();
        console.log(`🔗 After login attempt, URL: ${currentUrl}`);
        
        if (currentUrl.includes('/dashboard')) {
          console.log('✅ Login successful! Redirected to dashboard');
          await this.takeScreenshot('employer-dashboard-success');
          return true;
        } else {
          console.log('❌ Login failed or no redirect');
          await this.takeScreenshot('employer-login-failed');
          return false;
        }
      } else {
        console.log('❌ Not enough input fields found for login');
        return false;
      }

    } catch (error) {
      console.error('❌ Employer login test failed:', error);
      return false;
    }
  }

  async testEmployerDashboardAccess() {
    console.log('\n📊 TESTING DIRECT DASHBOARD ACCESS...');

    try {
      // Try to access dashboard directly
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForTimeout(3000);

      const currentUrl = this.page.url();
      console.log(`🔗 Dashboard access URL: ${currentUrl}`);

      if (currentUrl.includes('/dashboard')) {
        console.log('✅ Dashboard accessible');
        await this.takeScreenshot('dashboard-direct-access');
        
        // Check dashboard content
        const pageContent = await this.page.textContent('body');
        console.log(`📄 Dashboard content length: ${pageContent.length} characters`);
        
        // Look for employer-specific content
        const employerTerms = ['job', 'candidate', 'applicant', 'hire', 'posting'];
        const foundTerms = employerTerms.filter(term => 
          pageContent.toLowerCase().includes(term.toLowerCase())
        );
        console.log(`🎯 Employer terms found: ${foundTerms.join(', ')}`);
        
        return true;
      } else {
        console.log('❌ Redirected away from dashboard');
        await this.takeScreenshot('dashboard-redirect');
        return false;
      }

    } catch (error) {
      console.error('❌ Dashboard access test failed:', error);
      return false;
    }
  }

  async runDebugTests() {
    console.log('🚀 STARTING EMPLOYER DEBUG TESTS...\n');

    try {
      await this.setup();

      await this.debugAuthPage();
      const loginSuccess = await this.testEmployerLogin();
      
      if (loginSuccess) {
        console.log('\n✅ Proceeding with employer feature tests...');
        // Add more employer-specific tests here
      } else {
        console.log('\n⚠️ Login failed, testing dashboard access directly...');
        await this.testEmployerDashboardAccess();
      }

    } catch (error) {
      console.error('Debug tests failed:', error);
    } finally {
      await this.cleanup();
    }

    console.log('\n🎯 DEBUG TEST COMPLETE');
    console.log('📸 Check the screenshots/ directory for visual evidence');
  }
}

// Run debug tests
const debugTester = new EmployerDebugTest();
debugTester.runDebugTests().catch(console.error);
