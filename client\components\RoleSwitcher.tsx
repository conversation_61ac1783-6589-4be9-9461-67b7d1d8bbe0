import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { Loader2 } from 'lucide-react';

/**
 * RoleSwitcher component allows employers to switch to worker role
 * This is a one-way switch - workers cannot switch to employer role
 */
export function RoleSwitcher() {
  const { user, refreshUser } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Only show for employers
  if (!user || user.role !== 'employer') {
    return null;
  }

  const handleRoleSwitch = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/switch-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to switch role');
      }

      const data = await response.json();
      
      // Refresh the user data
      await refreshUser();

      toast({
        title: 'Role switched successfully',
        description: `You are now a ${data.user.role}`,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error switching role:', error);
      toast({
        title: 'Error switching role',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      variant="outline" 
      onClick={handleRoleSwitch} 
      disabled={isLoading}
      className="w-full"
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Switching...
        </>
      ) : (
        'Switch to Worker Role'
      )}
    </Button>
  );
}
