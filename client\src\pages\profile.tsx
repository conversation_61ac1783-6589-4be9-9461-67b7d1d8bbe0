import { useState, useEffect } from "react";
import Layout from "@/components/layout";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader } from "@/components/ui/loader";
import { apiRequest } from "@/lib/queryClient";
import { User, Mail, Briefcase, MapPin, Calendar, Shield, AlertCircle } from "lucide-react";
import { ProfilePictureUpload } from "@/components/profile-picture-upload";

export default function ProfilePage() {
  const { user, isLoading } = useAuth();
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);
  const [showRoleSwitchDialog, setShowRoleSwitchDialog] = useState(false);
  const [showEmailChangeDialog, setShowEmailChangeDialog] = useState(false);
  const [newEmail, setNewEmail] = useState("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [isChangingEmail, setIsChangingEmail] = useState(false);
  const [profileData, setProfileData] = useState({
    name: "",
    email: "",
    position: "",
    location: "",
    bio: "",
    company: "",
    yearsOfExperience: "",
    skills: "",
    profilePicture: ""
  });

  // Load profile data when user data is available
  useEffect(() => {
    if (user) {
      // Initialize with user data we already have
      setProfileData(prevData => ({
        ...prevData,
        name: user.name,
        email: user.email,
        // Other fields will be populated from the profile API call
      }));

      // Fetch additional profile data
      fetchProfileData();
    }
  }, [user]);

  const fetchProfileData = async () => {
    if (!user) return;

    try {
      const response = await apiRequest("GET", "/api/profile");
      if (response.ok) {
        const data = await response.json();
        setProfileData(prevData => ({
          ...prevData,
          ...data,
          // Ensure user name and email from auth context take precedence
          name: user.name,
          email: user.email
        }));
      }
    } catch (error) {
      console.error("Error fetching profile data:", error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfileData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsUpdating(true);

    try {
      const response = await apiRequest("PUT", "/api/profile", profileData);

      if (response.ok) {
        toast({
          title: "Profile Updated",
          description: "Your profile has been successfully updated.",
        });
      } else {
        throw new Error("Failed to update profile");
      }
    } catch (error) {
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "An error occurred while updating your profile.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRoleSwitch = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await apiRequest("POST", "/api/switch-role-request", {
        new_email: newEmail
      });

      if (response.ok) {
        toast({
          title: "Request Submitted",
          description: "Your role switch request has been submitted. Please check your email for verification.",
        });
        setShowRoleSwitchDialog(false);
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to submit role switch request");
      }
    } catch (error) {
      toast({
        title: "Request Failed",
        description: error instanceof Error ? error.message : "An error occurred while submitting your request.",
        variant: "destructive",
      });
    }
  };

  const handleEmailChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsChangingEmail(true);

    try {
      if (!newEmail || !currentPassword) {
        throw new Error("Please provide both your current password and new email");
      }

      const response = await apiRequest("POST", "/api/change-email", {
        currentPassword,
        newEmail
      });

      if (response.ok) {
        toast({
          title: "Email Change Requested",
          description: "A verification email has been sent to your new address. Please check your email to complete the change.",
        });
        setShowEmailChangeDialog(false);
        setCurrentPassword("");
        setNewEmail("");
      } else {
        const error = await response.json();
        throw new Error(error.message || "Failed to request email change");
      }
    } catch (error) {
      toast({
        title: "Email Change Failed",
        description: error instanceof Error ? error.message : "An error occurred while changing your email.",
        variant: "destructive",
      });
    } finally {
      setIsChangingEmail(false);
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="container mx-auto py-10 flex justify-center items-center min-h-[60vh]">
          <Loader size="large" text="Loading your profile..." />
        </div>
      </Layout>
    );
  }

  if (!user) {
    return (
      <Layout>
        <div className="container mx-auto py-10">
          <Card>
            <CardHeader>
              <CardTitle>Not Authenticated</CardTitle>
              <CardDescription>Please log in to view your profile.</CardDescription>
            </CardHeader>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto py-10">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Profile Summary Card */}
          <div className="md:w-1/3">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{user.name}</CardTitle>
                    <CardDescription>{user.email}</CardDescription>
                  </div>
                  <Badge variant={user.role === "employer" ? "default" : "secondary"}>
                    {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-4 pb-6">
                <div className="flex flex-col items-center space-y-4">
                  <ProfilePictureUpload
                    profilePicture={profileData.profilePicture || ""}
                    name={user.name}
                    onPictureUpdate={(url) => {
                      setProfileData(prev => ({
                        ...prev,
                        profilePicture: url
                      }));
                    }}
                  />

                  <div className="text-center">
                    {profileData.position && (
                      <div className="flex items-center justify-center text-sm text-muted-foreground mt-1">
                        <Briefcase className="h-4 w-4 mr-1" />
                        <span>{profileData.position}</span>
                      </div>
                    )}

                    {profileData.location && (
                      <div className="flex items-center justify-center text-sm text-muted-foreground mt-1">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span>{profileData.location}</span>
                      </div>
                    )}

                    {profileData.yearsOfExperience && (
                      <div className="flex items-center justify-center text-sm text-muted-foreground mt-1">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{profileData.yearsOfExperience} years of experience</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-center border-t pt-4">
                {user.role === "employer" ? (
                  <Button
                    variant="outline"
                    onClick={() => setShowRoleSwitchDialog(true)}
                    className="w-full"
                  >
                    Switch to Worker Role
                  </Button>
                ) : user.role === "worker" && (
                  <Button
                    variant="outline"
                    onClick={() => setShowRoleSwitchDialog(true)}
                    className="w-full"
                  >
                    Switch to Employer Role
                  </Button>
                )}
              </CardFooter>
            </Card>
          </div>

          {/* Profile Details Tabs */}
          <div className="md:w-2/3">
            <Tabs defaultValue="personal" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="personal">Personal Information</TabsTrigger>
                <TabsTrigger value="account">Account Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="personal">
                <Card>
                  <form onSubmit={handleProfileUpdate}>
                    <CardHeader>
                      <CardTitle>Personal Information</CardTitle>
                      <CardDescription>
                        Update your personal details and profile information.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name</Label>
                          <Input
                            id="name"
                            name="name"
                            value={profileData.name}
                            onChange={handleInputChange}
                            required
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="position">Position/Title</Label>
                          <Input
                            id="position"
                            name="position"
                            value={profileData.position}
                            onChange={handleInputChange}
                            placeholder="e.g. Software Engineer"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="location">Location</Label>
                          <Input
                            id="location"
                            name="location"
                            value={profileData.location}
                            onChange={handleInputChange}
                            placeholder="e.g. New York, NY"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="yearsOfExperience">Years of Experience</Label>
                          <Input
                            id="yearsOfExperience"
                            name="yearsOfExperience"
                            type="number"
                            value={profileData.yearsOfExperience}
                            onChange={handleInputChange}
                            placeholder="e.g. 5"
                          />
                        </div>

                        {user.role === "employer" && (
                          <div className="space-y-2">
                            <Label htmlFor="company">Company</Label>
                            <Input
                              id="company"
                              name="company"
                              value={profileData.company}
                              onChange={handleInputChange}
                              placeholder="e.g. Acme Inc."
                            />
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="skills">Skills</Label>
                        <Input
                          id="skills"
                          name="skills"
                          value={profileData.skills}
                          onChange={handleInputChange}
                          placeholder="e.g. JavaScript, React, Node.js"
                        />
                        <p className="text-xs text-muted-foreground">
                          Separate skills with commas
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bio">Bio</Label>
                        <Textarea
                          id="bio"
                          name="bio"
                          value={profileData.bio}
                          onChange={handleInputChange}
                          placeholder="Tell us about yourself"
                          rows={4}
                        />
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button type="submit" disabled={isUpdating}>
                        {isUpdating ? (
                          <>
                            <div className="w-4 h-2 mr-2 overflow-hidden">
                              <div className="loader h-2 w-16"></div>
                            </div>
                            Updating...
                          </>
                        ) : (
                          "Save Changes"
                        )}
                      </Button>
                    </CardFooter>
                  </form>
                </Card>
              </TabsContent>

              <TabsContent value="account">
                <Card>
                  <CardHeader>
                    <CardTitle>Account Settings</CardTitle>
                    <CardDescription>
                      Manage your account settings and preferences.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <Label htmlFor="email">Email Address</Label>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setShowEmailChangeDialog(true)}
                        >
                          Change Email
                        </Button>
                      </div>
                      <Input
                        id="email"
                        value={user.email}
                        disabled
                      />
                      <p className="text-xs text-muted-foreground">
                        Your email address is used for login and verification.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label>Account Type</Label>
                      <div className="flex items-center space-x-2">
                        <Badge variant={user.role === "employer" ? "default" : "secondary"}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </Badge>
                        {user.is_verified ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            Verified
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                            Unverified
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        {user.role === "employer"
                          ? "Employer accounts can post jobs and view worker profiles."
                          : "Worker accounts can apply to jobs and access career resources."}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Role Switch Dialog */}
      <Dialog open={showRoleSwitchDialog} onOpenChange={setShowRoleSwitchDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Switch Account Role</DialogTitle>
            <DialogDescription>
              {user.role === "employer"
                ? "You are about to switch from an Employer to a Worker account."
                : "To switch to an Employer account, you need to provide a valid company email address for verification."}
            </DialogDescription>
          </DialogHeader>

          {user.role === "worker" && (
            <form onSubmit={handleRoleSwitch} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="newEmail">Company Email Address</Label>
                <Input
                  id="newEmail"
                  type="email"
                  value={newEmail}
                  onChange={(e) => setNewEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
                <p className="text-xs flex items-start mt-1">
                  <AlertCircle className="h-4 w-4 mr-1 mt-0.5 text-amber-500" />
                  <span>
                    This email domain must be approved for employer registration.
                    You will need to verify this email before your role change is completed.
                  </span>
                </p>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowRoleSwitchDialog(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  Request Role Change
                </Button>
              </DialogFooter>
            </form>
          )}

          {user.role === "employer" && (
            <div className="space-y-4">
              <div className="rounded-md bg-blue-50 p-4">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <Shield className="h-5 w-5 text-blue-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      Switching to a Worker role is immediate. You can switch back to an Employer role at any time.
                    </p>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setShowRoleSwitchDialog(false)}>
                  Cancel
                </Button>
                <Button
                  type="button"
                  onClick={async () => {
                    try {
                      const response = await apiRequest("POST", "/api/switch-role", {});
                      if (response.ok) {
                        window.location.reload(); // Force reload to update user context
                      } else {
                        throw new Error("Failed to switch role");
                      }
                    } catch (error) {
                      toast({
                        title: "Role Switch Failed",
                        description: error instanceof Error ? error.message : "An error occurred while switching roles.",
                        variant: "destructive",
                      });
                    }
                  }}
                >
                  Switch to Worker
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Email Change Dialog */}
      <Dialog open={showEmailChangeDialog} onOpenChange={setShowEmailChangeDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Change Email Address</DialogTitle>
            <DialogDescription>
              Enter your current password and new email address. A verification email will be sent to your new address.
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleEmailChange} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Current Password</Label>
              <Input
                id="currentPassword"
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                placeholder="Enter your current password"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="changeEmail">New Email Address</Label>
              <Input
                id="changeEmail"
                type="email"
                value={newEmail}
                onChange={(e) => setNewEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
              <p className="text-xs flex items-start mt-1">
                <AlertCircle className="h-4 w-4 mr-1 mt-0.5 text-amber-500" />
                <span>
                  You will need to verify this email before the change is completed.
                  All notifications will be sent to this new email after verification.
                </span>
              </p>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => {
                setShowEmailChangeDialog(false);
                setCurrentPassword("");
                setNewEmail("");
              }}>
                Cancel
              </Button>
              <Button type="submit" disabled={isChangingEmail}>
                {isChangingEmail ? (
                  <>
                    <div className="w-4 h-2 mr-2 overflow-hidden">
                      <div className="loader h-2 w-16"></div>
                    </div>
                    Processing...
                  </>
                ) : (
                  "Change Email"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </Layout>
  );
}
