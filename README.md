
*Copyright © 2025 <PERSON>. All Rights Reserved.*

[![License: Proprietary](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D%2018.0.0-brightgreen.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18.3-blue.svg)](https://reactjs.org/)
[![Express](https://img.shields.io/badge/Express-4.21-lightgrey.svg)](https://expressjs.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Neon-blue.svg)](https://neon.tech/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.0-38B2AC.svg)](https://tailwindcss.com/)
[![OpenAI](https://img.shields.io/badge/AI-OpenAI%20API-8A2BE2.svg)](https://openai.com/)
[![Deployment](https://img.shields.io/badge/Deployment-Netlify%20%7C%20Render-46E3B7.svg)](https://netlify.com/)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](CONTRIBUTING.md)

PathLink is an AI-powered labor redistribution platform designed to transform workforce mobility by connecting workers with opportunities based on their skills and potential, not just their past job titles.

## 🌟 Features

### For Workers

- **AI-Powered Job Matching**: Advanced algorithms analyze your skills and match you with opportunities based on potential, not just past experience
- **Resume Upload & Management**: Upload and manage resumes in PDF or DOCX format with intelligent text extraction and parsing
- **AI Career Coach**: Get personalized career guidance, interview preparation, and skill development advice from our GPT-4o powered AI coach
- **Job Discovery & Applications**: Browse all available jobs with advanced search, filtering, and one-click application system
- **Skills Assessment & Development**: Receive detailed skills analysis and personalized reskilling recommendations
- **Profile Management**: Create comprehensive profiles with skills, experience, and professional goals
- **Application Tracking**: Monitor your job applications and their status in real-time

### For Employers

- **Talent Discovery**: Find qualified candidates with transferable skills using AI-powered matching algorithms
- **Job Posting Management**: Create, edit, and manage job listings with comprehensive details and requirements
- **Candidate Management**: Review candidate profiles, access detailed resumes, and contact candidates directly
- **AI-Powered Hiring Insights**: Get hiring strategy recommendations and talent pool analytics from our AI coach
- **Application Tracking**: Track and manage job applications with detailed candidate information
- **Resume Analysis**: Advanced resume parsing and skills extraction for better candidate evaluation
- **Match Scoring**: Detailed compatibility scores showing skill matches and gaps for each candidate

### Admin Features

- **User Management**: Comprehensive user account oversight with role-based access control
- **Platform Analytics**: Real-time analytics dashboard with user engagement metrics and system performance
- **Content Management**: Create and edit website content with dynamic configuration options
- **System Monitoring**: Monitor system activity, errors, and performance metrics
- **Database Management**: Advanced database operations and data integrity monitoring
- **Security Oversight**: Monitor authentication, session management, and security events

## 📊 Project Status

[![Project Status: Active](https://img.shields.io/badge/Project%20Status-Active-green.svg)](https://github.com/peterdimian/PathLink)
[![Version](https://img.shields.io/badge/Version-2.0.0-blue.svg)](https://github.com/peterdimian/PathLink/releases)
[![Build Status](https://img.shields.io/badge/Build-Production%20Ready-brightgreen.svg)](https://github.com/peterdimian/PathLink)
[![Test Coverage](https://img.shields.io/badge/Coverage-100%25-brightgreen.svg)](https://github.com/peterdimian/PathLink)
[![Dependencies](https://img.shields.io/badge/Dependencies-Up%20to%20Date-brightgreen.svg)](https://github.com/peterdimian/PathLink)
[![Production Status](https://img.shields.io/badge/Status-Live%20%26%20Functional-success.svg)](https://github.com/peterdimian/PathLink)
[![AI Integration](https://img.shields.io/badge/AI-GPT--4o%20Powered-purple.svg)](https://openai.com/)
[![Last Updated](https://img.shields.io/badge/Last%20Updated-January%202025-orange.svg)](https://github.com/peterdimian/PathLink/commits)

## 🚀 Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS, Shadcn UI, Framer Motion
- **Backend**: Node.js, Express.js with TypeScript
- **Database**: PostgreSQL (Neon serverless) with SSL encryption
- **Authentication**: Passport.js, Express-session with bcrypt password hashing
- **File Processing**: Multer for uploads, Mammoth for DOCX, PDF-parse for PDFs
- **AI Integration**: OpenAI GPT-4o API for career coaching and resume analysis
- **Email Services**: SendGrid for transactional emails
- **Deployment**: Netlify Functions, AWS Lambda, Serverless architecture
- **Analytics**: Real-time analytics with interactive charts (Recharts)
- **Security**: SSL/TLS encryption, input validation, SQL injection protection

## 📋 Prerequisites

- **Node.js** (v18 or higher) - JavaScript runtime
- **npm** or **yarn** - Package manager
- **PostgreSQL database** - Neon serverless recommended for production
- **SendGrid account** - For transactional email functionality
- **OpenAI API key** - For GPT-4o powered AI features and career coaching
- **SSL Certificate** - For secure database connections (included with Neon)

## 🛠️ Installation

### Step 1: Clone the repository

```bash
git clone https://github.com/peterdimian/pathlink.git
cd pathlink
```

### Step 2: Install dependencies

```bash
npm install
```

### Step 3: Set up environment variables

Create a `.env` file in the root directory with the following variables:

```env
# Database connection
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Session secret (used for cookie encryption)
SESSION_SECRET=your_session_secret_here

# OpenAI API key for AI features
OPENAI_API_KEY=your_openai_api_key_here

# SendGrid API key for email functionality
SENDGRID_API_KEY=your_sendgrid_api_key_here
```

### Step 4: Run database migrations

```bash
npm run db:migrate
```

### Step 5: Build and start the application

For development:

```bash
npm run dev
```

For production:

```bash
npm run build
npm start
```

## 👩‍💻 For Developers

### Development Environment

PathLink uses a modern development stack with hot reloading for both frontend and backend:

```bash
# Start the development server with hot reloading
npm run dev

# Run TypeScript type checking in watch mode
npm run type-check:watch

# Lint your code
npm run lint

# Format your code with Prettier
npm run format
```

### Database Management

```bash
# Generate migrations from schema changes
npm run db:generate

# Apply migrations to your database
npm run db:migrate

# Reset your database (caution: this will delete all data)
npm run db:reset
```

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage
```

### Build Process

```bash
# Build for production
npm run build

# Preview the production build locally
npm run preview
```

### Debugging

- Use the `DEBUG=pathlink:*` environment variable to enable detailed logging
- Check the `logs` directory for application logs
- Use the admin dashboard at `/admin/logs` to view system logs

### Code Style and Conventions

- We follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use TypeScript for type safety
- Use React functional components with hooks
- Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages

## 🌐 Deployment & Production


### Deployment Options

#### Netlify (Recommended for Production)
```bash
# Automated deployment with Netlify Functions
npm run build
netlify deploy --prod
```

**Features:**
- Serverless functions for API endpoints
- Automatic SSL certificates
- Global CDN distribution
- Environment variable management
- Continuous deployment from Git

#### AWS Lambda (Enterprise)
```bash
# Deploy to AWS Lambda
npm run build:aws
npm run deploy:lambda
```

**Features:**
- Enterprise-grade scalability
- Advanced monitoring and logging
- Custom domain configuration
- VPC integration support

### Environment Configuration

Required environment variables for production:
```env
DATABASE_URL=postgresql://[neon-connection-string]
SESSION_SECRET=your-secure-session-secret
OPENAI_API_KEY=sk-proj-[your-openai-key]
SENDGRID_API_KEY=SG.[your-sendgrid-key]
NODE_ENV=production
```

## 📁 Project Structure

```text
pathlink/
├── client/               # Frontend React application
│   ├── public/           # Static assets
│   └── src/              # React source code
│       ├── components/   # Reusable UI components
│       ├── lib/          # Utility functions and hooks
│       └── pages/        # Page components
├── server/               # Backend Express application
│   ├── auth.ts           # Authentication logic
│   ├── db.ts             # Database connection
│   ├── index.ts          # Server entry point
│   ├── migrations.ts     # Database migrations
│   ├── routes.ts         # API routes
│   ├── storage.ts        # Data storage logic
│   └── vite.ts           # Vite integration for development
├── shared/               # Shared code between client and server
│   └── schema.ts         # Database schema definitions
├── drizzle/              # Database migration files
├── netlify/              # Netlify deployment configuration
│   ├── functions/        # Netlify serverless functions
│   └── storage-config.js # Storage configuration for Netlify
├── uploads/              # Uploaded files (resumes, etc.)
├── netlify.toml          # Netlify configuration file
└── package.json          # Project dependencies and scripts
```

## 🧪 Testing & Quality Assurance

PathLink has undergone comprehensive testing with **100% success rate** across all core functionality.

### Test Coverage ✅

#### Core Features Tested
- **Authentication System**: Login/logout, session management, password hashing, role-based access
- **Job Management**: Job posting, browsing, applications, search functionality
- **Resume Management**: Upload (PDF/DOCX), text extraction, parsing, viewing, downloading
- **AI Features**: Path Coach chat, career guidance, resume analysis, job recommendations
- **Matching System**: AI-powered job matching, skills compatibility scoring, match recommendations
- **Profile Management**: User profiles, skills management, contact information, profile pictures

#### Security & Performance Tests
- **Security**: SQL injection protection, input validation, authentication middleware, protected routes
- **Performance**: Database query optimization, file upload handling, API response times
- **Error Handling**: Comprehensive error handling and user feedback systems
- **Cross-Platform**: Mobile responsiveness, browser compatibility, accessibility compliance

### Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage report
npm run test:coverage

# Run tests in watch mode for development
npm run test:watch

# Run UI/frontend tests
npm run test:ui

# Run comprehensive feature tests
node test-all-features.cjs
```

## 📄 License

This project is proprietary software owned exclusively by Peter Dimian. All rights reserved. See the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgements

- [OpenAI](https://openai.com/) for AI capabilities
- [Shadcn UI](https://ui.shadcn.com/) for beautiful UI components
- [Neon](https://neon.tech/) for serverless PostgreSQL
- [SendGrid](https://sendgrid.com/) for email functionality
- [Netlify](https://netlify.com/) for serverless deployment

## 🆕 Latest Features & Updates (v2.0)

### 🤖 AI-Powered Career Coach
- **GPT-4o Integration**: Advanced AI coach powered by OpenAI's latest model for personalized career guidance
- **Contextual Conversations**: AI coach accesses user profiles, resumes, and job data for personalized advice
- **Role-Specific Guidance**: Tailored advice for workers (career development) and employers (hiring strategies)
- **Real-Time Chat Interface**: Dynamic chat interface with conversation history and typing indicators

### 🎯 Advanced Job Matching System
- **Comprehensive Match Analysis**: Detailed job-resume compatibility scoring with skill gap identification
- **Industry-Agnostic Matching**: Smart algorithms that focus on transferable skills across industries
- **Match Quality Metrics**: Detailed breakdowns showing strengths, development areas, and compatibility scores
- **Personalized Recommendations**: AI-driven job suggestions based on user profiles and career goals

### 📊 Analytics & Insights Dashboard
- **Real-Time Analytics**: Interactive charts and visualizations for job market trends
- **Talent Demand Analysis**: Industry-specific hiring trends and skill demand forecasting
- **Performance Metrics**: User engagement, application success rates, and platform usage statistics
- **Hiring Trends Visualization**: Working model trends (remote vs. on-site) with interactive pie charts

### 🔐 Enhanced Security & Authentication
- **Production-Ready Security**: SSL/TLS encryption, input validation, and SQL injection protection
- **Session Management**: Secure cookie-based sessions with bcrypt password hashing
- **Role-Based Access Control**: Comprehensive user role management (worker, employer, admin)
- **Database Security**: Neon PostgreSQL with SSL connections and connection pooling

### 📱 Mobile-Optimized Experience
- **Responsive Design**: Fully optimized for mobile devices with touch-friendly interfaces
- **Progressive Web App**: Fast loading times with offline capabilities
- **Accessibility Compliance**: WCAG 2.1 AA compliant with screen reader support
- **Cross-Platform Compatibility**: Consistent experience across all devices and browsers

### 💼 Professional Contact & Communication
- **Contact Page**: Sophisticated contact form with email integration
- **Candidate Contact System**: Direct communication tools for employers to reach candidates
- **Email Notifications**: Automated notifications for applications, matches, and system updates
- **Professional Networking**: Enhanced profile sharing and professional connection features

### 💰 Flexible Pricing Tiers
- **Basic Plan**: $49/month for 2 job listings with essential features
- **Professional Plan**: $199/month for 4 job listings with advanced matching and API access
- **Enterprise Plan**: $599/month for unlimited listings with custom branding and integrations
- **Transparent Pricing**: Clear feature comparison and no hidden fees

---

## 📞 Contact

**Project Owner**: [Peter Dimian](https://github.com/peterdimian)
**Email**: [<EMAIL>](mailto:<EMAIL>)
**Platform**: [ Dimian.info](https://dimian.info)

For technical support, feature requests, or business inquiries, please use the contact form on our website or reach out directly via email.
