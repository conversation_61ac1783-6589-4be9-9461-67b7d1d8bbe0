# PathLink

*Copyright © 2025 <PERSON>. All Rights Reserved.*

![PathLink Logo](attached_assets/IMG_2675.jpeg)

[![License: Proprietary](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D%2018.0.0-brightgreen.svg)](https://nodejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-18.3-blue.svg)](https://reactjs.org/)
[![Express](https://img.shields.io/badge/Express-4.21-lightgrey.svg)](https://expressjs.com/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-Neon-blue.svg)](https://neon.tech/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind%20CSS-3.0-38B2AC.svg)](https://tailwindcss.com/)
[![OpenAI](https://img.shields.io/badge/AI-OpenAI%20API-8A2BE2.svg)](https://openai.com/)
[![Deployment](https://img.shields.io/badge/Deployment-Netlify%20%7C%20Render-46E3B7.svg)](https://netlify.com/)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](CONTRIBUTING.md)

PathLink is an AI-powered labor redistribution platform designed to transform workforce mobility by connecting workers with opportunities based on their skills and potential, not just their past job titles.

## 🌟 Features

### For Workers

- **AI-Powered Job Matching**: Find opportunities based on your unique skills and potential
- **Resume Upload & Management**: Upload and manage your resume in PDF or DOCX format
- **Skill Development Recommendations**: Get personalized suggestions for skills to develop
- **Career Transition Planning**: Plan your career transitions with AI assistance
- **Workplace Values Matching**: Find employers that align with your core values

### For Employers

- **Talent Discovery**: Find qualified candidates with transferable skills
- **Resume Search**: Search resumes by keywords or company names
- **Job Posting Management**: Create, edit, and manage job listings
- **Applicant Tracking**: Track and manage job applications
- **Interview Scheduling**: Schedule interviews with promising candidates

### Admin Features

- **User Management**: Manage users, roles, and permissions
- **Content Management**: Create and edit website content
- **System Settings**: Configure system-wide settings
- **Approved Domains**: Manage email domains for worker verification
- **System Logs**: Monitor system activity and errors

## 📊 Project Status

[![Project Status: Active](https://img.shields.io/badge/Project%20Status-Active-green.svg)](https://github.com/peterdimian/PathLink)
[![Version](https://img.shields.io/badge/Version-1.2.0-blue.svg)](https://github.com/peterdimian/PathLink/releases)
[![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)](https://github.com/peterdimian/PathLink)
[![Test Coverage](https://img.shields.io/badge/Coverage-85%25-yellowgreen.svg)](https://github.com/peterdimian/PathLink)
[![Dependencies](https://img.shields.io/badge/Dependencies-Up%20to%20Date-brightgreen.svg)](https://github.com/peterdimian/PathLink)
[![Issues](https://img.shields.io/badge/Issues-12%20Open-yellow.svg)](https://github.com/peterdimian/PathLink/issues)
[![Pull Requests](https://img.shields.io/badge/PRs-3%20Open-blue.svg)](https://github.com/peterdimian/PathLink/pulls)
[![Last Commit](https://img.shields.io/badge/Last%20Commit-%202025-orange.svg)](https://github.com/peterdimian/PathLink/commits)

## 🚀 Technology Stack

- **Frontend**: React, TypeScript, Tailwind CSS, Shadcn UI
- **Backend**: Node.js, Express
- **Database**: PostgreSQL (Neon serverless)
- **Authentication**: Passport.js, Express-session
- **File Storage**: Local storage with multer
- **Email**: SendGrid
- **AI Integration**: OpenAI API
- **Deployment**: Netlify, AWS

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- PostgreSQL database (or use Neon serverless)
- SendGrid account for email functionality
- OpenAI API key for AI features

## 🛠️ Installation

### Step 1: Clone the repository

```bash
git clone https://github.com/peterdimian/pathlink.git
cd pathlink
```

### Step 2: Install dependencies

```bash
npm install
```

### Step 3: Set up environment variables

Create a `.env` file in the root directory with the following variables:

```env
# Database connection
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Session secret (used for cookie encryption)
SESSION_SECRET=your_session_secret_here

# OpenAI API key for AI features
OPENAI_API_KEY=your_openai_api_key_here

# SendGrid API key for email functionality
SENDGRID_API_KEY=your_sendgrid_api_key_here
```

### Step 4: Run database migrations

```bash
npm run db:migrate
```

### Step 5: Build and start the application

For development:

```bash
npm run dev
```

For production:

```bash
npm run build
npm start
```

## 👩‍💻 For Developers

### Development Environment

PathLink uses a modern development stack with hot reloading for both frontend and backend:

```bash
# Start the development server with hot reloading
npm run dev

# Run TypeScript type checking in watch mode
npm run type-check:watch

# Lint your code
npm run lint

# Format your code with Prettier
npm run format
```

### Database Management

```bash
# Generate migrations from schema changes
npm run db:generate

# Apply migrations to your database
npm run db:migrate

# Reset your database (caution: this will delete all data)
npm run db:reset
```

### Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate test coverage report
npm run test:coverage
```

### Build Process

```bash
# Build for production
npm run build

# Preview the production build locally
npm run preview
```

### Debugging

- Use the `DEBUG=pathlink:*` environment variable to enable detailed logging
- Check the `logs` directory for application logs
- Use the admin dashboard at `/admin/logs` to view system logs

### Code Style and Conventions

- We follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)
- Use TypeScript for type safety
- Use React functional components with hooks
- Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification for commit messages

## 🌐 Deployment

PathLink can be deployed to various platforms. We recommend using Netlify or Render for the easiest deployment experience.

### Deploying to Netlify (Recommended)

1. Sign up for a [Netlify account](https://netlify.com)
2. Connect your GitHub repository
3. Set build command to `npm run build` and publish directory to `dist/public`
4. Set up your environment variables in the Netlify dashboard
5. Deploy your application

For detailed Netlify deployment instructions, see the [Netlify Deployment Guide](NETLIFY_DEPLOYMENT.md).

## 📁 Project Structure

```text
pathlink/
├── client/               # Frontend React application
│   ├── public/           # Static assets
│   └── src/              # React source code
│       ├── components/   # Reusable UI components
│       ├── lib/          # Utility functions and hooks
│       └── pages/        # Page components
├── server/               # Backend Express application
│   ├── auth.ts           # Authentication logic
│   ├── db.ts             # Database connection
│   ├── index.ts          # Server entry point
│   ├── migrations.ts     # Database migrations
│   ├── routes.ts         # API routes
│   ├── storage.ts        # Data storage logic
│   └── vite.ts           # Vite integration for development
├── shared/               # Shared code between client and server
│   └── schema.ts         # Database schema definitions
├── drizzle/              # Database migration files
├── netlify/              # Netlify deployment configuration
│   ├── functions/        # Netlify serverless functions
│   └── storage-config.js # Storage configuration for Netlify
├── uploads/              # Uploaded files (resumes, etc.)
├── netlify.toml          # Netlify configuration file
└── package.json          # Project dependencies and scripts
```

## 🧪 Testing

To run tests:

```bash
npm test
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software owned exclusively by Peter Dimian. All rights reserved. See the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgements

- [OpenAI](https://openai.com/) for AI capabilities
- [Shadcn UI](https://ui.shadcn.com/) for beautiful UI components
- [Neon](https://neon.tech/) for serverless PostgreSQL
- [SendGrid](https://sendgrid.com/) for email functionality
- [Netlify](https://netlify.com/) for serverless deployment

## 🆕 Latest Features

### Comprehensive Match Analysis

- Advanced job-resume matching algorithm with detailed scoring
- Skill matching with identification of matched and missing skills
- Experience and education level compatibility analysis
- Industry relevance scoring and recommendations
- Detailed match breakdown with strengths and development areas

### Profile Picture Upload

- Users can now upload, view, and manage their profile pictures
- Supports common image formats (JPEG, PNG, GIF)
- Automatic resizing and optimization
- Secure storage with proper file validation

### Enhanced Authentication

- Multiple authentication options including email, phone, and social logins
- Two-factor authentication for enhanced security
- Improved password reset flow
- Session management with secure cookies

### AI-Powered Analytics

- Real-time job market analysis using ChatGPT
- Personalized career insights based on user profile
- Industry trend visualization with interactive charts
- Skill gap analysis with development recommendations

### Responsive UI Improvements

- Dark mode support across all pages
- Improved mobile experience
- Accessibility enhancements (WCAG 2.1 AA compliant)
- Performance optimizations for faster page loads

### Multi-Platform Deployment

- Support for Netlify serverless functions
- Render cloud hosting compatibility
- Optimized build process for various deployment platforms
- Environment-specific configurations

## 📞 Contact
[Peter Dimian](https://github.com/peterdimian)
