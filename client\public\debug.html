<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PathLink Debug - Authentication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #1C2A42;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #2a3a5a;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 PathLink Debug Tool</h1>
        <p>This tool helps diagnose authentication and API issues on your deployed Netlify site.</p>
        
        <div class="test-section">
            <h2>🔍 System Status</h2>
            <button onclick="testAPI()">Test API Connection</button>
            <button onclick="testDebug()">Get Debug Info</button>
            <button onclick="testAuth()">Test Authentication</button>
            <div id="systemStatus"></div>
        </div>

        <div class="test-section">
            <h2>🔐 Login Test</h2>
            <div>
                <input type="email" id="email" placeholder="Email" value="<EMAIL>">
                <input type="password" id="password" placeholder="Password" value="password123">
                <button onclick="testLogin()">Test Login</button>
            </div>
            <div id="loginStatus"></div>
        </div>

        <div class="test-section">
            <h2>📊 API Endpoints Test</h2>
            <button onclick="testEndpoint('/api/profile')">Test Profile</button>
            <button onclick="testEndpoint('/api/matches')">Test Matches</button>
            <button onclick="testEndpoint('/api/surplus-employees')">Test Surplus Employees</button>
            <div id="endpointStatus"></div>
        </div>

        <div class="test-section">
            <h2>🍪 Cookie Information</h2>
            <button onclick="showCookies()">Show Cookies</button>
            <div id="cookieInfo"></div>
        </div>
    </div>

    <script>
        const API_BASE = window.location.origin;

        async function makeRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const data = await response.json();
                return { status: response.status, data, ok: response.ok };
            } catch (error) {
                return { status: 0, data: { error: error.message }, ok: false };
            }
        }

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function showJSON(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}"><pre>${JSON.stringify(data, null, 2)}</pre></div>`;
        }

        async function testAPI() {
            showStatus('systemStatus', '🔄 Testing API connection...');
            const result = await makeRequest('/api/test');
            
            if (result.ok) {
                showJSON('systemStatus', { message: '✅ API is working!', ...result.data }, 'success');
            } else {
                showJSON('systemStatus', { message: '❌ API connection failed', ...result }, 'error');
            }
        }

        async function testDebug() {
            showStatus('systemStatus', '🔄 Getting debug information...');
            const result = await makeRequest('/api/debug');
            
            if (result.ok) {
                showJSON('systemStatus', { message: '✅ Debug info retrieved', ...result.data }, 'success');
            } else {
                showJSON('systemStatus', { message: '❌ Debug info failed', ...result }, 'error');
            }
        }

        async function testAuth() {
            showStatus('systemStatus', '🔄 Testing authentication status...');
            const result = await makeRequest('/api/user');
            
            if (result.ok) {
                showJSON('systemStatus', { message: '✅ User is authenticated', ...result.data }, 'success');
            } else {
                showJSON('systemStatus', { message: '❌ User not authenticated', ...result }, 'error');
            }
        }

        async function testLogin() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            showStatus('loginStatus', '🔄 Attempting login...');
            
            const result = await makeRequest('/api/auth/login', {
                method: 'POST',
                body: JSON.stringify({ email, password })
            });
            
            if (result.ok) {
                showJSON('loginStatus', { message: '✅ Login successful!', ...result.data }, 'success');
            } else {
                showJSON('loginStatus', { message: '❌ Login failed', ...result }, 'error');
            }
        }

        async function testEndpoint(endpoint) {
            showStatus('endpointStatus', `🔄 Testing ${endpoint}...`);
            const result = await makeRequest(endpoint);
            
            if (result.ok) {
                showJSON('endpointStatus', { 
                    message: `✅ ${endpoint} working!`, 
                    endpoint,
                    data: result.data 
                }, 'success');
            } else {
                showJSON('endpointStatus', { 
                    message: `❌ ${endpoint} failed`, 
                    endpoint,
                    ...result 
                }, 'error');
            }
        }

        function showCookies() {
            const cookies = document.cookie.split(';').reduce((acc, cookie) => {
                const [name, value] = cookie.trim().split('=');
                acc[name] = value;
                return acc;
            }, {});
            
            showJSON('cookieInfo', {
                message: '🍪 Current cookies',
                cookies,
                cookieString: document.cookie
            }, 'info');
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
