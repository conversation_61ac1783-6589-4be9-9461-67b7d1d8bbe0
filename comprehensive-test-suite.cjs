const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:5000';

class ComprehensiveTestSuite {
  constructor() {
    this.results = [];
    this.cookies = '';
    this.performanceMetrics = [];
  }

  async log(category, test, status, details = '', metrics = {}) {
    const result = {
      category,
      test,
      status,
      details,
      metrics,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [${category}] ${test}: ${status} ${details}`);
  }

  async makeRequest(endpoint, options = {}) {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Cookie': this.cookies,
          ...options.headers
        }
      });

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Save cookies for session management
      if (response.headers.get('set-cookie')) {
        this.cookies = response.headers.get('set-cookie');
      }

      const data = await response.json().catch(() => ({}));

      return {
        status: response.status,
        data,
        headers: response.headers,
        responseTime
      };
    } catch (error) {
      const endTime = Date.now();
      return {
        status: 0,
        error: error.message,
        responseTime: endTime - startTime
      };
    }
  }

  async testPerformance() {
    console.log('\n⚡ TESTING PERFORMANCE...');

    const endpoints = [
      '/api/user',
      '/api/jobs',
      '/api/matches',
      '/api/profile',
      '/api/jobs/browse'
    ];

    for (const endpoint of endpoints) {
      const response = await this.makeRequest(endpoint);
      
      if (response.responseTime < 1000) {
        await this.log('Performance', `${endpoint} Response Time`, 'PASS', 
          `${response.responseTime}ms`, { responseTime: response.responseTime });
      } else if (response.responseTime < 3000) {
        await this.log('Performance', `${endpoint} Response Time`, 'WARN', 
          `${response.responseTime}ms (slow)`, { responseTime: response.responseTime });
      } else {
        await this.log('Performance', `${endpoint} Response Time`, 'FAIL', 
          `${response.responseTime}ms (too slow)`, { responseTime: response.responseTime });
      }
    }
  }

  async testSecurity() {
    console.log('\n🔒 TESTING SECURITY...');

    // Test unauthenticated access
    const tempCookies = this.cookies;
    this.cookies = '';
    
    const protectedEndpoints = [
      '/api/user',
      '/api/jobs',
      '/api/matches',
      '/api/profile'
    ];

    for (const endpoint of protectedEndpoints) {
      const response = await this.makeRequest(endpoint);
      
      if (response.status === 401) {
        await this.log('Security', `${endpoint} Auth Protection`, 'PASS', 'Properly protected');
      } else {
        await this.log('Security', `${endpoint} Auth Protection`, 'FAIL', 
          `Status: ${response.status} (should be 401)`);
      }
    }

    // Restore cookies
    this.cookies = tempCookies;

    // Test SQL injection protection
    const sqlInjectionPayloads = [
      "'; DROP TABLE users; --",
      "1' OR '1'='1",
      "admin'--",
      "' UNION SELECT * FROM users --"
    ];

    for (const payload of sqlInjectionPayloads) {
      const response = await this.makeRequest('/api/login', {
        method: 'POST',
        body: JSON.stringify({
          email: payload,
          password: 'test'
        })
      });

      if (response.status === 400 || response.status === 401) {
        await this.log('Security', 'SQL Injection Protection', 'PASS', 'Payload rejected');
      } else {
        await this.log('Security', 'SQL Injection Protection', 'FAIL', 
          `Payload accepted: ${payload}`);
      }
    }
  }

  async testDataValidation() {
    console.log('\n🔍 TESTING DATA VALIDATION...');

    // Test invalid email formats
    const invalidEmails = [
      'invalid-email',
      '@domain.com',
      'user@',
      '<EMAIL>'
    ];

    for (const email of invalidEmails) {
      const response = await this.makeRequest('/api/login', {
        method: 'POST',
        body: JSON.stringify({
          email: email,
          password: 'password123'
        })
      });

      if (response.status === 400 || response.status === 401) {
        await this.log('Validation', 'Invalid Email Rejection', 'PASS', `Rejected: ${email}`);
      } else {
        await this.log('Validation', 'Invalid Email Rejection', 'FAIL', 
          `Accepted invalid email: ${email}`);
      }
    }

    // Test empty/null data
    const response = await this.makeRequest('/api/path-coach/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: '',
        userType: 'worker'
      })
    });

    if (response.status === 400) {
      await this.log('Validation', 'Empty Message Rejection', 'PASS', 'Empty message rejected');
    } else {
      await this.log('Validation', 'Empty Message Rejection', 'FAIL', 
        `Empty message accepted: ${response.status}`);
    }
  }

  async testErrorHandling() {
    console.log('\n🚨 TESTING ERROR HANDLING...');

    // Test non-existent endpoints
    const response404 = await this.makeRequest('/api/nonexistent');
    if (response404.status === 404) {
      await this.log('Error Handling', '404 Not Found', 'PASS', 'Proper 404 response');
    } else {
      await this.log('Error Handling', '404 Not Found', 'FAIL', 
        `Status: ${response404.status}`);
    }

    // Test malformed JSON
    try {
      const malformedResponse = await fetch(`${BASE_URL}/api/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cookie': this.cookies
        },
        body: '{"invalid": json}'
      });

      if (malformedResponse.status === 400) {
        await this.log('Error Handling', 'Malformed JSON', 'PASS', 'Malformed JSON rejected');
      } else {
        await this.log('Error Handling', 'Malformed JSON', 'FAIL', 
          `Status: ${malformedResponse.status}`);
      }
    } catch (error) {
      await this.log('Error Handling', 'Malformed JSON', 'PASS', 'Request properly failed');
    }
  }

  async testConcurrency() {
    console.log('\n🔄 TESTING CONCURRENCY...');

    // Test multiple simultaneous requests
    const promises = [];
    for (let i = 0; i < 10; i++) {
      promises.push(this.makeRequest('/api/user'));
    }

    const startTime = Date.now();
    const responses = await Promise.all(promises);
    const endTime = Date.now();

    const successCount = responses.filter(r => r.status === 200).length;
    const avgResponseTime = responses.reduce((sum, r) => sum + r.responseTime, 0) / responses.length;

    if (successCount === 10) {
      await this.log('Concurrency', 'Simultaneous Requests', 'PASS', 
        `10/10 successful, avg: ${avgResponseTime.toFixed(0)}ms`);
    } else {
      await this.log('Concurrency', 'Simultaneous Requests', 'FAIL', 
        `${successCount}/10 successful`);
    }
  }

  async runComprehensiveTests() {
    console.log('🚀 STARTING COMPREHENSIVE PATHLINK TESTING...\n');

    // First, authenticate
    const loginResponse = await this.makeRequest('/api/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (loginResponse.status !== 200) {
      console.log('❌ Authentication failed - cannot proceed with tests');
      return;
    }

    await this.log('Setup', 'Authentication', 'PASS', 'Successfully logged in');

    // Run all test suites
    await this.testPerformance();
    await this.testSecurity();
    await this.testDataValidation();
    await this.testErrorHandling();
    await this.testConcurrency();

    // Generate comprehensive report
    const categories = [...new Set(this.results.map(r => r.category))];
    const summary = {};

    categories.forEach(category => {
      const categoryResults = this.results.filter(r => r.category === category);
      summary[category] = {
        total: categoryResults.length,
        passed: categoryResults.filter(r => r.status === 'PASS').length,
        failed: categoryResults.filter(r => r.status === 'FAIL').length,
        warnings: categoryResults.filter(r => r.status === 'WARN').length
      };
    });

    const totalTests = this.results.length;
    const totalPassed = this.results.filter(r => r.status === 'PASS').length;
    const totalFailed = this.results.filter(r => r.status === 'FAIL').length;
    const totalWarnings = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 COMPREHENSIVE TEST SUMMARY:');
    console.log('='.repeat(50));
    
    Object.entries(summary).forEach(([category, stats]) => {
      const successRate = ((stats.passed / stats.total) * 100).toFixed(1);
      console.log(`${category}: ${stats.passed}/${stats.total} (${successRate}%)`);
    });

    console.log('='.repeat(50));
    console.log(`✅ TOTAL PASSED: ${totalPassed}`);
    console.log(`❌ TOTAL FAILED: ${totalFailed}`);
    console.log(`⚠️  TOTAL WARNINGS: ${totalWarnings}`);
    console.log(`📈 OVERALL SUCCESS RATE: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);

    // Save detailed report
    const report = {
      summary,
      totalTests,
      totalPassed,
      totalFailed,
      totalWarnings,
      overallSuccessRate: (totalPassed / totalTests) * 100,
      results: this.results,
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('comprehensive-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Comprehensive report saved to comprehensive-test-report.json');

    return report;
  }
}

// Run comprehensive tests
const tester = new ComprehensiveTestSuite();
tester.runComprehensiveTests().catch(console.error);
