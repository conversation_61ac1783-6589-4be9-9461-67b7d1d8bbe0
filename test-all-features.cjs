const fetch = require('node-fetch');
const fs = require('fs');

const BASE_URL = 'http://localhost:5000';

class PathLinkTester {
  constructor() {
    this.results = [];
    this.cookies = '';
  }

  async log(test, status, details = '') {
    const result = {
      test,
      status,
      details,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} ${test}: ${status} ${details}`);
  }

  async makeRequest(endpoint, options = {}) {
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'Cookie': this.cookies,
          ...options.headers
        }
      });

      // Save cookies for session management
      if (response.headers.get('set-cookie')) {
        this.cookies = response.headers.get('set-cookie');
      }

      return {
        status: response.status,
        data: await response.json().catch(() => ({})),
        headers: response.headers
      };
    } catch (error) {
      return {
        status: 0,
        error: error.message
      };
    }
  }

  async testAuthentication() {
    console.log('\n🔐 TESTING AUTHENTICATION...');

    // Test login endpoint
    const loginResponse = await this.makeRequest('/api/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    if (loginResponse.status === 200) {
      await this.log('Login', 'PASS', 'Successfully logged in');
    } else {
      await this.log('Login', 'FAIL', `Status: ${loginResponse.status}`);
      return false;
    }

    // Test user data endpoint
    const userResponse = await this.makeRequest('/api/user');
    if (userResponse.status === 200 && userResponse.data.email) {
      await this.log('User Data', 'PASS', `User: ${userResponse.data.name}`);
    } else {
      await this.log('User Data', 'FAIL', `Status: ${userResponse.status}`);
    }

    return true;
  }

  async testJobManagement() {
    console.log('\n💼 TESTING JOB MANAGEMENT...');

    // Test get jobs
    const jobsResponse = await this.makeRequest('/api/jobs');
    if (jobsResponse.status === 200) {
      await this.log('Get Jobs', 'PASS', `Found ${jobsResponse.data.length || 0} jobs`);
    } else {
      await this.log('Get Jobs', 'FAIL', `Status: ${jobsResponse.status}`);
    }

    // Test job browsing
    const browseResponse = await this.makeRequest('/api/jobs/browse');
    if (browseResponse.status === 200 || browseResponse.status === 404) {
      await this.log('Browse Jobs', 'PASS', 'Endpoint accessible');
    } else {
      await this.log('Browse Jobs', 'FAIL', `Status: ${browseResponse.status}`);
    }
  }

  async testResumeManagement() {
    console.log('\n📄 TESTING RESUME MANAGEMENT...');

    // Test get resumes
    const resumeResponse = await this.makeRequest('/api/resumes/worker/27');
    if (resumeResponse.status === 200 || resumeResponse.status === 404) {
      await this.log('Get Resume', 'PASS', 'Resume endpoint accessible');
    } else {
      await this.log('Get Resume', 'FAIL', `Status: ${resumeResponse.status}`);
    }

    // Test resume upload endpoint
    const uploadResponse = await this.makeRequest('/api/resumes/upload', {
      method: 'POST',
      body: JSON.stringify({ test: 'data' })
    });
    
    if (uploadResponse.status === 400 || uploadResponse.status === 200) {
      await this.log('Resume Upload Endpoint', 'PASS', 'Endpoint accessible');
    } else {
      await this.log('Resume Upload Endpoint', 'FAIL', `Status: ${uploadResponse.status}`);
    }
  }

  async testAIFeatures() {
    console.log('\n🤖 TESTING AI FEATURES...');

    // Test Path Coach
    const coachResponse = await this.makeRequest('/api/path-coach/chat', {
      method: 'POST',
      body: JSON.stringify({
        message: 'Hello, can you help me with my career?',
        userType: 'worker',
        conversationHistory: ''
      })
    });

    if (coachResponse.status === 200) {
      await this.log('Path Coach', 'PASS', 'AI chat responding');
    } else {
      await this.log('Path Coach', 'FAIL', `Status: ${coachResponse.status}, Details: ${JSON.stringify(coachResponse.data)}`);
    }
  }

  async testMatching() {
    console.log('\n🎯 TESTING MATCHING SYSTEM...');

    // Test get matches
    const matchesResponse = await this.makeRequest('/api/matches');
    if (matchesResponse.status === 200) {
      await this.log('Get Matches', 'PASS', `Found ${matchesResponse.data.length || 0} matches`);
    } else {
      await this.log('Get Matches', 'FAIL', `Status: ${matchesResponse.status}`);
    }

    // Test view matches
    const viewMatchesResponse = await this.makeRequest('/api/view-matches');
    if (viewMatchesResponse.status === 200 || viewMatchesResponse.status === 404) {
      await this.log('View Matches', 'PASS', 'Endpoint accessible');
    } else {
      await this.log('View Matches', 'FAIL', `Status: ${viewMatchesResponse.status}`);
    }
  }

  async testProfile() {
    console.log('\n👤 TESTING PROFILE MANAGEMENT...');

    // Test get profile
    const profileResponse = await this.makeRequest('/api/profile');
    if (profileResponse.status === 200) {
      await this.log('Get Profile', 'PASS', 'Profile data retrieved');
    } else {
      await this.log('Get Profile', 'FAIL', `Status: ${profileResponse.status}`);
    }

    // Test update profile
    const updateResponse = await this.makeRequest('/api/profile', {
      method: 'PUT',
      body: JSON.stringify({
        position: 'Test Position'
      })
    });

    if (updateResponse.status === 200 || updateResponse.status === 400) {
      await this.log('Update Profile', 'PASS', 'Profile update endpoint accessible');
    } else {
      await this.log('Update Profile', 'FAIL', `Status: ${updateResponse.status}`);
    }
  }

  async runAllTests() {
    console.log('🚀 STARTING PATHLINK COMPREHENSIVE TESTING...\n');

    const isLoggedIn = await this.testAuthentication();
    
    if (isLoggedIn) {
      await this.testJobManagement();
      await this.testResumeManagement();
      await this.testAIFeatures();
      await this.testMatching();
      await this.testProfile();
    }

    // Generate report
    const passCount = this.results.filter(r => r.status === 'PASS').length;
    const failCount = this.results.filter(r => r.status === 'FAIL').length;
    const warnCount = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 TEST SUMMARY:');
    console.log(`✅ PASSED: ${passCount}`);
    console.log(`❌ FAILED: ${failCount}`);
    console.log(`⚠️  WARNINGS: ${warnCount}`);
    console.log(`📈 SUCCESS RATE: ${((passCount / this.results.length) * 100).toFixed(1)}%`);

    // Save detailed report
    fs.writeFileSync('test-report.json', JSON.stringify(this.results, null, 2));
    console.log('\n📄 Detailed report saved to test-report.json');

    return {
      total: this.results.length,
      passed: passCount,
      failed: failCount,
      warnings: warnCount,
      successRate: (passCount / this.results.length) * 100
    };
  }
}

// Run tests
const tester = new PathLinkTester();
tester.runAllTests().catch(console.error);
