[build]
  command = "npm run build"
  publish = "dist/public"
  functions = "netlify/functions"

[dev]
  command = "npm run dev"
  port = 8888
  targetPort = 5000
  publish = "dist/public"
  functionsPort = 8889

[functions]
  node_bundler = "esbuild"
  external_node_modules = ["express"]

[build.environment]
  NODE_VERSION = "18"
  DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
  SESSION_SECRET = "pathlink-secure-session-secret-key-2024"

[functions."*"]
  node_bundler = "esbuild"
  external_node_modules = ["express", "serverless-http", "passport-local", "pg"]

[functions.api]
  node_bundler = "esbuild"
  external_node_modules = ["express", "serverless-http", "passport-local", "passport", "pg", "cors"]

[functions.upload]
  node_bundler = "esbuild"
  external_node_modules = ["express", "serverless-http", "multer", "pg"]

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api/:splat"
  status = 200
  force = true

# Explicitly handle client-side routes
[[redirects]]
  from = "/contact"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/welcome"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/profile"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/dashboard"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/matches"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/jobs"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/resumes"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/auth"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/pricing"
  to = "/index.html"
  status = 200
  force = true

# Catch-all for any other routes
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = true
