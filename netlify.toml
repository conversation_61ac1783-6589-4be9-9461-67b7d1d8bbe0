[build]
  command = "npm run build && node netlify/test-connection.js"
  publish = "dist/public"
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "20"
  NPM_FLAGS = "--production=false"
  DATABASE_URL = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
  SESSION_SECRET = "pathlink-secure-session-secret-key-2024"
  JWT_SECRET = "pathlink-jwt-secret-key-2024"
  NODE_ENV = "production"
  OPENAI_API_KEY = "********************************************************************************************************************************************************************"
  SENDGRID_API_KEY = "SG.1234567890abcdefghijklmnopqrstuvwxyz"

[dev]
  command = "npm run dev"
  port = 8888
  targetPort = 5000
  publish = "dist/public"
  functionsPort = 8889

[functions]
  node_bundler = "esbuild"
  external_node_modules = ["express"]

[functions."*"]
  node_bundler = "esbuild"
  external_node_modules = ["express", "serverless-http", "passport-local", "pg"]

[functions.api]
  node_bundler = "esbuild"
  external_node_modules = ["express", "serverless-http", "passport-local", "passport", "pg", "cors"]

[functions.upload]
  node_bundler = "esbuild"
  external_node_modules = ["express", "serverless-http", "multer", "pg"]

# Redirect API calls to Netlify Functions
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/api"
  status = 200
  force = true

# Redirect upload calls to Netlify Functions
[[redirects]]
  from = "/upload/*"
  to = "/.netlify/functions/upload"
  status = 200
  force = true

# Health check
[[redirects]]
  from = "/health"
  to = "/.netlify/functions/api"
  status = 200
  force = true

# Explicitly handle client-side routes
[[redirects]]
  from = "/contact"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/welcome"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/profile"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/dashboard"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/matches"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/jobs"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/resumes"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/auth"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/pricing"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/browse-jobs"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/upload-resume"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/employee-management"
  to = "/index.html"
  status = 200
  force = true

[[redirects]]
  from = "/surplus-employees"
  to = "/index.html"
  status = 200
  force = true

# Catch-all for any other routes
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = true

# Security headers
[[headers]]
  for = "/api/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
    Access-Control-Allow-Headers = "Content-Type, Authorization, X-Requested-With"
    Access-Control-Allow-Credentials = "true"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://apis.google.com https://www.gstatic.com https://www.googleapis.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://api.netlify.com https://identitytoolkit.googleapis.com https://securetoken.googleapis.com https://www.googleapis.com https://firebase.googleapis.com wss: ws: https://*.netlify.app https://*.neon.tech; frame-src 'self' https://accounts.google.com; object-src 'none'; base-uri 'self';"
