/**
 * Simple in-memory cache implementation for frequently accessed data
 */

interface CacheItem<T> {
  data: T;
  expiry: number;
}

class Cache {
  private cache: Map<string, CacheItem<any>> = new Map();
  private defaultTTL: number = 60 * 1000; // 1 minute in milliseconds

  /**
   * Get an item from the cache
   * @param key The cache key
   * @returns The cached data or undefined if not found or expired
   */
  get<T>(key: string): T | undefined {
    const item = this.cache.get(key);
    
    // Return undefined if item doesn't exist or has expired
    if (!item || item.expiry < Date.now()) {
      if (item) {
        // Clean up expired item
        this.cache.delete(key);
      }
      return undefined;
    }
    
    return item.data as T;
  }

  /**
   * Set an item in the cache
   * @param key The cache key
   * @param data The data to cache
   * @param ttl Time to live in milliseconds (optional, defaults to 1 minute)
   */
  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {
    const expiry = Date.now() + ttl;
    this.cache.set(key, { data, expiry });
  }

  /**
   * Remove an item from the cache
   * @param key The cache key
   */
  delete(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all items from the cache
   */
  clear(): void {
    this.cache.clear();
  }

  /**
   * Get or set cache item with a callback function
   * @param key The cache key
   * @param callback Function to call if cache miss
   * @param ttl Time to live in milliseconds (optional)
   * @returns The cached or newly fetched data
   */
  async getOrSet<T>(key: string, callback: () => Promise<T>, ttl?: number): Promise<T> {
    const cachedItem = this.get<T>(key);
    if (cachedItem !== undefined) {
      return cachedItem;
    }

    const data = await callback();
    this.set(key, data, ttl);
    return data;
  }
}

// Export a singleton instance
export const cache = new Cache();
