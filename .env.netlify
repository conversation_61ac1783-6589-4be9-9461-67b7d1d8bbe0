# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Authentication
SESSION_SECRET=your-secure-session-secret

# API Keys
OPENAI_API_KEY=your-openai-api-key
SENDGRID_API_KEY=your-sendgrid-api-key

# Environment
NODE_ENV=production

# File Storage (for production)
# S3_BUCKET=your-s3-bucket-name
# S3_REGION=us-east-1
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
