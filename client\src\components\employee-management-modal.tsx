import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Save, Trash2, FileText } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import ResumeViewModal from "./resume-view-modal";

// Define the form schema for editing an employee
const employeeFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  previous_role: z.string().min(2, "Role must be at least 2 characters"),
  industry: z.string().min(1, "Please select an industry"),
  status: z.enum(["available", "placed", "pending"]),
  notes: z.string().optional(),
  skills: z.string().optional(),
  years_experience: z.number().min(0).optional(),
  transfer_reason: z.string().optional(),
  potential_positions: z.string().optional(),
});

type FormValues = z.infer<typeof employeeFormSchema>;

// Define the employee interface
interface SurplusEmployee {
  id: number;
  name: string;
  previous_role: string;
  industry: string;
  employer_id: number;
  status: "available" | "placed" | "pending";
  notes?: string;
  skills?: string;
  years_experience?: number;
  transfer_reason?: string;
  potential_positions?: string;
  user_id?: number;
}

interface EmployeeManagementModalProps {
  open: boolean;
  onClose: () => void;
  employee: SurplusEmployee;
}

export default function EmployeeManagementModal({
  open,
  onClose,
  employee
}: EmployeeManagementModalProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showResumeModal, setShowResumeModal] = useState(false);

  // Initialize form with employee data
  const form = useForm<FormValues>({
    resolver: zodResolver(employeeFormSchema),
    defaultValues: {
      name: employee.name,
      previous_role: employee.previous_role,
      industry: employee.industry,
      status: employee.status,
      notes: employee.notes || "",
      skills: employee.skills || "",
      years_experience: employee.years_experience || 0,
      transfer_reason: employee.transfer_reason || "",
      potential_positions: employee.potential_positions || "",
    },
  });

  // Update form values when employee changes
  useEffect(() => {
    form.reset({
      name: employee.name,
      previous_role: employee.previous_role,
      industry: employee.industry,
      status: employee.status,
      notes: employee.notes || "",
      skills: employee.skills || "",
      years_experience: employee.years_experience || 0,
      transfer_reason: employee.transfer_reason || "",
      potential_positions: employee.potential_positions || "",
    });
  }, [employee, form]);

  // Mutation for updating an employee
  const updateEmployeeMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      try {
        console.log("Updating employee with data:", data);
        const res = await apiRequest("PUT", `/api/surplus-employees/${employee.id}`, data);

        if (!res.ok) {
          const contentType = res.headers.get("content-type");
          let errorMessage = "Failed to update employee";

          try {
            if (contentType && contentType.includes("application/json")) {
              const errorData = await res.json();
              errorMessage = errorData.message || errorMessage;
            } else {
              const text = await res.text();
              console.error("Non-JSON error response:", text);
              errorMessage = `Server error: ${res.status}`;
            }
          } catch (parseError) {
            console.error("Error parsing error response:", parseError);
          }

          throw new Error(errorMessage);
        }

        // For successful responses, we'll accept any response format
        // This handles cases where the server returns empty responses or non-JSON
        try {
          const text = await res.text();
          if (!text || text.trim() === '') {
            // Empty response is fine for updates
            console.log("Server returned empty response, considering update successful");
            return { success: true, id: employee.id };
          }

          try {
            // Try to parse as JSON if there's content
            return JSON.parse(text);
          } catch (jsonError) {
            console.warn("Response is not valid JSON, but operation may have succeeded:", text.substring(0, 100));
            // Return a simple success object instead of throwing
            return { success: true, id: employee.id };
          }
        } catch (error) {
          console.error("Error reading response:", error);
          // Still consider it a success if we got a 2xx status code
          return { success: true, id: employee.id };
        }
      } catch (error) {
        console.error("Update employee error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      // Only invalidate the query; let React Query handle refetching
      queryClient.invalidateQueries({ queryKey: ["/api/surplus-employees/employer"] });
      toast({
        title: "Employee updated",
        description: "The employee has been successfully updated",
      });
      onClose();
    },
    onError: (error: Error) => {
      console.error("Update employee mutation error:", error);
      toast({
        title: "Failed to update employee",
        description: error.message || "There was an error updating the employee",
        variant: "destructive",
      });
    },
  });

  // Mutation for deleting an employee
  const deleteEmployeeMutation = useMutation({
    mutationFn: async () => {
      try {
        console.log("Deleting employee:", employee.id);
        const res = await apiRequest("DELETE", `/api/surplus-employees/${employee.id}`);

        if (!res.ok) {
          const contentType = res.headers.get("content-type");
          let errorMessage = "Failed to delete employee";

          try {
            if (contentType && contentType.includes("application/json")) {
              const errorData = await res.json();
              errorMessage = errorData.message || errorMessage;
            } else {
              const text = await res.text();
              console.error("Non-JSON error response:", text);
              errorMessage = `Server error: ${res.status}`;
            }
          } catch (parseError) {
            console.error("Error parsing error response:", parseError);
          }

          throw new Error(errorMessage);
        }

        // For successful responses, we'll accept any response format
        // This handles cases where the server returns empty responses or non-JSON
        try {
          const text = await res.text();
          if (!text || text.trim() === '') {
            // Empty response is fine for deletes
            console.log("Server returned empty response, considering delete successful");
            return { success: true };
          }

          try {
            // Try to parse as JSON if there's content
            return JSON.parse(text);
          } catch (jsonError) {
            console.warn("Response is not valid JSON, but operation may have succeeded:", text.substring(0, 100));
            // Return a simple success object instead of throwing
            return { success: true };
          }
        } catch (error) {
          console.error("Error reading response:", error);
          // Still consider it a success if we got a 2xx status code
          return { success: true };
        }
      } catch (error) {
        console.error("Delete employee error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      // Only invalidate the query; let React Query handle refetching
      queryClient.invalidateQueries({ queryKey: ["/api/surplus-employees/employer"] });
      toast({
        title: "Employee deleted",
        description: "The employee has been successfully removed from the list",
      });
      setShowDeleteConfirm(false);
      onClose();
    },
    onError: (error: Error) => {
      console.error("Delete employee mutation error:", error);
      toast({
        title: "Failed to delete employee",
        description: error.message || "There was an error deleting the employee",
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      await updateEmployeeMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle employee deletion
  const handleDelete = async () => {
    try {
      await deleteEmployeeMutation.mutateAsync();
    } catch (error) {
      console.error("Error deleting employee:", error);
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "available":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
      case "placed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200";
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-[#1C2A42] dark:text-white flex items-center justify-between">
              <span>Manage Employee</span>
              <Badge variant="outline" className={`${getStatusColor(employee.status)} ml-2`}>
                {employee.status.charAt(0).toUpperCase() + employee.status.slice(1)}
              </Badge>
            </DialogTitle>
            <DialogDescription>
              Update employee information and status
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 mt-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Employee Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="previous_role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Current Role</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="industry"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Industry</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select industry" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Finance">Finance</SelectItem>
                          <SelectItem value="Healthcare">Healthcare</SelectItem>
                          <SelectItem value="Retail">Retail</SelectItem>
                          <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="available">Available</SelectItem>
                        <SelectItem value="placed">Placed</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Update the employee's current status in the relocation process
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Separator className="my-4" />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="skills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Skills</FormLabel>
                      <FormControl>
                        <Input placeholder="JavaScript, React, Node.js" {...field} />
                      </FormControl>
                      <FormDescription>
                        Comma-separated list of skills
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="years_experience"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Years of Experience</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          placeholder="5"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          value={field.value || 0}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="potential_positions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Potential New Positions</FormLabel>
                    <FormControl>
                      <Input placeholder="Senior Developer, Team Lead" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="transfer_reason"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason for Transfer/Relocation</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Explain why this employee is being considered for transfer or relocation..."
                        {...field}
                        className="min-h-[80px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Additional Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Any additional information about this employee..."
                        {...field}
                        className="min-h-[80px]"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>

          <DialogFooter className="flex justify-between pt-4">
            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                className="text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200"
                onClick={() => setShowDeleteConfirm(true)}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>

              {employee.user_id && (
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowResumeModal(true)}
                >
                  <FileText className="h-4 w-4 mr-1" />
                  View Resume
                </Button>
              )}
            </div>

            <div className="flex space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="button"
                className="bg-[#1C2A42] hover:bg-opacity-90"
                disabled={isSubmitting}
                onClick={form.handleSubmit(onSubmit)}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently remove {employee.name} from your surplus employee list.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Resume View Modal */}
      {employee.user_id && (
        <ResumeViewModal
          open={showResumeModal}
          onClose={() => setShowResumeModal(false)}
          workerId={employee.user_id}
          workerName={employee.name}
        />
      )}
    </>
  );
}
