#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 PathLink Complete AWS Deployment');
console.log('===================================\n');

// Check if a command exists
function commandExists(command) {
  try {
    execSync(`${command} --version`, { stdio: 'pipe' });
    return true;
  } catch (error) {
    return false;
  }
}

// Setup AWS credentials from CSV
function setupAWSCredentials() {
  try {
    console.log('🔑 Setting up AWS credentials...');
    
    const csvPath = path.join(__dirname, 'pathlink-deployer_accessKeys.csv');
    const csvContent = fs.readFileSync(csvPath, 'utf8');
    const lines = csvContent.trim().split('\n');
    
    if (lines.length < 2) {
      throw new Error('Invalid CSV format');
    }
    
    const [accessKeyId, secretAccessKey] = lines[1].split(',');
    const credentials = {
      accessKeyId: accessKeyId.trim(),
      secretAccessKey: secretAccessKey.trim()
    };
    
    // Configure AWS CLI
    const configCommands = [
      `aws configure set aws_access_key_id ${credentials.accessKeyId}`,
      `aws configure set aws_secret_access_key ${credentials.secretAccessKey}`,
      `aws configure set default.region us-east-1`,
      `aws configure set default.output json`
    ];
    
    for (const command of configCommands) {
      execSync(command, { stdio: 'pipe' });
    }
    
    console.log('✅ AWS credentials configured');
    return true;
  } catch (error) {
    console.error('❌ Error setting up AWS credentials:', error.message);
    return false;
  }
}

// Install required tools
function installRequiredTools() {
  console.log('\n📦 Checking and installing required tools...');
  
  // Check AWS CLI
  if (!commandExists('aws')) {
    console.log('Installing AWS CLI...');
    try {
      execSync('winget install Amazon.AWSCLI --accept-package-agreements --accept-source-agreements', { stdio: 'inherit' });
      console.log('✅ AWS CLI installed');
    } catch (error) {
      console.log('❌ AWS CLI installation failed. Please install manually from: https://awscli.amazonaws.com/AWSCLIV2.msi');
      return false;
    }
  } else {
    console.log('✅ AWS CLI is available');
  }
  
  // Check EB CLI
  if (!commandExists('eb')) {
    console.log('Installing EB CLI...');
    try {
      execSync('pip install awsebcli --upgrade --user', { stdio: 'inherit' });
      console.log('✅ EB CLI installed');
    } catch (error) {
      console.log('❌ EB CLI installation failed. Please install Python and run: pip install awsebcli');
      return false;
    }
  } else {
    console.log('✅ EB CLI is available');
  }
  
  return true;
}

// Setup deployment files
function setupDeploymentFiles() {
  console.log('\n📁 Setting up deployment files...');
  
  // Create .ebextensions directory
  if (!fs.existsSync('.ebextensions')) {
    fs.mkdirSync('.ebextensions');
  }

  // Environment configuration
  const envConfig = `option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: pathlink-production-secret-2024
    JWT_SECRET: pathlink-jwt-secret-2024
    OPENAI_API_KEY: ********************************************************************************************************************************************************************
    SENDGRID_API_KEY: SG.1234567890abcdefghijklmnopqrstuvwxyz
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.19.0
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.micro
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
`;

  fs.writeFileSync('.ebextensions/environment.config', envConfig);
  
  // Procfile
  fs.writeFileSync('Procfile', 'web: npm start');
  
  // .ebignore
  const ebignoreContent = `node_modules/
.git/
.env
*.log
.DS_Store
screenshots/
test-*.json
*-test-report.json
.vscode/
.idea/
*.md
`;
  fs.writeFileSync('.ebignore', ebignoreContent);
  
  // Update package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (!packageJson.engines) packageJson.engines = {};
  packageJson.engines.node = '18.x';
  packageJson.engines.npm = '9.x';
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  
  console.log('✅ Deployment files configured');
}

// Build application
function buildApplication() {
  console.log('\n🔨 Building application...');
  try {
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

// Initialize and deploy to Elastic Beanstalk
function deployToElasticBeanstalk() {
  console.log('\n🌐 Deploying to AWS Elastic Beanstalk...');
  
  try {
    // Initialize EB if not already done
    if (!fs.existsSync('.elasticbeanstalk')) {
      console.log('Initializing Elastic Beanstalk...');
      execSync('eb init pathlink --region us-east-1 --platform "Node.js 18 running on 64bit Amazon Linux 2"', { 
        stdio: 'inherit' 
      });
    }
    
    // Check if environment exists
    let environmentExists = false;
    try {
      const status = execSync('eb status', { stdio: 'pipe' }).toString();
      environmentExists = status.includes('Environment details');
    } catch (e) {
      // Environment doesn't exist
    }
    
    if (!environmentExists) {
      console.log('Creating environment...');
      execSync('eb create pathlink-production --instance-type t3.micro --region us-east-1', { 
        stdio: 'inherit' 
      });
    }
    
    // Deploy
    console.log('Deploying application...');
    execSync('eb deploy', { stdio: 'inherit' });
    
    // Get application URL
    const status = execSync('eb status', { stdio: 'pipe' }).toString();
    const urlMatch = status.match(/CNAME:\s*(.+)/);
    
    if (urlMatch) {
      const appUrl = urlMatch[1].trim();
      console.log(`\n✅ Deployment successful!`);
      console.log(`🌍 Application URL: https://${appUrl}`);
      
      // Update Netlify configuration
      updateNetlifyConfig(appUrl);
      
      return appUrl;
    }
    
    return null;
  } catch (error) {
    console.log('❌ Deployment failed:', error.message);
    return null;
  }
}

// Update Netlify configuration
function updateNetlifyConfig(awsUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace any existing AWS URLs
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/[a-zA-Z0-9-]+\.us-east-1\.elasticbeanstalk\.com/g,
      `https://${awsUrl}`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated');
    console.log(`🔗 API calls will redirect to: https://${awsUrl}`);
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log(`Please manually update netlify.toml with: https://${awsUrl}`);
  }
}

// Test deployment
function testDeployment(appUrl) {
  console.log('\n🧪 Testing deployment...');
  try {
    console.log(`🔍 Health check: https://${appUrl}/health`);
    console.log(`🌐 Application: https://${appUrl}`);
    console.log('✅ Deployment URLs ready for testing');
  } catch (error) {
    console.log('⚠️ Could not test deployment automatically');
  }
}

// Main deployment function
async function main() {
  console.log('Starting complete PathLink AWS deployment...\n');
  
  // Step 1: Install required tools
  if (!installRequiredTools()) {
    console.log('\n❌ Failed to install required tools. Please install manually and try again.');
    process.exit(1);
  }
  
  // Step 2: Setup AWS credentials
  if (!setupAWSCredentials()) {
    console.log('\n❌ Failed to setup AWS credentials.');
    process.exit(1);
  }
  
  // Step 3: Setup deployment files
  setupDeploymentFiles();
  
  // Step 4: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed.');
    process.exit(1);
  }
  
  // Step 5: Deploy to AWS
  const appUrl = deployToElasticBeanstalk();
  if (!appUrl) {
    console.log('\n❌ Deployment failed.');
    process.exit(1);
  }
  
  // Step 6: Test deployment
  testDeployment(appUrl);
  
  console.log('\n🎉 DEPLOYMENT COMPLETE!');
  console.log('======================');
  console.log('✅ Backend deployed to AWS Elastic Beanstalk');
  console.log('✅ Netlify configuration updated');
  console.log('✅ API redirects configured');
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy frontend to Netlify (git push)');
  console.log('2. Test the complete application');
  console.log('3. Configure custom domain (optional)');
  console.log('\n🔧 Useful Commands:');
  console.log('- eb status: Check application status');
  console.log('- eb logs: View application logs');
  console.log('- eb deploy: Deploy updates');
  console.log(`\n🌍 Your application: https://${appUrl}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
