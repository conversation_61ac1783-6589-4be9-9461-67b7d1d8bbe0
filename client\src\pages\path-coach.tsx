import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { MessageCircle, Bot, User, Lightbulb, Target, TrendingUp, Users, Send, RotateCcw, Sparkles, Brain, CheckCircle } from 'lucide-react';
import { useAuth } from '@/hooks/use-auth';
import { useLocation } from 'wouter';
import Layout from '@/components/layout';

interface Message {
  id: string;
  type: 'bot' | 'user';
  content: string;
  timestamp: Date;
  isPresetQuestion?: boolean;
  questionId?: string;
}

interface PresetQuestion {
  id: string;
  question: string;
  category: string;
  userType: 'worker' | 'employer' | 'both';
}

const PathCoach: React.FC = () => {
  const { user } = useAuth();
  const [location, setLocation] = useLocation();
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionStarted, setSessionStarted] = useState(false);
  const [showWelcomeTyping, setShowWelcomeTyping] = useState(false);
  const [conversationHistory, setConversationHistory] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // No preset questions - free form conversation

  const scrollToBottom = () => {
    setTimeout(() => {
      messagesEndRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest'
      });
    }, 100);
  };

  // Auto-scroll when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (!user) {
      setLocation('/login');
      return;
    }

    // Initialize with welcome message with typing effect
    setShowWelcomeTyping(true);

    setTimeout(() => {
      const welcomeMessage: Message = {
        id: 'welcome',
        type: 'bot',
        content: `Welcome to PathLink AI Coach! 🎯\n\nI'm here to help ${user.role === 'worker' ? 'you advance your career and find the right opportunities' : 'you find and hire the best talent'}. \n\nFeel free to ask me anything related to:\n${user.role === 'worker' ? '• Career development and growth\n• Job search strategies\n• Skill development recommendations\n• Interview preparation\n• Professional networking' : '• Hiring strategies and best practices\n• Talent acquisition and recruitment\n• Team building and management\n• Interview techniques\n• Employee development'}\n\nWhat would you like to discuss today? 🚀`,
        timestamp: new Date()
      };

      setMessages([welcomeMessage]);
      setShowWelcomeTyping(false);
      setSessionStarted(true);
    }, 1500);
  }, [user, setLocation]);

  const handleSubmitMessage = async () => {
    if (!currentInput.trim()) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: currentInput,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);

    // Add user message to conversation history
    const updatedHistory = conversationHistory + `\nUser: ${currentInput}`;
    setConversationHistory(updatedHistory);

    const currentMessage = currentInput;
    setCurrentInput('');
    setIsLoading(true);

    try {
      // Check if the message is work-related
      if (!isWorkRelated(currentMessage)) {
        const restrictionMessage: Message = {
          id: `restriction-${Date.now()}`,
          type: 'bot',
          content: "I appreciate your question, but I can only help with work-related topics such as career development, job searching, hiring strategies, professional skills, and workplace guidance. Please feel free to ask me anything related to your professional growth or hiring needs! 😊",
          timestamp: new Date()
        };
        setMessages(prev => [...prev, restrictionMessage]);
        setIsLoading(false);
        return;
      }

      const response = await fetch('/api/path-coach/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: currentMessage,
          userType: user?.role,
          conversationHistory: updatedHistory
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const data = await response.json();

      const botMessage: Message = {
        id: `bot-${Date.now()}`,
        type: 'bot',
        content: data.response,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, botMessage]);

      // Add bot response to conversation history
      setConversationHistory(prev => prev + `\nAI Coach: ${data.response}`);
    } catch (error) {
      console.error('Error getting response:', error);
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        type: 'bot',
        content: "I apologize, but I'm having trouble responding right now. Please try again in a moment.",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to check if a message is work-related
  const isWorkRelated = (message: string): boolean => {
    const workKeywords = [
      'career', 'job', 'work', 'employment', 'hiring', 'interview', 'resume', 'cv',
      'skill', 'experience', 'professional', 'workplace', 'team', 'management',
      'leadership', 'promotion', 'salary', 'benefits', 'company', 'business',
      'industry', 'training', 'development', 'networking', 'mentor', 'coach',
      'performance', 'productivity', 'project', 'role', 'position', 'candidate',
      'recruit', 'talent', 'employee', 'employer', 'staff', 'colleague'
    ];

    const lowerMessage = message.toLowerCase();
    return workKeywords.some(keyword => lowerMessage.includes(keyword)) ||
           lowerMessage.length > 20; // Allow longer messages that might be work-related
  };

  const resetSession = () => {
    setSessionStarted(false);
    setConversationHistory('');
    setMessages([{
      id: 'welcome-reset',
      type: 'bot',
      content: `Let's start fresh! I'm ready to help ${user?.role === 'worker' ? 'you advance your career' : 'you find the right talent'}. What would you like to discuss today?`,
      timestamp: new Date()
    }]);
    setSessionStarted(true);
  };

  if (!user) {
    return null;
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full mb-4">
            <Brain className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-[#1C2A42] mb-3">
            PathLink AI Coach
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {user?.role === 'worker'
              ? 'Get personalized career guidance and skill development recommendations powered by AI'
              : 'Optimize your hiring process and find the best talent with intelligent insights'
            }
          </p>

          {/* Status Badges */}
          <div className="flex justify-center gap-3 mt-4">
            <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
              <Sparkles className="h-3 w-3 mr-1" />
              AI-Powered
            </Badge>
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              <CheckCircle className="h-3 w-3 mr-1" />
              Personalized
            </Badge>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <Target className="h-3 w-3 mr-1" />
              Career-Focused
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <Card className="h-[700px] flex flex-col shadow-lg border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-t-lg">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                      <Bot className="h-5 w-5" />
                    </div>
                    <span>AI Coach Chat</span>
                  </div>
                  {sessionStarted && (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                        Online
                      </Badge>
                    </div>
                  )}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="flex-1 flex flex-col p-0 chat-container">
                <div className="flex-1 overflow-hidden">
                  <ScrollArea className="chat-scroll-area">
                    <div className="p-4 space-y-4">
                      {messages.map((message, index) => (
                        <div
                          key={message.id}
                          className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'} animate-in slide-in-from-bottom-2 duration-300`}
                          style={{ animationDelay: `${index * 100}ms` }}
                        >
                          <div
                            className={`max-w-[85%] rounded-2xl p-4 shadow-sm chat-message ${
                              message.type === 'user'
                                ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white'
                                : 'bg-white border border-gray-200 text-gray-900'
                            }`}
                          >
                            <div className="flex items-start gap-3">
                              {message.type === 'bot' && (
                                <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                  <Bot className="h-3 w-3 text-white" />
                                </div>
                              )}
                              {message.type === 'user' && (
                                <div className="w-6 h-6 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                                  <User className="h-3 w-3 text-white" />
                                </div>
                              )}
                              <div className="flex-1 min-w-0">
                                <p className="whitespace-pre-wrap leading-relaxed chat-message">{message.content}</p>
                                <p className={`text-xs mt-2 ${
                                  message.type === 'user' ? 'text-white/70' : 'text-gray-500'
                                }`}>
                                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}

                      {showWelcomeTyping && (
                        <div className="flex justify-start animate-in slide-in-from-bottom-2 duration-300">
                          <div className="bg-white border border-gray-200 rounded-2xl p-4 shadow-sm">
                            <div className="flex items-center gap-3">
                              <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                                <Bot className="h-3 w-3 text-white" />
                              </div>
                              <div className="flex items-center gap-1">
                                <span className="text-sm text-gray-600 mr-2">Initializing AI Coach</span>
                                <div className="flex space-x-1">
                                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
                                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce [animation-delay:0.1s]"></div>
                                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce [animation-delay:0.2s]"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      {isLoading && (
                        <div className="flex justify-start animate-in slide-in-from-bottom-2 duration-300">
                          <div className="bg-white border border-gray-200 rounded-2xl p-4 shadow-sm">
                            <div className="flex items-center gap-3">
                              <div className="w-6 h-6 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center">
                                <Bot className="h-3 w-3 text-white" />
                              </div>
                              <div className="flex items-center gap-1">
                                <span className="text-sm text-gray-600 mr-2">AI is thinking</span>
                                <div className="flex space-x-1">
                                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce"></div>
                                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce [animation-delay:0.1s]"></div>
                                  <div className="w-2 h-2 bg-purple-600 rounded-full animate-bounce [animation-delay:0.2s]"></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div ref={messagesEndRef} />
                    </div>
                  </ScrollArea>
                </div>

                <div className="border-t border-gray-200 bg-gray-50/50">
                  <div className="p-4">
                    {sessionStarted ? (
                      <div className="space-y-3">
                        <div className="flex gap-3">
                          <div className="flex-1 relative">
                            <Input
                              value={currentInput}
                              onChange={(e) => setCurrentInput(e.target.value)}
                              placeholder="Ask me anything about your career or hiring needs..."
                              onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSubmitMessage()}
                              disabled={isLoading}
                              className="pr-12 py-3 rounded-xl border-gray-300 focus:border-purple-500 focus:ring-purple-500"
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                              <span className="text-xs">Enter ↵</span>
                            </div>
                          </div>
                          <Button
                            onClick={handleSubmitMessage}
                            disabled={!currentInput.trim() || isLoading}
                            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50"
                          >
                            <Send className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex justify-between items-center">
                          <p className="text-xs text-gray-500">
                            Press Enter to send • Shift+Enter for new line
                          </p>
                          <Button
                            onClick={resetSession}
                            variant="ghost"
                            size="sm"
                            className="text-gray-500 hover:text-purple-600"
                          >
                            <RotateCcw className="h-3 w-3 mr-1" />
                            Reset
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <p className="text-gray-600 mb-4">Chat will start automatically...</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Chat Status */}
            {sessionStarted && (
              <Card className="border-green-200 bg-green-50/50">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2 text-green-700">
                    <MessageCircle className="h-4 w-4" />
                    Chat Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>AI Coach is online and ready to help</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Session Focus */}
            <Card className="border-blue-200 bg-blue-50/50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-blue-700">
                  <Sparkles className="h-4 w-4" />
                  Session Focus
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {user?.role === 'worker' ? (
                  <>
                    <div className="flex items-center gap-3 text-sm p-2 bg-white rounded-lg">
                      <div className="w-6 h-6 bg-yellow-100 rounded-full flex items-center justify-center">
                        <Lightbulb className="h-3 w-3 text-yellow-600" />
                      </div>
                      <span className="text-gray-700">Career Development</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm p-2 bg-white rounded-lg">
                      <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">Skill Enhancement</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm p-2 bg-white rounded-lg">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Target className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-gray-700">Job Search Strategy</span>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="flex items-center gap-3 text-sm p-2 bg-white rounded-lg">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="h-3 w-3 text-blue-600" />
                      </div>
                      <span className="text-gray-700">Talent Acquisition</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm p-2 bg-white rounded-lg">
                      <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                        <Target className="h-3 w-3 text-green-600" />
                      </div>
                      <span className="text-gray-700">Hiring Strategy</span>
                    </div>
                    <div className="flex items-center gap-3 text-sm p-2 bg-white rounded-lg">
                      <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                        <TrendingUp className="h-3 w-3 text-purple-600" />
                      </div>
                      <span className="text-gray-700">Team Building</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Guidelines */}
            <Card className="border-gray-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-gray-700">
                  <Brain className="h-4 w-4" />
                  AI Guidelines
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-xs text-gray-600 space-y-2">
                  <div className="flex items-start gap-2">
                    <div className="w-1 h-1 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p>Focuses exclusively on career and hiring topics</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1 h-1 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p>Provides personalized professional guidance</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1 h-1 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p>All conversations are confidential and secure</p>
                  </div>
                  <div className="flex items-start gap-2">
                    <div className="w-1 h-1 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                    <p>Be detailed in responses for better insights</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default PathCoach;
