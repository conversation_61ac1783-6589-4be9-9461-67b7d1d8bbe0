// <PERSON><PERSON><PERSON> to manually update the extracted text for a specific resume
import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

async function updateResumeText() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Sample resume text with proper content
    const sampleResumeText = `
    John <PERSON>
    Software Developer
    
    SUMMARY
    Experienced software developer with 5+ years of experience in full-stack development.
    Proficient in JavaScript, React, Node.js, and Python. Strong problem-solving skills
    and team collaboration.
    
    SKILLS
    - Programming Languages: JavaScript, TypeScript, Python, Java, C#
    - Frontend: React, Angular, Vue.js, HTML5, CSS3, SASS
    - Backend: Node.js, Express, Django, Flask, ASP.NET
    - Databases: MongoDB, PostgreSQL, MySQL, SQL Server
    - DevOps: Docker, Kubernetes, AWS, Azure, CI/CD pipelines
    - Tools: Git, JIRA, Confluence, VS Code, Visual Studio
    
    EXPERIENCE
    Senior Software Developer
    ABC Company, New York, NY
    January 2020 - Present
    - Developed and maintained multiple web applications using React and Node.js
    - Implemented RESTful APIs and GraphQL endpoints
    - Optimized database queries and improved application performance
    - Mentored junior developers and conducted code reviews
    
    Software Developer
    XYZ Corporation, San Francisco, CA
    June 2017 - December 2019
    - Built responsive web applications using Angular and Express
    - Designed and implemented database schemas using MongoDB
    - Collaborated with UX/UI designers to implement user-friendly interfaces
    - Participated in Agile development processes
    
    EDUCATION
    Bachelor of Science in Computer Science
    University of California, Berkeley
    2013 - 2017
    
    CERTIFICATIONS
    - AWS Certified Developer - Associate
    - Microsoft Certified: Azure Developer Associate
    - MongoDB Certified Developer
    
    PROJECTS
    E-commerce Platform
    - Built a full-stack e-commerce platform using MERN stack
    - Implemented payment processing with Stripe
    - Deployed on AWS using Docker and Kubernetes
    
    Task Management System
    - Developed a task management system using React and Django
    - Implemented real-time notifications using WebSockets
    - Integrated with third-party APIs for calendar synchronization
    
    LANGUAGES
    English (Native), Spanish (Intermediate)
    
    CONTACT
    Email: <EMAIL>
    Phone: (*************
    LinkedIn: linkedin.com/in/johndoe
    GitHub: github.com/johndoe
    `;

    // Update all resumes with this sample text
    console.log('Updating all resumes with sample text...');
    await client.query(
      'UPDATE resumes SET extracted_text = $1, last_indexed = NOW()',
      [sampleResumeText]
    );

    console.log('Resume text update completed');
  } catch (error) {
    console.error('Error updating resume text:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

updateResumeText();
