import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import Layout from '@/components/layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Loader2 } from 'lucide-react';

// Define the statistics data types
interface JobStats {
  job_id: number;
  job_title: string;
  total_applications: number;
  pending: number;
  rejected: number;
  interview_scheduled: number;
  accepted: number;
}

interface OverallStats {
  total_applications: number;
  pending: number;
  rejected: number;
  interview_scheduled: number;
  accepted: number;
  total_jobs: number;
  avg_applications_per_job: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
const STATUS_COLORS = {
  pending: '#FFBB28',
  rejected: '#FF8042',
  interview_scheduled: '#0088FE',
  accepted: '#00C49F',
};

const StatisticsPage: React.FC = () => {
  // Fetch job statistics
  const { data: jobStats, isLoading: isLoadingJobStats } = useQuery({
    queryKey: ['/api/statistics/jobs'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/statistics/jobs');
      if (!res.ok) throw new Error('Failed to fetch job statistics');
      return res.json() as Promise<JobStats[]>;
    },
  });

  // Fetch overall statistics
  const { data: overallStats, isLoading: isLoadingOverallStats } = useQuery({
    queryKey: ['/api/statistics/overall'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/statistics/overall');
      if (!res.ok) throw new Error('Failed to fetch overall statistics');
      return res.json() as Promise<OverallStats>;
    },
  });

  // Prepare data for pie chart
  const pieChartData = overallStats
    ? [
        { name: 'Pending', value: overallStats.pending },
        { name: 'Rejected', value: overallStats.rejected },
        { name: 'Interview Scheduled', value: overallStats.interview_scheduled },
        { name: 'Accepted', value: overallStats.accepted },
      ]
    : [];

  // Loading state
  if (isLoadingJobStats || isLoadingOverallStats) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-lg">Loading statistics...</span>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <h1 className="text-3xl font-bold mb-8 text-[#1C2A42] dark:text-white">Application Statistics</h1>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="jobs">Jobs Breakdown</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {overallStats && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <Card className="bg-white dark:bg-gray-800 shadow-md">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Total Applications</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{overallStats.total_applications}</p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-gray-800 shadow-md">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Total Jobs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">{overallStats.total_jobs}</p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-gray-800 shadow-md">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Avg. Applications per Job</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">
                      {overallStats.avg_applications_per_job.toFixed(1)}
                    </p>
                  </CardContent>
                </Card>

                <Card className="bg-white dark:bg-gray-800 shadow-md">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-medium text-[#1C2A42] dark:text-white">Interview Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-3xl font-bold text-[#1C2A42] dark:text-white">
                      {overallStats.total_applications
                        ? Math.round((overallStats.interview_scheduled / overallStats.total_applications) * 100)
                        : 0}
                      %
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-white dark:bg-gray-800 shadow-md">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Application Status Distribution</CardTitle>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="bg-white dark:bg-gray-800 shadow-md">
                <CardHeader>
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Application Status Counts</CardTitle>
                </CardHeader>
                <CardContent className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        {
                          name: 'Pending',
                          count: overallStats?.pending || 0,
                          color: STATUS_COLORS.pending,
                        },
                        {
                          name: 'Rejected',
                          count: overallStats?.rejected || 0,
                          color: STATUS_COLORS.rejected,
                        },
                        {
                          name: 'Interview',
                          count: overallStats?.interview_scheduled || 0,
                          color: STATUS_COLORS.interview_scheduled,
                        },
                        {
                          name: 'Accepted',
                          count: overallStats?.accepted || 0,
                          color: STATUS_COLORS.accepted,
                        },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" name="Count">
                        {overallStats &&
                          [
                            { name: 'Pending', color: STATUS_COLORS.pending },
                            { name: 'Rejected', color: STATUS_COLORS.rejected },
                            { name: 'Interview', color: STATUS_COLORS.interview_scheduled },
                            { name: 'Accepted', color: STATUS_COLORS.accepted },
                          ].map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="jobs">
            {jobStats && jobStats.length > 0 ? (
              <div className="space-y-8">
                {jobStats.map((job) => (
                  <Card key={job.job_id} className="bg-white dark:bg-gray-800 shadow-md">
                    <CardHeader>
                      <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">{job.job_title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
                          <p className="text-sm text-gray-500 dark:text-gray-400">Total Applications</p>
                          <p className="text-2xl font-bold text-[#1C2A42] dark:text-white">{job.total_applications}</p>
                        </div>
                        <div className="bg-yellow-50 dark:bg-yellow-900/30 p-4 rounded-md">
                          <p className="text-sm text-yellow-600 dark:text-yellow-400">Pending</p>
                          <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{job.pending}</p>
                        </div>
                        <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-md">
                          <p className="text-sm text-blue-600 dark:text-blue-400">Interviews</p>
                          <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{job.interview_scheduled}</p>
                        </div>
                        <div className="bg-red-50 dark:bg-red-900/30 p-4 rounded-md">
                          <p className="text-sm text-red-600 dark:text-red-400">Rejected</p>
                          <p className="text-2xl font-bold text-red-600 dark:text-red-400">{job.rejected}</p>
                        </div>
                      </div>

                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={[
                              {
                                name: 'Pending',
                                count: job.pending,
                                color: STATUS_COLORS.pending,
                              },
                              {
                                name: 'Rejected',
                                count: job.rejected,
                                color: STATUS_COLORS.rejected,
                              },
                              {
                                name: 'Interview',
                                count: job.interview_scheduled,
                                color: STATUS_COLORS.interview_scheduled,
                              },
                              {
                                name: 'Accepted',
                                count: job.accepted,
                                color: STATUS_COLORS.accepted,
                              },
                            ]}
                            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="count" name="Count">
                              {[
                                { name: 'Pending', color: STATUS_COLORS.pending },
                                { name: 'Rejected', color: STATUS_COLORS.rejected },
                                { name: 'Interview', color: STATUS_COLORS.interview_scheduled },
                                { name: 'Accepted', color: STATUS_COLORS.accepted },
                              ].map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Bar>
                          </BarChart>
                        </ResponsiveContainer>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow text-center">
                <p className="text-lg text-gray-600 dark:text-gray-300">No job statistics available.</p>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  Statistics will appear here once you have jobs with applications.
                </p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default StatisticsPage;
