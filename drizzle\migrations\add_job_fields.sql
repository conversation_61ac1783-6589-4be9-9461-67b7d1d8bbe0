-- Add new fields to jobs table
ALTER TABLE jobs 
ADD COLUMN IF NOT EXISTS required_skills TEXT,
ADD COLUMN IF NOT EXISTS minimum_experience TEXT,
ADD COLUMN IF NOT EXISTS preferred_backgrounds TEXT,
ADD COLUMN IF NOT EXISTS work_model TEXT,
ADD COLUMN IF NOT EXISTS availability_needs TEXT,
ADD COLUMN IF NOT EXISTS language_requirements TEXT,
ADD COLUMN IF NOT EXISTS culture_fit_keywords TEXT,
ADD COLUMN IF NOT EXISTS salary_range TEXT,
ADD COLUMN IF NOT EXISTS diversity_goals TEXT;
