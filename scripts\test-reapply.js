import fetch from 'node-fetch';

async function testReapply() {
  try {
    console.log('=== TESTING REAPPLY FUNCTIONALITY ===');
    
    // First, let's check the current status of match 28
    console.log('1. Checking current match status...');
    
    // <PERSON><PERSON> as the worker first
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }
    
    const loginData = await loginResponse.json();
    console.log('✅ Login successful');
    
    // Get the session cookie
    const cookies = loginResponse.headers.get('set-cookie');
    
    // Check current matches
    const matchesResponse = await fetch('http://localhost:5000/api/matches', {
      headers: {
        'Cookie': cookies
      }
    });
    
    const matches = await matchesResponse.json();
    const match28 = matches.find(m => m.id === 28);
    
    if (match28) {
      console.log('Current match 28 status:', match28.status);
    } else {
      console.log('Match 28 not found');
      return;
    }
    
    // Now try to reapply for job 11
    console.log('2. Attempting to reapply for job 11...');
    
    const reapplyResponse = await fetch('http://localhost:5000/api/jobs/11/apply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      }
    });
    
    if (reapplyResponse.ok) {
      const reapplyData = await reapplyResponse.json();
      console.log('✅ Reapply successful:', reapplyData);
    } else {
      const errorData = await reapplyResponse.json();
      console.log('❌ Reapply failed:', errorData);
    }
    
    // Check the match status again
    console.log('3. Checking match status after reapply...');
    
    const updatedMatchesResponse = await fetch('http://localhost:5000/api/matches', {
      headers: {
        'Cookie': cookies
      }
    });
    
    const updatedMatches = await updatedMatchesResponse.json();
    const updatedMatch28 = updatedMatches.find(m => m.id === 28);
    
    if (updatedMatch28) {
      console.log('Updated match 28 status:', updatedMatch28.status);
      
      if (updatedMatch28.status === 'pending') {
        console.log('🎉 REAPPLY SUCCESS: Status changed from withdrawn to pending!');
      } else {
        console.log('⚠️  Status did not change as expected');
      }
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testReapply();
