const fetch = require('node-fetch');

async function testLocalMatching() {
  try {
    console.log('🔄 Testing local matching functionality...');

    // First login to get session
    console.log('📝 Logging in...');
    const loginResponse = await fetch('http://localhost:5001/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'employer123'
      })
    });

    if (!loginResponse.ok) {
      console.error('❌ Login failed:', loginResponse.status);
      const errorText = await loginResponse.text();
      console.error('Error details:', errorText);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful:', loginData.user?.name);

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('🍪 Cookies received');

    // Test comprehensive matches API
    console.log('🔍 Testing comprehensive matches...');
    const matchesResponse = await fetch('http://localhost:5001/api/matches/comprehensive', {
      headers: {
        'Cookie': cookies
      }
    });

    if (!matchesResponse.ok) {
      console.error('❌ Matches API failed:', matchesResponse.status);
      const errorText = await matchesResponse.text();
      console.error('Error details:', errorText);
      return;
    }

    const matchesData = await matchesResponse.json();
    console.log('✅ Comprehensive matches API working!');
    console.log(`📊 Found ${matchesData.length} matches`);

    if (matchesData.length > 0) {
      const firstMatch = matchesData[0];
      console.log('\n📋 Sample match:');
      console.log(`   Job: ${firstMatch.job?.title} at ${firstMatch.job?.company}`);
      console.log(`   Worker: ${firstMatch.worker?.name}`);
      console.log(`   Overall Score: ${firstMatch.score?.overall}%`);
      console.log(`   Skills Score: ${firstMatch.score?.skills}%`);
      console.log(`   Matched Skills: ${firstMatch.score?.details?.matchedSkills?.join(', ') || 'None'}`);
      console.log(`   Missing Skills: ${firstMatch.score?.details?.missingSkills?.join(', ') || 'None'}`);
    }

    // Test jobs API
    console.log('\n🔍 Testing jobs API...');
    const jobsResponse = await fetch('http://localhost:5001/api/jobs', {
      headers: {
        'Cookie': cookies
      }
    });

    if (jobsResponse.ok) {
      const jobsData = await jobsResponse.json();
      console.log(`✅ Jobs API working! Found ${jobsData.length} jobs`);
      if (jobsData.length > 0) {
        console.log(`   Sample job: ${jobsData[0].title} - ${jobsData[0].required_skills}`);
      }
    }

    // Test resumes API
    console.log('\n🔍 Testing resumes API...');
    const resumesResponse = await fetch('http://localhost:5001/api/resumes', {
      headers: {
        'Cookie': cookies
      }
    });

    if (resumesResponse.ok) {
      const resumesData = await resumesResponse.json();
      console.log(`✅ Resumes API working! Found ${resumesData.length} resumes`);
      if (resumesData.length > 0) {
        console.log(`   Sample resume: ${resumesData[0].filename}`);
      }
    }

    console.log('\n🎉 Local matching functionality test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
testLocalMatching();
