-- Add user_profiles table
CREATE TABLE IF NOT EXISTS "user_profiles" (
  "id" serial PRIMARY KEY NOT NULL,
  "user_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "position" text,
  "location" text,
  "bio" text,
  "company" text,
  "years_of_experience" integer,
  "skills" text,
  "profile_picture" text,
  "pending_email" text,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add unique constraint on user_id
CREATE UNIQUE INDEX IF NOT EXISTS "idx_user_profiles_user_id" ON "user_profiles"("user_id");

-- Add system_logs table
CREATE TABLE IF NOT EXISTS "system_logs" (
  "id" serial PRIMARY KEY NOT NULL,
  "level" text NOT NULL,
  "message" text NOT NULL,
  "source" text NOT NULL,
  "user_id" integer REFERENCES "users"("id"),
  "metadata" jsonb,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- Add PathCoach related tables
-- 1. Growth Memory Engine - Store reskilling suggestions history
CREATE TABLE IF NOT EXISTS "skilling_suggestions" (
  "id" serial PRIMARY KEY NOT NULL,
  "worker_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "suggestions" jsonb NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- 2. Career Quantum Leap Generator
CREATE TABLE IF NOT EXISTS "career_transition_plans" (
  "id" serial PRIMARY KEY NOT NULL,
  "worker_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "current_role" text,
  "dream_role" text NOT NULL,
  "transition_plan" jsonb NOT NULL,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- 3. Skill Context Analyzer - Extended worker profile
CREATE TABLE IF NOT EXISTS "worker_profiles" (
  "id" serial PRIMARY KEY NOT NULL,
  "worker_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "skills_context" jsonb,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add unique constraint on worker_id
CREATE UNIQUE INDEX IF NOT EXISTS "idx_worker_profiles_worker_id" ON "worker_profiles"("worker_id");

-- 4. GPT Reflection Journal
CREATE TABLE IF NOT EXISTS "reflection_journals" (
  "id" serial PRIMARY KEY NOT NULL,
  "worker_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "prompt" text NOT NULL,
  "entry" text NOT NULL,
  "ai_reflection" text,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- 5. Workplace Soul Match - Worker values
CREATE TABLE IF NOT EXISTS "worker_values" (
  "id" serial PRIMARY KEY NOT NULL,
  "worker_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "values_match_enabled" boolean DEFAULT false,
  "core_values" jsonb,
  "mission_statement" text,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add unique constraint on worker_id
CREATE UNIQUE INDEX IF NOT EXISTS "idx_worker_values_worker_id" ON "worker_values"("worker_id");

-- 6. AI Mentor Constellations
CREATE TABLE IF NOT EXISTS "mentor_preferences" (
  "id" serial PRIMARY KEY NOT NULL,
  "worker_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
  "mentor_tone" text,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add unique constraint on worker_id
CREATE UNIQUE INDEX IF NOT EXISTS "idx_mentor_preferences_worker_id" ON "mentor_preferences"("worker_id");
