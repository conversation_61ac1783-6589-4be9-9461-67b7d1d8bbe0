{"include": ["client/src/**/*", "shared/**/*", "server/**/*"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "target": "ES2020", "module": "ESNext", "strict": false, "lib": ["ES2020", "dom", "dom.iterable"], "jsx": "preserve", "esModuleInterop": true, "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "downlevelIteration": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "types": ["node", "vite/client"], "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"]}}}