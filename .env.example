# PathLink Development Environment Variables
# Copy this file to .env and update with your development values
# NEVER use production credentials in Codespace!

# Database Configuration (Development/Staging Only)
DATABASE_URL=postgresql://username:password@localhost:5432/pathlink_dev

# Session Configuration
SESSION_SECRET=your_development_session_secret_here

# OpenAI API Configuration (Optional for development)
OPENAI_API_KEY=your_development_openai_api_key_here

# SendGrid Email Configuration (Optional for development)
SENDGRID_API_KEY=your_development_sendgrid_api_key_here

# Environment
NODE_ENV=development

# Port Configuration
PORT=5000

# Security Settings (Development)
JWT_SECRET=your_development_jwt_secret_here

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_EMAIL_NOTIFICATIONS=false
ENABLE_ANALYTICS=false

# Development Database (Alternative)
# You can use a local SQLite database for development
# DATABASE_URL=sqlite:./dev.db

# Notes:
# - Use development/staging databases only
# - Never commit real API keys to version control
# - Test features work with mock data when possible
# - Contact maintainer for development API access if needed
