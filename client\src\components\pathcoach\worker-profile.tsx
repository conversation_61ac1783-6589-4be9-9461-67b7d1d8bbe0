import { useState, useEffect } from "react";
import { useWorkerProfile } from "@/hooks/use-worker-profile";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Save, User, BrainCircuit } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { WorkerProfile } from "@shared/schema";

// Define the skills context type to match what we expect in the database JSON field
interface SkillsContext {
  current_skills: string[];
  experience_years: number;
  preferred_learning_style: string;
  career_strengths: string;
}

export function SkillContextAnalyzer() {
  const { workerProfile, isLoading, createProfileMutation } = useWorkerProfile();
  
  // Parse skills context or use defaults
  const skillsContext = (workerProfile?.skills_context as SkillsContext) || 
    { current_skills: [], experience_years: 0, preferred_learning_style: "", career_strengths: "" };
  
  const [skills, setSkills] = useState<string>("");
  const [experienceYears, setExperienceYears] = useState<number>(0);
  const [learningStyle, setLearningStyle] = useState<string>("");
  const [careerStrengths, setCareerStrengths] = useState<string>("");

  // Update form values when worker profile data is loaded
  useEffect(() => {
    if (workerProfile && workerProfile.skills_context) {
      const ctx = workerProfile.skills_context as SkillsContext;
      setSkills(ctx.current_skills?.join(", ") || "");
      setExperienceYears(ctx.experience_years || 0);
      setLearningStyle(ctx.preferred_learning_style || "");
      setCareerStrengths(ctx.career_strengths || "");
    }
  }, [workerProfile]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert comma-separated skills to array
    const skillsArray = skills.split(",").map(skill => skill.trim()).filter(skill => skill !== "");
    
    createProfileMutation.mutate({
      skills_context: {
        current_skills: skillsArray,
        experience_years: Number(experienceYears),
        preferred_learning_style: learningStyle,
        career_strengths: careerStrengths
      }
    });
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BrainCircuit className="h-5 w-5" />
            Skill Context Analyzer
          </CardTitle>
          <CardDescription>Loading your skills profile...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BrainCircuit className="h-5 w-5" />
          Skill Context Analyzer
        </CardTitle>
        <CardDescription>
          Define your skills context to receive more personalized guidance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="skills">Your Current Skills</Label>
            <Textarea
              id="skills"
              placeholder="JavaScript, React, Project Management, etc."
              value={skills}
              onChange={(e) => setSkills(e.target.value)}
              className="resize-none"
              rows={3}
            />
            <p className="text-sm text-muted-foreground">
              Separate skills with commas
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="experience">Years of Experience</Label>
              <Input
                id="experience"
                type="number"
                min={0}
                max={50}
                value={experienceYears}
                onChange={(e) => setExperienceYears(parseInt(e.target.value) || 0)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="learning-style">Preferred Learning Style</Label>
              <Input
                id="learning-style"
                placeholder="Visual, Hands-on, Reading, etc."
                value={learningStyle}
                onChange={(e) => setLearningStyle(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="strengths">Career Strengths</Label>
            <Textarea
              id="strengths"
              placeholder="Describe your key professional strengths..."
              value={careerStrengths}
              onChange={(e) => setCareerStrengths(e.target.value)}
              className="resize-none"
              rows={3}
            />
          </div>

          <Button 
            type="submit" 
            className="w-full md:w-auto"
            disabled={createProfileMutation.isPending}
          >
            {createProfileMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save My Skills Context
              </>
            )}
          </Button>
        </form>
        
        {workerProfile && (
          <div className="mt-6 pt-6 border-t">
            <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
              <User className="h-4 w-4" />
              Your Current Profile
            </h4>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground mb-2">Skills:</p>
                <div className="flex flex-wrap gap-2">
                  {(skillsContext.current_skills || []).map((skill: string, i: number) => (
                    <Badge key={i} variant="outline">
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Experience:</p>
                  <p className="font-medium">
                    {skillsContext.experience_years} years
                  </p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Learning Style:</p>
                  <p className="font-medium">
                    {skillsContext.preferred_learning_style || "Not specified"}
                  </p>
                </div>
              </div>
              {skillsContext.career_strengths && (
                <div>
                  <p className="text-sm text-muted-foreground">Career Strengths:</p>
                  <p>{skillsContext.career_strengths}</p>
                </div>
              )}
              <p className="text-sm text-muted-foreground">
                Last updated: {workerProfile.updated_at ? new Date(workerProfile.updated_at).toLocaleDateString() : "N/A"}
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}