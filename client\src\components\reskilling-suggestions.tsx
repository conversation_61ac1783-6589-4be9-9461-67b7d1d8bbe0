import React from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/use-auth";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, AlertCircle, BookOpen, Clock, ArrowUpRight } from "lucide-react";

interface ReskillingSuggestion {
  title: string;
  description: string;
  relevance: 'High' | 'Medium' | 'Low';
  timeCommitment: string;
  transferableSkills: string[];
}

interface ReskillingSuggestionsResponse {
  hasResume: boolean;
  suggestions: ReskillingSuggestion[];
}

export function ReskillingSuggestions() {
  const { user } = useAuth();
  const { toast } = useToast();

  // Query to get reskilling suggestions
  const {
    data,
    isLoading,
    error,
  } = useQuery<ReskillingSuggestionsResponse>({
    queryKey: ['/api/reskilling-suggestions'],
    queryFn: async () => {
      const response = await apiRequest('GET', '/api/reskilling-suggestions');
      if (!response.ok) {
        throw new Error('Failed to fetch reskilling suggestions');
      }
      return response.json();
    },
    enabled: !!user && user.role === 'worker',
  });

  // Get relevance badge color
  const getRelevanceBadgeVariant = (relevance: string) => {
    switch (relevance) {
      case 'High':
        return 'default';
      case 'Medium':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (user?.role !== 'worker') {
    return null; // Don't render for non-workers
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Reskilling Suggestions</CardTitle>
        <CardDescription>
          Based on your {data?.hasResume ? 'resume and' : ''} job preferences, 
          here are some skill development opportunities to enhance your career prospects.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-6">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-center py-6 text-destructive">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>Error loading suggestions. Please try again later.</p>
          </div>
        ) : data?.suggestions.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <BookOpen className="h-8 w-8 mx-auto mb-2" />
            <p>Upload your resume and apply for jobs to receive personalized reskilling suggestions.</p>
          </div>
        ) : (
          <Accordion type="single" collapsible className="w-full">
            {data?.suggestions.map((suggestion, index) => (
              <AccordionItem value={`item-${index}`} key={index}>
                <AccordionTrigger className="hover:no-underline">
                  <div className="flex items-center text-left">
                    <div className="flex-1">{suggestion.title}</div>
                    <Badge 
                      variant={getRelevanceBadgeVariant(suggestion.relevance) as any} 
                      className="ml-2"
                    >
                      {suggestion.relevance} Relevance
                    </Badge>
                  </div>
                </AccordionTrigger>
                <AccordionContent>
                  <div className="space-y-3 pt-2">
                    <p>{suggestion.description}</p>
                    
                    <div className="flex items-center text-muted-foreground text-sm">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{suggestion.timeCommitment}</span>
                    </div>
                    
                    <div className="space-y-2">
                      <p className="text-sm font-medium">Transferable Skills</p>
                      <div className="flex flex-wrap gap-2">
                        {suggestion.transferableSkills.map((skill, idx) => (
                          <Badge variant="outline" key={idx}>
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <Button variant="outline" size="sm" className="mt-2">
                      <ArrowUpRight className="mr-2 h-4 w-4" />
                      Learn more
                    </Button>
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
        
        {!data?.hasResume && !isLoading && !error && (
          <div className="mt-4 p-4 bg-muted/50 rounded-md">
            <p className="text-sm text-muted-foreground">
              <strong>Tip:</strong> Upload your resume to receive more targeted reskilling suggestions
              based on your experience and skills.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}