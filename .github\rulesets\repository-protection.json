{"name": "PathLink Repository Protection Ruleset", "target": "branch", "source_type": "Repository", "source": "peterdimian/PathLink", "enforcement": "active", "conditions": {"ref_name": {"include": ["refs/heads/main", "refs/heads/master", "refs/heads/production", "refs/heads/staging"], "exclude": []}}, "rules": [{"type": "pull_request", "parameters": {"dismiss_stale_reviews_on_push": true, "require_code_owner_review": true, "require_last_push_approval": true, "required_approving_review_count": 1, "required_review_thread_resolution": true}}, {"type": "required_status_checks", "parameters": {"strict_required_status_checks_policy": true, "required_status_checks": [{"context": "security-check", "integration_id": null}, {"context": "build", "integration_id": null}, {"context": "test", "integration_id": null}]}}, {"type": "non_fast_forward", "parameters": {}}, {"type": "required_linear_history", "parameters": {}}, {"type": "required_signatures", "parameters": {}}, {"type": "restrict_pushes", "parameters": {"restrict_pushes_to_admins": false}}, {"type": "restrict_creations", "parameters": {}}, {"type": "restrict_updates", "parameters": {"update_allows_fetch_and_merge": false}}, {"type": "restrict_deletions", "parameters": {}}, {"type": "required_deployments", "parameters": {"required_deployment_environments": ["staging"]}}], "bypass_actors": [{"actor_id": 1, "actor_type": "RepositoryRole", "bypass_mode": "always"}]}