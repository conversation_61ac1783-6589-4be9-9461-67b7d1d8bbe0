{"name": "PathLink Security Ruleset", "target": "branch", "enforcement": "active", "conditions": {"ref_name": {"include": ["refs/heads/main", "refs/heads/master"], "exclude": ["refs/heads/dependabot/**"]}}, "rules": [{"type": "pull_request", "parameters": {"dismiss_stale_reviews_on_push": true, "require_code_owner_review": false, "require_last_push_approval": false, "required_approving_review_count": 1, "required_review_thread_resolution": true}}, {"type": "required_status_checks", "parameters": {"strict_required_status_checks_policy": true, "required_status_checks": [{"context": "security-check"}]}}, {"type": "non_fast_forward", "parameters": {}}, {"type": "required_linear_history", "parameters": {}}, {"type": "restrict_pushes", "parameters": {}}, {"type": "restrict_deletions", "parameters": {}}], "bypass_actors": []}