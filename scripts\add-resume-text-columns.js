import pg from 'pg';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

async function addResumeTextColumns() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Check if columns already exist
    const checkColumnsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'resumes' 
      AND column_name IN ('extracted_text', 'last_indexed');
    `;
    
    const columnsResult = await client.query(checkColumnsQuery);
    const existingColumns = columnsResult.rows.map(row => row.column_name);
    
    // Add columns if they don't exist
    if (!existingColumns.includes('extracted_text')) {
      console.log('Adding extracted_text column to resumes table...');
      await client.query('ALTER TABLE resumes ADD COLUMN extracted_text TEXT;');
      console.log('Added extracted_text column');
    } else {
      console.log('extracted_text column already exists');
    }
    
    if (!existingColumns.includes('last_indexed')) {
      console.log('Adding last_indexed column to resumes table...');
      await client.query('ALTER TABLE resumes ADD COLUMN last_indexed TIMESTAMP;');
      console.log('Added last_indexed column');
    } else {
      console.log('last_indexed column already exists');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

addResumeTextColumns();
