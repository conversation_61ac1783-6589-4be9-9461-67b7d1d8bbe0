import React from 'react';

interface LoaderProps {
  size?: 'small' | 'medium' | 'large';
  text?: string;
  className?: string;
}

export function Loader({ size = 'medium', text, className = '' }: LoaderProps) {
  const sizeClasses = {
    small: 'h-2 w-20',
    medium: 'h-3 w-32',
    large: 'h-4 w-40',
  };

  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className={`loader ${sizeClasses[size]}`} />
      {text && <p className="mt-4 text-sm text-gray-500">{text}</p>}
    </div>
  );
}
