import { useEffect, useState } from 'react';
import { useLocation } from 'wouter';
import { useAuth } from '@/hooks/use-auth';
import { Loader } from '@/components/ui/loader';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function AuthCallback() {
  const [_, setLocation] = useLocation();
  const { loginMutation } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const processOAuthCallback = async () => {
      try {
        // Get the access token from the URL hash
        const hash = window.location.hash.substring(1);
        const params = new URLSearchParams(hash);
        const accessToken = params.get('access_token');
        const refreshToken = params.get('refresh_token');
        const expiresIn = params.get('expires_in');
        const error = params.get('error');
        const errorDescription = params.get('error_description');

        if (error || !accessToken) {
          throw new Error(errorDescription || 'Authentication failed');
        }

        // Store the tokens in localStorage
        localStorage.setItem('supabase.auth.token', JSON.stringify({
          access_token: accessToken,
          refresh_token: refreshToken,
          expires_at: Date.now() + (parseInt(expiresIn || '3600') * 1000),
        }));

        // Refresh the user data
        await loginMutation.mutateAsync({ email: '', password: '' });

        // Redirect to dashboard
        setLocation('/dashboard');
      } catch (error) {
        console.error('OAuth callback error:', error);
        setError(error instanceof Error ? error.message : 'Authentication failed');
        setIsProcessing(false);
      }
    };

    processOAuthCallback();
  }, [loginMutation, setLocation]);

  // If there's an error, show it
  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-[#EAE6E1] dark:bg-gray-900">
        <Alert variant="destructive" className="max-w-md">
          <AlertDescription>
            <div className="flex flex-col">
              <span className="font-semibold">Authentication failed</span>
              <span className="text-sm mt-1">{error}</span>
              <button 
                className="mt-4 text-sm underline"
                onClick={() => setLocation('/auth')}
              >
                Return to login
              </button>
            </div>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // Show loading spinner while processing
  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-[#EAE6E1] dark:bg-gray-900">
      <div className="text-center">
        <Loader className="w-12 h-12 mb-4" />
        <h2 className="text-xl font-semibold text-[#1C2A42] dark:text-white">
          Completing authentication...
        </h2>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Please wait while we log you in.
        </p>
      </div>
    </div>
  );
}
