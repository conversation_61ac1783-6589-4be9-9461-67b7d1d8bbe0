import { Client } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function applyMigrations() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'drizzle', '0000_initial_schema.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Applying migrations...');
    await client.query(sql);
    console.log('Migrations applied successfully');

    // Create a sample user for testing
    const createSampleUserSql = `
      INSERT INTO users (email, password, name, role)
      VALUES ('<EMAIL>', '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 'Admin User', 'admin')
      ON CONFLICT (email) DO NOTHING;
    `;

    await client.query(createSampleUserSql);
    console.log('Sample user created');

  } catch (error) {
    console.error('Error applying migrations:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

applyMigrations();
