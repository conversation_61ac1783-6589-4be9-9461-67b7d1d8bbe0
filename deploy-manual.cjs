#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 PathLink Manual AWS Deployment');
console.log('==================================\n');

const AWS_CLI = '"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe"';

function runAWSCommand(command) {
  const fullCommand = `${AWS_CLI} ${command}`;
  console.log(`Running: ${fullCommand}`);
  return execSync(fullCommand, { stdio: 'inherit' });
}

function buildApplication() {
  console.log('🔨 Building application...');
  try {
    // Try the AWS build command first
    try {
      execSync('npm run build:aws', { stdio: 'inherit' });
      console.log('✅ Application built with build:aws');
      return true;
    } catch (e) {
      // Fall back to regular build
      execSync('npm run build', { stdio: 'inherit' });
      console.log('✅ Application built with regular build');
      return true;
    }
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

function createApplicationZip() {
  console.log('📦 Creating deployment package...');
  try {
    // Create a simple deployment package
    const deployFiles = [
      'dist/',
      'package.json',
      'Procfile'
    ];

    // Create Procfile if it doesn't exist
    if (!fs.existsSync('Procfile')) {
      fs.writeFileSync('Procfile', 'web: npm start');
      console.log('✅ Created Procfile');
    }

    // Create a simple zip using PowerShell
    const zipCommand = `Compress-Archive -Path dist,package.json,Procfile -DestinationPath pathlink-deployment.zip -Force`;
    execSync(`powershell -Command "${zipCommand}"`, { stdio: 'inherit' });
    
    console.log('✅ Deployment package created: pathlink-deployment.zip');
    return true;
  } catch (error) {
    console.log('❌ Failed to create deployment package:', error.message);
    return false;
  }
}

function createElasticBeanstalkApplication() {
  console.log('🏗️ Creating Elastic Beanstalk application...');
  try {
    // Check if application already exists
    try {
      const result = execSync(`${AWS_CLI} elasticbeanstalk describe-applications --application-names pathlink`, { encoding: 'utf8' });
      const applications = JSON.parse(result).Applications;

      if (applications.length > 0) {
        console.log('✅ Application already exists');
        return true;
      } else {
        // Application doesn't exist, create it
        runAWSCommand('elasticbeanstalk create-application --application-name pathlink --description "PathLink AI-Powered Labor Redistribution Platform"');
        console.log('✅ Application created');
        return true;
      }
    } catch (e) {
      // Application doesn't exist, create it
      runAWSCommand('elasticbeanstalk create-application --application-name pathlink --description "PathLink AI-Powered Labor Redistribution Platform"');
      console.log('✅ Application created');
      return true;
    }
  } catch (error) {
    console.log('❌ Failed to create application:', error.message);
    return false;
  }
}

function createApplicationVersion() {
  console.log('📋 Creating application version...');
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const versionLabel = `pathlink-v${timestamp}`;
    
    // Upload to S3 first
    const bucketName = `pathlink-deployments-${Date.now()}`;
    
    // Create S3 bucket
    try {
      runAWSCommand(`s3 mb s3://${bucketName} --region us-east-1`);
      console.log(`✅ Created S3 bucket: ${bucketName}`);
    } catch (e) {
      console.log('⚠️ S3 bucket creation failed, trying with existing bucket');
    }

    // Upload zip to S3
    runAWSCommand(`s3 cp pathlink-deployment.zip s3://${bucketName}/pathlink-deployment.zip`);
    console.log('✅ Uploaded deployment package to S3');

    // Create application version
    runAWSCommand(`elasticbeanstalk create-application-version --application-name pathlink --version-label ${versionLabel} --source-bundle S3Bucket=${bucketName},S3Key=pathlink-deployment.zip`);
    console.log(`✅ Created application version: ${versionLabel}`);
    
    return versionLabel;
  } catch (error) {
    console.log('❌ Failed to create application version:', error.message);
    return null;
  }
}

function createEnvironment(versionLabel) {
  console.log('🌍 Creating Elastic Beanstalk environment...');
  try {
    const environmentName = 'pathlink-production';
    
    // Check if environment already exists
    try {
      runAWSCommand(`elasticbeanstalk describe-environments --environment-names ${environmentName}`);
      console.log('✅ Environment already exists');
      
      // Update existing environment
      runAWSCommand(`elasticbeanstalk update-environment --environment-name ${environmentName} --version-label ${versionLabel}`);
      console.log('✅ Environment updated');
      return environmentName;
    } catch (e) {
      // Environment doesn't exist, create it
      console.log('Creating new environment (this will take 5-10 minutes)...');
      
      runAWSCommand(`elasticbeanstalk create-environment --application-name pathlink --environment-name ${environmentName} --version-label ${versionLabel} --solution-stack-name "64bit Amazon Linux 2 v5.8.4 running Node.js 18" --option-settings Namespace=aws:autoscaling:launchconfiguration,OptionName=InstanceType,Value=t3.micro Namespace=aws:elasticbeanstalk:application:environment,OptionName=NODE_ENV,Value=production Namespace=aws:elasticbeanstalk:application:environment,OptionName=PORT,Value=8080`);
      
      console.log('✅ Environment created');
      return environmentName;
    }
  } catch (error) {
    console.log('❌ Failed to create/update environment:', error.message);
    return null;
  }
}

function getEnvironmentURL(environmentName) {
  console.log('🔍 Getting environment URL...');
  try {
    const result = execSync(`${AWS_CLI} elasticbeanstalk describe-environments --environment-names ${environmentName}`, { encoding: 'utf8' });
    const environments = JSON.parse(result).Environments;
    
    if (environments.length > 0) {
      const url = environments[0].CNAME;
      console.log(`✅ Application URL: https://${url}`);
      return url;
    }
  } catch (error) {
    console.log('⚠️ Could not get environment URL');
  }
  return null;
}

function updateNetlifyConfig(awsUrl) {
  console.log('🔄 Updating Netlify configuration...');
  try {
    if (!fs.existsSync('netlify.toml')) {
      console.log('⚠️ netlify.toml not found, skipping update');
      return;
    }

    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace the placeholder AWS URL with the actual URL
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/pathlink-production\.us-east-1\.elasticbeanstalk\.com/g,
      `https://${awsUrl}`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated');
    console.log(`🔗 API calls will be redirected to: https://${awsUrl}`);
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
  }
}

async function deployManually() {
  console.log('Starting manual AWS deployment...\n');

  // Step 1: Build application
  if (!buildApplication()) {
    console.log('❌ Build failed, cannot continue');
    return false;
  }

  // Step 2: Create deployment package
  if (!createApplicationZip()) {
    console.log('❌ Failed to create deployment package');
    return false;
  }

  // Step 3: Create EB application
  if (!createElasticBeanstalkApplication()) {
    console.log('❌ Failed to create EB application');
    return false;
  }

  // Step 4: Create application version
  const versionLabel = createApplicationVersion();
  if (!versionLabel) {
    console.log('❌ Failed to create application version');
    return false;
  }

  // Step 5: Create/update environment
  const environmentName = createEnvironment(versionLabel);
  if (!environmentName) {
    console.log('❌ Failed to create environment');
    return false;
  }

  // Step 6: Get URL and update Netlify config
  const appUrl = getEnvironmentURL(environmentName);
  if (appUrl) {
    updateNetlifyConfig(appUrl);
  }

  console.log('\n🎉 MANUAL DEPLOYMENT COMPLETE!');
  console.log('===============================');
  console.log('✅ Application built and packaged');
  console.log('✅ Elastic Beanstalk application created');
  console.log('✅ Environment deployed');
  if (appUrl) {
    console.log(`✅ Application URL: https://${appUrl}`);
    console.log(`🔍 Test your app: https://${appUrl}/health`);
  }
  console.log('✅ Netlify configuration updated');
  
  console.log('\n📋 Next steps:');
  console.log('1. Test your AWS application');
  console.log('2. Deploy frontend to Netlify');
  console.log('3. Test end-to-end functionality');

  return true;
}

// Run manual deployment
if (require.main === module) {
  deployManually().catch(console.error);
}

module.exports = { deployManually };
