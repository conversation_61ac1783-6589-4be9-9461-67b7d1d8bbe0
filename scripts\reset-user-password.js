import pg from 'pg';
import crypto from 'crypto';
import { promisify } from 'util';
import dotenv from 'dotenv';

dotenv.config();

const scryptAsync = promisify(crypto.scrypt);

// Same hashing function as in auth.ts
async function hashPassword(password) {
  const salt = crypto.randomBytes(16).toString("hex");
  const buf = await scryptAsync(password, salt, 64);
  return `${buf.toString("hex")}.${salt}`;
}

async function resetUserPassword() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new pg.Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Find the user
    const findUserQuery = 'SELECT * FROM users WHERE email = $1';
    const userResult = await client.query(findUserQuery, ['<EMAIL>']);
    
    if (userResult.rows.length === 0) {
      console.log('User not found');
      return;
    }
    
    const user = userResult.rows[0];
    console.log('Found user:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });

    // Hash the new password using the same method as the application
    const newPassword = 'password123';
    const hashedPassword = await hashPassword(newPassword);
    console.log('Password hashed successfully');

    // Update the user's password
    const updateQuery = 'UPDATE users SET password = $1 WHERE id = $2 RETURNING id, email, name, role';
    const updateResult = await client.query(updateQuery, [hashedPassword, user.id]);
    
    console.log('Password reset successfully for user:', {
      id: updateResult.rows[0].id,
      email: updateResult.rows[0].email,
      name: updateResult.rows[0].name,
      role: updateResult.rows[0].role
    });
    console.log('New password is: password123');

  } catch (error) {
    console.error('Error resetting password:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

resetUserPassword();
