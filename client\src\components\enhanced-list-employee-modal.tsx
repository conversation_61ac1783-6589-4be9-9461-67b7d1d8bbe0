import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { insertSurplusEmployeeSchema } from "@shared/schema";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Search, UserPlus, FileText, RefreshCw, CheckCircle2 } from "lucide-react";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import ResumeViewModal from "./resume-view-modal";

// Define the form schema for manually adding an employee
const manualFormSchema = insertSurplusEmployeeSchema.omit({ employer_id: true, status: true }).extend({
  name: z.string().min(2, "Name must be at least 2 characters"),
  previous_role: z.string().min(2, "Previous role must be at least 2 characters"),
  industry: z.string().min(1, "Please select an industry"),
  notes: z.string().optional(),
  skills: z.string().optional(),
  years_experience: z.number().min(0).optional(),
  transfer_reason: z.string().optional(),
  potential_positions: z.string().optional(),
});

// Define the form schema for searching existing users
const searchFormSchema = z.object({
  searchQuery: z.string().min(2, "Search query must be at least 2 characters"),
});

type ManualFormValues = z.infer<typeof manualFormSchema>;
type SearchFormValues = z.infer<typeof searchFormSchema>;

// Define the user interface
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface EnhancedListEmployeeModalProps {
  open: boolean;
  onClose: () => void;
}

export default function EnhancedListEmployeeModal({ open, onClose }: EnhancedListEmployeeModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("search");
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showResumeModal, setShowResumeModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);

  // Form for manually adding an employee
  const manualForm = useForm<ManualFormValues>({
    resolver: zodResolver(manualFormSchema),
    defaultValues: {
      name: "",
      previous_role: "",
      industry: "",
      notes: "",
      skills: "",
      years_experience: 0,
      transfer_reason: "",
      potential_positions: "",
    },
  });

  // Form for searching existing users
  const searchForm = useForm<SearchFormValues>({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      searchQuery: "",
    },
  });

  // Mutation for creating a surplus employee
  const createEmployeeMutation = useMutation({
    mutationFn: async (data: ManualFormValues) => {
      const res = await apiRequest("POST", "/api/surplus-employees", data);
      return await res.json();
    },
    onSuccess: () => {
      // Force a complete refetch by removing the cache entry
      queryClient.removeQueries({ queryKey: ["/api/surplus-employees/employer"] });
      queryClient.invalidateQueries({ queryKey: ["/api/surplus-employees/employer"] });

      // Manually trigger a refetch
      queryClient.fetchQuery({ queryKey: ["/api/surplus-employees/employer"] });

      toast({
        title: "Employee listed",
        description: "Surplus employee has been successfully listed",
      });
      manualForm.reset();
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to list employee",
        description: error.message || "There was an error listing the employee",
        variant: "destructive",
      });
    },
  });

  // Mutation for adding an existing user as a surplus employee
  const addExistingUserMutation = useMutation({
    mutationFn: async (data: { userId: number; role: string; notes?: string; transfer_reason?: string; potential_positions?: string }) => {
      const res = await apiRequest("POST", "/api/surplus-employees/existing-user", data);
      return await res.json();
    },
    onSuccess: () => {
      // Force a complete refetch by removing the cache entry
      queryClient.removeQueries({ queryKey: ["/api/surplus-employees/employer"] });
      queryClient.invalidateQueries({ queryKey: ["/api/surplus-employees/employer"] });

      // Manually trigger a refetch
      queryClient.fetchQuery({ queryKey: ["/api/surplus-employees/employer"] });

      toast({
        title: "Employee added",
        description: "Existing user has been successfully added as a surplus employee",
      });
      setSelectedUser(null);
      setSearchQuery("");
      setSearchResults([]);
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to add employee",
        description: error.message || "There was an error adding the employee",
        variant: "destructive",
      });
    },
  });

  // Handle manual form submission
  const onManualSubmit = async (data: ManualFormValues) => {
    setIsSubmitting(true);
    try {
      await createEmployeeMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle search form submission
  const onSearchSubmit = async (data: SearchFormValues) => {
    setIsSearching(true);
    setSearchQuery(data.searchQuery);
    try {
      // Call the API to search for users
      const response = await apiRequest("GET", `/api/users/search?query=${encodeURIComponent(data.searchQuery)}`);
      if (!response.ok) {
        throw new Error("Failed to search for users");
      }
      const results = await response.json();
      setSearchResults(results);
    } catch (error) {
      toast({
        title: "Search failed",
        description: error instanceof Error ? error.message : "Failed to search for users",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Handle selecting a user from search results
  const handleSelectUser = (user: User) => {
    setSelectedUser(user);
  };

  // Handle adding an existing user as a surplus employee
  const handleAddExistingUser = async () => {
    if (!selectedUser) return;

    setIsSubmitting(true);
    try {
      await addExistingUserMutation.mutateAsync({
        userId: selectedUser.id,
        role: manualForm.getValues().previous_role || "Not specified",
        notes: manualForm.getValues().notes,
        transfer_reason: manualForm.getValues().transfer_reason,
        potential_positions: manualForm.getValues().potential_positions,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle viewing a user's resume
  const handleViewResume = (userId: number) => {
    setSelectedUserId(userId);
    setShowResumeModal(true);
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">List Surplus Employee</DialogTitle>
          <DialogDescription>
            Add an employee for potential relocation to other opportunities.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="search" value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="search">Search Existing Users</TabsTrigger>
            <TabsTrigger value="manual">Add Manually</TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-4 mt-4">
            <Form {...searchForm}>
              <form onSubmit={searchForm.handleSubmit(onSearchSubmit)} className="space-y-4">
                <FormField
                  control={searchForm.control}
                  name="searchQuery"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Search by Name</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <div className="relative w-full">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                            <Input placeholder="Enter first or last name..." className="pl-10" {...field} />
                          </div>
                        </FormControl>
                        <Button
                          type="submit"
                          className="bg-[#1C2A42] hover:bg-opacity-90"
                          disabled={isSearching}
                        >
                          {isSearching ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            "Search"
                          )}
                        </Button>
                      </div>
                      <FormDescription>
                        Search for existing users in the system to add as surplus employees.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>

            {/* Search Results */}
            {searchResults.length > 0 ? (
              <div className="space-y-3 mt-4">
                <h3 className="text-sm font-medium text-gray-500">Search Results</h3>
                <div className="space-y-2 max-h-[300px] overflow-y-auto pr-2">
                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      className={`p-3 border rounded-md flex justify-between items-center cursor-pointer transition-colors ${
                        selectedUser?.id === user.id
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                          : "border-gray-200 hover:bg-gray-50 dark:border-gray-700 dark:hover:bg-gray-800/50"
                      }`}
                      onClick={() => handleSelectUser(user)}
                    >
                      <div className="flex-1">
                        <div className="font-medium">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleViewResume(user.id);
                          }}
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                        {selectedUser?.id === user.id && (
                          <CheckCircle2 className="h-5 w-5 text-blue-500" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : searchQuery && !isSearching ? (
              <div className="text-center py-4 text-gray-500 border rounded-md">
                No users found matching your search criteria.
              </div>
            ) : null}

            {/* Selected User Details */}
            {selectedUser && (
              <div className="mt-4 border rounded-md p-4 bg-gray-50 dark:bg-gray-800/50">
                <h3 className="font-medium mb-2">Selected User: {selectedUser.name}</h3>

                <div className="space-y-4 mt-4">
                  <FormField
                    control={manualForm.control}
                    name="previous_role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Current Role</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Software Engineer" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={manualForm.control}
                    name="transfer_reason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reason for Transfer/Relocation</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Explain why this employee is being considered for transfer or relocation..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={manualForm.control}
                    name="potential_positions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Potential New Positions</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Senior Developer, Team Lead" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="manual">
            <Form {...manualForm}>
              <form onSubmit={manualForm.handleSubmit(onManualSubmit)} className="space-y-4 mt-4">
                <FormField
                  control={manualForm.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Employee Name</FormLabel>
                      <FormControl>
                        <Input placeholder="John Doe" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={manualForm.control}
                  name="previous_role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Previous Role</FormLabel>
                      <FormControl>
                        <Input placeholder="Software Engineer" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={manualForm.control}
                    name="industry"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Industry</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select industry" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="Technology">Technology</SelectItem>
                            <SelectItem value="Finance">Finance</SelectItem>
                            <SelectItem value="Healthcare">Healthcare</SelectItem>
                            <SelectItem value="Retail">Retail</SelectItem>
                            <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                            <SelectItem value="Education">Education</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={manualForm.control}
                    name="years_experience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Years of Experience</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            placeholder="5"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={manualForm.control}
                  name="skills"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Skills</FormLabel>
                      <FormControl>
                        <Input placeholder="JavaScript, React, Node.js" {...field} />
                      </FormControl>
                      <FormDescription>
                        Comma-separated list of skills
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={manualForm.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Any additional information about this employee..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </TabsContent>
        </Tabs>

        <DialogFooter className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>

          {activeTab === "search" ? (
            <Button
              type="button"
              className="bg-[#1C2A42] hover:bg-opacity-90"
              disabled={isSubmitting || !selectedUser}
              onClick={handleAddExistingUser}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Selected Employee
                </>
              )}
            </Button>
          ) : (
            <Button
              type="button"
              className="bg-[#1C2A42] hover:bg-opacity-90"
              disabled={isSubmitting}
              onClick={manualForm.handleSubmit(onManualSubmit)}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Listing...
                </>
              ) : (
                "List Employee"
              )}
            </Button>
          )}
        </DialogFooter>

        {/* Resume View Modal */}
        {showResumeModal && selectedUserId && (
          <ResumeViewModal
            open={showResumeModal}
            onClose={() => setShowResumeModal(false)}
            workerId={selectedUserId}
            workerName={selectedUser?.name || "Employee"}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
