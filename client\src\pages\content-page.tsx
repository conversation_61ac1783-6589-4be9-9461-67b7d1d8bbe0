import React, { useEffect, useState } from 'react';
import { useRoute } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'wouter';
import SharedNavbar from '@/components/shared-navbar';

interface ContentItem {
  id: number;
  title: string;
  type: string;
  slug: string;
  content: string;
  created_at: string;
  updated_at: string;
  published: boolean;
}

export default function ContentPage() {
  const [, params] = useRoute('/:slug');
  const [content, setContent] = useState<ContentItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContent = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch content from the API
        const res = await apiRequest('GET', `/api/content/${params.slug}`);
        if (!res.ok) {
          if (res.status === 404) {
            setError('Content not found');
          } else {
            throw new Error('Failed to fetch content');
          }
          return;
        }

        const contentItem = await res.json() as ContentItem;
        setContent(contentItem);
      } catch (err) {
        console.error('Error fetching content:', err);
        setError('Failed to load content');
      } finally {
        setLoading(false);
      }
    };

    if (params?.slug) {
      fetchContent();
    }
  }, [params?.slug]);

  if (loading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-lg">Loading content...</p>
      </div>
    );
  }

  if (error || !content) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <h1 className="text-2xl font-bold text-red-500 mb-4">Error</h1>
        <p className="text-lg mb-6">{error || 'Content not found'}</p>
        <Button asChild>
          <Link href="/">Return to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <SharedNavbar />

      <main className="py-12 px-8">
        <div className="max-w-4xl mx-auto">
          <article className="prose prose-lg max-w-none">
            <div dangerouslySetInnerHTML={{ __html: content.content }} />
          </article>
        </div>
      </main>

      <footer className="py-12 px-8 bg-[#1C2A42] text-white">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">PathLink</h3>
              <p className="text-gray-300">
                AI-powered labor redistribution platform transforming workforce mobility.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link href="/welcome"><span className="text-gray-300 hover:text-white cursor-pointer">Welcome</span></Link></li>
                <li><Link href="/how-it-works"><span className="text-gray-300 hover:text-white cursor-pointer">How It Works</span></Link></li>
                <li><Link href="/privacy-policy"><span className="text-gray-300 hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/pricing"><span className="text-gray-300 hover:text-white cursor-pointer">Pricing</span></Link></li>
                <li><Link href="/contact"><span className="text-gray-300 hover:text-white cursor-pointer">Contact</span></Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Contact</h3>
              <p className="text-gray-300">
                Have questions? <br />
                <Link href="/contact">
                  <span className="text-white hover:underline cursor-pointer">Contact Us</span>
                </Link>
                <br />
                <a href="mailto:<EMAIL>" className="text-white hover:underline"><EMAIL></a>
              </p>
            </div>
          </div>
          <div className="mt-12 pt-8 border-t border-gray-700 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} PathLink. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
