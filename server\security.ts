import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to add security headers to all responses
 */
export function securityHeaders(req: Request, res: Response, next: NextFunction) {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  res.setHeader('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data:; font-src 'self' data: https://fonts.gstatic.com;");
  res.setHeader('Referrer-Policy', 'same-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  next();
}

/**
 * Rate limiting middleware to prevent brute force attacks
 * This is a simple implementation - consider using a more robust solution like express-rate-limit
 */
// Reset request counts on server restart
const requestCounts: Record<string, { count: number, resetTime: number }> = {};

export function simpleRateLimit(maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) {
  return (req: Request, res: Response, next: NextFunction) => {
    // Skip rate limiting in development mode
    if (process.env.NODE_ENV !== 'production') {
      return next();
    }

    const ip = req.ip || req.socket.remoteAddress || 'unknown';
    const now = Date.now();

    // Initialize or reset if window has passed
    if (!requestCounts[ip] || now > requestCounts[ip].resetTime) {
      requestCounts[ip] = {
        count: 1,
        resetTime: now + windowMs
      };
      return next();
    }

    // Increment count
    requestCounts[ip].count++;

    // Check if over limit
    if (requestCounts[ip].count > maxRequests) {
      console.log(`Rate limit exceeded for IP: ${ip}, count: ${requestCounts[ip].count}, max: ${maxRequests}`);
      return res.status(429).json({
        message: 'Too many requests, please try again later.'
      });
    }

    next();
  };
}

/**
 * Middleware to validate and sanitize request parameters
 */
export function sanitizeParams(req: Request, res: Response, next: NextFunction) {
  // Simple sanitization for URL parameters
  if (req.params) {
    Object.keys(req.params).forEach(key => {
      if (typeof req.params[key] === 'string') {
        // Remove any potentially dangerous characters
        req.params[key] = req.params[key].replace(/[<>]/g, '');
      }
    });
  }

  next();
}
