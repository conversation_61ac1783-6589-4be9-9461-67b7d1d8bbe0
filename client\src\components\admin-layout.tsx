import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'wouter';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Users,
  LayoutDashboard,
  Settings,
  Shield,
  LogOut,
  Moon,
  Sun,
  Menu,
  X,
  Globe,
  FileText,
  BarChart2,
  AlertTriangle,
  Bell,
} from 'lucide-react';
import { useTheme } from '@/hooks/use-theme';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const [, setLocation] = useLocation();
  const [location] = useLocation();
  const { theme, setTheme } = useTheme();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    // Check if user is admin, if not redirect to home
    if (user && user.role !== 'admin') {
      setLocation('/');
    }
  }, [user, setLocation]);

  const handleLogout = async () => {
    await logout();
    setLocation('/login');
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  if (!user) {
    return null; // Or a loading spinner
  }

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Top Navigation */}
      <header className="bg-[#1C2A42] text-white shadow-md">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/admin" className="flex items-center">
                <Shield className="h-8 w-8 mr-2" />
                <span className="font-bold text-xl">PathLink Admin</span>
              </Link>
            </div>

            <div className="hidden md:flex items-center space-x-4">
              <Button variant="ghost" size="sm" onClick={toggleTheme} className="text-white">
                {theme === 'dark' ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder-avatar.jpg" alt={user.name} />
                      <AvatarFallback className="bg-primary text-white">
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 flex items-center justify-center text-[10px] font-bold">
                      3
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setLocation('/admin/profile')}>
                    Profile Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setLocation('/admin/notifications')}>
                    Notifications (3)
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setLocation('/')}>
                    View Public Site
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout}>Log out</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <button
              className="md:hidden text-white"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-4rem)]">
        {/* Sidebar Navigation */}
        <aside
          className={`bg-white dark:bg-gray-800 w-64 shadow-md fixed inset-y-16 left-0 transform ${
            isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
          } md:translate-x-0 transition-transform duration-200 ease-in-out z-30`}
        >
          <div className="p-4">
            <nav className="space-y-1">
              <Link to="/admin">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location === '/admin' ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <LayoutDashboard className="h-5 w-5 mr-3" />
                  Dashboard
                </Button>
              </Link>
              <Link to="/admin/users">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location.startsWith('/admin/users') ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <Users className="h-5 w-5 mr-3" />
                  User Management
                </Button>
              </Link>
              <Link to="/admin/domains">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location.startsWith('/admin/domains') ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <Globe className="h-5 w-5 mr-3" />
                  Approved Domains
                </Button>
              </Link>
              <Link to="/admin/content">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location.startsWith('/admin/content') ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <FileText className="h-5 w-5 mr-3" />
                  Content Management
                </Button>
              </Link>

              <Link to="/admin/system">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location.startsWith('/admin/system') ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <Settings className="h-5 w-5 mr-3" />
                  System Settings
                </Button>
              </Link>
              <Link to="/admin/logs">
                <Button
                  variant="ghost"
                  className={`w-full justify-start ${
                    location.startsWith('/admin/logs') ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <AlertTriangle className="h-5 w-5 mr-3" />
                  System Logs
                </Button>
              </Link>
            </nav>
          </div>

          <div className="absolute bottom-0 w-full p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-4">
              <Bell className="h-5 w-5 text-gray-500 dark:text-gray-400 mr-3" />
              <span className="text-sm text-gray-500 dark:text-gray-400">3 new notifications</span>
            </div>
            <Button
              variant="outline"
              className="w-full"
              onClick={handleLogout}
            >
              <LogOut className="h-4 w-4 mr-2" />
              Log Out
            </Button>
          </div>
        </aside>

        {/* Mobile overlay */}
        {isMobileMenuOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-20 md:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          ></div>
        )}

        {/* Main Content */}
        <main className="flex-1 ml-0 md:ml-64 bg-gray-100 dark:bg-gray-900 min-h-screen">
          {children}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
