{
  "name": "PathLink Development Environment",
  "image": "mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye",
  
  // Features to install
  "features": {
    "ghcr.io/devcontainers/features/git:1": {},
    "ghcr.io/devcontainers/features/github-cli:1": {}
  },

  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "settings": {
        "terminal.integrated.defaultProfile.linux": "bash",
        "git.enableSmartCommit": true,
        "git.confirmSync": false,
        "files.watcherExclude": {
          "**/node_modules/**": true,
          "**/dist/**": true,
          "**/.git/**": true
        },
        // Disable download/copy features
        "workbench.enableExperiments": false,
        "telemetry.telemetryLevel": "off",
        "git.allowNoVerifyCommit": false
      },
      "extensions": [
        "ms-vscode.vscode-typescript-next",
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "ms-vscode.vscode-json",
        "GitHub.vscode-pull-request-github"
      ]
    }
  },

  // Forward ports for development
  "forwardPorts": [5000, 3000, 8888],
  "portsAttributes": {
    "5000": {
      "label": "PathLink Server",
      "onAutoForward": "notify"
    },
    "3000": {
      "label": "Frontend Dev Server",
      "onAutoForward": "notify"
    }
  },

  // Post-create command to set up the environment
  "postCreateCommand": "npm install && echo 'Environment setup complete. Use npm run dev to start development.'",

  // Mount the workspace as read-only for certain paths
  "mounts": [
    "source=${localWorkspaceFolder}/.env.example,target=${containerWorkspaceFolder}/.env,type=bind,consistency=cached"
  ],

  // Container user
  "remoteUser": "node",

  // Restrict certain capabilities
  "capAdd": [],
  "securityOpt": ["no-new-privileges:true"],

  // Environment variables
  "containerEnv": {
    "NODE_ENV": "development",
    "CODESPACE_RESTRICTED": "true"
  },

  // Lifecycle scripts
  "initializeCommand": "echo 'Initializing PathLink Codespace...'",
  "onCreateCommand": "echo 'PathLink Codespace created successfully'",
  "updateContentCommand": "npm install",
  "postStartCommand": "echo 'Welcome to PathLink Development Environment!'"
}
