# Contributing to PathLink

Thank you for your interest in contributing to PathLink! Please note that PathLink is proprietary software owned exclusively by <PERSON>. All contributions you make will be owned by <PERSON>. This document provides guidelines and instructions for contributing to this project.

## Code of Conduct

By participating in this project, you agree to abide by our Code of Conduct. Please be respectful and considerate of others.

## How to Contribute

### Reporting Bugs

If you find a bug, please create an issue with the following information:

- A clear, descriptive title
- Steps to reproduce the bug
- Expected behavior
- Actual behavior
- Screenshots (if applicable)
- Environment information (browser, OS, etc.)

### Suggesting Features

We welcome feature suggestions! Please create an issue with:

- A clear, descriptive title
- A detailed description of the proposed feature
- Any relevant mockups or examples
- Why this feature would be beneficial to the project

### Pull Requests

1. Fork the repository
2. Create a new branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run tests to ensure your changes don't break existing functionality
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Pull Request Guidelines

- Follow the coding style of the project
- Include tests for new features or bug fixes
- Update documentation as needed
- Keep pull requests focused on a single change
- Link to relevant issues or discussions

## Development Setup

1. Fork and clone the repository
2. Install dependencies: `npm install`
3. Create a `.env` file with the required environment variables:

   ```env
   # Database connection
   DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

   # Session secret (used for cookie encryption)
   SESSION_SECRET=your_session_secret_here

   # OpenAI API key for AI features
   OPENAI_API_KEY=your_openai_api_key_here

   # SendGrid API key for email functionality
   SENDGRID_API_KEY=your_sendgrid_api_key_here
   ```

4. Run the development server: `npm run dev`
5. Access the application at `http://localhost:5000`

## Coding Standards

- Use TypeScript for type safety
- Follow the existing code style
- Write meaningful commit messages following [Conventional Commits](https://www.conventionalcommits.org/)
- Document new code with JSDoc comments
- Write tests for new functionality
- Use React functional components with hooks
- Follow the [Airbnb JavaScript Style Guide](https://github.com/airbnb/javascript)

## Testing

- Run tests before submitting a pull request: `npm test`
- Ensure your changes don't break existing functionality
- Add new tests for new features or bug fixes

## Documentation

- Update the README.md if necessary
- Document new features or changes in behavior
- Update API documentation for new or modified endpoints

## Review Process

1. A maintainer will review your pull request
2. Changes may be requested
3. Once approved, your pull request will be merged

## Thank You

Your contributions help make PathLink better for everyone. We appreciate your time and effort!
