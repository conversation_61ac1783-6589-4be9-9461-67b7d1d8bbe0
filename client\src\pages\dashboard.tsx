import { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Job } from '@shared/schema';

// Extended Match interface with worker_name
interface Match {
  id: number;
  status: "pending" | "matched" | "rejected" | "interview_scheduled" | "accepted" | "withdrawn";
  worker_id: number;
  job_id: number;
  match_score: number;
  match_date: string | Date;
  interview_date?: string | Date | null;
  worker_name?: string;
}

// Extended SurplusEmployee interface
interface SurplusEmployee {
  id: number;
  name: string;
  previous_role: string;
  industry: string;
  employer_id: number;
  status: "available" | "placed" | "pending";
  notes: string;
  skills: string;
  years_experience: number;
  transfer_reason: string;
  potential_positions: string;
  user_id?: number;
}
import { useAuth } from '@/hooks/use-auth';
import Layout from '@/components/layout';
import PostJobModal from '@/components/post-job-modal';
import EditJobModal from '@/components/edit-job-modal';
import EnhancedListEmployeeModal from '@/components/enhanced-list-employee-modal';
import EmployeeManagementModal from '@/components/employee-management-modal';
import InterviewModal from '@/components/interview-modal';
import { ResumeUploadSection } from '@/components/resume-upload-section';
import { ResumeSearch } from '@/components/resume-search';
import ResumeViewModal from '@/components/resume-view-modal';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import {
  Briefcase,
  Building,
  Calendar,
  Check,
  CheckCircle,
  Clock,
  DollarSign,
  Eye,
  FileText,
  Handshake,
  MapPin,
  ExternalLink,
  Pencil,
  Search,
  Send,
  Trash2,
  UserCog,
  UserPlus,
  X,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { queryClient, apiRequest } from '@/lib/queryClient';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export default function Dashboard() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [showPostJobModal, setShowPostJobModal] = useState(false);
  const [showEditJobModal, setShowEditJobModal] = useState(false);
  const [showListEmployeeModal, setShowListEmployeeModal] = useState(false);
  const [showResumeViewModal, setShowResumeViewModal] = useState(false);
  const [selectedWorker, setSelectedWorker] = useState<{id: number | undefined, name: string} | null>(null);
  const [showEmployeeManagementModal, setShowEmployeeManagementModal] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<SurplusEmployee | null>(null);
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [jobToDelete, setJobToDelete] = useState<number | null>(null);
  const [showJobDetailsModal, setShowJobDetailsModal] = useState(false);
  const [selectedJobForDetails, setSelectedJobForDetails] = useState<any>(null);
  const [showInterviewModal, setShowInterviewModal] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [interviewDate, setInterviewDate] = useState<string>('');
  const [showWithdrawConfirm, setShowWithdrawConfirm] = useState(false);
  const [matchToWithdraw, setMatchToWithdraw] = useState<number | null>(null);


  const { data: jobs = [] } = useQuery<Job[]>({
    queryKey: [user?.role === 'employer' ? '/api/jobs/employer' : '/api/jobs'],
  });

  const { data: surplusEmployees = [] } = useQuery<SurplusEmployee[]>({
    queryKey: [user?.role === 'employer' ? '/api/surplus-employees/employer' : '/api/surplus-employees'],
    enabled: !!user,
  });

  const { data: matches = [] } = useQuery<Match[]>({
    queryKey: ['/api/matches'],
    enabled: !!user,
  });

  // Delete job mutation
  const deleteJobMutation = useMutation({
    mutationFn: async (jobId: number) => {
      const res = await apiRequest("DELETE", `/api/jobs/${jobId}`);
      return res.ok;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/jobs/employer"] });
      toast({
        title: "Job deleted",
        description: "Your job has been successfully deleted",
      });
      setShowDeleteConfirm(false);
      setJobToDelete(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to delete job",
        description: error.message || "There was an error deleting your job",
        variant: "destructive",
      });
    },
  });

  // Modal handlers
  const openPostJobModal = () => setShowPostJobModal(true);
  const closePostJobModal = () => setShowPostJobModal(false);

  const openEditJobModal = (job: Job) => {
    setSelectedJob(job);
    setShowEditJobModal(true);
  };
  const closeEditJobModal = () => setShowEditJobModal(false);
  const openListEmployeeModal = () => setShowListEmployeeModal(true);
  const closeListEmployeeModal = () => setShowListEmployeeModal(false);

  const openEmployeeManagementModal = (employee: SurplusEmployee) => {
    setSelectedEmployee(employee);
    setShowEmployeeManagementModal(true);
  };

  const closeEmployeeManagementModal = () => {
    setShowEmployeeManagementModal(false);
    setSelectedEmployee(null);
    // No manual query cache manipulation here; React Query will refetch after mutation
  };

  // Delete job handlers
  const confirmDeleteJob = (jobId: number) => {
    setJobToDelete(jobId);
    setShowDeleteConfirm(true);
  };

  const handleDeleteJob = async () => {
    if (jobToDelete) {
      await deleteJobMutation.mutateAsync(jobToDelete);
    }
  };

  // View matches function
  const viewMatches = () => {
    window.location.href = "/matches";
  };

  // Apply for job mutation
  const applyForJobMutation = useMutation({
    mutationFn: async (jobId: number) => {
      try {
        const res = await apiRequest("POST", `/api/matches`, { job_id: jobId });
        if (!res.ok) {
          const errorData = await res.json();
          console.error('Application error response:', errorData);
          throw new Error(errorData.message || "Failed to apply for job");
        }
        return res.json();
      } catch (error) {
        console.error('Application error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/matches"] });
      queryClient.invalidateQueries({ queryKey: ["/api/jobs"] });
      toast({
        title: "Application Submitted",
        description: "Your job application has been successfully submitted.",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Application Failed",
        description: error.message || "There was an error submitting your application.",
        variant: "destructive",
      });
    },
  });

  // Apply for a job function
  const applyForJob = async (jobId: number) => {
    try {
      // First check if user has already applied for this job (excluding withdrawn applications)
      const existingMatch = matches.find(m => m.job_id === jobId && m.worker_id === user?.id);

      if (existingMatch && existingMatch.status !== 'withdrawn') {
        toast({
          title: "Already Applied",
          description: "You have already applied for this job.",
          variant: "destructive",
        });
        return;
      }

      // Check if user has a resume
      const resumeResponse = await fetch(`/api/resumes/worker/${user?.id}`);
      if (!resumeResponse.ok) {
        if (resumeResponse.status === 404) {
          toast({
            title: "Resume Required",
            description: "You need to upload a resume before applying for jobs.",
            variant: "destructive",
          });
          return;
        }
        throw new Error("Failed to check resume");
      }

      // Apply for the job using the mutation
      await applyForJobMutation.mutateAsync(jobId);
    } catch (error) {
      console.error('Error applying for job:', error);
      toast({
        title: "Application Failed",
        description: "There was an error submitting your application. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Withdraw application mutation
  const withdrawApplicationMutation = useMutation({
    mutationFn: async (matchId: number) => {
      try {
        const res = await apiRequest("DELETE", `/api/matches/${matchId}/withdraw`);
        if (!res.ok) {
          const errorData = await res.json();
          console.error('Withdraw application error response:', errorData);
          throw new Error(errorData.message || "Failed to withdraw application");
        }
        return res.json();
      } catch (error) {
        console.error('Withdraw application error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/matches"] });
      toast({
        title: "Application Withdrawn",
        description: "Your application has been successfully withdrawn.",
      });
      setShowWithdrawConfirm(false);
      setMatchToWithdraw(null);
    },
    onError: (error: Error) => {
      toast({
        title: "Withdrawal Failed",
        description: error.message || "There was an error withdrawing your application.",
        variant: "destructive",
      });
    },
  });



  // Update match status mutation
  const updateMatchStatusMutation = useMutation({
    mutationFn: async ({ matchId, status, interviewDate }: { matchId: number, status: string, interviewDate?: string }) => {
      try {
        const res = await apiRequest("PUT", `/api/matches/${matchId}/status`, {
          status,
          interview_date: interviewDate
        });
        if (!res.ok) {
          const errorData = await res.json();
          console.error('Update match status error response:', errorData);
          throw new Error(errorData.message || "Failed to update match status");
        }
        return res.json();
      } catch (error) {
        console.error('Update match status error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/matches"] });
      toast({
        title: "Status Updated",
        description: "The application status has been updated successfully.",
      });
      setShowInterviewModal(false);
      setSelectedMatch(null);
      setInterviewDate('');
    },
    onError: (error: Error) => {
      toast({
        title: "Update Failed",
        description: error.message || "There was an error updating the application status.",
        variant: "destructive",
      });
    },
  });

  // Update match status function
  const updateMatchStatus = async (matchId: number, status: string) => {
    try {
      await updateMatchStatusMutation.mutateAsync({ matchId, status });
    } catch (error) {
      console.error('Error updating match status:', error);
    }
  };

  // Schedule interview function
  const scheduleInterview = async () => {
    if (!selectedMatch || !interviewDate) {
      toast({
        title: "Missing Information",
        description: "Please select a date and time for the interview.",
        variant: "destructive",
      });
      return;
    }

    try {
      await updateMatchStatusMutation.mutateAsync({
        matchId: selectedMatch.id,
        status: 'interview_scheduled',
        interviewDate
      });
    } catch (error) {
      console.error('Error scheduling interview:', error);
    }
  };

  // Withdraw application handlers
  const confirmWithdrawApplication = (matchId: number) => {
    console.log('✅ OPENING WITHDRAW CONFIRMATION - Match ID:', matchId);
    setMatchToWithdraw(matchId);
    setShowWithdrawConfirm(true);
  };

  const handleWithdrawApplication = async () => {
    if (matchToWithdraw) {
      await withdrawApplicationMutation.mutateAsync(matchToWithdraw);
    }
  };



  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'matched':
      case 'accepted':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'withdrawn':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'interview_scheduled':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <Layout>


      <div className="container mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-xl sm:text-2xl font-bold text-[#1C2A42] dark:text-white">Welcome back, {user?.name}</h1>
          <p className="text-gray-600 dark:text-gray-300 text-sm sm:text-base">
            Role: {user?.role === 'employer' ? 'Employer' : 'Worker'}
          </p>
        </div>

        {/* Employer Metrics Dashboard */}
        {user?.role === 'employer' && (
          <div className="flex flex-col sm:flex-row gap-5 mb-12">
            <Card className="flex-1 bg-white dark:bg-gray-800 shadow-md border-l-4 border-l-blue-500 h-[120px]">
              <CardContent className="p-0 h-full">
                <div className="flex items-center justify-between h-full px-5">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Jobs Posted</p>
                    <h3 className="text-2xl font-bold text-[#1C2A42] dark:text-white mt-1">{jobs.length}</h3>
                  </div>
                  <div className="flex items-center justify-center bg-blue-100 w-12 h-12 rounded-full flex-shrink-0">
                    <Briefcase className="h-6 w-6 text-blue-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="flex-1 bg-white dark:bg-gray-800 shadow-md border-l-4 border-l-green-500 h-[120px]">
              <CardContent className="p-0 h-full">
                <div className="flex items-center justify-between h-full px-5">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Surplus Employees</p>
                    <h3 className="text-2xl font-bold text-[#1C2A42] dark:text-white mt-1">{surplusEmployees.length}</h3>
                  </div>
                  <div className="flex items-center justify-center bg-green-100 w-12 h-12 rounded-full flex-shrink-0">
                    <UserPlus className="h-6 w-6 text-green-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="flex-1 bg-white dark:bg-gray-800 shadow-md border-l-4 border-l-purple-500 h-[120px]">
              <CardContent className="p-0 h-full">
                <div className="flex items-center justify-between h-full px-5">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Applications Received</p>
                    <h3 className="text-2xl font-bold text-[#1C2A42] dark:text-white mt-1">{matches.length}</h3>
                  </div>
                  <div className="flex items-center justify-center bg-purple-100 w-12 h-12 rounded-full flex-shrink-0">
                    <Handshake className="h-6 w-6 text-purple-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Worker Metrics Dashboard */}
        {user?.role === 'worker' && (
          <div className="flex flex-col sm:flex-row gap-5 mb-12">
            <Card className="flex-1 bg-white dark:bg-gray-800 shadow-md border-l-4 border-l-blue-500 h-[120px]">
              <CardContent className="p-0 h-full">
                <div className="flex items-center justify-between h-full px-5">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Available Jobs</p>
                    <h3 className="text-2xl font-bold text-[#1C2A42] dark:text-white mt-1">{jobs.length}</h3>
                  </div>
                  <div className="flex items-center justify-center bg-blue-100 w-12 h-12 rounded-full flex-shrink-0">
                    <Search className="h-6 w-6 text-blue-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="flex-1 bg-white dark:bg-gray-800 shadow-md border-l-4 border-l-green-500 h-[120px]">
              <CardContent className="p-0 h-full">
                <div className="flex items-center justify-between h-full px-5">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Your Applications</p>
                    <h3 className="text-2xl font-bold text-[#1C2A42] dark:text-white mt-1">
                      {matches.filter(match => match.worker_id === user.id).length}
                    </h3>
                  </div>
                  <div className="flex items-center justify-center bg-green-100 w-12 h-12 rounded-full flex-shrink-0">
                    <FileText className="h-6 w-6 text-green-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="flex-1 bg-white dark:bg-gray-800 shadow-md border-l-4 border-l-purple-500 h-[120px]">
              <CardContent className="p-0 h-full">
                <div className="flex items-center justify-between h-full px-5">
                  <div>
                    <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Reviews</p>
                    <h3 className="text-2xl font-bold text-[#1C2A42] dark:text-white mt-1">
                      {matches.filter(match => match.worker_id === user.id && match.status === 'pending').length}
                    </h3>
                  </div>
                  <div className="flex items-center justify-center bg-purple-100 w-12 h-12 rounded-full flex-shrink-0">
                    <Clock className="h-6 w-6 text-purple-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}



        {/* Action buttons */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-5 mb-8 sm:mb-12">
          {user?.role === 'employer' ? (
            <>
              <div className="bg-[#1C2A42] dark:bg-gray-700 h-[140px] sm:h-[160px] rounded-lg shadow-md hover:shadow-lg transition-all">
                <button
                  type="button"
                  onClick={openPostJobModal}
                  className="w-full h-full flex flex-col items-center justify-center text-white p-4"
                >
                  <div className="flex items-center justify-center bg-blue-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 mb-2 sm:mb-3">
                    <Briefcase className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold">Post Job</h3>
                  <p className="text-xs sm:text-sm text-gray-200 mt-1 text-center">Create a new job posting</p>
                </button>
              </div>

              <div className="bg-[#1C2A42] dark:bg-gray-700 h-[140px] sm:h-[160px] rounded-lg shadow-md hover:shadow-lg transition-all">
                <button
                  type="button"
                  onClick={openListEmployeeModal}
                  className="w-full h-full flex flex-col items-center justify-center text-white p-4"
                >
                  <div className="flex items-center justify-center bg-green-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 mb-2 sm:mb-3">
                    <UserPlus className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold">List Employee</h3>
                  <p className="text-xs sm:text-sm text-gray-200 mt-1 text-center">Add employees for relocation</p>
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="bg-[#1C2A42] dark:bg-gray-700 h-[140px] sm:h-[160px] rounded-lg shadow-md hover:shadow-lg transition-all">
                <button
                  type="button"
                  onClick={() => window.location.href = '/browse-jobs'}
                  className="w-full h-full flex flex-col items-center justify-center text-white p-4"
                >
                  <div className="flex items-center justify-center bg-blue-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 mb-2 sm:mb-3">
                    <Search className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold">Browse Jobs</h3>
                  <p className="text-xs sm:text-sm text-gray-200 mt-1 text-center">Find your next opportunity</p>
                </button>
              </div>

              <div className="bg-[#1C2A42] dark:bg-gray-700 h-[140px] sm:h-[160px] rounded-lg shadow-md hover:shadow-lg transition-all">
                <button
                  type="button"
                  className="w-full h-full flex flex-col items-center justify-center text-white p-4"
                  onClick={() => {
                    // Generate personalized recommendations based on available jobs
                    const availableIndustries = [...new Set(jobs.map(job => job.industry))];
                    const availableSkills = jobs
                      .filter(job => job.required_skills)
                      .flatMap(job => job.required_skills.split(',').map(skill => skill.trim()))
                      .filter((skill, index, arr) => arr.indexOf(skill) === index)
                      .slice(0, 5);

                    const recommendations = [];

                    if (availableIndustries.length > 0) {
                      recommendations.push(`${availableIndustries[0]} Industry Fundamentals`);
                    }

                    if (availableSkills.length > 0) {
                      recommendations.push(...availableSkills.slice(0, 3).map(skill => `${skill} Certification`));
                    }

                    // Add some general recommendations if we don't have enough specific ones
                    if (recommendations.length < 3) {
                      recommendations.push("Professional Communication", "Project Management Basics", "Digital Literacy");
                    }

                    toast({
                      title: "Personalized Skill Recommendations",
                      description: (
                        <div className="space-y-2 mt-2">
                          <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                            Based on current job openings:
                          </p>
                          {recommendations.slice(0, 4).map((rec, index) => (
                            <div key={index} className="p-2 bg-gray-100 dark:bg-gray-800 rounded-md text-sm">
                              {rec}
                            </div>
                          ))}
                        </div>
                      ),
                      duration: 10000,
                    });
                  }}
                >
                  <div className="flex items-center justify-center bg-orange-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 mb-2 sm:mb-3">
                    <UserCog className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                  <h3 className="text-base sm:text-lg font-semibold">Skill Recommendations</h3>
                  <p className="text-xs sm:text-sm text-gray-200 mt-1 text-center">Get personalized learning</p>
                </button>
              </div>

            </>
          )}

          <div className="bg-[#1C2A42] dark:bg-gray-700 h-[140px] sm:h-[160px] rounded-lg shadow-md hover:shadow-lg transition-all sm:col-span-2 lg:col-span-1">
            <button
              type="button"
              onClick={viewMatches}
              className="w-full h-full flex flex-col items-center justify-center text-white p-4"
            >
              <div className="flex items-center justify-center bg-purple-500/20 rounded-full w-12 h-12 sm:w-16 sm:h-16 mb-2 sm:mb-3">
                <Handshake className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
              </div>
              <h3 className="text-base sm:text-lg font-semibold">View Matches</h3>
              <p className="text-xs sm:text-sm text-gray-200 mt-1 text-center">See matching opportunities</p>
            </button>
          </div>
        </div>

        {/* Dashboard content based on role */}
        {/* Resume Search Section for Employers */}
        {user?.role === 'employer' && (
          <div className="mb-8">
            <ResumeSearch />
          </div>
        )}

        {/* Job Recommendations for Workers */}
        {user?.role === 'worker' && jobs.length > 0 && (
          <div className="mb-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden">
              <CardHeader className="pb-3">
                <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Recommended for You</CardTitle>
                <p className="text-sm text-gray-600 dark:text-gray-300">Jobs that match your profile and interests</p>
              </CardHeader>
              <CardContent className="p-4">
                <div className="space-y-4">
                  {jobs
                    .filter(job => {
                      // Filter out jobs the user has already applied to (except withdrawn applications)
                      const existingMatch = matches.find(match => match.job_id === job.id && match.worker_id === user.id);
                      // Show job if no application exists OR if the application was withdrawn
                      return !existingMatch || existingMatch.status === 'withdrawn';
                    })
                    .slice(0, 3) // Show top 3 recommendations
                    .map((job) => {
                      const existingMatch = matches.find(match => match.job_id === job.id && match.worker_id === user.id);
                      const isReapply = existingMatch && existingMatch.status === 'withdrawn';

                      return (
                        <div key={job.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-all duration-200 hover:shadow-md">
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1">
                              <h3 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-1">{job.title}</h3>
                              <p className="text-[#1C2A42]/80 dark:text-white/80 font-medium text-sm">{job.company || 'Company Name'}</p>
                            </div>
                            <div className="flex flex-col items-end gap-1">
                              {isReapply ? (
                                <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs">
                                  Available to Reapply
                                </Badge>
                              ) : (
                                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 text-xs">
                                  Recommended
                                </Badge>
                              )}
                              {job.salary && (
                                <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                                  ${job.salary.toLocaleString()}
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3 text-sm">
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                              <span className="truncate">{job.location}</span>
                            </div>
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <Building className="h-4 w-4 mr-2 text-gray-400" />
                              <span className="truncate">{job.industry}</span>
                            </div>
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <Clock className="h-4 w-4 mr-2 text-gray-400" />
                              <span>{job.employment_type || 'Full-time'}</span>
                            </div>
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                              <span>Posted {job.created_at ? new Date(job.created_at).toLocaleDateString() : 'Recently'}</span>
                            </div>
                          </div>

                          <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-3">
                            {job.description}
                          </p>

                          {job.required_skills && (
                            <div className="mb-3">
                              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Skills:</p>
                              <div className="flex flex-wrap gap-1">
                                {job.required_skills.split(',').slice(0, 3).map((skill, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs px-2 py-1">
                                    {skill.trim()}
                                  </Badge>
                                ))}
                                {job.required_skills.split(',').length > 3 && (
                                  <Badge variant="secondary" className="text-xs px-2 py-1">
                                    +{job.required_skills.split(',').length - 3} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}

                          <div className="flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-700">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs"
                              onClick={() => {
                                setSelectedJobForDetails(job);
                                setShowJobDetailsModal(true);
                              }}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View Details
                            </Button>
                            <Button
                              size="sm"
                              className={isReapply ? "bg-orange-600 text-white text-xs hover:bg-orange-700" : "bg-green-600 text-white text-xs hover:bg-green-700"}
                              onClick={() => applyForJob(job.id)}
                            >
                              <Send className="h-3 w-3 mr-1" />
                              {isReapply ? 'Reapply Now' : 'Apply Now'}
                            </Button>
                          </div>
                        </div>
                      );
                    })}

                  {jobs.filter(job => {
                    const existingMatch = matches.find(match => match.job_id === job.id && match.worker_id === user.id);
                    return !existingMatch || existingMatch.status === 'withdrawn';
                  }).length === 0 && (
                    <div className="text-center py-6">
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
                      <p className="text-gray-500 dark:text-gray-400 text-lg font-medium mb-2">All Caught Up!</p>
                      <p className="text-gray-400 dark:text-gray-500 text-sm">You've applied to all available jobs. Check back later for new opportunities!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {user?.role === 'employer' ? (
            <>
              {/* Posted Jobs */}
              <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Your Posted Jobs</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {jobs.length > 0 ? (
                    <div className="space-y-4">
                      {jobs.map((job) => (
                        <div key={job.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-colors">
                          <h3 className="font-semibold text-lg text-[#1C2A42] dark:text-white truncate">{job.title}</h3>
                          <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2">{job.description}</p>
                          <div className="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                            <span className="truncate mr-2">{job.industry}</span>
                            <span className="truncate">{job.location}</span>
                          </div>
                          <div className="flex justify-end mt-3 space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center text-xs"
                              onClick={() => openEditJobModal(job)}
                            >
                              <Pencil className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center text-xs text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200"
                              onClick={() => confirmDeleteJob(job.id)}
                            >
                              <Trash2 className="h-3 w-3 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No jobs posted yet. Click "Post Job" to create one.</p>
                  )}
                </CardContent>
              </Card>

              {/* Job Applications */}
              <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Job Applications</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {matches.length > 0 ? (
                    <div className="space-y-4">
                      {matches.map((match) => {
                        const job = jobs.find(j => j.id === match.job_id);
                        if (!job) return null;

                        return (
                          <div key={match.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-colors">
                            <div className="flex justify-between items-start">
                              <div className="mr-2 flex-grow">
                                <h3 className="font-semibold text-lg text-[#1C2A42] dark:text-white truncate">{job.title}</h3>
                                <p className="text-gray-600 dark:text-gray-300 text-sm mt-1">
                                  Applicant: {match.worker_name || `Worker ${match.worker_id}`}
                                </p>
                                {match.status === 'interview_scheduled' && match.interview_date && (
                                  <p className="text-green-600 dark:text-green-400 text-xs mt-1">
                                    <Calendar className="h-3 w-3 inline mr-1" />
                                    Interview: {new Date(match.interview_date).toLocaleString()}
                                  </p>
                                )}
                              </div>
                              <Badge variant="outline" className={`${getMatchStatusColor(match.status)} flex-shrink-0`}>
                                {match.status.charAt(0).toUpperCase() + match.status.slice(1).replace('_', ' ')}
                              </Badge>
                            </div>
                            <div className="flex justify-between mt-3 text-xs">
                              <span className="text-gray-500 dark:text-gray-400">Match Score: {match.match_score}%</span>
                              <span className="text-gray-500 dark:text-gray-400">
                                Applied: {new Date(match.match_date).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="mt-3 flex justify-end space-x-2">
                              {match.status === 'pending' && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center text-xs text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200"
                                    onClick={() => updateMatchStatus(match.id, 'rejected')}
                                  >
                                    <X className="h-3 w-3 mr-1" />
                                    Reject
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center text-xs text-blue-500 hover:text-blue-600 hover:bg-blue-50 border-blue-200"
                                    onClick={() => {
                                      setSelectedMatch(match);
                                      setShowInterviewModal(true);
                                    }}
                                  >
                                    <Calendar className="h-3 w-3 mr-1" />
                                    Schedule Interview
                                  </Button>
                                </>
                              )}

                              {match.status === 'interview_scheduled' && (
                                <>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center text-xs text-red-500 hover:text-red-600 hover:bg-red-50 border-red-200"
                                    onClick={() => updateMatchStatus(match.id, 'rejected')}
                                  >
                                    <X className="h-3 w-3 mr-1" />
                                    Cancel & Reject
                                  </Button>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center text-xs text-green-500 hover:text-green-600 hover:bg-green-50 border-green-200"
                                    onClick={() => updateMatchStatus(match.id, 'accepted')}
                                  >
                                    <Check className="h-3 w-3 mr-1" />
                                    Accept
                                  </Button>
                                </>
                              )}

                              {match.status === 'rejected' && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex items-center text-xs text-blue-500 hover:text-blue-600 hover:bg-blue-50 border-blue-200"
                                  onClick={() => {
                                    setSelectedMatch(match);
                                    setShowInterviewModal(true);
                                  }}
                                >
                                  <Calendar className="h-3 w-3 mr-1" />
                                  Reconsider & Schedule
                                </Button>
                              )}
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center text-xs"
                                onClick={() => {
                                  // View applicant's resume
                                  setSelectedWorker({id: match.worker_id, name: match.worker_name || `Applicant ${match.worker_id}`});
                                  setShowResumeViewModal(true);
                                }}
                              >
                                <FileText className="h-3 w-3 mr-1" />
                                View Resume
                              </Button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No applications received yet.</p>
                  )}
                </CardContent>
              </Card>

              {/* Listed Surplus Employees */}
              <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Your Surplus Employees</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {surplusEmployees.length > 0 ? (
                    <div className="space-y-4">
                      {surplusEmployees.map((employee) => (
                        <div key={employee.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-md hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-colors">
                          <h3 className="font-semibold text-lg text-[#1C2A42] dark:text-white truncate">{employee.name}</h3>
                          <p className="text-gray-600 dark:text-gray-300 truncate">Current Role: {employee.previous_role}</p>

                          <div className="flex justify-between mt-2 text-xs">
                            <span className="text-gray-500 dark:text-gray-400 truncate mr-2">{employee.industry}</span>
                            <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 flex-shrink-0">
                              {employee.status}
                            </Badge>
                          </div>

                          {employee.skills && (
                            <div className="mt-2 text-sm">
                              <span className="text-gray-700 dark:text-gray-300 font-medium">Skills: </span>
                              <span className="text-gray-600 dark:text-gray-400">{employee.skills}</span>
                            </div>
                          )}

                          {employee.years_experience && (
                            <div className="mt-1 text-sm">
                              <span className="text-gray-700 dark:text-gray-300 font-medium">Experience: </span>
                              <span className="text-gray-600 dark:text-gray-400">{employee.years_experience} years</span>
                            </div>
                          )}

                          {employee.potential_positions && (
                            <div className="mt-1 text-sm">
                              <span className="text-gray-700 dark:text-gray-300 font-medium">Potential Positions: </span>
                              <span className="text-gray-600 dark:text-gray-400">{employee.potential_positions}</span>
                            </div>
                          )}

                          {employee.transfer_reason && (
                            <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700/50 rounded-md text-sm">
                              <span className="text-gray-700 dark:text-gray-300 font-medium">Transfer Reason: </span>
                              <span className="text-gray-600 dark:text-gray-400">{employee.transfer_reason}</span>
                            </div>
                          )}

                          <div className="mt-3 flex justify-end space-x-2">
                            {employee.user_id && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center text-xs"
                                onClick={() => {
                                  setSelectedWorker({id: employee.user_id, name: employee.name});
                                  setShowResumeViewModal(true);
                                }}
                              >
                                <FileText className="h-3 w-3 mr-1" />
                                View Resume
                              </Button>
                            )}
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center text-xs text-blue-500 hover:text-blue-600 hover:bg-blue-50 border-blue-200"
                              onClick={() => openEmployeeManagementModal(employee)}
                            >
                              <Pencil className="h-3 w-3 mr-1" />
                              Manage
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No surplus employees listed yet. Click "List Employee" to add one.</p>
                  )}
                </CardContent>
              </Card>
            </>
          ) : (
            <>
              {/* Available Jobs */}
              <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Available Jobs</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {jobs.length > 0 ? (
                    <div className="space-y-4">
                      {jobs.slice(0, 5).map((job) => (
                        <div key={job.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-all duration-200 hover:shadow-md">
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1">
                              <h3 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-1">{job.title}</h3>
                              <p className="text-[#1C2A42]/80 dark:text-white/80 font-medium text-sm">{job.company}</p>
                            </div>
                            <div className="flex flex-col items-end gap-1">
                              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs">
                                {job.industry}
                              </Badge>
                              {job.salary && (
                                <span className="text-green-600 dark:text-green-400 font-semibold text-sm">
                                  ${job.salary.toLocaleString()}
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3 text-sm">
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                              <span className="truncate">{job.location}</span>
                            </div>
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <Clock className="h-4 w-4 mr-2 text-gray-400" />
                              <span>{job.employment_type || 'Full-time'}</span>
                            </div>
                            {job.experience_level && (
                              <div className="flex items-center text-gray-600 dark:text-gray-300">
                                <Briefcase className="h-4 w-4 mr-2 text-gray-400" />
                                <span>{job.experience_level}</span>
                              </div>
                            )}
                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                              <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                              <span>Posted {job.created_at ? new Date(job.created_at).toLocaleDateString() : 'Recently'}</span>
                            </div>
                          </div>

                          <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-3">
                            {job.description}
                          </p>

                          {job.required_skills && (
                            <div className="mb-3">
                              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Skills:</p>
                              <div className="flex flex-wrap gap-1">
                                {job.required_skills.split(',').slice(0, 4).map((skill, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs px-2 py-1">
                                    {skill.trim()}
                                  </Badge>
                                ))}
                                {job.required_skills.split(',').length > 4 && (
                                  <Badge variant="secondary" className="text-xs px-2 py-1">
                                    +{job.required_skills.split(',').length - 4} more
                                  </Badge>
                                )}
                              </div>
                            </div>
                          )}

                          <div className="flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-700">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs"
                              onClick={() => {
                                setSelectedJobForDetails(job);
                                setShowJobDetailsModal(true);
                              }}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View Full Details
                            </Button>
                            <Button
                              size="sm"
                              className="bg-[#1C2A42] text-white text-xs hover:bg-[#1C2A42]/90"
                              onClick={() => applyForJob(job.id)}
                            >
                              <Send className="h-3 w-3 mr-1" />
                              Apply Now
                            </Button>
                          </div>
                        </div>
                      ))}
                      {jobs.length > 5 && (
                        <div className="text-center pt-4">
                          <Button variant="outline" size="sm" className="text-sm">
                            View All {jobs.length} Available Jobs
                          </Button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No jobs available at the moment. Check back later.</p>
                  )}
                </CardContent>
              </Card>

              {/* Your Resume */}
              <ResumeUploadSection />

              {/* Your Applications */}
              <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden">
                <CardHeader className="pb-3">
                  <CardTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Your Applications</CardTitle>
                </CardHeader>
                <CardContent className="p-4">
                  {user?.role === 'worker' && matches.length > 0 ? (
                    <div className="space-y-4">
                      {matches
                        .filter(match => match.worker_id === user.id) // Only show matches for current worker
                        .map((match) => {
                        const job = jobs.find(j => j.id === match.job_id);
                        return job ? (
                          <div key={match.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-all duration-200 hover:shadow-md">
                            <div className="flex justify-between items-start mb-3">
                              <div className="flex-1">
                                <h3 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-1">{job.title}</h3>
                                <p className="text-[#1C2A42]/80 dark:text-white/80 font-medium text-sm">{job.company || 'Company Name'}</p>
                              </div>
                              <div className="flex flex-col items-end gap-2">
                                <Badge variant="outline" className={`${getMatchStatusColor(match.status)} flex-shrink-0 text-xs`}>
                                  {match.status.charAt(0).toUpperCase() + match.status.slice(1).replace('_', ' ')}
                                </Badge>
                                {match.match_score && (
                                  <div className="text-xs text-green-600 dark:text-green-400 font-semibold">
                                    {match.match_score}% Match
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3 text-sm">
                              <div className="flex items-center text-gray-600 dark:text-gray-300">
                                <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                                <span className="truncate">{job.location}</span>
                              </div>
                              <div className="flex items-center text-gray-600 dark:text-gray-300">
                                <Building className="h-4 w-4 mr-2 text-gray-400" />
                                <span className="truncate">{job.industry}</span>
                              </div>
                              <div className="flex items-center text-gray-600 dark:text-gray-300">
                                <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                                <span>Applied {match.match_date ? new Date(match.match_date).toLocaleDateString() : 'Recently'}</span>
                              </div>
                              {job.salary && (
                                <div className="flex items-center text-green-600 dark:text-green-400">
                                  <DollarSign className="h-4 w-4 mr-2" />
                                  <span className="font-semibold">${job.salary.toLocaleString()}</span>
                                </div>
                              )}
                            </div>

                            {match.status === 'interview_scheduled' && match.interview_date && (
                              <div className="mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-md border border-blue-200 dark:border-blue-800">
                                <div className="flex items-center text-blue-700 dark:text-blue-300 text-sm">
                                  <Calendar className="h-4 w-4 mr-2" />
                                  <span className="font-medium">Interview Scheduled:</span>
                                  <span className="ml-2">{new Date(match.interview_date).toLocaleString()}</span>
                                </div>
                              </div>
                            )}

                            {match.status === 'accepted' && (
                              <div className="mb-3 p-2 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800">
                                <div className="flex items-center text-green-700 dark:text-green-300 text-sm">
                                  <CheckCircle className="h-4 w-4 mr-2" />
                                  <span className="font-medium">Congratulations! Your application was accepted.</span>
                                </div>
                              </div>
                            )}

                            {match.status === 'rejected' && (
                              <div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
                                <div className="flex items-center text-red-700 dark:text-red-300 text-sm">
                                  <XCircle className="h-4 w-4 mr-2" />
                                  <span className="font-medium">Application was not selected for this position.</span>
                                </div>
                              </div>
                            )}

                            {match.status === 'withdrawn' && (
                              <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-900/20 rounded-md border border-gray-200 dark:border-gray-800">
                                <div className="flex items-center text-gray-700 dark:text-gray-300 text-sm">
                                  <X className="h-4 w-4 mr-2" />
                                  <span className="font-medium">You withdrew your application for this position.</span>
                                </div>
                              </div>
                            )}

                            <div className="flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-700">
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs"
                                onClick={() => {
                                  setSelectedJobForDetails(job);
                                  setShowJobDetailsModal(true);
                                }}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                View Job Details
                              </Button>
                              <div className="flex space-x-2">
                                {match.status === 'pending' && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-xs text-red-500 border-red-200 hover:bg-red-50 dark:hover:bg-red-900/20"
                                    onClick={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                      console.log('🔴 WITHDRAW BUTTON CLICKED - Match ID:', match.id);
                                      confirmWithdrawApplication(match.id);
                                    }}
                                  >
                                    <X className="h-3 w-3 mr-1" />
                                    Withdraw
                                  </Button>
                                )}
                                {match.status === 'withdrawn' && (
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-xs text-green-600 border-green-200 hover:bg-green-50 dark:hover:bg-green-900/20"
                                    onClick={() => applyForJob(job.id)}
                                  >
                                    <Send className="h-3 w-3 mr-1" />
                                    Reapply
                                  </Button>
                                )}

                              </div>
                            </div>
                          </div>
                        ) : null;
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="mb-4">
                        <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400 text-lg font-medium mb-2">No Applications Yet</p>
                        <p className="text-gray-400 dark:text-gray-500 text-sm">You haven't applied to any jobs yet. Browse available jobs above to get started!</p>
                      </div>
                      <Button
                        size="sm"
                        className="bg-[#1C2A42] text-white hover:bg-opacity-90"
                      >
                        Browse Available Jobs
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      <PostJobModal open={showPostJobModal} onClose={closePostJobModal} />
      <EnhancedListEmployeeModal open={showListEmployeeModal} onClose={closeListEmployeeModal} />
      {selectedEmployee && (
        <EmployeeManagementModal
          open={showEmployeeManagementModal}
          onClose={closeEmployeeManagementModal}
          employee={selectedEmployee}
        />
      )}
      {selectedWorker && (
        <ResumeViewModal
          open={showResumeViewModal}
          onClose={() => setShowResumeViewModal(false)}
          workerId={selectedWorker.id}
          workerName={selectedWorker.name}
        />
      )}
      {selectedJob && (
        <EditJobModal
          open={showEditJobModal}
          onClose={closeEditJobModal}
          job={selectedJob}
        />
      )}

      {/* Interview Scheduling Modal */}
      <InterviewModal
        open={showInterviewModal}
        onClose={() => {
          setShowInterviewModal(false);
          setSelectedMatch(null);
          setInterviewDate('');
        }}
        match={selectedMatch}
        onSchedule={(date) => {
          setInterviewDate(date);
          scheduleInterview();
        }}
      />

      {/* Job Details Modal */}
      {selectedJobForDetails && (
        <AlertDialog open={showJobDetailsModal} onOpenChange={setShowJobDetailsModal}>
          <AlertDialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <AlertDialogHeader>
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
                <div className="flex-1">
                  <AlertDialogTitle className="text-2xl font-bold text-[#1C2A42] dark:text-white mb-2">
                    {selectedJobForDetails.title}
                  </AlertDialogTitle>
                  <div className="flex items-center gap-2 mb-3">
                    <h3 className="text-lg font-semibold text-[#1C2A42]/80 dark:text-white/80">
                      {selectedJobForDetails.company || 'Company Name'}
                    </h3>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                      {selectedJobForDetails.industry}
                    </Badge>
                  </div>
                </div>
                {selectedJobForDetails.salary && (
                  <div className="text-right">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      ${selectedJobForDetails.salary.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500">Annual Salary</div>
                  </div>
                )}
              </div>
            </AlertDialogHeader>

            <div className="space-y-6 py-4">
              {/* Key Information Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-1 text-sm">Location</h4>
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <MapPin className="h-4 w-4 mr-2 text-gray-400" />
                    <span className="text-sm">{selectedJobForDetails.location}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-1 text-sm">Employment Type</h4>
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <Clock className="h-4 w-4 mr-2 text-gray-400" />
                    <span className="text-sm capitalize">{selectedJobForDetails.employment_type || 'Full-time'}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-1 text-sm">Experience Level</h4>
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <Briefcase className="h-4 w-4 mr-2 text-gray-400" />
                    <span className="text-sm capitalize">{selectedJobForDetails.experience_level || 'Not specified'}</span>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-1 text-sm">Posted Date</h4>
                  <div className="flex items-center text-gray-600 dark:text-gray-300">
                    <Calendar className="h-4 w-4 mr-2 text-gray-400" />
                    <span className="text-sm">{selectedJobForDetails.created_at ? new Date(selectedJobForDetails.created_at).toLocaleDateString() : 'Recently'}</span>
                  </div>
                </div>
              </div>

              {/* Job Description */}
              <div>
                <h4 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-3 border-b border-gray-200 dark:border-gray-700 pb-2">
                  Job Description
                </h4>
                <div className="prose prose-sm max-w-none text-gray-600 dark:text-gray-300">
                  <p className="whitespace-pre-wrap leading-relaxed">
                    {selectedJobForDetails.description}
                  </p>
                </div>
              </div>

              {/* Requirements */}
              {selectedJobForDetails.requirements && (
                <div>
                  <h4 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-3 border-b border-gray-200 dark:border-gray-700 pb-2">
                    Requirements
                  </h4>
                  <div className="prose prose-sm max-w-none text-gray-600 dark:text-gray-300">
                    <p className="whitespace-pre-wrap leading-relaxed">
                      {selectedJobForDetails.requirements}
                    </p>
                  </div>
                </div>
              )}

              {/* Required Skills */}
              {selectedJobForDetails.required_skills && (
                <div>
                  <h4 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-3 border-b border-gray-200 dark:border-gray-700 pb-2">
                    Required Skills
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedJobForDetails.required_skills.split(',').map((skill, index) => (
                      <Badge key={index} variant="secondary" className="text-sm px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {skill.trim()}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Benefits */}
              {selectedJobForDetails.benefits && (
                <div>
                  <h4 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-3 border-b border-gray-200 dark:border-gray-700 pb-2">
                    Benefits & Perks
                  </h4>
                  <div className="prose prose-sm max-w-none text-gray-600 dark:text-gray-300">
                    <p className="whitespace-pre-wrap leading-relaxed">
                      {selectedJobForDetails.benefits}
                    </p>
                  </div>
                </div>
              )}

              {/* Additional Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Work Model */}
                {selectedJobForDetails.work_model && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Work Model</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.work_model}
                    </p>
                  </div>
                )}

                {/* Minimum Experience */}
                {selectedJobForDetails.minimum_experience && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Minimum Experience</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.minimum_experience}
                    </p>
                  </div>
                )}

                {/* Preferred Backgrounds */}
                {selectedJobForDetails.preferred_backgrounds && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Preferred Backgrounds</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.preferred_backgrounds}
                    </p>
                  </div>
                )}

                {/* Availability Needs */}
                {selectedJobForDetails.availability_needs && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Availability Requirements</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.availability_needs}
                    </p>
                  </div>
                )}

                {/* Language Requirements */}
                {selectedJobForDetails.language_requirements && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Language Requirements</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.language_requirements}
                    </p>
                  </div>
                )}

                {/* Culture Fit Keywords */}
                {selectedJobForDetails.culture_fit_keywords && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Culture & Values</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.culture_fit_keywords}
                    </p>
                  </div>
                )}

                {/* Salary Range */}
                {selectedJobForDetails.salary_range && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Salary Range</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.salary_range}
                    </p>
                  </div>
                )}

                {/* Diversity Goals */}
                {selectedJobForDetails.diversity_goals && (
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] dark:text-white mb-2">Diversity & Inclusion</h4>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      {selectedJobForDetails.diversity_goals}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <AlertDialogFooter className="flex flex-col sm:flex-row gap-2 pt-6 border-t border-gray-200 dark:border-gray-700">
              <AlertDialogCancel className="sm:order-1">Close</AlertDialogCancel>
              {user?.role === 'worker' && (
                <Button
                  className="bg-[#1C2A42] text-white hover:bg-[#1C2A42]/90 sm:order-2"
                  onClick={() => {
                    setShowJobDetailsModal(false);
                    applyForJob(selectedJobForDetails.id);
                  }}
                >
                  <Send className="h-4 w-4 mr-2" />
                  Apply for This Job
                </Button>
              )}
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      {/* Delete Job Confirmation */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the job posting
              and remove it from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteJob}
              className="bg-red-500 hover:bg-red-600 text-white"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Custom Withdraw Confirmation Modal */}
      {showWithdrawConfirm && (
        <div className="fixed inset-0 z-[9999] flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={() => {
              console.log('🔴 BACKDROP CLICKED - CLOSING MODAL');
              setShowWithdrawConfirm(false);
              setMatchToWithdraw(null);
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-2xl border-4 border-red-500 p-6 max-w-md w-full mx-4 z-10">
            {/* Header */}
            <div className="mb-4">
              <h2 className="text-xl font-bold text-red-600 flex items-center">
                <AlertCircle className="h-6 w-6 mr-2" />
                ⚠️ Withdraw Application?
              </h2>
              <p className="text-gray-700 mt-2">
                Are you sure you want to withdraw your application? This action cannot be undone.
                You will need to reapply if you change your mind.
              </p>
              <div className="mt-3 p-2 bg-red-50 rounded border border-red-200">
                <p className="text-sm text-red-800">
                  <strong>Match ID: {matchToWithdraw}</strong>
                </p>
              </div>
            </div>

            {/* Footer Buttons */}
            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  console.log('🔴 CANCEL CLICKED');
                  setShowWithdrawConfirm(false);
                  setMatchToWithdraw(null);
                }}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800"
              >
                Cancel
              </Button>
              <Button
                type="button"
                onClick={() => {
                  console.log('🔴 CONFIRM WITHDRAW CLICKED');
                  handleWithdrawApplication();
                }}
                className="bg-red-500 hover:bg-red-600 text-white font-bold"
              >
                <X className="h-4 w-4 mr-2" />
                Withdraw Application
              </Button>
            </div>
          </div>
        </div>
      )}


    </Layout>
  );
}
