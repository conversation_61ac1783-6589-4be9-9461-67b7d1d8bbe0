import OpenAI from "openai";

// Initialize OpenAI client with API key from environment
// Verify API key exists
if (!process.env.OPENAI_API_KEY) {
  console.error("OPENAI_API_KEY is not set in environment variables");
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || "" // Provide empty string as fallback to avoid null
});

// Validate response format
interface ReskillingRecommendation {
  title: string;
  description: string;
  relevance: 'High' | 'Medium' | 'Low';
  timeCommitment: string;
  transferableSkills: string[];
}

interface SkillsAssessment {
  currentSkills: string[];
  skillGaps: string[];
  suggestions: ReskillingRecommendation[];
}

/**
 * Safely parse JSON with a fallback value
 */
function safeJsonParse<T>(jsonString: string | null | undefined, fallback: T): T {
  if (!jsonString) return fallback;

  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return fallback;
  }
}

/**
 * Analyzes resume text to extract skills and provide reskilling recommendations
 * @param resumeText - Plain text extracted from resume
 * @param jobTitle - Optional job title to target recommendations
 */
export async function analyzeResume(resumeText: string, jobTitle?: string): Promise<SkillsAssessment> {
  const fallbackResponse: SkillsAssessment = {
    currentSkills: ["Communication", "Problem Solving", "Adaptability"],
    skillGaps: ["Data Analysis", "Automation", "Digital Marketing"],
    suggestions: [
      {
        title: "Data Analysis Fundamentals",
        description: "Learn the basics of data analysis with tools like Excel, SQL and Python. This course provides practical skills for any industry.",
        relevance: "High",
        timeCommitment: "2-3 months part-time",
        transferableSkills: ["Problem Solving", "Attention to Detail"]
      },
      {
        title: "Digital Marketing Essentials",
        description: "Master the fundamentals of digital marketing including social media, SEO, and content creation strategies.",
        relevance: "Medium",
        timeCommitment: "4-6 weeks part-time",
        transferableSkills: ["Communication", "Creativity"]
      },
      {
        title: "Project Management Certification",
        description: "Gain credentials in project management to enhance organizational and leadership capabilities.",
        relevance: "Medium",
        timeCommitment: "2-4 months part-time",
        transferableSkills: ["Organization", "Leadership", "Communication"]
      }
    ]
  };

  try {
    // Define the prompt for GPT-4o
    const prompt = `
    Analyze the following resume and extract key information:
    1. Identify all technical and soft skills present
    2. Identify skill gaps for ${jobTitle || "modern workforce needs"}
    3. Provide 3-5 personalized reskilling recommendations

    For each reskilling recommendation, please include:
    - A clear title
    - A brief description (1-2 sentences)
    - Relevance level (High/Medium/Low)
    - Time commitment estimate (e.g., "2-3 months part-time")
    - Transferable skills from current resume that would help

    Return the result as a valid JSON object with the following structure:
    {
      "currentSkills": ["skill1", "skill2"...],
      "skillGaps": ["gap1", "gap2"...],
      "suggestions": [
        {
          "title": "string",
          "description": "string",
          "relevance": "High|Medium|Low",
          "timeCommitment": "string",
          "transferableSkills": ["skill1", "skill2"]
        }
      ]
    }

    Resume to analyze:
    ${resumeText}
    `;

    // Call OpenAI API with GPT-4o
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: "You are an expert career coach and skills analyst." },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" }
    });

    // Parse the response with fallback
    return safeJsonParse(response.choices[0].message.content, fallbackResponse);
  } catch (error) {
    console.error("OpenAI API error:", error);
    return fallbackResponse;
  }
}

/**
 * Generates personalized reskilling suggestions based on worker profile
 * @param skills - List of current skills
 * @param desiredRole - Target role or industry
 */
export async function generateReskillingPath(skills: string[], desiredRole: string): Promise<ReskillingRecommendation[]> {
  const fallbackRecommendations: ReskillingRecommendation[] = [
    {
      title: "Web Development Fundamentals",
      description: "Learn the basics of web development with HTML, CSS and JavaScript. This provides a foundation for many tech roles.",
      relevance: "High",
      timeCommitment: "2-3 months part-time",
      transferableSkills: skills.filter(s => ["Analytical Thinking", "Problem Solving", "Attention to Detail"].includes(s))
    },
    {
      title: "Data Science Essentials",
      description: "Learn the fundamentals of data analysis, statistics, and machine learning applications.",
      relevance: "Medium",
      timeCommitment: "3-4 months part-time",
      transferableSkills: skills.filter(s => ["Mathematics", "Problem Solving", "Critical Thinking"].includes(s))
    }
  ];

  try {
    const prompt = `
    Based on the following current skills and desired role, provide 3-5 personalized reskilling recommendations.

    Current skills: ${skills.join(", ")}
    Desired role: ${desiredRole}

    For each reskilling recommendation, include:
    - A clear title
    - A brief description (1-2 sentences)
    - Relevance level (High/Medium/Low)
    - Time commitment estimate
    - Transferable skills from current skillset that would help

    Return the result as a valid JSON array of recommendation objects with this structure:
    [
      {
        "title": "string",
        "description": "string",
        "relevance": "High|Medium|Low",
        "timeCommitment": "string",
        "transferableSkills": ["skill1", "skill2"]
      }
    ]
    `;

    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: "You are an expert career coach and skills analyst." },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" }
    });

    const result = safeJsonParse<any>(response.choices[0].message.content, { suggestions: fallbackRecommendations });

    // The OpenAI response could be either an array directly or an object with a suggestions property
    // Handle both cases to ensure we always return an array
    if (Array.isArray(result)) {
      return result;
    } else if (result && Array.isArray(result.suggestions)) {
      return result.suggestions;
    } else {
      // Return fallback if format doesn't match expectations
      return fallbackRecommendations;
    }
  } catch (error) {
    console.error("OpenAI API error:", error);
    return fallbackRecommendations;
  }
}

/**
 * Interface for market analysis response
 */
interface MarketAnalysis {
  overview: string;
  trends: string[];
  skills: string[];
  outlook: string;
  salary: string;
  recommendations: string[];
}

/**
 * Analyzes the job market for a specific field using OpenAI
 * @param field - The field or job title to analyze
 */
export async function analyzeMarket(field: string): Promise<MarketAnalysis> {
  // Fallback response in case of API failure
  const fallbackResponse: MarketAnalysis = {
    overview: `The ${field} field is currently experiencing steady growth with increasing demand for specialized professionals.`,
    trends: [
      "Remote work opportunities are expanding in this sector",
      "Increasing integration with AI and automation technologies",
      "Growing emphasis on cross-functional collaboration"
    ],
    skills: [
      "Data analysis and interpretation",
      "Project management",
      "Communication and presentation skills",
      "Problem-solving and critical thinking"
    ],
    outlook: `The job outlook for ${field} remains positive with projected growth of 8-10% over the next five years.`,
    salary: `Entry-level positions in ${field} typically start at $60,000-$75,000, with senior roles commanding $100,000-$150,000 depending on location and experience.`,
    recommendations: [
      "Focus recruitment on candidates with both technical and soft skills",
      "Consider implementing training programs to develop specialized skills",
      "Explore flexible work arrangements to attract top talent"
    ]
  };

  try {
    // Define the prompt for GPT-4o
    const prompt = `
    Provide a comprehensive market analysis for the ${field} field or job role. Include the following sections:

    1. Market Overview: A brief overview of the current state of the ${field} market
    2. Key Trends: 3-5 important trends affecting this field
    3. In-Demand Skills: 4-6 most valuable skills for professionals in this field
    4. Hiring Outlook: Projections for job growth and demand
    5. Salary Insights: Typical salary ranges for different experience levels
    6. Recommendations: 3-4 actionable recommendations for recruiters in this field

    Return the analysis as a valid JSON object with the following structure:
    {
      "overview": "string",
      "trends": ["string", "string", ...],
      "skills": ["string", "string", ...],
      "outlook": "string",
      "salary": "string",
      "recommendations": ["string", "string", ...]
    }

    Make sure the analysis is data-driven, specific to the ${field} field, and provides valuable insights for recruiters.
    `;

    // Call OpenAI API with GPT-4o
    const response = await openai.chat.completions.create({
      model: "gpt-4o", // the newest OpenAI model is "gpt-4o" which was released May 13, 2024. do not change this unless explicitly requested by the user
      messages: [
        { role: "system", content: "You are an expert labor market analyst with deep knowledge of industry trends, skills demand, and hiring practices." },
        { role: "user", content: prompt }
      ],
      response_format: { type: "json_object" }
    });

    // Parse the response with fallback
    return safeJsonParse(response.choices[0].message.content, fallbackResponse);
  } catch (error) {
    console.error("OpenAI API error:", error);
    return fallbackResponse;
  }
}