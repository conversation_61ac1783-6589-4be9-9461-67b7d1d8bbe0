# 🚀 PathLink AWS Deployment Guide

## Quick Start (5 Minutes)

### Step 1: Install Prerequisites
```bash
# Install AWS CLI (if not already installed)
# Windows: Download from https://aws.amazon.com/cli/
# Mac: brew install awscli
# Linux: sudo apt install awscli

# Install Elastic Beanstalk CLI
pip install awsebcli

# Verify installations
aws --version
eb --version
```

### Step 2: Configure AWS Credentials
```bash
# Configure your AWS credentials
aws configure

# Enter your:
# - AWS Access Key ID
# - AWS Secret Access Key  
# - Default region: us-east-1
# - Default output format: json
```

### Step 3: Run Automated Deployment
```bash
# Run the automated deployment script
node deploy-to-aws.js
```

## Manual Deployment (If Automated Script Fails)

### Step 1: Build Application
```bash
npm run build:aws
```

### Step 2: Initialize Elastic Beanstalk
```bash
eb init pathlink --region us-east-1 --platform "Node.js 18"
```

### Step 3: Create Environment
```bash
eb create pathlink-production --instance-type t3.micro
```

### Step 4: Deploy
```bash
eb deploy
```

### Step 5: Get Application URL
```bash
eb status
```

## Architecture Overview

```
User Request → Netlify (Frontend) → AWS Elastic Beanstalk (Backend API) → Neon Database
```

### What Gets Deployed Where:

**Netlify (Frontend):**
- React application (static files)
- Routing and UI
- Redirects API calls to AWS

**AWS Elastic Beanstalk (Backend):**
- Node.js/Express server
- API endpoints
- Authentication
- Database connections
- File uploads

**Neon Database:**
- PostgreSQL database
- User data, jobs, resumes
- Persistent storage

## Environment Variables

The following environment variables are automatically configured in AWS:

```
NODE_ENV=production
PORT=8080
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
SESSION_SECRET=pathlink-production-secret-2024
```

**Important:** Update `OPENAI_API_KEY` and `SENDGRID_API_KEY` in AWS console:
1. Go to AWS Elastic Beanstalk console
2. Select your application
3. Go to Configuration → Software
4. Add environment variables

## Useful Commands

```bash
# Check application status
eb status

# View logs
eb logs

# Deploy updates
eb deploy

# Open application in browser
eb open

# SSH into instance (for debugging)
eb ssh

# Terminate environment (careful!)
eb terminate
```

## Troubleshooting

### Common Issues:

1. **Build Fails:**
   ```bash
   # Check Node.js version
   node --version
   
   # Rebuild dependencies
   rm -rf node_modules package-lock.json
   npm install
   npm run build:aws
   ```

2. **Deployment Fails:**
   ```bash
   # Check EB logs
   eb logs
   
   # Check application health
   eb health
   ```

3. **Database Connection Issues:**
   - Verify DATABASE_URL in AWS environment variables
   - Check Neon database status
   - Ensure SSL is enabled

4. **API Calls Fail:**
   - Check netlify.toml redirects
   - Verify AWS application URL
   - Check CORS settings

## Monitoring

### AWS CloudWatch:
- Application logs
- Performance metrics
- Error tracking

### Health Checks:
- Endpoint: `/health`
- Expected response: `{"status": "healthy"}`

## Scaling

### Auto Scaling (Configured):
- Min instances: 1
- Max instances: 4
- Instance type: t3.micro

### Manual Scaling:
```bash
# Scale up
eb scale 2

# Scale down  
eb scale 1
```

## Cost Estimation

**AWS Elastic Beanstalk (t3.micro):**
- ~$8-15/month for low traffic
- Includes load balancer, auto-scaling
- Additional costs for high traffic

**Netlify:**
- Free tier: 100GB bandwidth
- Pro: $19/month for more features

**Total Estimated Cost:** $8-35/month

## Security

### Configured Security Features:
- HTTPS/SSL certificates
- Security headers
- Environment variable encryption
- VPC isolation
- IAM roles and policies

## Backup Strategy

### Database:
- Neon handles automatic backups
- Point-in-time recovery available

### Application:
- Code stored in Git repository
- Easy redeployment from source

## Next Steps After Deployment

1. **Test Application:**
   - Visit AWS URL
   - Test API endpoints
   - Verify database connections

2. **Deploy Frontend to Netlify:**
   - Push code to Git
   - Netlify auto-deploys
   - API calls redirect to AWS

3. **Configure Domain (Optional):**
   - Purchase domain
   - Configure DNS
   - Set up SSL certificates

4. **Set up Monitoring:**
   - Configure CloudWatch alerts
   - Set up error notifications
   - Monitor performance metrics

## Support

If you encounter issues:
1. Check the logs: `eb logs`
2. Review AWS console
3. Verify environment variables
4. Test database connectivity
5. Check Netlify redirects

Your PathLink application will be running on enterprise-grade AWS infrastructure with automatic scaling, monitoring, and high availability! 🚀
