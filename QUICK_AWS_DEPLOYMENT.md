# 🚀 Quick AWS Deployment Guide for PathLink

## Step 1: Install AWS CLI (Manual)

### Option A: Using winget (Recommended)
```powershell
winget install Amazon.AWSCLI --accept-package-agreements --accept-source-agreements
```

### Option B: Download and Install
1. Download AWS CLI v2: https://awscli.amazonaws.com/AWSCLIV2.msi
2. Run the installer
3. Restart your terminal

## Step 2: Install EB CLI

```powershell
pip install awsebcli --upgrade --user
```

If pip is not available, install Python first from: https://www.python.org/downloads/

## Step 3: Configure AWS Credentials

Run the setup script:
```powershell
node setup-aws-credentials.cjs
```

Or manually configure:
```powershell
aws configure set aws_access_key_id ********************
aws configure set aws_secret_access_key K+C5zLa71kb0I5sRldB/ZMUnPoWSh/eLr1UgMdI2
aws configure set default.region us-east-1
aws configure set default.output json
```

## Step 4: Test AWS Connection

```powershell
aws sts get-caller-identity
```

You should see your account information.

## Step 5: Deploy to AWS

```powershell
node deploy-to-aws.cjs
```

## Step 6: Verify Deployment

After deployment, you'll get an AWS URL like:
`https://pathlink-production-env.us-east-1.elasticbeanstalk.com`

Test the endpoints:
- Health check: `https://your-aws-url/health`
- API test: `https://your-aws-url/api/health`

## Step 7: Update Netlify Configuration

The deployment script should automatically update `netlify.toml`, but verify these redirects:

```toml
[[redirects]]
  from = "/api/*"
  to = "https://your-aws-url/api/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/upload/*"
  to = "https://your-aws-url/upload/:splat"
  status = 200
  force = true

[[redirects]]
  from = "/health"
  to = "https://your-aws-url/health"
  status = 200
  force = true
```

## Step 8: Deploy Frontend to Netlify

1. Commit your changes:
```bash
git add .
git commit -m "Update AWS deployment configuration"
git push
```

2. Netlify will automatically deploy your frontend
3. Your frontend will be served by Netlify
4. API calls will be redirected to AWS

## Architecture Overview

```
User → Netlify (Frontend) → AWS Elastic Beanstalk (Backend API) → Neon Database
```

## Troubleshooting

### AWS CLI Issues
- Restart terminal after installation
- Check PATH environment variable
- Try `aws --version` to verify installation

### EB CLI Issues
- Ensure Python is installed
- Try `pip3` instead of `pip`
- Add Python Scripts to PATH

### Deployment Issues
- Check `eb logs` for error details
- Verify environment variables in AWS console
- Ensure database connection string is correct

### API Connection Issues
- Verify netlify.toml redirects
- Check CORS settings
- Test AWS endpoints directly

## Useful Commands

```powershell
# Check deployment status
eb status

# View application logs
eb logs

# Deploy updates
eb deploy

# Open application in browser
eb open

# SSH into instance (for debugging)
eb ssh

# Terminate environment (careful!)
eb terminate
```

## Environment Variables in AWS

Make sure these are set in AWS Elastic Beanstalk:

- `NODE_ENV=production`
- `PORT=8080`
- `DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require`
- `SESSION_SECRET=pathlink-production-secret-2024`
- `JWT_SECRET=pathlink-jwt-secret-2024`
- `OPENAI_API_KEY=********************************************************************************************************************************************************************`
- `SENDGRID_API_KEY=SG.1234567890abcdefghijklmnopqrstuvwxyz`

## Success Indicators

✅ AWS CLI installed and configured
✅ EB CLI installed
✅ AWS credentials working
✅ Application built successfully
✅ Deployed to Elastic Beanstalk
✅ Health check responds
✅ Netlify redirects configured
✅ Frontend deployed to Netlify
✅ Full application working

Your PathLink application will be running with:
- Frontend: Netlify (fast CDN delivery)
- Backend: AWS Elastic Beanstalk (scalable, managed)
- Database: Neon PostgreSQL (serverless)
- Monitoring: AWS CloudWatch
- SSL: Automatic via AWS/Netlify
