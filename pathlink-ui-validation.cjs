const { chromium } = require('playwright');
const fs = require('fs');

class PathLinkUIValidator {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5000';
  }

  async log(test, status, details = '', screenshot = null) {
    const result = {
      test,
      status,
      details,
      screenshot,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [UI] ${test}: ${status} ${details}`);
  }

  async setup() {
    console.log('🚀 Setting up PathLink UI validation...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 1000
    });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1280, height: 720 });
    console.log('✅ Browser ready for PathLink testing');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 PathLink UI validation complete');
    }
  }

  async takeScreenshot(name) {
    const filename = `screenshots/pathlink-${name}-${Date.now()}.png`;
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    await this.page.screenshot({ path: filename, fullPage: true });
    return filename;
  }

  async testLandingPage() {
    console.log('\n🏠 TESTING PATHLINK LANDING PAGE...');

    try {
      await this.page.goto(this.baseUrl);
      await this.page.waitForTimeout(3000);

      // Check for PathLink branding
      const pageContent = await this.page.textContent('body');
      
      if (pageContent.includes('PathLink')) {
        await this.log('PathLink Branding', 'PASS', 'PathLink branding found');
      } else {
        await this.log('PathLink Branding', 'FAIL', 'PathLink branding not found');
      }

      // Check for main navigation or buttons
      const buttons = await this.page.locator('button').count();
      const links = await this.page.locator('a').count();
      
      if (buttons > 0 || links > 0) {
        await this.log('Interactive Elements', 'PASS', `Found ${buttons} buttons and ${links} links`);
      } else {
        await this.log('Interactive Elements', 'FAIL', 'No interactive elements found');
      }

      // Look for "Sign In" or "Get Started" buttons
      const signInButton = this.page.locator('text=Sign In');
      const getStartedButton = this.page.locator('text=Get Started');
      
      if (await signInButton.isVisible() || await getStartedButton.isVisible()) {
        await this.log('Call-to-Action Buttons', 'PASS', 'Sign In or Get Started button found');
      } else {
        await this.log('Call-to-Action Buttons', 'WARN', 'No clear CTA buttons found');
      }

      const screenshot = await this.takeScreenshot('landing-page');
      await this.log('Landing Page Screenshot', 'PASS', 'Screenshot captured', screenshot);

    } catch (error) {
      await this.log('Landing Page Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testAuthenticationFlow() {
    console.log('\n🔐 TESTING AUTHENTICATION FLOW...');

    try {
      // Try to navigate to auth page
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(3000);

      const screenshot = await this.takeScreenshot('auth-page');
      
      // Look for any form inputs
      const emailInputs = await this.page.locator('input[type="email"]').count();
      const passwordInputs = await this.page.locator('input[type="password"]').count();
      const textInputs = await this.page.locator('input[type="text"]').count();
      const allInputs = await this.page.locator('input').count();

      await this.log('Form Inputs Found', 'PASS', 
        `Email: ${emailInputs}, Password: ${passwordInputs}, Text: ${textInputs}, Total: ${allInputs}`, screenshot);

      // Look for any buttons
      const buttons = await this.page.locator('button').count();
      const submitButtons = await this.page.locator('button[type="submit"]').count();

      if (buttons > 0) {
        await this.log('Form Buttons', 'PASS', `Found ${buttons} buttons (${submitButtons} submit)`);
      } else {
        await this.log('Form Buttons', 'FAIL', 'No form buttons found');
      }

      // Try to find login form by looking for common patterns
      const loginText = await this.page.textContent('body');
      if (loginText.includes('email') || loginText.includes('password') || loginText.includes('login') || loginText.includes('sign in')) {
        await this.log('Login Form Content', 'PASS', 'Login-related content found');
      } else {
        await this.log('Login Form Content', 'FAIL', 'No login-related content found');
      }

    } catch (error) {
      await this.log('Authentication Flow Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testNavigationAndRouting() {
    console.log('\n🧭 TESTING NAVIGATION AND ROUTING...');

    const routes = [
      { name: 'Dashboard', path: '/dashboard' },
      { name: 'Browse Jobs', path: '/browse-jobs' },
      { name: 'Path Coach', path: '/path-coach' },
      { name: 'Profile', path: '/profile' },
      { name: 'Contact', path: '/contact' }
    ];

    for (const route of routes) {
      try {
        await this.page.goto(`${this.baseUrl}${route.path}`);
        await this.page.waitForTimeout(2000);

        const title = await this.page.title();
        const url = this.page.url();

        if (url.includes(route.path)) {
          await this.log(`${route.name} Route`, 'PASS', `Successfully navigated to ${route.path}`);
        } else {
          await this.log(`${route.name} Route`, 'WARN', `Redirected to ${url}`);
        }

        // Check if page has content
        const bodyText = await this.page.textContent('body');
        if (bodyText && bodyText.length > 100) {
          await this.log(`${route.name} Content`, 'PASS', `Page has content (${bodyText.length} chars)`);
        } else {
          await this.log(`${route.name} Content`, 'WARN', 'Page has minimal content');
        }

      } catch (error) {
        await this.log(`${route.name} Route Test`, 'FAIL', `Error: ${error.message}`);
      }
    }
  }

  async testUIComponents() {
    console.log('\n🎨 TESTING UI COMPONENTS...');

    try {
      await this.page.goto(this.baseUrl);
      await this.page.waitForTimeout(3000);

      // Test for common UI components
      const components = [
        { name: 'Headers', selector: 'h1, h2, h3, h4, h5, h6' },
        { name: 'Buttons', selector: 'button' },
        { name: 'Links', selector: 'a' },
        { name: 'Images', selector: 'img' },
        { name: 'Forms', selector: 'form' },
        { name: 'Inputs', selector: 'input' },
        { name: 'Navigation', selector: 'nav' }
      ];

      for (const component of components) {
        const count = await this.page.locator(component.selector).count();
        
        if (count > 0) {
          await this.log(`${component.name} Component`, 'PASS', `Found ${count} ${component.name.toLowerCase()}`);
        } else {
          await this.log(`${component.name} Component`, 'WARN', `No ${component.name.toLowerCase()} found`);
        }
      }

      // Test for responsive design indicators
      const hasResponsiveClasses = await this.page.evaluate(() => {
        const body = document.body;
        const html = body.innerHTML;
        return html.includes('responsive') || html.includes('mobile') || html.includes('tablet') || 
               html.includes('sm:') || html.includes('md:') || html.includes('lg:') || html.includes('xl:');
      });

      if (hasResponsiveClasses) {
        await this.log('Responsive Design', 'PASS', 'Responsive design classes detected');
      } else {
        await this.log('Responsive Design', 'WARN', 'No responsive design indicators found');
      }

    } catch (error) {
      await this.log('UI Components Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testUserInteractions() {
    console.log('\n👆 TESTING USER INTERACTIONS...');

    try {
      await this.page.goto(this.baseUrl);
      await this.page.waitForTimeout(3000);

      // Test clicking on various elements
      const clickableElements = await this.page.locator('button, a, [role="button"]').count();
      
      if (clickableElements > 0) {
        await this.log('Clickable Elements', 'PASS', `Found ${clickableElements} clickable elements`);
        
        // Try clicking the first button or link
        try {
          const firstClickable = this.page.locator('button, a').first();
          if (await firstClickable.isVisible()) {
            await firstClickable.click();
            await this.page.waitForTimeout(1000);
            await this.log('Element Click Test', 'PASS', 'Successfully clicked an element');
          }
        } catch (clickError) {
          await this.log('Element Click Test', 'WARN', 'Click test failed but elements exist');
        }
      } else {
        await this.log('Clickable Elements', 'FAIL', 'No clickable elements found');
      }

      // Test form interactions if any forms exist
      const forms = await this.page.locator('form').count();
      const inputs = await this.page.locator('input').count();

      if (forms > 0 || inputs > 0) {
        await this.log('Form Interactions', 'PASS', `Found ${forms} forms and ${inputs} inputs`);
      } else {
        await this.log('Form Interactions', 'WARN', 'No forms or inputs found for interaction testing');
      }

    } catch (error) {
      await this.log('User Interactions Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async runPathLinkValidation() {
    console.log('🚀 STARTING PATHLINK UI VALIDATION...\n');
    console.log('This will comprehensively test your PathLink frontend UI.\n');

    try {
      await this.setup();

      await this.testLandingPage();
      await this.testAuthenticationFlow();
      await this.testNavigationAndRouting();
      await this.testUIComponents();
      await this.testUserInteractions();

    } catch (error) {
      console.error('PathLink UI validation failed:', error);
    } finally {
      await this.cleanup();
    }

    // Generate comprehensive report
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const warningTests = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 PATHLINK UI VALIDATION SUMMARY:');
    console.log('='.repeat(60));
    console.log(`✅ PASSED: ${passedTests}`);
    console.log(`❌ FAILED: ${failedTests}`);
    console.log(`⚠️  WARNINGS: ${warningTests}`);
    console.log(`📈 UI SUCCESS RATE: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));

    // Categorize results by test type
    const categories = {};
    this.results.forEach(result => {
      const category = result.test.split(' ')[0];
      if (!categories[category]) {
        categories[category] = { pass: 0, fail: 0, warn: 0 };
      }
      categories[category][result.status.toLowerCase()]++;
    });

    console.log('\n📋 DETAILED BREAKDOWN BY CATEGORY:');
    Object.entries(categories).forEach(([category, stats]) => {
      const total = stats.pass + stats.fail + stats.warn;
      const successRate = ((stats.pass / total) * 100).toFixed(1);
      console.log(`${category}: ${stats.pass}/${total} (${successRate}%)`);
    });

    // Save detailed report
    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        warningTests,
        successRate: (passedTests / totalTests) * 100
      },
      categories,
      results: this.results,
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('pathlink-ui-validation-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed PathLink UI validation report saved to pathlink-ui-validation-report.json');
    console.log('📸 Screenshots saved in the screenshots/ directory');

    // Final recommendations
    console.log('\n🎯 PATHLINK UI RECOMMENDATIONS:');
    if (passedTests / totalTests >= 0.8) {
      console.log('   ✅ PathLink UI is working excellently!');
      console.log('   ✅ Frontend is ready for production use');
      console.log('   ✅ User experience is well-implemented');
    } else if (passedTests / totalTests >= 0.6) {
      console.log('   ⚠️  PathLink UI is mostly functional');
      console.log('   🔧 Some areas need minor improvements');
      console.log('   📈 Consider addressing warnings for better UX');
    } else {
      console.log('   ❌ PathLink UI needs attention');
      console.log('   🔧 Several critical issues to address');
      console.log('   📋 Review failed tests for improvement areas');
    }

    return report;
  }
}

// Run PathLink UI validation
const validator = new PathLinkUIValidator();
validator.runPathLinkValidation().catch(console.error);
