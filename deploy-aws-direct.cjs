#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 PathLink AWS Direct Deployment');
console.log('=================================\n');

// AWS CLI path
const AWS_CLI = '"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe"';

function createDeploymentPackage() {
  console.log('📦 Creating deployment package...');

  // Create a deployment package
  const packageName = `pathlink-${Date.now()}.zip`;

  try {
    // Create zip file with application
    execSync(`powershell Compress-Archive -Path "dist/*", "package.json", "Procfile", ".ebextensions" -DestinationPath "${packageName}" -Force`, { stdio: 'inherit' });
    console.log(`✅ Created deployment package: ${packageName}`);
    return packageName;
  } catch (error) {
    console.log('❌ Failed to create deployment package:', error.message);
    return null;
  }
}

function createS3Bucket() {
  console.log('🪣 Creating S3 bucket for deployment...');
  
  const bucketName = `pathlink-deployment-${Date.now()}`;
  
  try {
    // Create S3 bucket
    execSync(`${AWS_CLI} s3 mb s3://${bucketName} --region us-east-1`, { stdio: 'inherit' });
    console.log(`✅ Created S3 bucket: ${bucketName}`);
    return bucketName;
  } catch (error) {
    console.log('❌ Failed to create S3 bucket:', error.message);
    return null;
  }
}

function uploadToS3(packageName, bucketName) {
  console.log('⬆️ Uploading package to S3...');
  
  try {
    execSync(`${AWS_CLI} s3 cp ${packageName} s3://${bucketName}/${packageName}`, { stdio: 'inherit' });
    console.log('✅ Package uploaded to S3');
    return true;
  } catch (error) {
    console.log('❌ Failed to upload to S3:', error.message);
    return false;
  }
}

function createElasticBeanstalkApplication() {
  console.log('🌱 Creating Elastic Beanstalk application...');
  
  try {
    // Check if application already exists
    try {
      const result = execSync(`${AWS_CLI} elasticbeanstalk describe-applications --application-names pathlink`, { stdio: 'pipe' });
      const apps = JSON.parse(result.toString());
      if (apps.Applications && apps.Applications.length > 0) {
        console.log('✅ Application already exists');
        return true;
      }
    } catch (e) {
      // Application doesn't exist, create it
    }
    
    execSync(`${AWS_CLI} elasticbeanstalk create-application --application-name pathlink --description "PathLink Job Matching Platform"`, { stdio: 'inherit' });
    console.log('✅ Created Elastic Beanstalk application');
    return true;
  } catch (error) {
    console.log('❌ Failed to create application:', error.message);
    return false;
  }
}

function createApplicationVersion(packageName, bucketName) {
  console.log('📋 Creating application version...');
  
  const versionLabel = `v${Date.now()}`;
  
  try {
    execSync(`${AWS_CLI} elasticbeanstalk create-application-version --application-name pathlink --version-label ${versionLabel} --source-bundle S3Bucket=${bucketName},S3Key=${packageName}`, { stdio: 'inherit' });
    console.log(`✅ Created application version: ${versionLabel}`);
    return versionLabel;
  } catch (error) {
    console.log('❌ Failed to create application version:', error.message);
    return null;
  }
}

function createEnvironment(versionLabel) {
  console.log('🌍 Creating environment...');
  
  const environmentName = 'pathlink-production';
  
  try {
    // Check if environment already exists
    try {
      const result = execSync(`${AWS_CLI} elasticbeanstalk describe-environments --environment-names ${environmentName}`, { stdio: 'pipe' });
      const envs = JSON.parse(result.toString());
      if (envs.Environments && envs.Environments.length > 0) {
        console.log('✅ Environment already exists');
        return environmentName;
      }
    } catch (e) {
      // Environment doesn't exist, create it
    }
    
    // Create environment configuration
    const optionSettings = [
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=NODE_ENV,Value=production',
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=PORT,Value=8080',
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=DATABASE_URL,Value=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=SESSION_SECRET,Value=pathlink-production-secret-2024',
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=JWT_SECRET,Value=pathlink-jwt-secret-2024',
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=OPENAI_API_KEY,Value=********************************************************************************************************************************************************************',
      'Namespace=aws:elasticbeanstalk:application:environment,OptionName=SENDGRID_API_KEY,Value=SG.1234567890abcdefghijklmnopqrstuvwxyz',
      'Namespace=aws:autoscaling:launchconfiguration,OptionName=InstanceType,Value=t3.micro',
      'Namespace=aws:elasticbeanstalk:environment,OptionName=LoadBalancerType,Value=application'
    ];
    
    const optionSettingsStr = optionSettings.map(setting => `--option-settings ${setting}`).join(' ');
    
    execSync(`${AWS_CLI} elasticbeanstalk create-environment --application-name pathlink --environment-name ${environmentName} --solution-stack-name "64bit Amazon Linux 2023 v6.6.0 running Node.js 18" --version-label ${versionLabel} ${optionSettingsStr}`, { stdio: 'inherit' });
    
    console.log('✅ Environment creation initiated');
    console.log('⏳ Waiting for environment to be ready...');
    
    // Wait for environment to be ready
    let attempts = 0;
    const maxAttempts = 30; // 15 minutes max
    
    while (attempts < maxAttempts) {
      try {
        const result = execSync(`${AWS_CLI} elasticbeanstalk describe-environments --environment-names ${environmentName}`, { stdio: 'pipe' });
        const envs = JSON.parse(result.toString());
        
        if (envs.Environments && envs.Environments.length > 0) {
          const env = envs.Environments[0];
          console.log(`Status: ${env.Status}, Health: ${env.Health || 'Unknown'}`);
          
          if (env.Status === 'Ready') {
            console.log('✅ Environment is ready!');
            return environmentName;
          }
        }
      } catch (e) {
        console.log('Checking environment status...');
      }
      
      attempts++;
      // Wait 30 seconds before next check
      execSync('timeout /t 30 /nobreak', { stdio: 'pipe' });
    }
    
    console.log('⚠️ Environment creation is taking longer than expected');
    return environmentName;
    
  } catch (error) {
    console.log('❌ Failed to create environment:', error.message);
    return null;
  }
}

function getEnvironmentURL(environmentName) {
  console.log('🔍 Getting environment URL...');
  
  try {
    const result = execSync(`${AWS_CLI} elasticbeanstalk describe-environments --environment-names ${environmentName}`, { stdio: 'pipe' });
    const envs = JSON.parse(result.toString());
    
    if (envs.Environments && envs.Environments.length > 0) {
      const env = envs.Environments[0];
      if (env.CNAME) {
        console.log(`✅ Environment URL: https://${env.CNAME}`);
        return env.CNAME;
      }
    }
    
    console.log('⚠️ Could not get environment URL');
    return null;
  } catch (error) {
    console.log('❌ Failed to get environment URL:', error.message);
    return null;
  }
}

function updateNetlifyConfig(awsUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace any existing AWS URLs
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/[a-zA-Z0-9-]+\.us-east-1\.elasticbeanstalk\.com/g,
      `https://${awsUrl}`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated');
    console.log(`🔗 API calls will redirect to: https://${awsUrl}`);
    
    // Show updated redirects
    console.log('\n📋 Updated redirects:');
    console.log(`   /api/* → https://${awsUrl}/api/*`);
    console.log(`   /upload/* → https://${awsUrl}/upload/*`);
    console.log(`   /health → https://${awsUrl}/health`);
    
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log(`Please manually update netlify.toml with: https://${awsUrl}`);
  }
}

function setupDeploymentFiles() {
  console.log('📁 Setting up deployment files...');
  
  // Create .ebextensions directory
  if (!fs.existsSync('.ebextensions')) {
    fs.mkdirSync('.ebextensions');
  }

  // Environment configuration
  const envConfig = `option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: pathlink-production-secret-2024
    JWT_SECRET: pathlink-jwt-secret-2024
    OPENAI_API_KEY: ********************************************************************************************************************************************************************
    SENDGRID_API_KEY: SG.1234567890abcdefghijklmnopqrstuvwxyz
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.19.0
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.micro
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
`;

  fs.writeFileSync('.ebextensions/environment.config', envConfig);
  
  // Procfile
  fs.writeFileSync('Procfile', 'web: npm start');
  
  console.log('✅ Deployment files configured');
}

function buildApplication() {
  console.log('\n🔨 Building application...');
  try {
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

async function main() {
  console.log('Starting PathLink AWS direct deployment...\n');
  
  // Step 1: Setup deployment files
  setupDeploymentFiles();
  
  // Step 2: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed.');
    process.exit(1);
  }
  
  // Step 3: Create deployment package
  const packageName = createDeploymentPackage();
  if (!packageName) {
    console.log('\n❌ Failed to create deployment package.');
    process.exit(1);
  }
  
  // Step 4: Create S3 bucket
  const bucketName = createS3Bucket();
  if (!bucketName) {
    console.log('\n❌ Failed to create S3 bucket.');
    process.exit(1);
  }
  
  // Step 5: Upload to S3
  if (!uploadToS3(packageName, bucketName)) {
    console.log('\n❌ Failed to upload package.');
    process.exit(1);
  }
  
  // Step 6: Create Elastic Beanstalk application
  if (!createElasticBeanstalkApplication()) {
    console.log('\n❌ Failed to create application.');
    process.exit(1);
  }
  
  // Step 7: Create application version
  const versionLabel = createApplicationVersion(packageName, bucketName);
  if (!versionLabel) {
    console.log('\n❌ Failed to create application version.');
    process.exit(1);
  }
  
  // Step 8: Create environment
  const environmentName = createEnvironment(versionLabel);
  if (!environmentName) {
    console.log('\n❌ Failed to create environment.');
    process.exit(1);
  }
  
  // Step 9: Get environment URL
  const appUrl = getEnvironmentURL(environmentName);
  if (appUrl) {
    updateNetlifyConfig(appUrl);
    
    console.log('\n🎉 DEPLOYMENT COMPLETE!');
    console.log('======================');
    console.log('✅ Backend deployed to AWS Elastic Beanstalk');
    console.log('✅ Netlify configuration updated');
    console.log('✅ API redirects configured');
    console.log(`\n🌍 Your application: https://${appUrl}`);
    console.log(`🔍 Health check: https://${appUrl}/health`);
    console.log(`🔌 API test: https://${appUrl}/api/health`);
  } else {
    console.log('\n⚠️ Deployment completed but could not get URL');
    console.log('Check AWS console for environment status');
  }
  
  // Cleanup
  try {
    fs.unlinkSync(packageName);
    console.log('✅ Cleaned up deployment package');
  } catch (e) {
    // Ignore cleanup errors
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
