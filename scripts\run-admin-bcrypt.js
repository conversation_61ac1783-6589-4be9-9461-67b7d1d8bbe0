import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createAdmin() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'create-admin-bcrypt.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Creating admin user with bcrypt hash...');
    await client.query(sql);
    
    // Verify the admin user was created
    const result = await client.query('SELECT * FROM users WHERE email = $1', ['<EMAIL>']);
    if (result.rows.length > 0) {
      console.log('Admin user created/updated successfully:', {
        id: result.rows[0].id,
        email: result.rows[0].email,
        name: result.rows[0].name,
        role: result.rows[0].role,
        password_hash: result.rows[0].password
      });
    } else {
      console.error('Failed to create admin user');
    }

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

createAdmin();
