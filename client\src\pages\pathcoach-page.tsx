import Layout from "@/components/layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Lightbulb, RefreshCw, ChevronRight, Bell, LineChart, Target, BookOpenText, BrainCircuit, Heart, Users } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent } from "@/components/ui/card";
import { ReskillingSuggestions } from "@/components/reskilling-suggestions";
import { SkillContextAnalyzer } from "@/components/pathcoach/worker-profile";
import { CareerQuantumLeapGenerator } from "@/components/pathcoach/career-transition";
import { GPTReflectionJournal } from "@/components/pathcoach/reflection-journal";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Sample skill suggestions for workers
const workerSkillSuggestions = [
  ["Intro to ReactJS", "Project Management Certificate", "Business Analytics Bootcamp"],
  ["Python for Data Science", "Leadership Development Course", "Digital Marketing Fundamentals"],
  ["Cloud Computing Certification", "UX/UI Design Basics", "Public Speaking Workshop"]
];

// Sample insights for employers
const employerInsights = [
  ["2 surplus employees match logistics roles", "Data analytics skills are missing in your Sales team", "Consider cross-training HR staff for digital compliance"],
  ["3 employees have transferable skills for IT support", "Your engineering team needs cloud architecture training", "Consider upskilling marketing team in data visualization"],
  ["Remote work productivity increased by 12%", "Consider implementing mentorship program for new hires", "Technical writing skills gap identified in product team"]
];

export default function PathCoachPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [suggestionSet, setSuggestionSet] = useState(0);

  const handleNotifyMe = () => {
    toast({
      title: "Thank You!",
      description: "We'll notify you when PathCoach AI is ready.",
    });
  };

  const refreshSuggestions = () => {
    // Cycle through suggestion sets
    setSuggestionSet((prev) => (prev + 1) % (user?.role === "employer" ? employerInsights.length : workerSkillSuggestions.length));
    
    toast({
      title: "Refreshed",
      description: user?.role === "employer" 
        ? "New workforce insights generated" 
        : "New skill suggestions generated",
      duration: 2000,
    });
  };

  // Determine which suggestions to show based on user role and current set
  const currentSuggestions = user?.role === "employer" 
    ? employerInsights[suggestionSet] 
    : workerSkillSuggestions[suggestionSet];

  return (
    <Layout>
      <div className="container mx-auto px-4 py-6">
        {user?.role === "employer" ? (
          // Employer View
          <div className="max-w-3xl mx-auto text-center">
            <div className="bg-blue-100 p-4 inline-flex rounded-full mb-8">
              <Lightbulb className="h-8 w-8 text-blue-600" />
            </div>
            
            <h1 className="text-4xl font-bold text-[#1C2A42] dark:text-white mb-6">
              Welcome to PathCoach Pro
            </h1>
            
            <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
              Your AI workforce strategist is here to optimize your hiring and redeployment decisions.
            </p>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-semibold text-[#1C2A42] dark:text-white">
                  Workforce Insights
                </h2>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={refreshSuggestions}
                  className="flex items-center gap-2 transition-all hover:bg-[#1C2A42]/10"
                >
                  <RefreshCw className="h-4 w-4" />
                  Refresh
                </Button>
              </div>
              
              <div className="space-y-4 mb-8">
                {currentSuggestions.map((insight, index) => (
                  <Card key={index} className="overflow-hidden border-l-4 border-l-blue-500">
                    <CardContent className="p-4 flex items-start gap-3">
                      <div className="bg-blue-100 p-2 rounded-full h-min">
                        <LineChart className="h-5 w-5 text-blue-600" />
                      </div>
                      <p className="text-left text-gray-700 dark:text-gray-300 font-medium">
                        {insight}
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
              
              <Button 
                onClick={handleNotifyMe}
                className="bg-[#1C2A42] hover:bg-opacity-90 text-white px-6 py-2 rounded-md font-medium transition-all"
              >
                Join Early Access List
              </Button>
            </div>
          </div>
        ) : (
          // Worker View - Enhanced with PathCoach features
          <div>
            <div className="max-w-4xl mx-auto mb-10">
              <h1 className="text-4xl font-bold text-[#1C2A42] dark:text-white mb-6 text-center">
                PathCoach - Your AI Career Companion
              </h1>
              
              <p className="text-xl text-gray-600 dark:text-gray-300 mb-8 text-center">
                Unlock your potential with our comprehensive career development suite
              </p>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 my-10">
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6 px-5 text-center">
                    <BrainCircuit className="h-12 w-12 mx-auto mb-4 text-primary" />
                    <h3 className="font-semibold text-lg mb-2">Skill Context Analyzer</h3>
                    <p className="text-sm text-muted-foreground">
                      Define your skills context and professional profile
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6 px-5 text-center">
                    <Target className="h-12 w-12 mx-auto mb-4 text-primary" />
                    <h3 className="font-semibold text-lg mb-2">Career Quantum Leap</h3>
                    <p className="text-sm text-muted-foreground">
                      Map your journey from your current role to dream career
                    </p>
                  </CardContent>
                </Card>
                
                <Card className="bg-primary/5 border-primary/20">
                  <CardContent className="pt-6 px-5 text-center">
                    <BookOpenText className="h-12 w-12 mx-auto mb-4 text-primary" />
                    <h3 className="font-semibold text-lg mb-2">GPT Reflection Journal</h3>
                    <p className="text-sm text-muted-foreground">
                      Document your career journey with AI-powered insights
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            <Tabs defaultValue="skills" className="max-w-4xl mx-auto">
              <TabsList className="grid grid-cols-3 mb-8">
                <TabsTrigger value="skills" className="flex items-center gap-2">
                  <BrainCircuit className="h-4 w-4" />
                  <span className="hidden sm:inline">Skills Profile</span>
                  <span className="sm:hidden">Skills</span>
                </TabsTrigger>
                <TabsTrigger value="career" className="flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  <span className="hidden sm:inline">Career Transition</span>
                  <span className="sm:hidden">Career</span>
                </TabsTrigger>
                <TabsTrigger value="journal" className="flex items-center gap-2">
                  <BookOpenText className="h-4 w-4" />
                  <span className="hidden sm:inline">Reflection Journal</span>
                  <span className="sm:hidden">Journal</span>
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="skills" className="mt-0">
                <SkillContextAnalyzer />
              </TabsContent>
              
              <TabsContent value="career" className="mt-0">
                <CareerQuantumLeapGenerator />
              </TabsContent>
              
              <TabsContent value="journal" className="mt-0">
                <GPTReflectionJournal />
              </TabsContent>
            </Tabs>
            
            {/* Legacy features - we keep this for backward compatibility */}
            <div className="max-w-4xl mx-auto mt-12">
              <div className="mb-8">
                <ReskillingSuggestions />
              </div>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 mb-8">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-semibold text-[#1C2A42] dark:text-white">
                    Legacy Recommendations
                  </h2>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={refreshSuggestions}
                    className="flex items-center gap-2 transition-all hover:bg-[#1C2A42]/10"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Reskill Me Again
                  </Button>
                </div>
                
                <div className="space-y-4 mb-8">
                  {currentSuggestions.map((skill, index) => (
                    <Card key={index} className="overflow-hidden border-l-4 border-l-green-500">
                      <CardContent className="p-4 flex items-start gap-3">
                        <div className="bg-green-100 p-2 rounded-full h-min">
                          <ChevronRight className="h-5 w-5 text-green-600" />
                        </div>
                        <p className="text-left text-gray-700 dark:text-gray-300 font-medium">
                          {skill}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
          
        <p className="text-gray-500 dark:text-gray-400 italic text-center max-w-3xl mx-auto mt-10">
          PathCoach is powered by AI trained on industry data to provide accurate and helpful career guidance. Our system continually improves to deliver personalized career support.
        </p>
      </div>
    </Layout>
  );
}