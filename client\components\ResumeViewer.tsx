import React, { useState, useEffect } from 'react';
import { Box, Button, Modal, Typography, Paper, CircularProgress, Alert } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import DownloadIcon from '@mui/icons-material/Download';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { Resume } from '@shared/schema';

const ViewerContainer = styled(Paper)(({ theme }) => ({
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: '80%',
  maxWidth: 900,
  maxHeight: '90vh',
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
  boxShadow: theme.shadows[5],
}));

const ViewerHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
  paddingBottom: theme.spacing(1),
}));

const ViewerContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  overflow: 'auto',
  padding: theme.spacing(2),
  backgroundColor: theme.palette.grey[50],
  borderRadius: theme.shape.borderRadius,
}));

const PdfFrame = styled('iframe')({
  width: '100%',
  height: '75vh',
  border: 'none',
});

const TextContent = styled(Typography)({
  whiteSpace: 'pre-wrap',
  fontFamily: 'monospace',
  fontSize: '0.9rem',
  lineHeight: 1.5,
});

interface ResumeViewerProps {
  resumeId: number | null;
  onClose: () => void;
}

const ResumeViewer: React.FC<ResumeViewerProps> = ({ resumeId, onClose }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [resumeData, setResumeData] = useState<any>(null);
  const [isPdf, setIsPdf] = useState<boolean>(false);

  useEffect(() => {
    if (!resumeId) return;

    const fetchResumeContent = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/api/resumes/view/${resumeId}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to load resume');
        }
        
        // Check if the response is a PDF
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/pdf')) {
          setIsPdf(true);
          // For PDF, we'll use the URL directly in an iframe
          setResumeData({ url: `/api/resumes/view/${resumeId}` });
        } else {
          // For other formats, we'll get the extracted text
          const data = await response.json();
          setIsPdf(false);
          setResumeData(data);
        }
      } catch (err) {
        console.error('Error fetching resume:', err);
        setError(err instanceof Error ? err.message : 'Failed to load resume');
      } finally {
        setLoading(false);
      }
    };

    fetchResumeContent();
  }, [resumeId]);

  const handleDownload = () => {
    if (resumeId) {
      window.open(`/api/resumes/download/${resumeId}`, '_blank');
    }
  };

  return (
    <Modal open={!!resumeId} onClose={onClose}>
      <ViewerContainer>
        <ViewerHeader>
          <Typography variant="h6">
            {resumeData?.filename || 'Resume Viewer'}
          </Typography>
          <Box>
            <Button 
              startIcon={<DownloadIcon />} 
              onClick={handleDownload}
              sx={{ mr: 1 }}
            >
              Download
            </Button>
            <Button 
              startIcon={<CloseIcon />} 
              onClick={onClose}
            >
              Close
            </Button>
          </Box>
        </ViewerHeader>
        
        <ViewerContent>
          {loading && (
            <Box display="flex" justifyContent="center" alignItems="center" height="100%">
              <CircularProgress />
            </Box>
          )}
          
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          
          {!loading && !error && isPdf && resumeData?.url && (
            <PdfFrame src={resumeData.url} title="Resume PDF" />
          )}
          
          {!loading && !error && !isPdf && resumeData?.content && (
            <TextContent>
              {resumeData.content}
            </TextContent>
          )}
          
          {!loading && !error && !isPdf && !resumeData?.content && (
            <Alert severity="info">
              This file type cannot be viewed in the browser. Please download the file to view it.
            </Alert>
          )}
        </ViewerContent>
      </ViewerContainer>
    </Modal>
  );
};

// Button component to open the resume viewer
export const ViewResumeButton: React.FC<{ resumeId: number }> = ({ resumeId }) => {
  const [open, setOpen] = useState(false);
  
  return (
    <>
      <Button 
        startIcon={<VisibilityIcon />}
        variant="outlined" 
        size="small"
        onClick={() => setOpen(true)}
      >
        View Resume
      </Button>
      
      {open && (
        <ResumeViewer 
          resumeId={resumeId} 
          onClose={() => setOpen(false)} 
        />
      )}
    </>
  );
};

export default ResumeViewer;
