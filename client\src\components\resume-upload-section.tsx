import React, { useState, useRef } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Upload, FileText, Trash2, RefreshCw, Eye } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { useAuth } from "@/hooks/use-auth";
import ResumeViewModal from "./resume-view-modal";

// Resume interface to match the server schema
interface Resume {
  id: number;
  worker_id: number;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: 'pdf' | 'docx';
  upload_date: string;
}

export function ResumeUploadSection() {
  const { user } = useAuth();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showResumeModal, setShowResumeModal] = useState(false);

  // Query to get the current user's resume
  const {
    data: resume,
    isLoading: isLoadingResume,
    error: resumeError,
  } = useQuery<Resume>({
    queryKey: ['/api/resumes/worker', user?.id],
    queryFn: () => getQueryFn(`/api/resumes/worker/${user?.id}`),
    enabled: !!user && user.role === 'worker',
  });

  // Helper function to fetch resume
  async function getQueryFn(url: string) {
    const response = await apiRequest('GET', url);
    if (!response.ok) {
      if (response.status === 404) {
        return null; // No resume found is not an error
      }
      throw new Error('Failed to fetch resume');
    }
    return response.json();
  }

  // Upload/Replace resume mutation
  const uploadMutation = useMutation({
    mutationFn: async (formData: FormData) => {
      try {
        console.log('Uploading resume...', formData.get('resumeFile'));

        const response = await fetch('/api/resumes', {
          method: 'POST',
          body: formData,
          credentials: 'include',
        });

        console.log('Upload response status:', response.status);

        const responseData = await response.json();
        console.log('Upload response data:', responseData);

        if (!response.ok) {
          throw new Error(responseData.message || 'Failed to upload resume');
        }

        return responseData;
      } catch (error) {
        console.error('Resume upload error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: resume ? 'Resume Updated' : 'Resume Uploaded',
        description: resume
          ? 'Your resume has been successfully updated.'
          : 'Your resume has been successfully uploaded.',
      });

      // Reset the selected file
      setSelectedFile(null);

      // Invalidate the resume query to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/resumes/worker', user?.id] });
    },
    onError: (error: Error) => {
      console.error('Upload mutation error:', error);

      // Provide more specific error messages based on common issues
      let errorMessage = error.message;

      if (error.message.includes('Not authenticated')) {
        errorMessage = 'You need to be logged in to upload a resume. Please sign in and try again.';
      } else if (error.message.includes('Only workers can upload')) {
        errorMessage = 'Only worker accounts can upload resumes.';
      } else if (error.message.includes('No file uploaded')) {
        errorMessage = 'No file was selected. Please select a file and try again.';
      } else if (error.message.includes('file type')) {
        errorMessage = 'Invalid file type. Please upload a PDF or DOCX file.';
      } else if (error.message.includes('file size')) {
        errorMessage = 'File is too large. Maximum size is 25MB.';
      }

      toast({
        title: 'Upload Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    }
  });

  // Delete resume mutation
  const deleteMutation = useMutation({
    mutationFn: async (resumeId: number) => {
      const response = await apiRequest('DELETE', `/api/resumes/${resumeId}`);
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete resume');
      }
      return true;
    },
    onSuccess: () => {
      toast({
        title: 'Resume Deleted',
        description: 'Your resume has been successfully deleted.',
      });

      // Invalidate the resume query to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/resumes/worker', user?.id] });
    },
    onError: (error: Error) => {
      toast({
        title: 'Delete Failed',
        description: error.message,
        variant: 'destructive',
      });
    }
  });

  // Handle file selection
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      // Validate file type
      const fileType = file.name.split('.').pop()?.toLowerCase();
      if (fileType !== 'pdf' && fileType !== 'docx') {
        toast({
          title: 'Invalid File Type',
          description: 'Please upload a PDF or DOCX file only.',
          variant: 'destructive',
        });
        return;
      }

      // Validate file size (25MB limit)
      if (file.size > 25 * 1024 * 1024) {
        toast({
          title: 'File Too Large',
          description: 'Please upload a file smaller than 25MB.',
          variant: 'destructive',
        });
        return;
      }

      setSelectedFile(file);
    }
  };

  // Handle upload/replace button click
  const handleUpload = () => {
    if (!selectedFile) {
      fileInputRef.current?.click();
      return;
    }

    const formData = new FormData();
    formData.append('resumeFile', selectedFile);
    uploadMutation.mutate(formData);
  };

  // Handle delete button click
  const handleDelete = () => {
    if (resume) {
      if (confirm('Are you sure you want to delete your resume?')) {
        deleteMutation.mutate(resume.id);
      }
    }
  };

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  if (user?.role !== 'worker') {
    return null; // Don't render this component for non-workers
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Resume</CardTitle>
        <CardDescription>
          Upload your resume to increase your chances of finding a job match.
          We accept PDF and DOCX files up to 25MB.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf,.docx"
          className="hidden"
          aria-label="Upload resume file"
          title="Upload resume file"
        />

        {isLoadingResume ? (
          <div className="flex justify-center py-6">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : resume ? (
          <div className="border rounded-md p-4">
            <div className="flex items-start">
              <FileText className="h-10 w-10 mr-3 text-primary" />
              <div className="flex-1">
                <h4 className="font-medium">{resume.filename}</h4>
                <p className="text-sm text-muted-foreground">
                  {formatFileSize(resume.file_size)} • {resume.file_type.toUpperCase()} •
                  Uploaded {formatDistanceToNow(new Date(resume.upload_date), { addSuffix: true })}
                </p>
              </div>
            </div>
            <div className="flex space-x-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowResumeModal(true)}
              >
                <Eye className="mr-2 h-4 w-4" /> View Resume
              </Button>
              {selectedFile ? (
                <Button
                  size="sm"
                  onClick={handleUpload}
                  disabled={uploadMutation.isPending}
                >
                  {uploadMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Replace with {selectedFile.name}
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Replace
                </Button>
              )}
              <Button
                variant="destructive"
                size="sm"
                onClick={handleDelete}
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="mr-2 h-4 w-4" />
                )}
                Delete
              </Button>
            </div>
          </div>
        ) : (
          <div className="border border-dashed rounded-md p-10 text-center">
            {selectedFile ? (
              <div className="space-y-4">
                <FileText className="h-12 w-12 mx-auto text-primary" />
                <h4 className="font-medium">{selectedFile.name}</h4>
                <p className="text-sm text-muted-foreground">
                  {formatFileSize(selectedFile.size)}
                </p>
                <Button
                  onClick={handleUpload}
                  disabled={uploadMutation.isPending}
                >
                  {uploadMutation.isPending && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  Upload Resume
                </Button>
                <Button
                  variant="outline"
                  className="ml-2"
                  onClick={() => setSelectedFile(null)}
                >
                  Cancel
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                <h3 className="text-lg font-medium">No resume uploaded</h3>
                <p className="text-sm text-muted-foreground max-w-md mx-auto">
                  Upload your resume to help us match you with relevant job opportunities
                  and provide personalized reskilling suggestions.
                </p>
                <Button
                  onClick={() => fileInputRef.current?.click()}
                >
                  <Upload className="mr-2 h-4 w-4" /> Select File
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>

      {/* Resume View Modal */}
      {resume && (
        <ResumeViewModal
          open={showResumeModal}
          onClose={() => setShowResumeModal(false)}
          workerId={user?.id}
          workerName={user?.name || "Your"}
        />
      )}
    </Card>
  );
}