{"name": "PathLink Basic Protection", "target": "branch", "enforcement": "active", "conditions": {"ref_name": {"include": ["refs/heads/main"], "exclude": []}}, "rules": [{"type": "pull_request", "parameters": {"required_approving_review_count": 1, "dismiss_stale_reviews_on_push": true, "require_code_owner_review": true, "required_review_thread_resolution": true}}, {"type": "required_status_checks", "parameters": {"required_status_checks": [{"context": "security-check"}], "strict_required_status_checks_policy": true}}, {"type": "non_fast_forward"}, {"type": "required_linear_history"}, {"type": "restrict_deletions"}, {"type": "restrict_pushes"}], "bypass_actors": []}