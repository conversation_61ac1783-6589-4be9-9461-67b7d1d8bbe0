import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import {
  Loader2,
  Save,
  RefreshCw,
  Settings,
  Mail,
  FileText,
  Shield,
  Users,
  Clock,
  AlertTriangle,
} from 'lucide-react';
import { useLocation } from 'wouter';

interface SystemSettings {
  site_name: string;
  maintenance_mode: boolean;
  user_registration_enabled: boolean;
  default_user_role: string;
  email_notifications_enabled: boolean;
  resume_file_types_allowed: string[];
  max_resume_file_size_mb: number;
  job_listing_expiry_days: number;
  enable_worker_verification: boolean;
  enable_employer_verification: boolean;
  system_version: string;
}

const SystemSettingsPage: React.FC = () => {
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  // Fetch settings
  const { data: originalSettings, isLoading, refetch } = useQuery({
    queryKey: ['/api/admin/settings'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/settings');
      if (!res.ok) throw new Error('Failed to fetch system settings');
      return res.json() as Promise<SystemSettings>;
    },
    onSuccess: (data) => {
      if (!settings) {
        setSettings(data);
      }
    },
    retry: 1,
    staleTime: 60000, // 1 minute
  });

  // Update settings mutation
  const updateSettingsMutation = useMutation({
    mutationFn: async (updatedSettings: SystemSettings) => {
      const res = await apiRequest('PUT', '/api/admin/settings', updatedSettings);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to update settings');
      }
      return await res.json() as SystemSettings;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/settings'] });
      toast({
        title: 'Settings Updated',
        description: 'System settings have been successfully updated.',
      });
      setIsEditing(false);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update settings',
        variant: 'destructive',
      });
    },
  });

  // Handle save settings
  const handleSaveSettings = () => {
    if (!settings) return;
    updateSettingsMutation.mutate(settings);
  };

  // Handle cancel editing
  const handleCancelEditing = () => {
    setSettings(originalSettings || null);
    setIsEditing(false);
  };

  // Handle input change
  const handleInputChange = (key: keyof SystemSettings, value: any) => {
    if (!settings) return;
    setSettings({ ...settings, [key]: value });
    setIsEditing(true);
  };

  // Handle file types change
  const handleFileTypesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!settings) return;
    const fileTypes = e.target.value.split(',').map((type) => type.trim().toLowerCase());
    setSettings({ ...settings, resume_file_types_allowed: fileTypes });
    setIsEditing(true);
  };

  if (isLoading || !settings) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-8">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">System Settings</h1>
            <Button variant="outline" disabled>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="w-full h-[200px] animate-pulse">
                <CardHeader>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-2"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex items-center justify-center mt-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
            <span className="text-lg">Loading system settings...</span>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">System Settings</h1>
          <div className="space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={handleCancelEditing}>
                  Cancel
                </Button>
                <Button onClick={handleSaveSettings} disabled={updateSettingsMutation.isPending}>
                  {updateSettingsMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Save Changes
                </Button>
              </>
            ) : (
              <Button variant="outline" onClick={() => refetch()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="general" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="mb-6">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="users">Users & Registration</TabsTrigger>
            <TabsTrigger value="content">Content & Files</TabsTrigger>
            <TabsTrigger value="email">Email</TabsTrigger>
            <TabsTrigger value="system">System</TabsTrigger>
          </TabsList>

          <TabsContent value="general">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-xl font-bold">General Settings</CardTitle>
                <CardDescription>Basic configuration for your PathLink instance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="site-name">Site Name</Label>
                  <Input
                    id="site-name"
                    value={settings.site_name}
                    onChange={(e) => handleInputChange('site_name', e.target.value)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      When enabled, the site will display a maintenance message to all users except admins
                    </p>
                  </div>
                  <Switch
                    id="maintenance-mode"
                    checked={settings.maintenance_mode}
                    onCheckedChange={(checked) => handleInputChange('maintenance_mode', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-xl font-bold">User & Registration Settings</CardTitle>
                <CardDescription>Configure user registration and verification options</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="user-registration">User Registration</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Allow new users to register on the platform
                    </p>
                  </div>
                  <Switch
                    id="user-registration"
                    checked={settings.user_registration_enabled}
                    onCheckedChange={(checked) => handleInputChange('user_registration_enabled', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="default-role">Default User Role</Label>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant={settings.default_user_role === 'worker' ? 'default' : 'outline'}
                      onClick={() => handleInputChange('default_user_role', 'worker')}
                      className="flex-1"
                    >
                      Worker
                    </Button>
                    <Button
                      variant={settings.default_user_role === 'employer' ? 'default' : 'outline'}
                      onClick={() => handleInputChange('default_user_role', 'employer')}
                      className="flex-1"
                    >
                      Employer
                    </Button>
                  </div>
                </div>

                <Separator />

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="worker-verification">Worker Email Verification</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Require workers to verify their email address
                    </p>
                  </div>
                  <Switch
                    id="worker-verification"
                    checked={settings.enable_worker_verification}
                    onCheckedChange={(checked) => handleInputChange('enable_worker_verification', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="employer-verification">Employer Domain Verification</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Require employers to use email from approved domains
                    </p>
                  </div>
                  <Switch
                    id="employer-verification"
                    checked={settings.enable_employer_verification}
                    onCheckedChange={(checked) => handleInputChange('enable_employer_verification', checked)}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => setLocation('/admin/domains')}>
                  <Shield className="h-4 w-4 mr-2" />
                  Manage Approved Domains
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="content">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-xl font-bold">Content & Files Settings</CardTitle>
                <CardDescription>Configure file uploads and content management</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="allowed-file-types">Allowed Resume File Types</Label>
                  <Input
                    id="allowed-file-types"
                    value={settings.resume_file_types_allowed.join(', ')}
                    onChange={handleFileTypesChange}
                    placeholder="pdf, docx"
                  />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Comma-separated list of allowed file extensions
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-file-size">Maximum Resume File Size (MB)</Label>
                  <Input
                    id="max-file-size"
                    type="number"
                    value={settings.max_resume_file_size_mb}
                    onChange={(e) => handleInputChange('max_resume_file_size_mb', parseInt(e.target.value))}
                    min={1}
                    max={50}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="job-expiry">Job Listing Expiry (Days)</Label>
                  <Input
                    id="job-expiry"
                    type="number"
                    value={settings.job_listing_expiry_days}
                    onChange={(e) => handleInputChange('job_listing_expiry_days', parseInt(e.target.value))}
                    min={1}
                    max={365}
                  />
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Number of days before job listings are automatically marked as expired
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => setLocation('/admin/content')}>
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Content
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="email">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-xl font-bold">Email Settings</CardTitle>
                <CardDescription>Configure email notifications and templates</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notifications">Email Notifications</Label>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Enable or disable all email notifications
                    </p>
                  </div>
                  <Switch
                    id="email-notifications"
                    checked={settings.email_notifications_enabled}
                    onCheckedChange={(checked) => handleInputChange('email_notifications_enabled', checked)}
                  />
                </div>

                <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-md">
                  <h3 className="font-medium mb-2">Email Configuration</h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Email is configured using SendGrid. To change email settings, update the environment variables.
                  </p>
                  <div className="grid grid-cols-2 gap-2 text-sm">
                    <div className="font-medium">Sender Email:</div>
                    <div><EMAIL></div>
                    <div className="font-medium">Verified Sender:</div>
                    <div><EMAIL></div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => setLocation('/admin/email-templates')}>
                  <Mail className="h-4 w-4 mr-2" />
                  Manage Email Templates
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="system">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="text-xl font-bold">System Information</CardTitle>
                <CardDescription>View system information and perform maintenance tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">System Version</h3>
                    <p className="text-lg font-medium">{settings.system_version}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Database</h3>
                    <p className="text-lg font-medium">PostgreSQL (Neon)</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Node.js Version</h3>
                    <p className="text-lg font-medium">v18.x</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Environment</h3>
                    <p className="text-lg font-medium">Production</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h3 className="font-medium">System Maintenance</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <Button variant="outline">
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Clear Cache
                    </Button>
                    <Button variant="outline">
                      <Clock className="h-4 w-4 mr-2" />
                      Run Scheduled Tasks
                    </Button>
                    <Button variant="outline">
                      <Users className="h-4 w-4 mr-2" />
                      Sync User Data
                    </Button>
                    <Button variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      System Diagnostics
                    </Button>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => setLocation('/admin/logs')}>
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View System Logs
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>

        {isEditing && (
          <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-800 p-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              You have unsaved changes
            </div>
            <div className="space-x-2">
              <Button variant="outline" onClick={handleCancelEditing}>
                Cancel
              </Button>
              <Button onClick={handleSaveSettings} disabled={updateSettingsMutation.isPending}>
                {updateSettingsMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default SystemSettingsPage;
