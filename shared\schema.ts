import { pgTable, text, serial, integer, timestamp, boolean, json, date } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  role: text("role", { enum: ["worker", "employer", "admin"] }).notNull(),
  original_role: text("original_role", { enum: ["worker", "employer", "admin"] }),
  is_verified: boolean("is_verified").default(false),
  verification_token: text("verification_token"),
  verification_expires: timestamp("verification_expires"),
  created_at: timestamp("created_at").defaultNow(),
});

// Approved Email Domains table
export const approvedDomains = pgTable("approved_domains", {
  id: serial("id").primaryKey(),
  domain: text("domain").notNull().unique(),
  description: text("description"),
  is_active: boolean("is_active").default(true),
  added_by: integer("added_by").references(() => users.id),
  created_at: timestamp("created_at").defaultNow(),
});

// Jobs table
export const jobs = pgTable("jobs", {
  id: serial("id").primaryKey(),
  title: text("title").notNull(),
  description: text("description").notNull(),
  required_skills: text("required_skills"),
  minimum_experience: text("minimum_experience"),
  preferred_backgrounds: text("preferred_backgrounds"),
  work_model: text("work_model"),
  location: text("location").notNull(),
  availability_needs: text("availability_needs"),
  language_requirements: text("language_requirements"),
  culture_fit_keywords: text("culture_fit_keywords"),
  salary_range: text("salary_range"),
  diversity_goals: text("diversity_goals"),
  industry: text("industry").notNull(),
  employer_id: integer("employer_id").references(() => users.id).notNull(),
  company: text("company"),
  salary: integer("salary"),
  employment_type: text("employment_type"),
  experience_level: text("experience_level"),
  benefits: text("benefits"),
  requirements: text("requirements"),
  created_at: timestamp("created_at").defaultNow(),
});

// Surplus employees table
export const surplusEmployees = pgTable("surplus_employees", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  previous_role: text("previous_role").notNull(),
  industry: text("industry").notNull(),
  employer_id: integer("employer_id").references(() => users.id).notNull(),
  status: text("status", { enum: ["available", "placed", "pending"] }).notNull().default("available"),
  notes: text("notes").default(""),
  skills: text("skills").default(""),
  years_experience: integer("years_experience").default(0),
  transfer_reason: text("transfer_reason").default(""),
  potential_positions: text("potential_positions").default(""),
  user_id: integer("user_id").references(() => users.id),
});

// Matches table
export const matches = pgTable("matches", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull(),
  job_id: integer("job_id").references(() => jobs.id).notNull(),
  status: text("status", { enum: ["pending", "matched", "rejected", "interview_scheduled", "accepted", "withdrawn"] }).notNull().default("pending"),
  match_score: integer("match_score"),
  match_date: timestamp("match_date").defaultNow(),
  interview_date: timestamp("interview_date"),
});

// Resumes table
export const resumes = pgTable("resumes", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull(),
  filename: text("filename").notNull(),
  file_path: text("file_path").notNull(),
  // Note: file_url is not stored in the database, it's generated on the fly
  file_size: integer("file_size").notNull(),
  file_type: text("file_type", { enum: ["pdf", "docx"] }).notNull(),
  upload_date: timestamp("upload_date").defaultNow(),
  extracted_text: text("extracted_text"),  // Extracted text content for searching
  last_indexed: timestamp("last_indexed"),  // When the resume was last indexed
});

// User Schema
export const insertUserSchema = createInsertSchema(users).omit({ id: true });
export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;

// Job Schema
export const insertJobSchema = createInsertSchema(jobs).omit({ id: true });
export type InsertJob = z.infer<typeof insertJobSchema>;
export type Job = typeof jobs.$inferSelect;

// Surplus Employee Schema
export const insertSurplusEmployeeSchema = createInsertSchema(surplusEmployees)
  .omit({ id: true })
  .extend({
    notes: z.string().optional().default(""),
    skills: z.string().optional().default(""),
    years_experience: z.number().optional().default(0),
    transfer_reason: z.string().optional().default(""),
    potential_positions: z.string().optional().default(""),
    user_id: z.number().optional(),
  });
export type InsertSurplusEmployee = z.infer<typeof insertSurplusEmployeeSchema>;
export type SurplusEmployee = typeof surplusEmployees.$inferSelect;

// Match Schema
export const insertMatchSchema = createInsertSchema(matches).omit({ id: true });
export type InsertMatch = z.infer<typeof insertMatchSchema>;
export type Match = typeof matches.$inferSelect;

// Resume Schema
export const insertResumeSchema = createInsertSchema(resumes).omit({ id: true, upload_date: true });
export type InsertResume = z.infer<typeof insertResumeSchema> & { file_url?: string };
export type Resume = typeof resumes.$inferSelect & { file_url?: string };

// 1. Growth Memory Engine - Store reskilling suggestions history
export const skillingSuggestions = pgTable("skilling_suggestions", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull(),
  suggestions: json("suggestions").notNull(), // Array of suggestion objects
  created_at: timestamp("created_at").defaultNow(),
});

// 2. Career Quantum Leap Generator
export const careerTransitionPlans = pgTable("career_transition_plans", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull(),
  current_role: text("current_role"),
  dream_role: text("dream_role").notNull(),
  transition_plan: json("transition_plan").notNull(), // Step-by-step plan
  created_at: timestamp("created_at").defaultNow(),
});

// 3. Skill Context Analyzer - Extended worker profile
export const workerProfiles = pgTable("worker_profiles", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull().unique(),
  skills_context: json("skills_context"), // Array of {skill, context, purpose}
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

// 4. GPT Reflection Journal
export const reflectionJournals = pgTable("reflection_journals", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull(),
  prompt: text("prompt").notNull(),
  entry: text("entry").notNull(),
  ai_reflection: text("ai_reflection"),
  created_at: timestamp("created_at").defaultNow(),
});

// 5. Workplace Soul Match - Worker values
export const workerValues = pgTable("worker_values", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull().unique(),
  values_match_enabled: boolean("values_match_enabled").default(false),
  core_values: json("core_values"), // Array of value strings
  mission_statement: text("mission_statement"),
  updated_at: timestamp("updated_at").defaultNow(),
});

// 6. AI Mentor Constellations
export const mentorPreferences = pgTable("mentor_preferences", {
  id: serial("id").primaryKey(),
  worker_id: integer("worker_id").references(() => users.id).notNull().unique(),
  mentor_tone: text("mentor_tone", { enum: ["rebel", "leader", "hacker"] }),
  updated_at: timestamp("updated_at").defaultNow(),
});

// Schema definitions for new tables
export const insertSkillingSuggestionsSchema = createInsertSchema(skillingSuggestions).omit({ id: true, created_at: true });
export type InsertSkillingSuggestions = z.infer<typeof insertSkillingSuggestionsSchema>;
export type SkillingSuggestions = typeof skillingSuggestions.$inferSelect;

export const insertCareerTransitionPlanSchema = createInsertSchema(careerTransitionPlans).omit({ id: true, created_at: true });
export type InsertCareerTransitionPlan = z.infer<typeof insertCareerTransitionPlanSchema>;
export type CareerTransitionPlan = typeof careerTransitionPlans.$inferSelect;

export const insertWorkerProfileSchema = createInsertSchema(workerProfiles).omit({ id: true, created_at: true, updated_at: true });
export type InsertWorkerProfile = z.infer<typeof insertWorkerProfileSchema>;
export type WorkerProfile = typeof workerProfiles.$inferSelect;

export const insertReflectionJournalSchema = createInsertSchema(reflectionJournals).omit({ id: true, created_at: true, ai_reflection: true });
export type InsertReflectionJournal = z.infer<typeof insertReflectionJournalSchema>;
export type ReflectionJournal = typeof reflectionJournals.$inferSelect;

export const insertWorkerValuesSchema = createInsertSchema(workerValues).omit({ id: true, updated_at: true });
export type InsertWorkerValues = z.infer<typeof insertWorkerValuesSchema>;
export type WorkerValues = typeof workerValues.$inferSelect;

export const insertMentorPreferencesSchema = createInsertSchema(mentorPreferences).omit({ id: true, updated_at: true });
export type InsertMentorPreferences = z.infer<typeof insertMentorPreferencesSchema>;
export type MentorPreferences = typeof mentorPreferences.$inferSelect;

// Approved Domains Schema
export const insertApprovedDomainSchema = createInsertSchema(approvedDomains).omit({ id: true, created_at: true });
export type InsertApprovedDomain = z.infer<typeof insertApprovedDomainSchema>;
export type ApprovedDomain = typeof approvedDomains.$inferSelect;

// System Logs table
export const systemLogs = pgTable("system_logs", {
  id: serial("id").primaryKey(),
  level: text("level", { enum: ["info", "warning", "error"] }).notNull(),
  message: text("message").notNull(),
  source: text("source").notNull(),
  user_id: integer("user_id").references(() => users.id),
  metadata: json("metadata"),
  created_at: timestamp("created_at").defaultNow(),
});

// User Profiles table
export const userProfiles = pgTable("user_profiles", {
  id: serial("id").primaryKey(),
  user_id: integer("user_id").references(() => users.id).notNull().unique(),
  position: text("position"),
  location: text("location"),
  bio: text("bio"),
  company: text("company"),
  years_of_experience: integer("years_of_experience"),
  skills: text("skills"),
  profile_picture: text("profile_picture"),
  pending_email: text("pending_email"),
  created_at: timestamp("created_at").defaultNow(),
  updated_at: timestamp("updated_at").defaultNow(),
});

// User Profiles Schema
export const insertUserProfileSchema = createInsertSchema(userProfiles).omit({ id: true, created_at: true, updated_at: true });
export type InsertUserProfile = z.infer<typeof insertUserProfileSchema>;
export type UserProfile = typeof userProfiles.$inferSelect;

// System Logs Schema
export const insertSystemLogSchema = createInsertSchema(systemLogs).omit({ id: true, created_at: true });
export type InsertSystemLog = z.infer<typeof insertSystemLogSchema>;
export type SystemLog = typeof systemLogs.$inferSelect;
