# Use official Node.js runtime as base image
FROM mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye

# Set working directory
WORKDIR /workspace

# Install additional security tools
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r pathlink && useradd -r -g pathlink -s /bin/bash pathlink

# Set up restricted environment
RUN echo '#!/bin/bash\necho "Download and copy operations are restricted in this environment."\necho "You can only edit code and submit pull requests."\nexit 1' > /usr/local/bin/restrict-downloads \
    && chmod +x /usr/local/bin/restrict-downloads

# Disable certain commands for security
RUN echo 'alias wget="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias curl="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias scp="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias rsync="restrict-downloads"' >> /etc/bash.bashrc

# Set up git configuration for PR workflow
RUN git config --global init.defaultBranch main \
    && git config --global pull.rebase false \
    && git config --global push.default simple

# Create welcome message
RUN echo '#!/bin/bash\necho "🚀 Welcome to PathLink Development Environment!"\necho "📝 You can edit code and submit pull requests."\necho "🚫 Downloads and copying are restricted."\necho "💡 Use: npm run dev to start development"' > /usr/local/bin/welcome \
    && chmod +x /usr/local/bin/welcome

# Switch to non-root user
USER pathlink

# Set the default command
CMD ["/bin/bash"]
