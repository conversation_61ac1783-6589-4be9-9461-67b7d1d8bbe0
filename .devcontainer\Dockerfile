# Use official Node.js runtime as base image
FROM mcr.microsoft.com/devcontainers/typescript-node:1-20-bullseye

# Set working directory
WORKDIR /workspace

# Install additional security tools
RUN apt-get update && apt-get install -y \
    git \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r pathlink && useradd -r -g pathlink -s /bin/bash pathlink

# Set up restricted environment with comprehensive restrictions
RUN echo '#!/bin/bash\necho "RESTRICTED: Download and copy operations are not allowed in this environment."\necho "📝 You can only edit code and submit pull requests."\necho "🔒 This action has been logged for security monitoring."\nexit 1' > /usr/local/bin/restrict-downloads \
    && chmod +x /usr/local/bin/restrict-downloads

# Create restricted git wrapper
RUN echo '#!/bin/bash\nif [[ "$1" == "clone" ]] || [[ "$1" == "archive" ]] || [[ "$1" == "bundle" ]]; then\n  echo "🚫 Git operation $1 is restricted in this environment"\n  exit 1\nfi\n/usr/bin/git.orig "$@"' > /usr/local/bin/git-restricted \
    && chmod +x /usr/local/bin/git-restricted \
    && mv /usr/bin/git /usr/bin/git.orig \
    && ln -s /usr/local/bin/git-restricted /usr/bin/git

# Disable download/copy commands and file transfer utilities
RUN echo 'alias wget="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias curl="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias scp="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias rsync="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias ftp="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias sftp="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias nc="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias netcat="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias tar="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias zip="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias unzip="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias gzip="restrict-downloads"' >> /etc/bash.bashrc \
    && echo 'alias gunzip="restrict-downloads"' >> /etc/bash.bashrc

# Set up git configuration for PR workflow
RUN git config --global init.defaultBranch main \
    && git config --global pull.rebase false \
    && git config --global push.default simple

# Remove potentially dangerous utilities and create restricted environment
RUN apt-get update && apt-get remove -y \
    wget \
    curl \
    && rm -f /usr/bin/wget /usr/bin/curl /usr/bin/scp /usr/bin/rsync \
    && rm -rf /var/lib/apt/lists/*

# Create monitoring script for restricted actions
RUN echo '#!/bin/bash\necho "$(date): User attempted restricted action: $*" >> /var/log/restricted-actions.log\necho "🚫 SECURITY ALERT: This action is not permitted"\necho "📋 Allowed actions: edit code, run npm commands, create pull requests"\necho "🔒 This attempt has been logged for security review"\nexit 1' > /usr/local/bin/security-monitor \
    && chmod +x /usr/local/bin/security-monitor \
    && touch /var/log/restricted-actions.log \
    && chmod 666 /var/log/restricted-actions.log

# Create welcome message with security notice
RUN echo '#!/bin/bash\necho "🚀 Welcome to PathLink Development Environment!"\necho "📝 ALLOWED: Edit code, run npm commands, submit pull requests"\necho "🚫 RESTRICTED: Downloads, copying, file transfers, archiving"\necho "🔒 All actions are monitored for security compliance"\necho "💡 Use: npm run dev to start development"\necho "📋 Use: git status, git add, git commit, git push for version control"' > /usr/local/bin/welcome \
    && chmod +x /usr/local/bin/welcome

# Copy security scripts
COPY security-monitor.sh /usr/local/bin/security-monitor
COPY startup-security.sh /usr/local/bin/startup-security
RUN chmod +x /usr/local/bin/security-monitor /usr/local/bin/startup-security

# Set up automatic security initialization
RUN echo '#!/bin/bash\n/usr/local/bin/startup-security\nexec "$@"' > /usr/local/bin/secure-entrypoint \
    && chmod +x /usr/local/bin/secure-entrypoint

# Switch to non-root user
USER pathlink

# Set the default command with security initialization
ENTRYPOINT ["/usr/local/bin/secure-entrypoint"]
CMD ["/bin/bash"]
