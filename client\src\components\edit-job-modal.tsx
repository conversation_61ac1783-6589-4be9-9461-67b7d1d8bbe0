import { useState, useEffect } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Job, insertJobSchema } from "@shared/schema";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";

const formSchema = insertJobSchema.omit({ employer_id: true }).extend({
  title: z.string().min(3, "Title must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  required_skills: z.string().optional(),
  minimum_experience: z.string().optional(),
  preferred_backgrounds: z.string().optional(),
  work_model: z.string().optional(),
  location: z.string().min(1, "Location is required"),
  availability_needs: z.string().optional(),
  language_requirements: z.string().optional(),
  culture_fit_keywords: z.string().optional(),
  salary_range: z.string().optional(),
  diversity_goals: z.string().optional(),
  industry: z.string().min(1, "Please select an industry"),
});

type FormValues = z.infer<typeof formSchema>;

interface EditJobModalProps {
  open: boolean;
  onClose: () => void;
  job: Job;
}

export default function EditJobModal({ open, onClose, job }: EditJobModalProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: job.title,
      description: job.description,
      required_skills: job.required_skills || "",
      minimum_experience: job.minimum_experience || "",
      preferred_backgrounds: job.preferred_backgrounds || "",
      work_model: job.work_model || "",
      location: job.location,
      availability_needs: job.availability_needs || "",
      language_requirements: job.language_requirements || "",
      culture_fit_keywords: job.culture_fit_keywords || "",
      salary_range: job.salary_range || "",
      diversity_goals: job.diversity_goals || "",
      industry: job.industry,
    },
  });

  // Update form values when job changes
  useEffect(() => {
    form.reset({
      title: job.title,
      description: job.description,
      required_skills: job.required_skills || "",
      minimum_experience: job.minimum_experience || "",
      preferred_backgrounds: job.preferred_backgrounds || "",
      work_model: job.work_model || "",
      location: job.location,
      availability_needs: job.availability_needs || "",
      language_requirements: job.language_requirements || "",
      culture_fit_keywords: job.culture_fit_keywords || "",
      salary_range: job.salary_range || "",
      diversity_goals: job.diversity_goals || "",
      industry: job.industry,
    });
  }, [job, form]);

  const updateJobMutation = useMutation({
    mutationFn: async (data: FormValues) => {
      const res = await apiRequest("PUT", `/api/jobs/${job.id}`, data);
      return await res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/jobs/employer"] });
      toast({
        title: "Job updated",
        description: "Your job has been successfully updated",
      });
      onClose();
    },
    onError: (error: Error) => {
      toast({
        title: "Failed to update job",
        description: error.message || "There was an error updating your job",
        variant: "destructive",
      });
    },
  });

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      await updateJobMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[550px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-[#1C2A42] dark:text-white">Edit Job</DialogTitle>
          <DialogDescription>
            Update your job posting details.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Job Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Frontend Developer" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Describe the job responsibilities and requirements"
                      className="resize-none"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="required_skills"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Required Skills (hard and soft)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="List required technical and soft skills"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="minimum_experience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Experience</FormLabel>
                    <FormControl>
                      <Input placeholder="Years, industries, certifications" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preferred_backgrounds"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred Backgrounds</FormLabel>
                    <FormControl>
                      <Input placeholder="Industries, education, transferable roles" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="work_model"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work Model</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select work model" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="On-site">On-site</SelectItem>
                        <SelectItem value="Remote">Remote</SelectItem>
                        <SelectItem value="Hybrid">Hybrid</SelectItem>
                        <SelectItem value="Part-time">Part-time</SelectItem>
                        <SelectItem value="Shift-based">Shift-based</SelectItem>
                        <SelectItem value="Gig">Gig</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location or Geographic Radius</FormLabel>
                    <FormControl>
                      <Input placeholder="New York, Remote, 50 mile radius of Chicago, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="availability_needs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Availability Needs</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select availability" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Immediate">Immediate</SelectItem>
                        <SelectItem value="Future pipeline">Future pipeline</SelectItem>
                        <SelectItem value="Seasonal">Seasonal</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="language_requirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Language Requirements</FormLabel>
                    <FormControl>
                      <Input placeholder="English, Spanish, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="culture_fit_keywords"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Culture Fit Keywords (optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="Team-oriented, self-starter, etc." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="salary_range"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Salary Range or Pay Bands</FormLabel>
                    <FormControl>
                      <Input placeholder="$50,000-$70,000, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="diversity_goals"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Diversity Goals (optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Workforce development goals" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="industry"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Industry</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select industry" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Finance">Finance</SelectItem>
                      <SelectItem value="Healthcare">Healthcare</SelectItem>
                      <SelectItem value="Retail">Retail</SelectItem>
                      <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-[#1C2A42] hover:bg-opacity-90"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update Job"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
