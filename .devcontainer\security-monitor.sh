#!/bin/bash

# PathLink Codespace Security Monitor
# This script monitors and restricts potentially dangerous operations

LOG_FILE="/var/log/pathlink-security.log"
RESTRICTED_COMMANDS=("wget" "curl" "scp" "rsync" "ftp" "sftp" "nc" "netcat" "tar" "zip" "unzip" "gzip" "gunzip")

# Function to log security events
log_security_event() {
    local action="$1"
    local user="${USER:-unknown}"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] USER: $user | ACTION: $action | STATUS: BLOCKED" >> "$LOG_FILE"
}

# Function to display security warning
show_security_warning() {
    local command="$1"
    echo "🚫 SECURITY RESTRICTION ACTIVATED"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "❌ Command '$command' is not permitted in this environment"
    echo "🔒 This environment is restricted to code editing and PR submission"
    echo "📋 Allowed operations:"
    echo "   • Edit source code files"
    echo "   • Run npm/yarn commands"
    echo "   • Use git for version control (push/pull/commit)"
    echo "   • Create and manage pull requests"
    echo "   • Run development servers (npm run dev)"
    echo ""
    echo "🚨 This security event has been logged"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Function to check if command should be restricted
is_restricted_command() {
    local cmd="$1"
    for restricted in "${RESTRICTED_COMMANDS[@]}"; do
        if [[ "$cmd" == "$restricted" ]]; then
            return 0
        fi
    done
    return 1
}

# Function to monitor git operations
monitor_git_command() {
    local git_cmd="$1"
    case "$git_cmd" in
        "clone"|"archive"|"bundle")
            log_security_event "git $git_cmd (BLOCKED)"
            echo "🚫 Git operation '$git_cmd' is restricted"
            echo "📝 You can only use: git add, git commit, git push, git pull, git status"
            echo "🔒 Repository cloning and archiving are not permitted"
            return 1
            ;;
        *)
            return 0
            ;;
    esac
}

# Function to monitor file operations
monitor_file_operations() {
    local operation="$1"
    shift
    local files=("$@")
    
    # Check for attempts to access sensitive files
    for file in "${files[@]}"; do
        if [[ "$file" =~ \.env ]] || [[ "$file" =~ config.*\.json ]] || [[ "$file" =~ \.key$ ]] || [[ "$file" =~ \.pem$ ]]; then
            log_security_event "$operation on sensitive file: $file (BLOCKED)"
            echo "🚫 Access to sensitive file '$file' is restricted"
            return 1
        fi
    done
    
    return 0
}

# Main security check function
security_check() {
    local command="$1"
    shift
    local args=("$@")
    
    # Check for restricted commands
    if is_restricted_command "$command"; then
        log_security_event "$command ${args[*]} (BLOCKED)"
        show_security_warning "$command"
        return 1
    fi
    
    # Special handling for git commands
    if [[ "$command" == "git" ]] && [[ ${#args[@]} -gt 0 ]]; then
        if ! monitor_git_command "${args[0]}"; then
            return 1
        fi
    fi
    
    # Monitor file operations
    case "$command" in
        "cp"|"mv"|"cat"|"less"|"more"|"head"|"tail")
            if ! monitor_file_operations "$command" "${args[@]}"; then
                return 1
            fi
            ;;
    esac
    
    return 0
}

# Function to display security status
show_security_status() {
    echo "🔒 PathLink Codespace Security Status"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo "✅ Security monitoring: ACTIVE"
    echo "🚫 Download restrictions: ENABLED"
    echo "📝 Code editing: ALLOWED"
    echo "🔄 Git operations: RESTRICTED (push/pull/commit only)"
    echo "📋 Pull requests: ALLOWED"
    echo "🖥️  Development servers: ALLOWED"
    echo ""
    echo "📊 Security Log: $LOG_FILE"
    if [[ -f "$LOG_FILE" ]]; then
        local event_count=$(wc -l < "$LOG_FILE" 2>/dev/null || echo "0")
        echo "📈 Security events logged: $event_count"
    fi
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Function to show recent security events
show_security_log() {
    echo "📋 Recent Security Events:"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    if [[ -f "$LOG_FILE" ]]; then
        tail -10 "$LOG_FILE" 2>/dev/null || echo "No security events logged yet."
    else
        echo "No security events logged yet."
    fi
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
}

# Handle script arguments
case "${1:-}" in
    "check")
        shift
        security_check "$@"
        ;;
    "status")
        show_security_status
        ;;
    "log")
        show_security_log
        ;;
    "monitor")
        shift
        if security_check "$@"; then
            exec "$@"
        else
            exit 1
        fi
        ;;
    *)
        echo "PathLink Codespace Security Monitor"
        echo "Usage: $0 {check|status|log|monitor} [command] [args...]"
        echo ""
        echo "Commands:"
        echo "  check [cmd] [args]  - Check if command is allowed"
        echo "  status             - Show security status"
        echo "  log                - Show recent security events"
        echo "  monitor [cmd]      - Monitor and execute command if allowed"
        exit 1
        ;;
esac
