import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface PhoneInputProps {
  onSubmit: (phoneNumber: string, verificationCode: string) => Promise<void>;
  className?: string;
  isLoading?: boolean;
}

export function PhoneInput({ onSubmit, className, isLoading = false }: PhoneInputProps) {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [codeSent, setCodeSent] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSendCode = async () => {
    if (!phoneNumber || phoneNumber.length < 10) {
      setError('Please enter a valid phone number');
      return;
    }

    setError(null);
    // Here you would typically call an API to send a verification code
    // For now, we'll just simulate it
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setCodeSent(true);
    } catch (err) {
      setError('Failed to send verification code. Please try again.');
    }
  };

  const handleVerify = async () => {
    if (!verificationCode || verificationCode.length < 4) {
      setError('Please enter a valid verification code');
      return;
    }

    setError(null);
    try {
      await onSubmit(phoneNumber, verificationCode);
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Verification failed. Please try again.');
      }
    }
  };

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Format as (XXX) XXX-XXXX for US numbers
    if (digits.length <= 3) {
      return digits;
    } else if (digits.length <= 6) {
      return `(${digits.slice(0, 3)}) ${digits.slice(3)}`;
    } else {
      return `(${digits.slice(0, 3)}) ${digits.slice(3, 6)}-${digits.slice(6, 10)}`;
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedNumber = formatPhoneNumber(e.target.value);
    setPhoneNumber(formattedNumber);
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <Input
          id="phone"
          type="tel"
          placeholder="(*************"
          value={phoneNumber}
          onChange={handlePhoneChange}
          disabled={codeSent || isLoading}
        />
      </div>

      {codeSent ? (
        <div className="space-y-2">
          <Label htmlFor="code">Verification Code</Label>
          <Input
            id="code"
            type="text"
            placeholder="Enter code"
            value={verificationCode}
            onChange={(e) => setVerificationCode(e.target.value)}
            disabled={isLoading}
            maxLength={6}
          />
          <Button 
            type="button" 
            onClick={handleVerify} 
            disabled={isLoading}
            className="w-full mt-2"
          >
            {isLoading ? 'Verifying...' : 'Verify Code'}
          </Button>
          <Button
            type="button"
            variant="link"
            onClick={() => setCodeSent(false)}
            disabled={isLoading}
            className="w-full mt-0 p-0"
          >
            Use a different phone number
          </Button>
        </div>
      ) : (
        <Button 
          type="button" 
          onClick={handleSendCode} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Sending...' : 'Send Verification Code'}
        </Button>
      )}

      {error && (
        <p className="text-sm text-red-500 mt-2">{error}</p>
      )}
    </div>
  );
}
