import { execSync } from 'child_process';
import fs from 'fs';

console.log('🚀 PathLink AWS Lambda Deployment');
console.log('=================================\n');

// Install serverless framework
function installServerless() {
  try {
    execSync('npm install -g serverless', { stdio: 'inherit' });
    console.log('✅ Serverless framework installed');
    return true;
  } catch (error) {
    console.log('❌ Failed to install serverless framework');
    return false;
  }
}

// Update server index for Lambda
function updateServerForLambda() {
  const serverPath = 'server/index.ts';
  let serverContent = fs.readFileSync(serverPath, 'utf8');
  
  // Add serverless-http wrapper
  if (!serverContent.includes('serverless-http')) {
    serverContent = `import serverless from 'serverless-http';\n${serverContent}`;
    
    // Add export for Lambda
    serverContent += `\n\n// Lambda handler\nexport const handler = serverless(app);`;
    
    fs.writeFileSync(serverPath, serverContent);
    console.log('✅ Server updated for Lambda deployment');
  }
}

// Build for Lambda
function buildForLambda() {
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('✅ Application built for Lambda');
    return true;
  } catch (error) {
    console.log('❌ Build failed');
    return false;
  }
}

// Deploy to AWS Lambda
function deployToLambda() {
  try {
    execSync('serverless deploy', { stdio: 'inherit' });
    console.log('✅ Deployed to AWS Lambda');
    return true;
  } catch (error) {
    console.log('❌ Lambda deployment failed');
    return false;
  }
}

async function main() {
  console.log('Installing dependencies...');
  execSync('npm install serverless-offline --save-dev', { stdio: 'inherit' });
  
  updateServerForLambda();
  
  if (!buildForLambda()) {
    process.exit(1);
  }
  
  console.log('\n🎉 Ready for AWS Lambda deployment!');
  console.log('Next steps:');
  console.log('1. Configure AWS credentials: aws configure');
  console.log('2. Deploy: serverless deploy');
  console.log('3. Your API will be available at the provided endpoint');
}

main().catch(console.error);