// Script to add interview_date column to matches table
import pg from 'pg';
import dotenv from 'dotenv';

const { Pool } = pg;

// Load environment variables
dotenv.config();

// Create a connection to the database
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function addInterviewDateColumn() {
  const client = await pool.connect();

  try {
    console.log('Starting migration: Adding interview_date column to matches table...');

    // Check if the column already exists
    const checkColumnQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'matches' AND column_name = 'interview_date'
    `;

    const checkResult = await client.query(checkColumnQuery);

    if (checkResult.rows.length === 0) {
      // Column doesn't exist, add it
      console.log('Column interview_date does not exist. Adding it now...');

      await client.query('BEGIN');

      const addColumnQuery = `
        ALTER TABLE matches
        ADD COLUMN interview_date TIMESTAMP
      `;

      await client.query(addColumnQuery);

      // Update the status enum to include new statuses
      const updateEnumQuery = `
        ALTER TABLE matches
        DROP CONSTRAINT IF EXISTS matches_status_check;

        ALTER TABLE matches
        ADD CONSTRAINT matches_status_check
        CHECK (status IN ('pending', 'matched', 'rejected', 'interview_scheduled', 'accepted'));
      `;

      await client.query(updateEnumQuery);

      await client.query('COMMIT');

      console.log('Successfully added interview_date column to matches table');
    } else {
      console.log('Column interview_date already exists. No changes needed.');
    }

    console.log('Migration completed successfully');
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error during migration:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
addInterviewDateColumn();
