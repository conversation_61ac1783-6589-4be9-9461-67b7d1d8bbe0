const fs = require('fs');
const path = require('path');

class ProductionOptimizer {
  constructor() {
    this.optimizations = [];
    this.issues = [];
  }

  log(category, item, status, details = '') {
    const result = { category, item, status, details, timestamp: new Date().toISOString() };
    
    if (status === 'OPTIMIZED' || status === 'VERIFIED') {
      this.optimizations.push(result);
    } else {
      this.issues.push(result);
    }
    
    const emoji = status === 'OPTIMIZED' || status === 'VERIFIED' ? '✅' : 
                  status === 'WARNING' ? '⚠️' : '❌';
    console.log(`${emoji} [${category}] ${item}: ${status} ${details}`);
  }

  checkEnvironmentVariables() {
    console.log('\n🔧 CHECKING ENVIRONMENT CONFIGURATION...');
    
    const requiredEnvVars = [
      'DATABASE_URL',
      'SESSION_SECRET',
      'OPENAI_API_KEY',
      'SENDGRID_API_KEY'
    ];

    const envContent = fs.readFileSync('.env', 'utf8');
    
    requiredEnvVars.forEach(envVar => {
      if (envContent.includes(`${envVar}=`)) {
        this.log('Environment', envVar, 'VERIFIED', 'Present in .env file');
      } else {
        this.log('Environment', envVar, 'MISSING', 'Not found in .env file');
      }
    });

    // Check for production-ready values
    if (envContent.includes('SESSION_SECRET=your-secret-key-here')) {
      this.log('Environment', 'SESSION_SECRET', 'WARNING', 'Using default value - should be changed for production');
    }
  }

  checkDatabaseConfiguration() {
    console.log('\n🗄️ CHECKING DATABASE CONFIGURATION...');
    
    try {
      const storageFile = fs.readFileSync('server/storage.ts', 'utf8');
      
      if (storageFile.includes('Using Neon Database storage')) {
        this.log('Database', 'Neon Configuration', 'VERIFIED', 'Properly configured for Neon database');
      }
      
      if (storageFile.includes('ssl: { rejectUnauthorized: false }')) {
        this.log('Database', 'SSL Configuration', 'VERIFIED', 'SSL properly configured');
      }
      
      if (storageFile.includes('pool.connect()')) {
        this.log('Database', 'Connection Pooling', 'VERIFIED', 'Connection pooling implemented');
      }
      
    } catch (error) {
      this.log('Database', 'Configuration Check', 'ERROR', error.message);
    }
  }

  checkSecurityFeatures() {
    console.log('\n🔒 CHECKING SECURITY FEATURES...');
    
    try {
      const authFile = fs.readFileSync('server/auth.ts', 'utf8');
      
      if (authFile.includes('bcrypt.compare')) {
        this.log('Security', 'Password Hashing', 'VERIFIED', 'bcrypt password hashing implemented');
      }
      
      if (authFile.includes('req.isAuthenticated()')) {
        this.log('Security', 'Authentication Middleware', 'VERIFIED', 'Authentication checks in place');
      }
      
      const routesFile = fs.readFileSync('server/routes.ts', 'utf8');
      
      if (routesFile.includes('if (!req.isAuthenticated())')) {
        this.log('Security', 'Route Protection', 'VERIFIED', 'Protected routes implemented');
      }
      
      if (routesFile.includes('res.status(401)')) {
        this.log('Security', 'Unauthorized Handling', 'VERIFIED', 'Proper 401 responses');
      }
      
    } catch (error) {
      this.log('Security', 'Security Check', 'ERROR', error.message);
    }
  }

  checkPerformanceOptimizations() {
    console.log('\n⚡ CHECKING PERFORMANCE OPTIMIZATIONS...');
    
    try {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      
      if (packageJson.scripts && packageJson.scripts.build) {
        this.log('Performance', 'Build Script', 'VERIFIED', 'Build script configured');
      }
      
      // Check for production dependencies
      const prodDeps = packageJson.dependencies || {};
      const devDeps = packageJson.devDependencies || {};
      
      if (prodDeps['compression']) {
        this.log('Performance', 'Compression Middleware', 'VERIFIED', 'Compression available');
      } else {
        this.log('Performance', 'Compression Middleware', 'MISSING', 'Consider adding compression middleware');
      }
      
      // Check if TypeScript is properly configured
      if (fs.existsSync('tsconfig.json')) {
        this.log('Performance', 'TypeScript Configuration', 'VERIFIED', 'TypeScript properly configured');
      }
      
    } catch (error) {
      this.log('Performance', 'Performance Check', 'ERROR', error.message);
    }
  }

  checkErrorHandling() {
    console.log('\n🚨 CHECKING ERROR HANDLING...');
    
    try {
      const routesFile = fs.readFileSync('server/routes.ts', 'utf8');
      
      if (routesFile.includes('try {') && routesFile.includes('catch (error)')) {
        this.log('Error Handling', 'Try-Catch Blocks', 'VERIFIED', 'Error handling implemented');
      }
      
      if (routesFile.includes('console.error')) {
        this.log('Error Handling', 'Error Logging', 'VERIFIED', 'Error logging in place');
      }
      
      if (routesFile.includes('res.status(500)')) {
        this.log('Error Handling', '500 Error Responses', 'VERIFIED', 'Proper 500 error responses');
      }
      
    } catch (error) {
      this.log('Error Handling', 'Error Check', 'ERROR', error.message);
    }
  }

  checkAPIEndpoints() {
    console.log('\n🔗 CHECKING API ENDPOINTS...');
    
    try {
      const routesFile = fs.readFileSync('server/routes.ts', 'utf8');
      
      const endpoints = [
        '/api/login',
        '/api/user',
        '/api/jobs',
        '/api/matches',
        '/api/profile',
        '/api/path-coach/chat',
        '/api/resumes/upload'
      ];
      
      endpoints.forEach(endpoint => {
        if (routesFile.includes(`"${endpoint}"`)) {
          this.log('API', endpoint, 'VERIFIED', 'Endpoint implemented');
        } else {
          this.log('API', endpoint, 'MISSING', 'Endpoint not found');
        }
      });
      
    } catch (error) {
      this.log('API', 'Endpoint Check', 'ERROR', error.message);
    }
  }

  checkFrontendOptimizations() {
    console.log('\n🎨 CHECKING FRONTEND OPTIMIZATIONS...');
    
    try {
      // Check if build directory exists
      if (fs.existsSync('dist')) {
        this.log('Frontend', 'Build Directory', 'VERIFIED', 'Build directory exists');
        
        // Check for optimized assets
        const distFiles = fs.readdirSync('dist');
        
        if (distFiles.some(file => file.includes('.js'))) {
          this.log('Frontend', 'JavaScript Bundle', 'VERIFIED', 'JavaScript bundle created');
        }
        
        if (distFiles.some(file => file.includes('.css'))) {
          this.log('Frontend', 'CSS Bundle', 'VERIFIED', 'CSS bundle created');
        }
        
        if (distFiles.includes('index.html')) {
          this.log('Frontend', 'HTML Entry Point', 'VERIFIED', 'HTML entry point exists');
        }
      } else {
        this.log('Frontend', 'Build Directory', 'MISSING', 'Run npm run build to create optimized build');
      }
      
      // Check Vite configuration
      if (fs.existsSync('vite.config.ts')) {
        this.log('Frontend', 'Vite Configuration', 'VERIFIED', 'Vite properly configured');
      }
      
    } catch (error) {
      this.log('Frontend', 'Frontend Check', 'ERROR', error.message);
    }
  }

  generateOptimizationReport() {
    const totalChecks = this.optimizations.length + this.issues.length;
    const successRate = (this.optimizations.length / totalChecks) * 100;
    
    console.log('\n📊 PRODUCTION READINESS REPORT:');
    console.log('='.repeat(60));
    console.log(`✅ OPTIMIZED/VERIFIED: ${this.optimizations.length}`);
    console.log(`❌ ISSUES FOUND: ${this.issues.length}`);
    console.log(`📈 PRODUCTION READINESS: ${successRate.toFixed(1)}%`);
    console.log('='.repeat(60));
    
    if (this.issues.length > 0) {
      console.log('\n🔧 ISSUES TO ADDRESS:');
      this.issues.forEach(issue => {
        console.log(`   • ${issue.category}: ${issue.item} - ${issue.details}`);
      });
    }
    
    console.log('\n🎯 RECOMMENDATIONS:');
    if (successRate >= 90) {
      console.log('   ✅ Application is production-ready!');
      console.log('   ✅ All critical systems are optimized');
      console.log('   ✅ Security measures are in place');
      console.log('   ✅ Performance is optimized');
    } else if (successRate >= 75) {
      console.log('   ⚠️  Application is mostly ready for production');
      console.log('   🔧 Address the issues listed above');
      console.log('   📈 Consider additional optimizations');
    } else {
      console.log('   ❌ Application needs more work before production');
      console.log('   🔧 Critical issues must be resolved');
      console.log('   📋 Review all failed checks');
    }
    
    // Save detailed report
    const report = {
      totalChecks,
      optimizations: this.optimizations,
      issues: this.issues,
      successRate,
      timestamp: new Date().toISOString()
    };
    
    fs.writeFileSync('production-readiness-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed report saved to production-readiness-report.json');
    
    return report;
  }

  runAllChecks() {
    console.log('🚀 STARTING PRODUCTION READINESS CHECK...\n');
    
    this.checkEnvironmentVariables();
    this.checkDatabaseConfiguration();
    this.checkSecurityFeatures();
    this.checkPerformanceOptimizations();
    this.checkErrorHandling();
    this.checkAPIEndpoints();
    this.checkFrontendOptimizations();
    
    return this.generateOptimizationReport();
  }
}

// Run production optimization check
const optimizer = new ProductionOptimizer();
optimizer.runAllChecks();
