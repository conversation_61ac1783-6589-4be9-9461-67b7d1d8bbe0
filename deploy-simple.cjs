#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 PathLink Simple AWS Deployment');
console.log('==================================\n');

function deployToVercel() {
  console.log('🌐 Deploying to Vercel (Alternative to AWS)...');
  try {
    // Install Vercel CLI if not present
    try {
      execSync('vercel --version', { stdio: 'pipe' });
    } catch (e) {
      console.log('📦 Installing Vercel CLI...');
      execSync('npm install -g vercel', { stdio: 'inherit' });
    }

    // Build the application
    console.log('🔨 Building application...');
    execSync('npm run build', { stdio: 'inherit' });

    // Deploy to Vercel
    console.log('🚀 Deploying to Vercel...');
    execSync('vercel --prod', { stdio: 'inherit' });

    console.log('✅ Deployed to Vercel successfully!');
    return true;
  } catch (error) {
    console.log('❌ Vercel deployment failed:', error.message);
    return false;
  }
}

function deployToRender() {
  console.log('🌐 Alternative: Deploy to Render.com');
  console.log('1. Go to https://render.com');
  console.log('2. Connect your GitHub repository');
  console.log('3. Create a new Web Service');
  console.log('4. Use these settings:');
  console.log('   - Build Command: npm run build');
  console.log('   - Start Command: npm start');
  console.log('   - Environment: Node.js');
  console.log('5. Add environment variables:');
  console.log('   - NODE_ENV=production');
  console.log('   - DATABASE_URL=your-neon-url');
  console.log('   - PORT=10000');
}

function deployToHeroku() {
  console.log('🌐 Alternative: Deploy to Heroku');
  try {
    // Check if Heroku CLI is installed
    try {
      execSync('heroku --version', { stdio: 'pipe' });
    } catch (e) {
      console.log('❌ Heroku CLI not installed');
      console.log('Install from: https://devcenter.heroku.com/articles/heroku-cli');
      return false;
    }

    // Create Heroku app
    console.log('🏗️ Creating Heroku app...');
    try {
      execSync('heroku create pathlink-app', { stdio: 'inherit' });
    } catch (e) {
      console.log('⚠️ App might already exist, continuing...');
    }

    // Set environment variables
    console.log('⚙️ Setting environment variables...');
    execSync('heroku config:set NODE_ENV=production', { stdio: 'inherit' });
    execSync(`heroku config:set DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"`, { stdio: 'inherit' });

    // Deploy
    console.log('🚀 Deploying to Heroku...');
    execSync('git add .', { stdio: 'inherit' });
    execSync('git commit -m "Deploy to Heroku" || true', { stdio: 'inherit' });
    execSync('git push heroku main || git push heroku master', { stdio: 'inherit' });

    console.log('✅ Deployed to Heroku successfully!');
    return true;
  } catch (error) {
    console.log('❌ Heroku deployment failed:', error.message);
    return false;
  }
}

function showNetlifyInstructions() {
  console.log('🌐 Deploy to Netlify (Recommended)');
  console.log('===================================');
  console.log('1. Push your code to GitHub');
  console.log('2. Go to https://netlify.com');
  console.log('3. Click "New site from Git"');
  console.log('4. Connect your GitHub repository');
  console.log('5. Use these build settings:');
  console.log('   - Build command: npm run build');
  console.log('   - Publish directory: dist/public');
  console.log('6. Add environment variables in Netlify dashboard:');
  console.log('   - NODE_ENV=production');
  console.log('   - DATABASE_URL=your-neon-url');
  console.log('7. Deploy!');
  console.log('\n✅ Your netlify.toml is already configured for this!');
}

function main() {
  console.log('AWS deployment had permission issues.');
  console.log('Let me show you alternative deployment options:\n');

  console.log('🎯 RECOMMENDED DEPLOYMENT OPTIONS:');
  console.log('==================================\n');

  console.log('1. 🌐 NETLIFY (Easiest - Recommended)');
  showNetlifyInstructions();

  console.log('\n2. 🚀 VERCEL (Good alternative)');
  console.log('   - Run: npm install -g vercel');
  console.log('   - Run: vercel --prod');
  console.log('   - Follow the prompts');

  console.log('\n3. 🎨 RENDER.COM (Free tier available)');
  deployToRender();

  console.log('\n4. 💜 HEROKU (Classic choice)');
  console.log('   - Install Heroku CLI');
  console.log('   - Run this script again');

  console.log('\n🎯 QUICK NETLIFY DEPLOYMENT:');
  console.log('============================');
  console.log('Your project is already configured for Netlify!');
  console.log('Just push to GitHub and connect to Netlify.');
  console.log('The netlify.toml file will handle everything.');

  console.log('\n📋 WHAT YOU HAVE:');
  console.log('✅ Working application');
  console.log('✅ Database (Neon) configured');
  console.log('✅ Build scripts ready');
  console.log('✅ Netlify configuration file');
  console.log('✅ All dependencies installed');

  console.log('\n🚀 NEXT STEPS:');
  console.log('1. Push your code to GitHub');
  console.log('2. Deploy to Netlify (easiest)');
  console.log('3. Your app will be live!');

  console.log('\n💡 TIP: Netlify is perfect for your PathLink app!');
  console.log('It handles both frontend and serverless functions.');
}

if (require.main === module) {
  main();
}

module.exports = { deployToVercel, deployToHeroku, showNetlifyInstructions };
