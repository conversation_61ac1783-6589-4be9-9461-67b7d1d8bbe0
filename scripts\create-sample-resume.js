/**
 * This script creates a sample resume file in the uploads/resumes directory
 * for testing purposes.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Sample resume content
const sampleResumeContent = `
# John Smith
Email: <EMAIL>

## Skills
JavaScript, React, Node.js, TypeScript, MongoDB

## Experience
5 years of frontend development experience at tech startups

## Education
Bachelor's in Computer Science, Stanford University

## Industry
Technology

## Additional Information
Passionate professional with a strong background in technology.
Experienced in JavaScript, React, Node.js, and other technologies.
Looking for opportunities to leverage my expertise in JavaScript and React.

References available upon request.
`;

// Create the uploads/resumes directory if it doesn't exist
const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');
if (!fs.existsSync(resumesDir)) {
  fs.mkdirSync(resumesDir, { recursive: true });
}

// Create a sample text file (since we can't easily create a PDF programmatically)
const sampleFilePath = path.join(resumesDir, 'sample_resume.txt');
fs.writeFileSync(sampleFilePath, sampleResumeContent);

console.log(`Created sample resume at: ${sampleFilePath}`);
console.log('You can now run the import-resumes script to import this file into the database.');
