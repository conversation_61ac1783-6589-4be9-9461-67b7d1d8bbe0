import { useState } from "react";
import { Helmet } from "react-helmet-async";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Check, HelpCircle } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from "@/hooks/use-auth";
import { useLocation, Link } from "wouter";
import SharedNavbar from "@/components/shared-navbar";

// Define pricing tiers
const pricingTiers = {
  monthly: [
    {
      name: "Basic",
      description: "Essential features for small businesses",
      price: 49,
      features: [
        "Up to 2 job listings",
        "Basic candidate matching",
        "Email support",
        "Resume parsing",
        "Basic analytics"
      ],
      limitations: [
        "No API access",
        "No custom branding",
        "Limited integrations"
      ],
      cta: "Start Basic",
      popular: false
    },
    {
      name: "Professional",
      description: "Advanced features for growing teams",
      price: 199,
      features: [
        "Up to 4 job listings",
        "Advanced candidate matching",
        "Priority email support",
        "Resume parsing & analysis",
        "Comprehensive analytics",
        "API access (limited)",
        "Basic integrations"
      ],
      limitations: [
        "Limited custom branding"
      ],
      cta: "Start Professional",
      popular: true
    },
    {
      name: "Enterprise",
      description: "Custom solutions for large organizations",
      price: 599,
      features: [
        "Unlimited job listings",
        "AI-powered candidate matching",
        "24/7 priority support",
        "Advanced resume analysis",
        "Custom analytics & reporting",
        "Full API access",
        "Custom branding",
        "Advanced integrations",
        "Dedicated account manager"
      ],
      limitations: [],
      cta: "Contact Sales",
      popular: false
    }
  ],
  annual: [
    {
      name: "Basic",
      description: "Essential features for small businesses",
      price: 39,
      features: [
        "Up to 2 job listings",
        "Basic candidate matching",
        "Email support",
        "Resume parsing",
        "Basic analytics"
      ],
      limitations: [
        "No API access",
        "No custom branding",
        "Limited integrations"
      ],
      cta: "Start Basic",
      popular: false
    },
    {
      name: "Professional",
      description: "Advanced features for growing teams",
      price: 159,
      features: [
        "Up to 4 job listings",
        "Advanced candidate matching",
        "Priority email support",
        "Resume parsing & analysis",
        "Comprehensive analytics",
        "API access (limited)",
        "Basic integrations"
      ],
      limitations: [
        "Limited custom branding"
      ],
      cta: "Start Professional",
      popular: true
    },
    {
      name: "Enterprise",
      description: "Custom solutions for large organizations",
      price: 479,
      features: [
        "Unlimited job listings",
        "AI-powered candidate matching",
        "24/7 priority support",
        "Advanced resume analysis",
        "Custom analytics & reporting",
        "Full API access",
        "Custom branding",
        "Advanced integrations",
        "Dedicated account manager"
      ],
      limitations: [],
      cta: "Contact Sales",
      popular: false
    }
  ]
};

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");
  const { user } = useAuth();
  const [, navigate] = useLocation();

  const handlePlanSelection = (planName: string) => {
    if (!user) {
      // Redirect to login if not authenticated
      navigate("/login?redirect=pricing&plan=" + planName.toLowerCase());
      return;
    }

    // Handle plan selection for authenticated users
    // This would typically redirect to a checkout page or show a modal
    console.log(`Selected plan: ${planName}, billing cycle: ${billingCycle}`);
    // For now, we'll just alert the user
    alert(`Thank you for selecting the ${planName} plan! Our team will contact you shortly.`);
  };

  const [location] = useLocation();

  return (
    <>
      <Helmet>
        <title>Pricing | PathLink</title>
      </Helmet>

      {/* Navigation Bar */}
      <SharedNavbar />

      <div className="container mx-auto py-12 px-4 md:px-6">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-[#1C2A42] mb-4">Simple, Transparent Pricing</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Choose the plan that works best for your organization's needs. All plans include our core matching technology.
          </p>

          <div className="flex items-center justify-center mt-8 space-x-2">
            <Label htmlFor="billing-toggle" className={`text-sm font-medium ${billingCycle === "monthly" ? "text-[#1C2A42]" : "text-gray-500"}`}>
              Monthly
            </Label>
            <Switch
              id="billing-toggle"
              checked={billingCycle === "annual"}
              onCheckedChange={(checked) => setBillingCycle(checked ? "annual" : "monthly")}
              className="data-[state=checked]:bg-[#1C2A42]"
            />
            <Label htmlFor="billing-toggle" className={`text-sm font-medium ${billingCycle === "annual" ? "text-[#1C2A42]" : "text-gray-500"}`}>
              Annual <span className="text-green-600 font-semibold">(Save 20%)</span>
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {pricingTiers[billingCycle].map((tier, index) => (
            <Card
              key={tier.name}
              className={`relative overflow-hidden ${tier.popular ? 'border-[#1C2A42] shadow-lg' : 'border-gray-200'}`}
            >
              {tier.popular && (
                <div className="absolute top-0 right-0 bg-[#1C2A42] text-white px-4 py-1 text-sm font-medium rounded-bl-lg">
                  Most Popular
                </div>
              )}
              <CardHeader className={`pb-8 ${tier.popular ? 'bg-[#E5DEFF]/30' : ''}`}>
                <CardTitle className="text-2xl font-bold">{tier.name}</CardTitle>
                <CardDescription className="text-gray-600 mt-2">{tier.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold text-[#1C2A42]">${tier.price}</span>
                  <span className="text-gray-600 ml-2">/ month</span>
                  {billingCycle === "annual" && (
                    <div className="text-green-600 text-sm font-medium mt-1">Billed annually (${tier.price * 12}/year)</div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Features</h4>
                  <ul className="space-y-3">
                    {tier.features.map((feature, i) => (
                      <li key={i} className="flex items-start">
                        <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {tier.limitations.length > 0 && (
                  <div>
                    <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                      Limitations
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-gray-400 ml-1" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="w-[200px] text-sm">These features are not included in this plan.</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </h4>
                    <ul className="space-y-3">
                      {tier.limitations.map((limitation, i) => (
                        <li key={i} className="flex items-start text-gray-500">
                          <span className="mr-2">•</span>
                          <span>{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button
                  className={`w-full ${tier.popular ? 'bg-[#1C2A42] hover:bg-[#1C2A42]/90' : 'bg-gray-800 hover:bg-gray-700'}`}
                  onClick={() => handlePlanSelection(tier.name)}
                >
                  {tier.cta}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold text-[#1C2A42] mb-4">Need a custom solution?</h2>
          <p className="text-gray-600 max-w-2xl mx-auto mb-6">
            We offer tailored solutions for organizations with specific requirements. Contact our sales team to discuss your needs.
          </p>
          <Button
            variant="outline"
            className="border-[#1C2A42] text-[#1C2A42] hover:bg-[#1C2A42] hover:text-white"
            onClick={() => navigate("/contact")}
          >
            Contact Sales
          </Button>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-[#1C2A42] dark:bg-gray-800 text-white p-8 mt-16">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-bold mb-4">PathLink</h3>
              <p className="text-gray-300">
                AI-powered labor redistribution platform transforming workforce mobility.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><Link href="/welcome"><span className="text-gray-300 hover:text-white cursor-pointer">Welcome</span></Link></li>
                <li><Link href="/how-it-works"><span className="text-gray-300 hover:text-white cursor-pointer">How It Works</span></Link></li>
                <li><Link href="/privacy-policy"><span className="text-gray-300 hover:text-white cursor-pointer">Privacy Policy</span></Link></li>
                <li><Link href="/pricing"><span className="text-gray-300 hover:text-white cursor-pointer">Pricing</span></Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Contact</h3>
              <p className="text-gray-300">
                Have questions? <br />
                <a href="mailto:<EMAIL>" className="text-white hover:underline"><EMAIL></a>
              </p>
            </div>
          </div>
          <div className="border-t border-gray-700 pt-8 text-center text-gray-400 text-sm">
            <p>&copy; {new Date().getFullYear()} PathLink. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </>
  );
}
