import fs from 'fs';
import { promisify } from 'util';
import mammoth from 'mammoth';
import { extractTextFromDocx } from './text-extractor';

const readFileAsync = promisify(fs.readFile);

/**
 * Convert DOCX to HTML for browser viewing using mammoth.js
 */
export async function convertDocxToHtml(filePath: string): Promise<string> {
  try {
    // Read the file
    const buffer = await readFileAsync(filePath);

    // Use mammoth.js to convert DOCX to HTML with styling
    const result = await mammoth.convertToHtml({ buffer });
    const htmlBody = result.value;

    // Create a full HTML document with styling
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Document Viewer</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            color: #333;
            background-color: #f9f9f9;
          }
          .document-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            min-height: 100vh;
          }
          h1, h2, h3, h4, h5, h6 {
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            color: #2c3e50;
          }
          p {
            margin-bottom: 1em;
          }
          table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 1em;
          }
          th, td {
            border: 1px solid #ddd;
            padding: 8px;
          }
          th {
            background-color: #f2f2f2;
            text-align: left;
          }
          ul, ol {
            margin-bottom: 1em;
          }
          img {
            max-width: 100%;
            height: auto;
          }
          a {
            color: #3498db;
            text-decoration: none;
          }
          a:hover {
            text-decoration: underline;
          }
          /* PDF-like page styling */
          @media print {
            body {
              background-color: white;
            }
            .document-container {
              box-shadow: none;
              padding: 0;
            }
          }
        </style>
      </head>
      <body>
        <div class="document-container">
          ${htmlBody}
        </div>
      </body>
      </html>
    `;

    return htmlContent;
  } catch (error) {
    console.error('Error converting DOCX to HTML with mammoth:', error);

    // Fallback to our text extraction method if mammoth fails
    try {
      const extractedText = await extractTextFromDocx(filePath);

      // Convert the extracted text to simple HTML
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Document Viewer</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
              color: #333;
              background-color: #f9f9f9;
            }
            .document-container {
              max-width: 800px;
              margin: 0 auto;
              padding: 40px;
              background-color: white;
              box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
              min-height: 100vh;
            }
            p {
              margin-bottom: 1em;
            }
            .fallback-notice {
              background-color: #fff8e1;
              padding: 10px;
              border-left: 4px solid #ffc107;
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="document-container">
            <div class="fallback-notice">
              <p><strong>Note:</strong> Using simplified view. Some formatting may be lost.</p>
            </div>
            ${extractedText.split('\n').map(line => `<p>${line}</p>`).join('')}
          </div>
        </body>
        </html>
      `;

      return htmlContent;
    } catch (fallbackError) {
      console.error('Fallback extraction also failed:', fallbackError);

      // Return error page if both methods fail
      return `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Document Viewer</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              margin: 20px;
              color: #333;
              text-align: center;
              background-color: #f9f9f9;
            }
            .error-container {
              max-width: 600px;
              margin: 100px auto;
              padding: 40px;
              background-color: white;
              box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
              border-top: 4px solid #e74c3c;
            }
            h1 {
              color: #e74c3c;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <h1>Error Viewing Document</h1>
            <p>This document could not be displayed in the browser. Please download it to view.</p>
          </div>
        </body>
        </html>
      `;
    }
  }
}
