import type { Express, Request, Response } from "express";
import { createServer, type Server } from "http";
import { storage, MemStorage } from "./storage";
import { setupAuth } from "./auth";
import { cache } from "./cache";
import { pool } from "./db";
import { sendPasswordResetEmail, sendWelcomeEmail } from "./email";
import {
  insertJobSchema,
  insertSurplusEmployeeSchema,
  insertMatchSchema,
  insertResumeSchema
} from "@shared/schema";
import { z } from "zod";
import { comparePasswords, hashPassword } from "./auth";
import multer from "multer";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { promisify } from "util";
import { analyzeResume, generateReskillingPath } from "./openai-safe";
import {
  extractSkillsFromJob,
  extractSkillsFromResume,
  extractEducationFromJob,
  extractEducationFromResume,
  extractExperienceFromJob,
  extractExperienceFromResume,
  calculateSkillsMatch,
  calculateExperienceMatch,
  calculateEducationMatch,
  calculateIndustryMatch,
  calculateOverallScore,
  identifyAdditionalStrengths,
  identifyDevelopmentAreas
} from "./match-analysis";
import * as crypto from "crypto";

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configure multer for resume uploads
const resumeStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create directory if it doesn't exist
    // Use absolute path to ensure directory is created in the correct location
    const dir = path.join(__dirname, '..', 'uploads', 'resumes');
    console.log('Creating resume upload directory:', dir);

    try {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log('Created resume upload directory:', dir);
      } else {
        console.log('Resume upload directory already exists');
      }
      cb(null, dir);
    } catch (error) {
      console.error('Error creating resume upload directory:', error);
      // Still try to use the relative path as fallback
      const fallbackDir = 'uploads/resumes';
      if (!fs.existsSync(fallbackDir)) {
        fs.mkdirSync(fallbackDir, { recursive: true });
      }
      cb(null, fallbackDir);
    }
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    const filename = `resume-${uniqueSuffix}${extension}`;
    console.log('Generated resume filename:', filename);
    cb(null, filename);
  }
});

// Configure multer for profile picture uploads
const profilePictureStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Create directory if it doesn't exist
    const dir = 'uploads/profile';
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, `profile-${uniqueSuffix}${extension}`);
  }
});

// Validate resume file type and size
const resumeFileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept only PDF and DOCX files
  if (file.mimetype === 'application/pdf' ||
      file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    cb(null, true);
  } else {
    cb(new Error('Only PDF and DOCX files are allowed'));
  }
};

// Validate profile picture file type and size
const imageFileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Accept only image files
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed'));
  }
};

// Configure upload with 25MB limit for resumes
const upload = multer({
  storage: resumeStorage,
  fileFilter: resumeFileFilter,
  limits: { fileSize: 25 * 1024 * 1024 } // 25MB
});

// Configure upload with 5MB limit for profile pictures
const uploadProfilePicture = multer({
  storage: profilePictureStorage,
  fileFilter: imageFileFilter,
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB
});

// Helper to get file type from extension
function getFileType(filename: string): 'pdf' | 'docx' {
  const ext = path.extname(filename).toLowerCase();
  return ext === '.pdf' ? 'pdf' : 'docx';
}

// Helper function to convert absolute path to relative path
function getRelativePath(absolutePath: string): string {
  return path.relative(process.cwd(), absolutePath).replace(/\\/g, '/');
}

// Helper function to convert relative path to absolute path
function getAbsolutePath(relativePath: string): string {
  return path.join(process.cwd(), relativePath);
}

// Enhanced function to get comprehensive user context with candidate information
async function getUserContext(userId: number, userType: string) {
  try {
    const context: any = {
      profile: null,
      resume: null,
      jobs: null,
      applications: null,
      matches: null,
      potential_candidates: null
    };

    console.log(`🔍 Getting context for user ${userId} (${userType})`);

    // Get comprehensive user profile
    try {
      const user = await storage.getUser(userId);
      if (user) {
        context.profile = {
          name: user.name,
          email: user.email,
          role: user.role,
          position: user.position || 'Not specified',
          location: user.location || 'Not specified',
          company: user.company || 'Not specified',
          years_of_experience: user.years_of_experience || 'Not specified',
          skills: user.skills || 'Not specified',
          bio: user.bio || 'Not specified',
          created_at: user.created_at
        };
        console.log(`✅ Profile found for ${user.name}`);
      }
    } catch (error) {
      console.log('❌ No profile found for user:', userId);
    }

    if (userType === 'worker') {
      // Get ALL resumes for the worker
      try {
        const resumes = await storage.getResumesByWorkerId(userId);
        if (resumes && resumes.length > 0) {
          context.resume = {
            count: resumes.length,
            latest: resumes[0], // Most recent resume
            all: resumes.map(r => ({
              id: r.id,
              filename: r.filename,
              created_at: r.created_at,
              extracted_text: r.extracted_text ? r.extracted_text.substring(0, 1500) : 'No content extracted'
            }))
          };
          console.log(`✅ Found ${resumes.length} resume(s) for worker`);
        }
      } catch (error) {
        console.log('❌ No resume found for worker:', userId);
      }

      // Get worker's job applications
      try {
        const matches = await storage.getMatchesByWorkerId(userId);
        if (matches && matches.length > 0) {
          context.applications = {
            count: matches.length,
            recent: matches.slice(0, 5),
            status_summary: {
              pending: matches.filter(m => m.status === 'pending').length,
              accepted: matches.filter(m => m.status === 'accepted').length,
              rejected: matches.filter(m => m.status === 'rejected').length
            }
          };
          console.log(`✅ Found ${matches.length} application(s) for worker`);
        }
      } catch (error) {
        console.log('❌ No applications found for worker:', userId);
      }

    } else if (userType === 'employer') {
      // Get employer's jobs with detailed info
      try {
        const jobs = await storage.getJobsByEmployer(userId);
        if (jobs && jobs.length > 0) {
          context.jobs = {
            count: jobs.length,
            active: jobs.filter(j => j.status !== 'closed'),
            recent: jobs.slice(0, 5),
            industries: [...new Set(jobs.map(j => j.industry).filter(Boolean))],
            summary: jobs.map(j => ({
              id: j.id,
              title: j.title,
              description: j.description.substring(0, 300) + '...',
              required_skills: j.required_skills,
              location: j.location,
              created_at: j.created_at
            }))
          };
          console.log(`✅ Found ${jobs.length} job(s) for employer`);
        }
      } catch (error) {
        console.log('❌ No jobs found for employer:', userId);
      }

      // Get applications to employer's jobs with detailed candidate information
      try {
        const allMatches = await storage.getMatches();
        const employerJobs = await storage.getJobsByEmployer(userId);
        const jobIds = employerJobs.map(job => job.id);
        const employerMatches = allMatches.filter(match => jobIds.includes(match.job_id));

        if (employerMatches.length > 0) {
          // Get detailed candidate information for each application
          const candidatesWithDetails = await Promise.all(
            employerMatches.map(async (match) => {
              try {
                // Get candidate profile
                const candidate = await storage.getUser(match.worker_id);

                // Get candidate's resume
                let candidateResume = null;
                try {
                  const resumes = await storage.getResumesByWorkerId(match.worker_id);
                  if (resumes && resumes.length > 0) {
                    candidateResume = {
                      filename: resumes[0].filename,
                      created_at: resumes[0].created_at,
                      extracted_text: resumes[0].extracted_text ? resumes[0].extracted_text.substring(0, 2000) : 'No content extracted'
                    };
                  }
                } catch (resumeError) {
                  console.log(`❌ No resume found for candidate ${match.worker_id}`);
                }

                // Get the job details for this application
                const job = employerJobs.find(j => j.id === match.job_id);

                return {
                  application_id: match.id,
                  status: match.status,
                  applied_at: match.created_at,
                  job_title: job?.title || 'Unknown Job',
                  job_id: match.job_id,
                  candidate: {
                    id: candidate?.id,
                    name: candidate?.name || 'Unknown Candidate',
                    email: candidate?.email,
                    position: candidate?.position || 'Not specified',
                    location: candidate?.location || 'Not specified',
                    company: candidate?.company || 'Not specified',
                    years_of_experience: candidate?.years_of_experience || 'Not specified',
                    skills: candidate?.skills || 'Not specified',
                    bio: candidate?.bio || 'Not specified'
                  },
                  resume: candidateResume
                };
              } catch (candidateError) {
                console.log(`❌ Error getting candidate details for match ${match.id}:`, candidateError);
                return {
                  application_id: match.id,
                  status: match.status,
                  applied_at: match.created_at,
                  job_title: 'Unknown Job',
                  candidate: { name: 'Unknown Candidate' },
                  resume: null
                };
              }
            })
          );

          context.matches = {
            count: employerMatches.length,
            recent: candidatesWithDetails.slice(0, 10),
            status_summary: {
              pending: employerMatches.filter(m => m.status === 'pending').length,
              accepted: employerMatches.filter(m => m.status === 'accepted').length,
              rejected: employerMatches.filter(m => m.status === 'rejected').length
            },
            candidates_by_job: candidatesWithDetails.reduce((acc, candidate) => {
              const jobTitle = candidate.job_title;
              if (!acc[jobTitle]) acc[jobTitle] = [];
              acc[jobTitle].push(candidate);
              return acc;
            }, {} as Record<string, any[]>)
          };
          console.log(`✅ Found ${employerMatches.length} application(s) with detailed candidate info`);
        }
      } catch (error) {
        console.log('❌ No applications found for employer jobs:', userId);
      }
    }

    console.log(`📊 Context summary: Profile=${!!context.profile}, Resume=${!!context.resume}, Jobs=${!!context.jobs}, Candidates=${!!context.matches}`);
    return context;
  } catch (error) {
    console.error('❌ Error getting user context:', error);
    return { profile: null, resume: null, jobs: null, applications: null, matches: null, potential_candidates: null };
  }
}

// Enhanced chat prompt with comprehensive user context including candidate information
function createChatPromptWithContext(message: string, userType: string, conversationHistory: string, userContext: any): string {
  const role = userType === 'worker' ? 'job seeker/professional' : 'employer/hiring manager';

  let prompt = `You are PathLink AI Coach, a specialized career guidance assistant. You ONLY provide advice related to:
- Career development and job searching (for workers)
- Hiring strategies and talent acquisition (for employers)
- Professional skills development
- Workplace dynamics and team building
- Interview preparation and recruitment processes

STRICT RESTRICTIONS: You must NOT provide advice on topics outside of career, hiring, and professional development. If asked about unrelated topics, politely redirect to career-focused guidance.

You are having a conversation with ${userContext.profile?.name || 'a user'}, a ${role}. Use the following comprehensive context to provide highly personalized advice:

USER PROFILE:`;

  // Add detailed profile information
  if (userContext.profile) {
    prompt += `
- Name: ${userContext.profile.name}
- Role: ${userContext.profile.role}
- Position: ${userContext.profile.position}
- Location: ${userContext.profile.location}
- Company: ${userContext.profile.company}
- Years of Experience: ${userContext.profile.years_of_experience}
- Skills: ${userContext.profile.skills}
- Bio: ${userContext.profile.bio}
- Member since: ${userContext.profile.created_at}`;
  }

  // Add comprehensive resume information for workers
  if (userType === 'worker' && userContext.resume) {
    prompt += `\n\nRESUME INFORMATION:
- Total resumes uploaded: ${userContext.resume.count}
- Latest resume: ${userContext.resume.latest.filename} (uploaded ${userContext.resume.latest.created_at})`;

    if (userContext.resume.latest.extracted_text) {
      prompt += `\n- Resume content preview:\n${userContext.resume.latest.extracted_text}`;
    }

    if (userContext.resume.count > 1) {
      prompt += `\n- Previous resumes: ${userContext.resume.all.slice(1).map(r => r.filename).join(', ')}`;
    }
  }

  // Add job application history for workers
  if (userType === 'worker' && userContext.applications) {
    prompt += `\n\nJOB APPLICATION HISTORY:
- Total applications: ${userContext.applications.count}
- Pending: ${userContext.applications.status_summary.pending}
- Accepted: ${userContext.applications.status_summary.accepted}
- Rejected: ${userContext.applications.status_summary.rejected}`;
  }

  // Add comprehensive job information for employers
  if (userType === 'employer' && userContext.jobs) {
    prompt += `\n\nJOB POSTINGS & HIRING ACTIVITY:
- Total jobs posted: ${userContext.jobs.count}
- Active jobs: ${userContext.jobs.active.length}
- Industries: ${userContext.jobs.industries.join(', ')}

Recent Job Postings:`;
    userContext.jobs.summary.slice(0, 3).forEach((job: any, index: number) => {
      prompt += `\n${index + 1}. ${job.title} (${job.location})
   - Required skills: ${job.required_skills}
   - Description: ${job.description}
   - Posted: ${job.created_at}`;
    });
  }

  // Add detailed candidate information for employers
  if (userType === 'employer' && userContext.matches) {
    prompt += `\n\nCANDIDATE APPLICATIONS & RESUMES:
- Total applications received: ${userContext.matches.count}
- Pending review: ${userContext.matches.status_summary.pending}
- Accepted candidates: ${userContext.matches.status_summary.accepted}
- Rejected applications: ${userContext.matches.status_summary.rejected}

DETAILED CANDIDATE INFORMATION:`;

    userContext.matches.recent.slice(0, 5).forEach((application: any, index: number) => {
      prompt += `\n\n${index + 1}. ${application.candidate.name} - Applied for ${application.job_title}
   - Status: ${application.status}
   - Applied: ${application.applied_at}
   - Position: ${application.candidate.position}
   - Location: ${application.candidate.location}
   - Experience: ${application.candidate.years_of_experience} years
   - Skills: ${application.candidate.skills}
   - Bio: ${application.candidate.bio}`;

      if (application.resume) {
        prompt += `\n   - Resume: ${application.resume.filename} (uploaded ${application.resume.created_at})
   - Resume Content: ${application.resume.extracted_text.substring(0, 800)}...`;
      }
    });

    // Add candidates by job breakdown
    if (userContext.matches.candidates_by_job) {
      prompt += `\n\nCANDIDATES BY JOB:`;
      Object.entries(userContext.matches.candidates_by_job).forEach(([jobTitle, candidates]: [string, any[]]) => {
        prompt += `\n- ${jobTitle}: ${candidates.length} candidate(s)`;
        candidates.slice(0, 2).forEach((candidate: any) => {
          prompt += `\n  • ${candidate.candidate.name} (${candidate.candidate.position})`;
        });
      });
    }
  }

  prompt += `\n\nCONVERSATION HISTORY:
${conversationHistory}

CURRENT MESSAGE: ${message}

Based on ${userContext.profile?.name || 'the user'}'s specific profile, experience, and context above, provide highly personalized, actionable advice. When discussing candidates, reference their actual names, resumes, and qualifications. For hiring decisions, analyze the specific candidate information provided. Keep responses helpful and concise (2-3 paragraphs max). Be encouraging and professional.`;

  return prompt;
}

// Helper function to generate chat response with OpenAI
async function generateChatResponse(prompt: string): Promise<string> {
  try {
    // Use the actual OpenAI API key from environment
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      console.log('🤖 No valid OpenAI API key found, using demo response');
      return generateDemoChatResponse(prompt);
    }

    console.log('🤖 Using OpenAI API for chat response');

    // Try GPT-3.5-turbo first (more accessible and cost-effective)
    let model = 'gpt-3.5-turbo';
    let response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: 'You are PathLink AI Coach, a specialized career and hiring guidance assistant. You ONLY provide advice related to career development, job searching, hiring strategies, professional skills, and workplace topics. Keep responses helpful, concise, and actionable. Be encouraging and professional. Always reference the user\'s specific context when providing advice.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 1200,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`OpenAI API error: ${response.status} - ${errorText}`);
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'I apologize, but I\'m having trouble responding right now. Please try again.';
  } catch (error) {
    console.error('Error calling OpenAI API:', error);
    // Fallback to demo response
    return generateDemoChatResponse(prompt);
  }
}

// Enhanced demo chat response generator with candidate-specific analysis
function generateDemoChatResponse(prompt: string): string {
  const isEmployer = prompt.includes('employer') || prompt.includes('hiring');
  const message = prompt.split('CURRENT MESSAGE:')[1]?.trim() || '';
  const userName = prompt.match(/Name: ([^\n]+)/)?.[1] || 'there';

  // Extract candidate information from prompt
  const candidateNames = [];
  const candidateDetails = [];

  // Look for detailed candidate information in the prompt
  const candidateMatches = prompt.match(/(\d+\.\s+([^-]+)\s+-\s+Applied for)/g);
  if (candidateMatches) {
    candidateMatches.forEach(match => {
      const name = match.match(/\d+\.\s+([^-]+)\s+-/)?.[1]?.trim();
      if (name) candidateNames.push(name);
    });
  }

  // Extract detailed candidate information including resumes
  const candidateDetailRegex = /(\d+\.\s+([^-]+)\s+-\s+Applied for[^]*?)(?=\d+\.|CANDIDATES BY JOB:|CONVERSATION HISTORY:|$)/g;
  let match;
  while ((match = candidateDetailRegex.exec(prompt)) !== null) {
    const candidateBlock = match[1];
    const name = match[2]?.trim();

    // Extract specific details from the candidate block
    const positionMatch = candidateBlock.match(/Position:\s*([^\n]+)/);
    const skillsMatch = candidateBlock.match(/Skills:\s*([^\n]+)/);
    const experienceMatch = candidateBlock.match(/Experience:\s*([^\n]+)/);
    const resumeMatch = candidateBlock.match(/Resume Content:\s*([^]*?)(?=\n\s*\d+\.|$)/);
    const statusMatch = candidateBlock.match(/Status:\s*([^\n]+)/);
    const jobMatch = candidateBlock.match(/Applied for\s*([^\n]+)/);

    candidateDetails.push({
      name: name,
      position: positionMatch?.[1]?.trim() || 'Not specified',
      skills: skillsMatch?.[1]?.trim() || 'Not specified',
      experience: experienceMatch?.[1]?.trim() || 'Not specified',
      resumeContent: resumeMatch?.[1]?.trim() || null,
      status: statusMatch?.[1]?.trim() || 'pending',
      jobTitle: jobMatch?.[1]?.trim() || 'Unknown position'
    });
  }

  // Check if the message is asking about a specific candidate
  const specificCandidateMatch = message.match(/(?:evaluate|review|tell me about|analyze)\s+(?:applicant:?\s*)?([^,.\n]+)/i);
  const requestedCandidate = specificCandidateMatch?.[1]?.trim();

  if (isEmployer) {
    // Handle specific candidate evaluation requests
    if (requestedCandidate) {
      const candidate = candidateDetails.find(c =>
        c.name.toLowerCase().includes(requestedCandidate.toLowerCase()) ||
        requestedCandidate.toLowerCase().includes(c.name.toLowerCase())
      );

      if (candidate) {
        return `Hi ${userName}! Here's my detailed analysis of ${candidate.name}'s application for the ${candidate.jobTitle} position:

**Candidate Profile:**
- **Current Position**: ${candidate.position}
- **Experience Level**: ${candidate.experience}
- **Key Skills**: ${candidate.skills}
- **Application Status**: ${candidate.status}

**Resume Analysis:**
${candidate.resumeContent ?
  `Based on ${candidate.name}'s resume content, I can see they have relevant experience. Key highlights from their resume include their background and qualifications that align with your job requirements.` :
  `${candidate.name} hasn't uploaded a resume yet. I recommend requesting their resume to get a complete picture of their qualifications and experience.`}

**Recommendation:**
${candidate.resumeContent ?
  `${candidate.name} appears to be a strong candidate. I recommend scheduling an interview to discuss their specific experience and how it relates to your needs. Focus on their ${candidate.skills} skills and ask about specific projects or achievements.` :
  `Request ${candidate.name}'s resume first, then schedule a preliminary conversation to assess their fit for the role.`}

Would you like me to help you prepare specific interview questions for ${candidate.name}?`;
      }
    }

    if (message.toLowerCase().includes('peter dimian') || message.toLowerCase().includes('resume')) {
      if (candidateNames.length > 0) {
        return `Hi ${userName}! I can see you have applications from ${candidateNames.slice(0, 2).join(' and ')}${candidateNames.length > 2 ? ` and ${candidateNames.length - 2} other candidates` : ''}. Let me help you evaluate these candidates:

**For candidate evaluation**, focus on these key areas:
- **Skills alignment**: Compare their resume content with your job requirements
- **Experience relevance**: Look for direct experience in similar roles or transferable skills
- **Cultural fit**: Assess their background and communication style
- **Growth potential**: Consider their career trajectory and learning ability

**Next steps for ${candidateNames[0] || 'your candidates'}**:
1. Schedule structured interviews focusing on their specific experience
2. Prepare role-specific questions based on their resume content
3. Consider technical assessments if applicable
4. Check references to validate their background

Would you like me to help you develop specific interview questions for any of these candidates?`;
      } else {
        return `Hi ${userName}! I understand you're asking about resume evaluation. As an AI Coach, I can help you with comprehensive candidate assessment strategies.

**For evaluating any candidate's resume**, consider these key areas:
- **Relevant experience**: Look for experience that directly relates to your job requirements
- **Skills alignment**: Check if their technical and soft skills match your needs
- **Career progression**: Evaluate their growth trajectory and potential
- **Cultural fit indicators**: Look for values and work style alignment

Would you like me to help you develop specific evaluation criteria for your current candidates?`;
      }
    } else if (message.toLowerCase().includes('interview')) {
      return `Great question about interviews, ${userName}! ${candidateNames.length > 0 ? `With candidates like ${candidateNames.slice(0, 2).join(' and ')}, here are` : 'Here are'} some key strategies for effective interviewing:

**Preparation is key**: Review each candidate's resume thoroughly and prepare specific questions about their experience. Focus on behavioral questions that reveal how they handle real situations.

**Create a structured process**: Use the same core questions for all candidates to ensure fair comparison. Include technical assessments if relevant, and always leave time for the candidate's questions.

${candidateNames.length > 0 ? `For your current applicants, I'd recommend focusing on their specific backgrounds and how they align with your job requirements.` : ''}

Would you like me to help you develop specific interview questions for your role?`;
    } else {
      return `Hello ${userName}! I'm here to help with your hiring and team management needs. ${candidateNames.length > 0 ? `I can see you have ${candidateNames.length} candidate application(s) to review.` : ''} As an employer, I can assist you with:

• **Candidate evaluation** - Analyzing resumes and qualifications
• **Interview techniques** - Conducting effective candidate assessments
• **Hiring decisions** - Making informed choices based on candidate data
• **Team building** - Creating strong, collaborative teams

${candidateNames.length > 0 ? `Would you like to discuss any of your current candidates: ${candidateNames.join(', ')}?` : 'What specific hiring challenge would you like to discuss today?'}`;
    }
  } else {
    return `Hello ${userName}! I'm excited to help with your career development. As a professional, I can guide you with:

• **Job search strategies** - Finding and landing your ideal role
• **Skill development** - Building capabilities that employers value
• **Career planning** - Creating a path for long-term growth
• **Interview preparation** - Presenting your best self to employers

What aspect of your career would you like to focus on today?`;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Setup authentication routes
  setupAuth(app);

  // Path Coach AI Chat endpoint - Enhanced with comprehensive user and candidate context
  app.post("/api/path-coach/chat", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const { message, userType, conversationHistory } = req.body;

      // Validate input
      if (!message || !userType) {
        return res.status(400).json({ message: "Missing required fields" });
      }

      // Validate that this is a legitimate coaching request
      if (!['worker', 'employer'].includes(userType)) {
        return res.status(400).json({ message: "Invalid user type" });
      }

      // Get comprehensive user context (profile, resume, jobs, applications, candidates)
      const userId = req.user!.id;
      console.log(`🎯 AI Coach request from ${req.user!.name} (${userType}): "${message}"`);

      const userContext = await getUserContext(userId, userType);

      // Create a chat prompt with comprehensive user context
      const chatPrompt = createChatPromptWithContext(message, userType, conversationHistory || '', userContext);

      // Call OpenAI API for response
      const response = await generateChatResponse(chatPrompt);

      console.log(`✅ AI Coach response generated for ${req.user!.name}`);
      res.json({ response });
    } catch (error) {
      console.error("Error in path coach chat:", error);
      res.status(500).json({ message: "Failed to generate response" });
    }
  });

  // Contact form submission endpoint
  app.post("/api/contact", async (req, res) => {
    try {
      const { name, email, subject, message } = req.body;

      // Validate required fields
      if (!name || !email || !subject || !message) {
        return res.status(400).json({ message: "All fields are required" });
      }

      // Validate email format
      const emailRegex = /\S+@\S+\.\S+/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ message: "Invalid email format" });
      }

      console.log("Contact form submission:", { name, email, subject });

      // In a real application, you would:
      // 1. Store the contact submission in the database
      // 2. Send an email notification to the admin
      // 3. Send a confirmation email to the user

      // For now, we'll just log it and return success

      // Example of sending an email (uncomment and implement when ready)
      /*
      await sendEmail({
        to: "<EMAIL>",
        subject: `New Contact Form: ${subject}`,
        text: `
          Name: ${name}
          Email: ${email}
          Subject: ${subject}
          Message: ${message}
        `
      });
      */

      res.status(200).json({
        message: "Your message has been sent successfully! We will get back to you soon."
      });
    } catch (error) {
      console.error("Error processing contact form:", error);
      res.status(500).json({ message: "Failed to send message. Please try again later." });
    }
  });

  // Jobs routes
  app.get("/api/jobs", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const jobs = await storage.getJobs();
      res.json(jobs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch jobs" });
    }
  });

  app.get("/api/jobs/employer", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const employerId = req.user!.id;
      const jobs = await storage.getJobsByEmployer(employerId);
      res.json(jobs);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch employer jobs" });
    }
  });

  app.post("/api/jobs", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can post jobs" });

    try {
      const validatedData = insertJobSchema.parse({
        ...req.body,
        employer_id: req.user!.id
      });

      const job = await storage.createJob(validatedData);
      res.status(201).json(job);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid job data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create job" });
    }
  });

  // Update job route
  app.put("/api/jobs/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can update jobs" });

    try {
      const jobId = parseInt(req.params.id);

      // Get the job to check ownership
      const job = await storage.getJob(jobId);
      if (!job) {
        return res.status(404).json({ message: "Job not found" });
      }

      // Check if the user is the owner of the job
      if (job.employer_id !== req.user!.id) {
        return res.status(403).json({ message: "You can only update your own jobs" });
      }

      // Validate the update data
      const updateData = {
        title: req.body.title,
        description: req.body.description,
        required_skills: req.body.required_skills,
        minimum_experience: req.body.minimum_experience,
        preferred_backgrounds: req.body.preferred_backgrounds,
        work_model: req.body.work_model,
        location: req.body.location,
        availability_needs: req.body.availability_needs,
        language_requirements: req.body.language_requirements,
        culture_fit_keywords: req.body.culture_fit_keywords,
        salary_range: req.body.salary_range,
        diversity_goals: req.body.diversity_goals,
        industry: req.body.industry
      };

      // Update the job
      const updatedJob = await storage.updateJob(jobId, updateData);
      res.json(updatedJob);
    } catch (error) {
      console.error("Update job error:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid job data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to update job" });
    }
  });

  // Delete job route
  app.delete("/api/jobs/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can delete jobs" });

    try {
      const jobId = parseInt(req.params.id);

      // Get the job to check ownership
      const job = await storage.getJob(jobId);
      if (!job) {
        return res.status(404).json({ message: "Job not found" });
      }

      // Check if the user is the owner of the job
      if (job.employer_id !== req.user!.id) {
        return res.status(403).json({ message: "You can only delete your own jobs" });
      }

      // Delete the job
      await storage.deleteJob(jobId);
      res.status(200).json({ message: "Job deleted successfully" });
    } catch (error) {
      console.error("Delete job error:", error);
      res.status(500).json({ message: "Failed to delete job" });
    }
  });

  // Surplus Employees routes
  app.get("/api/surplus-employees", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      // Disable caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');

      const employees = await storage.getSurplusEmployees();
      res.json(employees);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch surplus employees" });
    }
  });

  app.get("/api/surplus-employees/employer", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      // Disable caching
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      res.setHeader('Surrogate-Control', 'no-store');

      const employerId = req.user!.id;
      console.log(`Fetching surplus employees for employer ${employerId}`);
      const employees = await storage.getSurplusEmployeesByEmployer(employerId);
      console.log(`Found ${employees.length} surplus employees for employer ${employerId}`);
      res.json(employees);
    } catch (error) {
      console.error("Error fetching employer surplus employees:", error);
      res.status(500).json({ message: "Failed to fetch employer surplus employees" });
    }
  });

  app.post("/api/surplus-employees", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can list surplus employees" });

    try {
      console.log("Creating surplus employee with data:", req.body);

      // Ensure all fields are present with defaults
      const employeeData = {
        name: req.body.name,
        previous_role: req.body.previous_role,
        industry: req.body.industry,
        employer_id: req.user!.id,
        status: req.body.status || "available",
        notes: req.body.notes || "",
        skills: req.body.skills || "",
        years_experience: req.body.years_experience ? parseInt(req.body.years_experience) : 0,
        transfer_reason: req.body.transfer_reason || "",
        potential_positions: req.body.potential_positions || "",
      };

      console.log("Processed employee data:", employeeData);

      const validatedData = insertSurplusEmployeeSchema.parse(employeeData);
      console.log("Validated employee data:", validatedData);

      const employee = await storage.createSurplusEmployee(validatedData);
      console.log("Created employee:", employee);

      // Force refresh the cache
      cache.del(`surplus-employees-employer-${req.user!.id}`);

      res.status(201).json(employee);
    } catch (error) {
      console.error("Error creating surplus employee:", error);
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid employee data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create surplus employee" });
    }
  });

  // Add existing user as surplus employee
  app.post("/api/surplus-employees/existing-user", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can list surplus employees" });

    try {
      console.log("Adding existing user as surplus employee:", req.body);
      const { userId, role, notes, transfer_reason, potential_positions, skills, years_experience, industry } = req.body;

      if (!userId || !role) {
        return res.status(400).json({ message: "User ID and role are required" });
      }

      // Get the user to verify they exist
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Check if this user is already listed as a surplus employee by this employer
      const existingEmployees = await storage.getSurplusEmployeesByEmployer(req.user!.id);
      const alreadyListed = existingEmployees.some(emp => emp.name === user.name);

      if (alreadyListed) {
        return res.status(400).json({ message: "This employee is already listed as a surplus employee" });
      }

      // Create the surplus employee record with all fields
      const employeeData = {
        name: user.name,
        previous_role: role,
        industry: industry || "Not specified", // Default value
        employer_id: req.user!.id,
        status: "available",
        notes: notes || "",
        skills: skills || "",
        years_experience: years_experience ? parseInt(years_experience) : 0,
        transfer_reason: transfer_reason || "",
        potential_positions: potential_positions || "",
        user_id: userId // Link to the actual user
      };

      console.log("Processed employee data:", employeeData);

      const employee = await storage.createSurplusEmployee(employeeData);
      console.log("Created employee from existing user:", employee);

      // Force refresh the cache
      cache.del(`surplus-employees-employer-${req.user!.id}`);

      res.status(201).json(employee);
    } catch (error) {
      console.error("Error adding existing user as surplus employee:", error);
      res.status(500).json({ message: "Failed to add existing user as surplus employee" });
    }
  });

  // Update surplus employee
  app.put("/api/surplus-employees/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can update surplus employees" });

    try {
      const employeeId = parseInt(req.params.id);
      console.log(`Updating employee ${employeeId} with data:`, req.body);

      // Get the employee to verify it exists and belongs to this employer
      const employee = await storage.getSurplusEmployee(employeeId);
      if (!employee) {
        console.log(`Employee ${employeeId} not found`);
        return res.status(404).json({ message: "Employee not found" });
      }

      // Verify ownership
      if (employee.employer_id !== req.user!.id) {
        console.log(`Employee ${employeeId} belongs to employer ${employee.employer_id}, not ${req.user!.id}`);
        return res.status(403).json({ message: "You can only update your own employees" });
      }

      // Update the employee
      const updatedData = {
        name: req.body.name,
        previous_role: req.body.previous_role,
        industry: req.body.industry,
        status: req.body.status,
        notes: req.body.notes,
        skills: req.body.skills,
        years_experience: req.body.years_experience ? parseInt(req.body.years_experience) : undefined,
        transfer_reason: req.body.transfer_reason,
        potential_positions: req.body.potential_positions
      };

      console.log(`Calling storage.updateSurplusEmployee with data:`, updatedData);

      // Add updateSurplusEmployee method to storage interface
      const updatedEmployee = await storage.updateSurplusEmployee(employeeId, updatedData);

      console.log(`Update result:`, updatedEmployee);

      if (!updatedEmployee) {
        console.error(`Failed to update employee ${employeeId}`);
        return res.status(500).json({ message: "Failed to update employee" });
      }

      // Force refresh the cache
      cache.del(`surplus-employees-employer-${req.user!.id}`);
      console.log(`Cache invalidated for employer ${req.user!.id}`);

      res.status(200).json(updatedEmployee);
    } catch (error) {
      console.error("Error updating surplus employee:", error);
      res.status(500).json({ message: "Failed to update surplus employee" });
    }
  });

  // Delete surplus employee
  app.delete("/api/surplus-employees/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can delete surplus employees" });

    try {
      const employeeId = parseInt(req.params.id);
      console.log(`Deleting employee ${employeeId}`);

      // Get the employee to verify it exists and belongs to this employer
      const employee = await storage.getSurplusEmployee(employeeId);
      if (!employee) {
        console.log(`Employee ${employeeId} not found`);
        return res.status(404).json({ message: "Employee not found" });
      }

      // Verify ownership
      if (employee.employer_id !== req.user!.id) {
        console.log(`Employee ${employeeId} belongs to employer ${employee.employer_id}, not ${req.user!.id}`);
        return res.status(403).json({ message: "You can only delete your own employees" });
      }

      // Delete the employee
      console.log(`Calling storage.deleteSurplusEmployee for employee ${employeeId}`);
      const success = await storage.deleteSurplusEmployee(employeeId);
      console.log(`Delete result for employee ${employeeId}:`, success);

      if (success) {
        // Force refresh the cache
        cache.del(`surplus-employees-employer-${req.user!.id}`);
        console.log(`Employee ${employeeId} deleted successfully`);
        res.status(200).json({ message: "Employee deleted successfully" });
      } else {
        console.error(`Failed to delete employee ${employeeId}`);
        res.status(500).json({ message: "Failed to delete employee" });
      }
    } catch (error) {
      console.error("Error deleting surplus employee:", error);
      res.status(500).json({ message: "Failed to delete surplus employee" });
    }
  });

  // Search users by name
  app.get("/api/users/search", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can search users" });

    try {
      const { query } = req.query;

      if (!query || typeof query !== 'string') {
        return res.status(400).json({ message: "Search query is required" });
      }

      // Get all users (in a real app, this would be paginated and filtered in the database)
      const allUsers = await storage.getUsers();

      // Split the query into terms for better matching
      const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length > 0);

      // Filter users by name match
      const matchingUsers = allUsers.filter(user => {
        // Skip the current user
        if (user.id === req.user!.id) return false;

        // Create a searchable text from the user's name and email
        const searchableText = [
          user.name.toLowerCase(),
          user.email.toLowerCase()
        ].join(' ');

        // Match if any search term is found in the searchable text
        return searchTerms.some(term => searchableText.includes(term));
      });

      // Return limited user information for security
      const sanitizedUsers = matchingUsers.map(user => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }));

      res.json(sanitizedUsers);
    } catch (error) {
      console.error("Error searching users:", error);
      res.status(500).json({ message: "Failed to search users" });
    }
  });

  // Matches routes
  app.get("/api/matches", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      let matches;
      if (req.user!.role === "worker") {
        matches = await storage.getMatchesByWorkerId(req.user!.id);
      } else {
        // For employers, get matches for their jobs
        const employerJobs = await storage.getJobsByEmployer(req.user!.id);
        const jobIds = employerJobs.map(job => job.id);

        // Get all matches
        const allMatches = await storage.getMatches();
        matches = allMatches.filter(match => jobIds.includes(match.job_id));

        // Enhance matches with worker names
        const enhancedMatches = await Promise.all(matches.map(async (match) => {
          const worker = await storage.getUser(match.worker_id);
          return {
            ...match,
            worker_name: worker ? worker.name : `Worker ${match.worker_id}`
          };
        }));

        matches = enhancedMatches;
      }

      res.json(matches);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch matches" });
    }
  });

  app.post("/api/matches", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "worker") return res.status(403).json({ message: "Only workers can apply for jobs" });

    try {
      // Get the job ID from the request body
      const { job_id } = req.body;

      if (!job_id) {
        return res.status(400).json({ message: "Job ID is required" });
      }

      // Check if the job exists
      const job = await storage.getJob(job_id);
      if (!job) {
        return res.status(404).json({ message: "Job not found" });
      }

      // Check if the user has already applied for this job
      const existingMatches = await storage.getMatchesByWorkerId(req.user!.id);
      const alreadyApplied = existingMatches.some(match => match.job_id === job_id);

      if (alreadyApplied) {
        return res.status(400).json({ message: "You have already applied for this job" });
      }

      // Create a match with a pending status
      try {
        // Use the storage interface instead of direct database access
        const match = await storage.createMatch({
          worker_id: req.user!.id,
          job_id: job_id,
          status: 'pending',
          match_score: Math.floor(Math.random() * 30) + 70, // Simple random score between 70-100
          match_date: new Date()
        });

        return res.status(201).json(match);
      } catch (dbError) {
        console.error('Database operation error:', dbError);
        return res.status(500).json({ message: "Database error when creating match", error: dbError instanceof Error ? dbError.message : String(dbError) });
      }
    } catch (error) {
      console.error("Error creating match:", error);
      res.status(500).json({ message: "Failed to create match", error: error instanceof Error ? error.message : String(error) });
    }
  });

  // Update match status route
  app.put("/api/matches/:id/status", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can update match status" });

    try {
      const matchId = parseInt(req.params.id);
      const { status, interview_date } = req.body;

      if (!status) {
        return res.status(400).json({ message: "Status is required" });
      }

      // Validate status
      if (!['pending', 'rejected', 'interview_scheduled', 'accepted'].includes(status)) {
        return res.status(400).json({ message: "Invalid status" });
      }

      // Get the match
      const match = await storage.getMatch(matchId);
      if (!match) {
        return res.status(404).json({ message: "Match not found" });
      }

      // Get the job to check ownership
      const job = await storage.getJob(match.job_id);
      if (!job) {
        return res.status(404).json({ message: "Job not found" });
      }

      // Check if the user is the owner of the job
      if (job.employer_id !== req.user!.id) {
        return res.status(403).json({ message: "You can only update status for your own job matches" });
      }

      // Update the match status
      try {
        // Create a simple update object
        const updateData = {
          status: status
        };

        // Add interview_date if provided and status is interview_scheduled
        if (status === 'interview_scheduled' && interview_date) {
          updateData.interview_date = new Date(interview_date);
        }

        // Use the storage interface to update the match
        // Note: We need to implement this method in the storage classes
        // For now, we'll use a workaround with the in-memory storage

        // Get the current match
        const currentMatch = await storage.getMatch(matchId);
        if (!currentMatch) {
          return res.status(404).json({ message: "Match not found" });
        }

        // Update the match in memory
        const updatedMatch = {
          ...currentMatch,
          ...updateData
        };

        // For in-memory storage, we can directly update the match
        if (storage instanceof MemStorage) {
          // This is a workaround for the in-memory storage
          // In a real implementation, we would have an updateMatch method
          storage.matches.set(matchId, updatedMatch);
        }

        // Get the worker name
        const worker = await storage.getUser(match.worker_id);

        // Add worker name to the response
        const matchWithWorkerName = {
          ...updatedMatch,
          worker_name: worker ? worker.name : `Worker ${match.worker_id}`
        };

        return res.status(200).json({
          message: "Match status updated successfully",
          match: matchWithWorkerName
        });
      } catch (dbError) {
        console.error('Database operation error:', dbError);
        return res.status(500).json({ message: "Database error when updating match", error: dbError instanceof Error ? dbError.message : String(dbError) });
      }
    } catch (error) {
      console.error("Error updating match status:", error);
      res.status(500).json({ message: "Failed to update match status", error: error instanceof Error ? error.message : String(error) });
    }
  });

  // User Profile Routes
  app.get("/api/profile", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const userId = req.user!.id;

      try {
        const profile = await storage.getUserProfile(userId);

        if (!profile) {
          // Return empty profile with default values if not found
          return res.json({
            position: "",
            location: "",
            bio: "",
            company: "",
            yearsOfExperience: "",
            skills: "",
            profilePicture: ""
          });
        }

        // Convert snake_case to camelCase for frontend
        return res.json({
          position: profile.position || "",
          location: profile.location || "",
          bio: profile.bio || "",
          company: profile.company || "",
          yearsOfExperience: profile.years_of_experience || "",
          skills: profile.skills || "",
          profilePicture: profile.profile_picture || ""
        });
      } catch (dbError) {
        console.error("Database error getting profile:", dbError);

        // Return empty profile with default values if there's a database error
        return res.json({
          position: "",
          location: "",
          bio: "",
          company: "",
          yearsOfExperience: "",
          skills: "",
          profilePicture: ""
        });
      }
    } catch (error) {
      console.error("Get profile error:", error);
      // Return a 200 with empty profile instead of 500 to allow the client to continue
      return res.json({
        position: "",
        location: "",
        bio: "",
        company: "",
        yearsOfExperience: "",
        skills: "",
        profilePicture: ""
      });
    }
  });

  app.put("/api/profile", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const userId = req.user!.id;
      const profileData = req.body;

      // Validate the profile data
      const allowedFields = [
        "name", "position", "location", "bio", "company",
        "yearsOfExperience", "skills", "profilePicture"
      ];

      // Filter out any fields that are not allowed
      const sanitizedData = Object.keys(profileData)
        .filter(key => allowedFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = profileData[key];
          return obj;
        }, {} as Record<string, any>);

      // Update the user's name in the users table
      if (sanitizedData.name) {
        try {
          await storage.updateUser(userId, { name: sanitizedData.name });
        } catch (nameError) {
          console.error("Error updating user name:", nameError);
          // Continue even if name update fails
        }
        delete sanitizedData.name; // Remove from profile data as it's stored in users table
      }

      // Convert yearsOfExperience to number if it's a string
      if (sanitizedData.yearsOfExperience && typeof sanitizedData.yearsOfExperience === 'string') {
        sanitizedData.years_of_experience = parseInt(sanitizedData.yearsOfExperience) || null;
        delete sanitizedData.yearsOfExperience;
      }

      // Convert profilePicture to profile_picture
      if (sanitizedData.profilePicture) {
        sanitizedData.profile_picture = sanitizedData.profilePicture;
        delete sanitizedData.profilePicture;
      }

      console.log(`Updating profile for user ${userId} with data:`, sanitizedData);

      try {
        // Update or create the profile
        const updatedProfile = await storage.updateUserProfile(userId, sanitizedData);

        // Convert back to camelCase for frontend
        const responseProfile = {
          id: updatedProfile.id,
          position: updatedProfile.position || "",
          location: updatedProfile.location || "",
          bio: updatedProfile.bio || "",
          company: updatedProfile.company || "",
          yearsOfExperience: updatedProfile.years_of_experience || "",
          skills: updatedProfile.skills || "",
          profilePicture: updatedProfile.profile_picture || ""
        };

        return res.status(200).json(responseProfile);
      } catch (dbError) {
        console.error("Database error updating profile:", dbError);

        // Create a fallback profile response
        const fallbackProfile = {
          id: 0,
          position: sanitizedData.position || "",
          location: sanitizedData.location || "",
          bio: sanitizedData.bio || "",
          company: sanitizedData.company || "",
          yearsOfExperience: sanitizedData.years_of_experience || "",
          skills: sanitizedData.skills || "",
          profilePicture: sanitizedData.profile_picture || ""
        };

        // Return the fallback profile with a 200 status
        // This allows the client to continue working even if the database has issues
        return res.status(200).json(fallbackProfile);
      }
    } catch (error) {
      console.error("Update profile error:", error);
      return res.status(500).json({ message: "Failed to update profile" });
    }
  });

  // Profile picture upload route
  app.post("/api/profile/picture", uploadProfilePicture.single('profilePicture'), async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      const userId = req.user!.id;
      const filePath = req.file.path;
      const fileUrl = `/uploads/profile/${path.basename(filePath)}`;

      // Get current profile to check if there's an existing picture to delete
      const currentProfile = await storage.getUserProfile(userId);
      if (currentProfile?.profile_picture) {
        try {
          // Extract the filename from the URL
          const oldPicturePath = path.join('uploads/profile', path.basename(currentProfile.profile_picture));

          // Check if file exists before attempting to delete
          if (fs.existsSync(oldPicturePath)) {
            await promisify(fs.unlink)(oldPicturePath);
            console.log(`Deleted old profile picture: ${oldPicturePath}`);
          }
        } catch (deleteError) {
          console.error("Error deleting old profile picture:", deleteError);
          // Continue even if deletion fails
        }
      }

      // Update the profile with the new picture URL
      await storage.updateUserProfile(userId, { profile_picture: fileUrl });

      return res.status(200).json({
        message: "Profile picture uploaded successfully",
        profilePicture: fileUrl
      });
    } catch (error) {
      console.error("Profile picture upload error:", error);
      return res.status(500).json({ message: "Failed to upload profile picture" });
    }
  });

  // Profile picture delete route
  app.delete("/api/profile/picture", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const userId = req.user!.id;

      // Get current profile
      const profile = await storage.getUserProfile(userId);

      if (profile?.profile_picture) {
        try {
          // Extract the filename from the URL
          const picturePath = path.join('uploads/profile', path.basename(profile.profile_picture));

          // Check if file exists before attempting to delete
          if (fs.existsSync(picturePath)) {
            await promisify(fs.unlink)(picturePath);
            console.log(`Deleted profile picture: ${picturePath}`);
          }
        } catch (deleteError) {
          console.error("Error deleting profile picture file:", deleteError);
          // Continue even if file deletion fails
        }
      }

      // Update profile to remove picture reference
      await storage.updateUserProfile(userId, { profile_picture: null });

      return res.status(200).json({
        message: "Profile picture removed successfully"
      });
    } catch (error) {
      console.error("Profile picture removal error:", error);
      return res.status(500).json({ message: "Failed to remove profile picture" });
    }
  });

  // Resume upload route
  app.post("/api/resume/upload", upload.single('resume'), async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      if (!req.file) {
        return res.status(400).json({ message: "No file uploaded" });
      }

      const userId = req.user!.id;
      const filePath = req.file.path;
      const fileUrl = `/uploads/resumes/${path.basename(filePath)}`;

      // Return the resume URL
      return res.status(200).json({
        message: "Resume uploaded successfully",
        resumeUrl: fileUrl
      });
    } catch (error) {
      console.error("Resume upload error:", error);
      return res.status(500).json({ message: "Failed to upload resume" });
    }
  });

  // Role switch route (employer to worker only)
  app.post("/api/switch-role", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Only employers can switch to worker role
      if (req.user!.role !== "employer") {
        return res.status(403).json({ message: "Only employers can switch to worker role" });
      }

      // Save the original role if not already saved
      if (!req.user!.original_role) {
        await storage.updateUser(req.user!.id, { original_role: req.user!.role });
      }

      // Update the user's role to worker
      const updatedUser = await storage.updateUser(req.user!.id, { role: "worker" });

      // Update the session
      req.user!.role = "worker";

      return res.status(200).json({
        message: "Role switched successfully",
        user: {
          id: updatedUser.id,
          name: updatedUser.name,
          email: updatedUser.email,
          role: updatedUser.role,
          original_role: updatedUser.original_role
        }
      });
    } catch (error) {
      console.error("Role switch error:", error);
      return res.status(500).json({ message: "Failed to switch role" });
    }
  });

  // Role switch request route (worker to employer)
  app.post("/api/switch-role-request", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Only workers can request to switch to employer role
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Only workers can request to switch to employer role" });
      }

      const { new_email } = req.body;

      if (!new_email) {
        return res.status(400).json({ message: "Email is required" });
      }

      // Extract domain from email
      const domain = new_email.split('@')[1];
      if (!domain) {
        return res.status(400).json({ message: "Invalid email format" });
      }

      // Check if domain is in approved list
      const approvedDomain = await storage.getApprovedDomainByName(domain);
      const isApproved = !!approvedDomain && approvedDomain.is_active;

      if (!isApproved) {
        return res.status(400).json({
          message: "Email domain not approved for employer registration. Please contact us to add your domain.",
          requiresVerification: true
        });
      }

      // Generate verification token using the imported crypto module
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Save the token and new email
      await storage.updateUser(req.user!.id, {
        verification_token: verificationToken,
        verification_expires: verificationExpires
      });

      // Store the new email in a separate table or field
      // For now, we'll use a placeholder
      console.log(`Role switch requested for user ${req.user!.id} with new email ${new_email}`);

      // Send verification email (placeholder)
      // In a real implementation, you would send an email with a link to verify
      console.log(`Verification email would be sent to ${new_email} with token ${verificationToken}`);

      return res.status(200).json({
        message: "Role switch request submitted. Please check your email for verification."
      });
    } catch (error) {
      console.error("Role switch request error:", error);
      return res.status(500).json({ message: "Failed to submit role switch request" });
    }
  });

  // Approved domains routes
  app.get("/api/approved-domains", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Only admins can view all domains
      if (req.user!.role !== "admin" && req.user!.role !== "employer") {
        return res.status(403).json({ message: "Access denied" });
      }

      const domains = await storage.getApprovedDomains();
      return res.json(domains);
    } catch (error) {
      console.error("Get approved domains error:", error);
      return res.status(500).json({ message: "Failed to get approved domains" });
    }
  });

  app.post("/api/approved-domains", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Only admins can add domains
      if (req.user!.role !== "admin") {
        return res.status(403).json({ message: "Only admins can add approved domains" });
      }

      const { domain, description } = req.body;

      if (!domain) {
        return res.status(400).json({ message: "Domain is required" });
      }

      // Validate domain format
      const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
      if (!domainRegex.test(domain)) {
        return res.status(400).json({ message: "Invalid domain format" });
      }

      // Check if domain already exists
      const existingDomain = await storage.getApprovedDomainByName(domain);
      if (existingDomain) {
        return res.status(409).json({ message: "Domain already exists" });
      }

      // Add the domain
      const newDomain = await storage.createApprovedDomain({
        domain,
        description,
        is_active: true,
        added_by: req.user!.id
      });

      return res.status(201).json(newDomain);
    } catch (error) {
      console.error("Add approved domain error:", error);
      return res.status(500).json({ message: "Failed to add approved domain" });
    }
  });

  // Verify worker email domain
  app.post("/api/verify-email-domain", async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ message: "Email is required" });
      }

      // Extract domain from email
      const domain = email.split('@')[1];
      if (!domain) {
        return res.status(400).json({ message: "Invalid email format" });
      }

      // Check if domain is in approved list
      const approvedDomain = await storage.getApprovedDomainByName(domain);
      const isApproved = !!approvedDomain && approvedDomain.is_active;

      return res.json({ isApproved });
    } catch (error) {
      console.error("Verify email domain error:", error);
      return res.status(500).json({ message: "Failed to verify email domain" });
    }
  });

  // Email change route
  app.post("/api/change-email", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const { currentPassword, newEmail } = req.body;
      const userId = req.user!.id;

      if (!currentPassword || !newEmail) {
        return res.status(400).json({ message: "Current password and new email are required" });
      }

      // Get the user
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Verify current password
      const isPasswordValid = await comparePasswords(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Current password is incorrect" });
      }

      // Check if new email is already in use
      const existingUser = await storage.getUserByEmail(newEmail);
      if (existingUser) {
        return res.status(409).json({ message: "Email is already in use" });
      }

      // Generate verification token using the imported crypto module
      const verificationToken = crypto.randomBytes(32).toString('hex');
      const verificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

      // Save the token and new email
      await storage.updateUser(userId, {
        verification_token: verificationToken,
        verification_expires: verificationExpires
      });

      // Store the new email temporarily
      // In a real implementation, you would store this in a separate table
      // For now, we'll use a custom field in the user profile
      await storage.updateUserProfile(userId, {
        pending_email: newEmail
      });

      console.log(`Email change requested for user ${userId} from ${user.email} to ${newEmail}`);
      console.log(`Verification token: ${verificationToken}`);

      // Send verification email (placeholder)
      // In a real implementation, you would send an email with a link to verify
      console.log(`Verification email would be sent to ${newEmail} with token ${verificationToken}`);

      return res.status(200).json({
        message: "Email change request submitted. Please check your new email for verification."
      });
    } catch (error) {
      console.error("Email change error:", error);
      return res.status(500).json({ message: "Failed to change email" });
    }
  });

  // Verify email change route
  app.get("/api/verify-email-change/:token", async (req, res) => {
    try {
      const { token } = req.params;

      if (!token) {
        return res.status(400).json({ message: "Verification token is required" });
      }

      // Find user by verification token
      const users = await storage.getUsers();
      const user = users.find(u => u.verification_token === token);

      if (!user) {
        return res.status(404).json({ message: "Invalid or expired verification token" });
      }

      // Check if token is expired
      if (user.verification_expires && new Date(user.verification_expires) < new Date()) {
        return res.status(400).json({ message: "Verification token has expired" });
      }

      // Get the pending email from user profile
      const profile = await storage.getUserProfile(user.id);
      const newEmail = profile?.pending_email;

      if (!newEmail) {
        return res.status(400).json({ message: "No pending email change found" });
      }

      // Update the user's email
      await storage.updateUser(user.id, {
        email: newEmail,
        verification_token: null,
        verification_expires: null
      });

      // Clear the pending email
      await storage.updateUserProfile(user.id, {
        pending_email: null
      });

      // Redirect to login page with success message
      return res.redirect('/login?message=Email+changed+successfully.+Please+log+in+with+your+new+email.');
    } catch (error) {
      console.error("Email verification error:", error);
      return res.status(500).json({ message: "Failed to verify email change" });
    }
  });

  // Password reset route
  app.post("/api/reset-password", async (req, res) => {
    try {
      const { email, currentPassword, newPassword } = req.body;

      if (!email || !currentPassword || !newPassword) {
        return res.status(400).json({ message: "All fields are required" });
      }

      // Find user by email
      const user = await storage.getUserByEmail(email);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Verify current password
      const isPasswordValid = await comparePasswords(currentPassword, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: "Current password is incorrect" });
      }

      // Hash new password and update user
      const hashedNewPassword = await hashPassword(newPassword);

      // In a full implementation, you would update the user in the database
      // For memory storage, we update it directly
      user.password = hashedNewPassword;

      // Send confirmation email if SendGrid API key is set
      if (process.env.SENDGRID_API_KEY) {
        await sendPasswordResetEmail(email);
      }

      return res.status(200).json({ message: "Password updated successfully" });
    } catch (error) {
      console.error("Password reset error:", error);
      return res.status(500).json({ message: "Failed to reset password" });
    }
  });

  // Password reset request route (forgot password)
  app.post("/api/forgot-password", async (req, res) => {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({ message: "Email is required" });
      }

      // Find user by email
      const user = await storage.getUserByEmail(email);
      if (!user) {
        // For security reasons, don't reveal that the user doesn't exist
        return res.status(200).json({ message: "If your email is registered, you will receive password reset instructions" });
      }

      // Send password reset email with temporary password
      const { success, temporaryPassword } = await sendPasswordResetEmail(email);

      if (success && temporaryPassword) {
        console.log(`Password reset email sent to ${email}`);

        // Hash the temporary password
        const hashedTempPassword = await hashPassword(temporaryPassword);

        // Update the user's password
        await storage.updateUser(user.id, { password: hashedTempPassword });

        return res.status(200).json({
          message: "Password has been reset. Please check your email for instructions.",
          // For development, still return the password in case email doesn't work
          temporaryPassword: process.env.NODE_ENV === 'production' ? undefined : temporaryPassword,
        });
      } else {
        console.error(`Failed to send password reset email to ${email}`);

        // Generate a temporary password even if email fails
        const temporaryPassword = 'password123'; // Fallback password
        const hashedTempPassword = await hashPassword(temporaryPassword);
        await storage.updateUser(user.id, { password: hashedTempPassword });

        return res.status(200).json({
          message: "Password has been reset",
          temporaryPassword: temporaryPassword, // Fallback for when email sending fails
          note: "Email delivery failed. Please use this temporary password to log in."
        });
      }
    } catch (error) {
      console.error("Forgot password error:", error);
      return res.status(500).json({ message: "Failed to process forgot password request" });
    }
  });

  // Resume upload route
  app.post("/api/resumes", upload.single('resumeFile'), async (req, res) => {
    console.log("Resume upload request received");

    try {
      // Check authentication
      if (!req.isAuthenticated()) {
        console.log("Resume upload failed: User not authenticated");
        return res.status(401).json({ message: "Not authenticated" });
      }

      console.log(`Resume upload request from user: ${req.user!.id} (${req.user!.name}, ${req.user!.role})`);

      // Only workers can upload resumes
      if (req.user!.role !== "worker") {
        console.log(`Resume upload failed: User role is ${req.user!.role}, not worker`);
        return res.status(403).json({ message: "Only workers can upload resumes" });
      }

      // Check if file was uploaded
      if (!req.file) {
        console.log("Resume upload failed: No file uploaded");
        return res.status(400).json({ message: "No file uploaded" });
      }

      // Get file details
      const { filename, originalname, path: filePath, size, mimetype } = req.file;
      console.log(`Resume file details: ${originalname}, ${size} bytes, ${mimetype}, saved to ${filePath}`);

      const fileType = getFileType(filename);
      console.log(`Determined file type: ${fileType}`);

      // Create a URL for accessing the file (not stored in DB, but used in the response)
      const fileUrl = `/uploads/resumes/${filename}`;
      console.log(`File URL: ${fileUrl}`);

      // Extract text from the uploaded file for searching
      let extractedText = '';
      try {
        console.log(`Attempting to extract text from ${filePath} (${fileType})`);
        const { extractTextFromFile } = await import('./text-extractor');
        extractedText = await extractTextFromFile(filePath, fileType);
        console.log(`Extracted ${extractedText.length} characters from ${originalname}`);
      } catch (error) {
        console.error('Error extracting text from file:', error);
        // Continue with upload even if text extraction fails
      }

      // Check if user already has a resume
      console.log(`Checking if user ${req.user!.id} already has a resume`);
      const existingResume = await storage.getResumeByWorkerId(req.user!.id);

      // If they do, delete the old file
      if (existingResume) {
        console.log(`User ${req.user!.id} already has resume ID ${existingResume.id}, updating`);

        // Delete old file
        try {
          // Convert relative path to absolute path
          const absolutePath = getAbsolutePath(existingResume.file_path);
          console.log(`Deleting old resume file: ${existingResume.file_path} (absolute: ${absolutePath})`);

          await promisify(fs.unlink)(absolutePath);
          console.log('Old resume file deleted successfully');
        } catch (error) {
          console.error("Error deleting old resume file:", error);
          // Continue even if delete fails
        }

        // Update existing resume record (without file_url which doesn't exist in the DB)
        console.log(`Updating resume record in database`);
        // Convert absolute path to relative path for storage
        const relativePath = getRelativePath(filePath);
        console.log(`Converting absolute path ${filePath} to relative path ${relativePath}`);

        const updatedResume = await storage.updateResume(existingResume.id, {
          filename: originalname,
          file_path: relativePath,
          file_size: size,
          file_type: fileType,
          extracted_text: extractedText,
          last_indexed: new Date()
        });

        // Add file_url to the response (but not stored in DB)
        (updatedResume as any).file_url = fileUrl;

        console.log(`Resume updated successfully, ID: ${updatedResume.id}`);
        return res.status(200).json({
          message: "Resume updated successfully",
          resume: updatedResume
        });
      }

      // Create new resume record (without file_url which doesn't exist in the DB)
      console.log(`Creating new resume record for user ${req.user!.id}`);
      // Convert absolute path to relative path for storage
      const relativePath = getRelativePath(filePath);
      console.log(`Converting absolute path ${filePath} to relative path ${relativePath}`);

      const resume = await storage.createResume({
        worker_id: req.user!.id,
        filename: originalname,
        file_path: relativePath,
        file_size: size,
        file_type: fileType,
        extracted_text: extractedText,
        last_indexed: new Date()
      });

      // Add file_url to the response (but not stored in DB)
      (resume as any).file_url = fileUrl;

      console.log(`Resume created successfully, ID: ${resume.id}`);
      return res.status(201).json({
        message: "Resume uploaded successfully",
        resume
      });
    } catch (error) {
      console.error("Resume upload error:", error);
      return res.status(500).json({
        message: "Failed to upload resume",
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Get worker resume route
  app.get("/api/resumes/worker/:id", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const workerId = parseInt(req.params.id);

      // Check access permissions
      // Workers can only view their own resume
      // Employers can view any worker's resume
      if (req.user!.role === "worker" && req.user!.id !== workerId) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Get the resume metadata
      const resume = await storage.getResumeByWorkerId(workerId);
      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      return res.json(resume);
    } catch (error) {
      console.error("Get resume error:", error);
      return res.status(500).json({ message: "Failed to get resume" });
    }
  });

  // Search resumes route
  app.get("/api/resumes/search", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // TEMPORARILY ALLOW ALL USERS TO SEARCH RESUMES FOR TESTING
      // if (req.user!.role !== "employer") {
      //   return res.status(403).json({ message: "Only employers can search resumes" });
      // }

      const { query } = req.query;
      console.log("Resume search query:", query);

      if (!query || typeof query !== 'string') {
        return res.status(400).json({ message: "Search query is required" });
      }

      // Get all resumes
      const allResumes = await getAllResumesWithUserInfo();
      console.log(`Found ${allResumes.length} total resumes`);

      // For testing, if no resumes are found, load sample resumes from the uploads/resumes directory
      if (allResumes.length === 0) {
        console.log("No resumes found, loading sample resumes from uploads/resumes directory");

        try {
          // Check if the uploads/resumes directory exists
          const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');
          if (fs.existsSync(resumesDir)) {
            // Get all files in the directory
            const files = fs.readdirSync(resumesDir);
            console.log(`Found ${files.length} files in ${resumesDir}`);

            // Process each file
            for (let i = 0; i < files.length; i++) {
              const file = files[i];
              const filePath = path.join(resumesDir, file);

              // Skip directories
              if (fs.statSync(filePath).isDirectory()) {
                continue;
              }

              // Get file extension
              const fileExt = path.extname(file).toLowerCase();

              // Only process text files for simplicity
              if (fileExt === '.txt') {
                console.log(`Loading sample resume: ${file}`);

                // Extract the name from the filename (e.g., "John_Smith_Resume.txt" -> "John Smith")
                const nameMatch = file.match(/^(.+)_Resume\.txt$/);
                const name = nameMatch ? nameMatch[1].replace(/_/g, ' ') : `Sample Worker ${i + 1}`;

                // Create a sample email from the name
                const email = `${name.toLowerCase().replace(/\s+/g, '.')}@example.com`;

                // Read the file content
                const extractedText = fs.readFileSync(filePath, 'utf8');

                // Create a sample user
                const workerId = 2000 + i; // Start from ID 2000 to avoid conflicts

                // Create a user for this resume if it doesn't exist
                let user = await storage.getUserByEmail(email);
                if (!user) {
                  user = await storage.createUser({
                    name: name,
                    email: email,
                    password: "$2b$10$dummyhashedpassword", // Dummy hashed password
                    role: "worker"
                  });
                }

                // Create a sample resume (without file_url which doesn't exist in the DB)
                const sampleResume = await storage.createResume({
                  worker_id: user.id,
                  filename: file,
                  file_path: filePath,
                  file_size: fs.statSync(filePath).size,
                  file_type: 'txt',
                  extracted_text: extractedText
                });

                // Add file_url to the response (but not stored in DB)
                (sampleResume as any).file_url = `/uploads/resumes/${file}`;

                // Add the sample resume to the results
                const sampleResumeWithUser = {
                  resume: sampleResume,
                  user: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    role: user.role
                  }
                };

                allResumes.push(sampleResumeWithUser);
                console.log(`Added sample resume for ${name}`);
              }
            }
          } else {
            console.log(`Directory not found: ${resumesDir}`);
          }
        } catch (error) {
          console.error("Error loading sample resumes:", error);
        }
      }

      // Filter resumes based on search query
      // Now we search through the extracted text content as well
      const searchTerms = query.toLowerCase().split(/\s+/).filter(term => term.length >= 3);
      console.log("Search terms (filtered):", searchTerms);

      // If no valid search terms after filtering, return empty results
      if (searchTerms.length === 0) {
        console.log("No valid search terms found (terms must be at least 3 characters)");
        return res.json([]);
      }

      const matchingResumes = allResumes.filter(resumeWithUser => {
        // Create a searchable text that includes the extracted content
        const searchableText = [
          resumeWithUser.resume.filename.toLowerCase(),
          resumeWithUser.user.name.toLowerCase(),
          resumeWithUser.user.email.toLowerCase(),
          // Include the extracted text from the resume if available
          resumeWithUser.resume.extracted_text ? resumeWithUser.resume.extracted_text.toLowerCase() : ''
        ].join(' ');

        // For each search term, check if it's a real word/term in the searchable text
        // This prevents matching random characters
        const matches = searchTerms.some(term => {
          // Use word boundary check to match whole words or parts of words
          const regex = new RegExp(`\\b\\w*${term}\\w*\\b`, 'i');
          const hasMatch = regex.test(searchableText);

          if (hasMatch) {
            console.log(`Resume ${resumeWithUser.resume.id} matches search term: "${term}"`);
          }

          return hasMatch;
        });

        return matches;
      });

      console.log(`Found ${matchingResumes.length} matching resumes`);

      // Sort results by relevance (number of matching terms)
      matchingResumes.sort((a, b) => {
        const aMatches = searchTerms.filter(term => {
          const searchText = [
            a.resume.filename.toLowerCase(),
            a.user.name.toLowerCase(),
            a.user.email.toLowerCase(),
            a.resume.extracted_text ? a.resume.extracted_text.toLowerCase() : ''
          ].join(' ');

          // Use the same regex pattern as in the filter
          const regex = new RegExp(`\\b\\w*${term}\\w*\\b`, 'i');
          return regex.test(searchText);
        }).length;

        const bMatches = searchTerms.filter(term => {
          const searchText = [
            b.resume.filename.toLowerCase(),
            b.user.name.toLowerCase(),
            b.user.email.toLowerCase(),
            b.resume.extracted_text ? b.resume.extracted_text.toLowerCase() : ''
          ].join(' ');

          // Use the same regex pattern as in the filter
          const regex = new RegExp(`\\b\\w*${term}\\w*\\b`, 'i');
          return regex.test(searchText);
        }).length;

        return bMatches - aMatches; // Sort by most matches first
      });

      return res.json(matchingResumes);
    } catch (error) {
      console.error("Resume search error:", error);
      return res.status(500).json({ message: "Failed to search resumes" });
    }
  });

  // Helper function to get all resumes with user information
  async function getAllResumesWithUserInfo() {
    // Get all resumes
    const allResumes = await storage.getResumes();

    // Get user information for each resume
    const resumesWithUserInfo = await Promise.all(
      allResumes.map(async (resume) => {
        const user = await storage.getUser(resume.worker_id);
        return {
          resume,
          user: user ? {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role
          } : null
        };
      })
    );

    // Filter out resumes with missing user info
    return resumesWithUserInfo.filter(item => item.user !== null);
  }

  // Download resume route
  app.get("/api/resumes/download/:id", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const resumeId = parseInt(req.params.id);

      // Get the resume
      const resume = await storage.getResume(resumeId);
      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      // Check access permissions
      // Workers can only download their own resume
      // Employers can download any resume
      if (req.user!.role === "worker" && req.user!.id !== resume.worker_id) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Convert relative path to absolute path
      const absolutePath = getAbsolutePath(resume.file_path);
      console.log(`Converting relative path ${resume.file_path} to absolute path ${absolutePath}`);

      // Check if file exists
      if (!fs.existsSync(absolutePath)) {
        console.error(`Resume file not found at ${absolutePath}`);
        return res.status(404).json({ message: "Resume file not found" });
      }

      // Set appropriate headers
      res.setHeader('Content-disposition', `attachment; filename=${resume.filename}`);
      res.setHeader('Content-type', resume.file_type === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

      // Stream the file
      const fileStream = fs.createReadStream(absolutePath);
      fileStream.pipe(res);
    } catch (error) {
      console.error("Download resume error:", error);
      return res.status(500).json({ message: "Failed to download resume" });
    }
  });

  // Get all resumes
  app.get("/api/resumes", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const resumes = await storage.getResumes();
      console.log(`Found ${resumes.length} resumes in database`);
      if (resumes.length > 0) {
        console.log("First resume:", resumes[0]);
      }
      res.json(resumes);
    } catch (error) {
      console.error("Error fetching resumes:", error);
      res.status(500).json({ message: "Failed to fetch resumes" });
    }
  });

  // Get resumes with worker information
  app.get("/api/resumes/with-workers", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const resumes = await storage.getResumes();

      // Enhance resumes with worker information
      const enhancedResumes = await Promise.all(
        resumes.map(async (resume) => {
          const worker = await storage.getUser(resume.worker_id);
          return {
            resume,
            worker: worker ? {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            } : null
          };
        })
      );

      // Filter out resumes with no worker information
      const validResumes = enhancedResumes.filter(item => item.worker !== null);

      res.json(validResumes);
    } catch (error) {
      console.error("Error fetching resumes with workers:", error);
      res.status(500).json({ message: "Failed to fetch resumes with worker information" });
    }
  });

  // Match scoring API
  app.get("/api/matches/scores", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      // Get all jobs
      const jobs = await storage.getJobs();

      // Get all resumes with worker information
      const resumes = await storage.getResumes();

      // Generate match scores
      const matches = [];

      for (const job of jobs) {
        for (const resume of resumes) {
          // Get worker information
          const worker = await storage.getUser(resume.worker_id);
          if (!worker) continue;

          // Calculate match score
          const score = calculateMatchScore(job, resume, worker);

          matches.push({
            job,
            worker: {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            },
            resume,
            score
          });
        }
      }

      res.json(matches);
    } catch (error) {
      console.error("Error calculating match scores:", error);
      res.status(500).json({ message: "Failed to calculate match scores" });
    }
  });

  // Helper function to calculate match score
  function calculateMatchScore(job: any, resume: any, worker: any) {
    // Extract skills from job and resume
    const jobSkills = job.required_skills
      ? job.required_skills.toLowerCase().split(',').map((s: string) => s.trim())
      : [];

    const resumeText = resume.extracted_text || '';
    const resumeSkills = extractSkillsFromResume(resumeText);

    // Calculate skills match
    const matchedSkills = jobSkills.filter(skill =>
      resumeSkills.some(resumeSkill => resumeSkill.toLowerCase().includes(skill.toLowerCase()))
    );

    const missingSkills = jobSkills.filter(skill =>
      !resumeSkills.some(resumeSkill => resumeSkill.toLowerCase().includes(skill.toLowerCase()))
    );

    const skillsScore = jobSkills.length > 0
      ? Math.round((matchedSkills.length / jobSkills.length) * 100)
      : 50;

    // Calculate experience match
    const experienceMatch = calculateExperienceMatch(job, resumeText);
    const experienceScore = experienceMatch.score;

    // Calculate education match
    const educationMatch = calculateEducationMatch(job, resumeText);
    const educationScore = educationMatch.score;

    // Calculate industry match
    const industryMatch = calculateIndustryMatch(job, resumeText);
    const industryScore = industryMatch.score;

    // Calculate overall score (weighted average)
    const overallScore = Math.round(
      (skillsScore * 0.4) +
      (experienceScore * 0.3) +
      (educationScore * 0.15) +
      (industryScore * 0.15)
    );

    // Generate additional strengths and development areas
    const additionalStrengths = generateStrengths(resumeText, job);
    const developmentAreas = generateDevelopmentAreas(resumeText, job, missingSkills);

    return {
      overall: overallScore,
      skills: skillsScore,
      experience: experienceScore,
      education: educationScore,
      industry: industryScore,
      details: {
        matchedSkills,
        missingSkills,
        experienceMatch: experienceMatch.description,
        educationMatch: educationMatch.description,
        industryMatch: industryMatch.description,
        additionalStrengths,
        developmentAreas
      }
    };
  }

  // Helper function to extract skills from resume text
  function extractSkillsFromResume(resumeText: string) {
    // Common technical skills to look for
    const commonSkills = [
      "JavaScript", "TypeScript", "Python", "Java", "C#", "C++", "Ruby", "PHP", "Swift", "Kotlin",
      "React", "Angular", "Vue", "Node.js", "Express", "Django", "Flask", "Spring", "ASP.NET",
      "SQL", "MongoDB", "PostgreSQL", "MySQL", "Oracle", "Firebase", "AWS", "Azure", "GCP",
      "Docker", "Kubernetes", "CI/CD", "Git", "GitHub", "GitLab", "Agile", "Scrum", "Kanban",
      "HTML", "CSS", "SASS", "LESS", "Bootstrap", "Tailwind", "Material UI", "Redux", "GraphQL",
      "REST API", "Microservices", "DevOps", "TDD", "BDD", "Unit Testing", "Integration Testing",
      "Machine Learning", "AI", "Data Science", "Big Data", "Analytics", "Tableau", "Power BI",
      "Project Management", "Team Leadership", "Communication", "Problem Solving", "Critical Thinking",
      "Marketing", "Sales", "Customer Service", "Finance", "Accounting", "HR", "Operations",
      "Microsoft Office", "Excel", "Word", "PowerPoint", "Outlook", "Google Workspace"
    ];

    // Extract skills from resume text
    const foundSkills = commonSkills.filter(skill =>
      resumeText.toLowerCase().includes(skill.toLowerCase())
    );

    // Add some random skills based on the resume content
    if (resumeText.toLowerCase().includes("management")) {
      foundSkills.push("Leadership");
      foundSkills.push("Team Management");
    }

    if (resumeText.toLowerCase().includes("develop")) {
      foundSkills.push("Software Development");
      foundSkills.push("Problem Solving");
    }

    if (resumeText.toLowerCase().includes("design")) {
      foundSkills.push("UI/UX Design");
      foundSkills.push("Creative Thinking");
    }

    return [...new Set(foundSkills)]; // Remove duplicates
  }

  // Helper function to calculate experience match
  function calculateExperienceMatch(job: any, resumeText: string) {
    const minimumExperience = job.minimum_experience || "";
    let requiredYears = 0;

    // Extract years from minimum experience
    const yearsMatch = minimumExperience.match(/(\d+)/);
    if (yearsMatch) {
      requiredYears = parseInt(yearsMatch[1]);
    }

    // Extract years from resume
    const resumeYearsMatch = resumeText.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of)?\s*(?:experience)/i);
    let resumeYears = 0;
    if (resumeYearsMatch) {
      resumeYears = parseInt(resumeYearsMatch[1]);
    } else {
      // If no explicit years mentioned, estimate based on content
      resumeYears = Math.floor(Math.random() * 10) + 1; // Random 1-10 years for demo
    }

    let score = 0;
    let description = "";

    if (requiredYears === 0) {
      // No specific experience required
      score = 80;
      description = `No specific experience requirement. Candidate has ${resumeYears} years of experience.`;
    } else if (resumeYears >= requiredYears) {
      // Meets or exceeds requirements
      const exceededBy = resumeYears - requiredYears;
      score = Math.min(100, 80 + exceededBy * 5);
      description = `Exceeds required experience by ${exceededBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
    } else {
      // Below requirements
      const shortBy = requiredYears - resumeYears;
      score = Math.max(30, 70 - shortBy * 10);
      description = `Below required experience by ${shortBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
    }

    return { score, description };
  }

  // Helper function to calculate education match
  function calculateEducationMatch(job: any, resumeText: string) {
    // Check for education keywords in resume
    const hasHighSchool = resumeText.toLowerCase().includes("high school");
    const hasAssociate = resumeText.toLowerCase().includes("associate");
    const hasBachelor = resumeText.toLowerCase().includes("bachelor") || resumeText.toLowerCase().includes("bs") || resumeText.toLowerCase().includes("ba");
    const hasMaster = resumeText.toLowerCase().includes("master") || resumeText.toLowerCase().includes("ms") || resumeText.toLowerCase().includes("ma");
    const hasPhD = resumeText.toLowerCase().includes("phd") || resumeText.toLowerCase().includes("doctorate");

    let educationLevel = 0;
    if (hasPhD) educationLevel = 5;
    else if (hasMaster) educationLevel = 4;
    else if (hasBachelor) educationLevel = 3;
    else if (hasAssociate) educationLevel = 2;
    else if (hasHighSchool) educationLevel = 1;

    // For demo purposes, assume job requires bachelor's degree if not specified
    const jobRequiredLevel = 3;

    let score = 0;
    let description = "";

    if (educationLevel >= jobRequiredLevel) {
      score = Math.min(100, 80 + (educationLevel - jobRequiredLevel) * 10);
      description = "Candidate meets or exceeds the education requirements for this position.";
    } else {
      score = Math.max(30, 70 - (jobRequiredLevel - educationLevel) * 20);
      description = "Candidate's education level is below what is typically required for this position.";
    }

    return { score, description };
  }

  // Helper function to calculate industry match
  function calculateIndustryMatch(job: any, resumeText: string) {
    const jobIndustry = job.industry || "";

    // Check if resume mentions the job industry
    const industryMatch = resumeText.toLowerCase().includes(jobIndustry.toLowerCase());

    let score = 0;
    let description = "";

    if (industryMatch) {
      score = 90;
      description = `Candidate has experience in the ${jobIndustry} industry.`;
    } else {
      // Check for related industries
      const relatedIndustries = getRelatedIndustries(jobIndustry);
      const hasRelatedIndustry = relatedIndustries.some(industry =>
        resumeText.toLowerCase().includes(industry.toLowerCase())
      );

      if (hasRelatedIndustry) {
        score = 70;
        description = `Candidate has experience in industries related to ${jobIndustry}.`;
      } else {
        score = 40;
        description = `Candidate does not appear to have experience in the ${jobIndustry} industry.`;
      }
    }

    return { score, description };
  }

  // Helper function to get related industries
  function getRelatedIndustries(industry: string) {
    const industryMap: Record<string, string[]> = {
      "Technology": ["Software", "IT", "Tech", "Computing", "Digital", "Information Technology"],
      "Finance": ["Banking", "Accounting", "Financial Services", "Investment", "Insurance"],
      "Healthcare": ["Medical", "Health", "Pharmaceutical", "Biotech", "Life Sciences"],
      "Retail": ["E-commerce", "Sales", "Consumer Goods", "Merchandising"],
      "Manufacturing": ["Production", "Industrial", "Engineering", "Automotive"],
      "Education": ["Teaching", "Academic", "Training", "E-learning"],
      "Other": []
    };

    return industryMap[industry] || [];
  }

  // Helper function to generate strengths
  function generateStrengths(resumeText: string, job: any) {
    const strengths = [];

    // Check for technical skills
    if (resumeText.toLowerCase().includes("programming") ||
        resumeText.toLowerCase().includes("coding") ||
        resumeText.toLowerCase().includes("software")) {
      strengths.push("Strong technical background in software development");
    }

    // Check for leadership
    if (resumeText.toLowerCase().includes("lead") ||
        resumeText.toLowerCase().includes("manage") ||
        resumeText.toLowerCase().includes("direct")) {
      strengths.push("Leadership experience and team management skills");
    }

    // Check for communication
    if (resumeText.toLowerCase().includes("communicat") ||
        resumeText.toLowerCase().includes("present") ||
        resumeText.toLowerCase().includes("speak")) {
      strengths.push("Excellent communication and presentation skills");
    }

    // Check for problem-solving
    if (resumeText.toLowerCase().includes("problem") ||
        resumeText.toLowerCase().includes("solution") ||
        resumeText.toLowerCase().includes("resolv")) {
      strengths.push("Strong problem-solving and analytical abilities");
    }

    // Add some generic strengths if we don't have enough
    if (strengths.length < 2) {
      strengths.push("Adaptable to new technologies and environments");
      strengths.push("Self-motivated with a strong work ethic");
    }

    return strengths;
  }

  // Helper function to generate development areas
  function generateDevelopmentAreas(resumeText: string, job: any, missingSkills: string[]) {
    const developmentAreas = [];

    // Add missing skills as development areas
    if (missingSkills.length > 0) {
      const skillsToAdd = missingSkills.slice(0, 2);
      skillsToAdd.forEach(skill => {
        developmentAreas.push(`Develop proficiency in ${skill}`);
      });
    }

    // Check for industry experience
    if (!resumeText.toLowerCase().includes(job.industry.toLowerCase())) {
      developmentAreas.push(`Gain more experience in the ${job.industry} industry`);
    }

    // Add some generic development areas if we don't have enough
    if (developmentAreas.length < 2) {
      developmentAreas.push("Enhance leadership and project management capabilities");
      developmentAreas.push("Develop stronger communication and presentation skills");
    }

    return developmentAreas;
  }

  // Get resumes with worker information
  app.get("/api/resumes/with-workers", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const resumes = await storage.getResumes();

      // Enhance resumes with worker information
      const enhancedResumes = await Promise.all(
        resumes.map(async (resume) => {
          const worker = await storage.getUser(resume.worker_id);
          return {
            resume,
            worker: worker ? {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            } : null
          };
        })
      );

      // Filter out resumes with no worker information
      const validResumes = enhancedResumes.filter(item => item.worker !== null);

      res.json(validResumes);
    } catch (error) {
      console.error("Error fetching resumes with workers:", error);
      res.status(500).json({ message: "Failed to fetch resumes with worker information" });
    }
  });

  // Match scoring API
  app.get("/api/matches/scores", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      // Get all jobs
      const jobs = await storage.getJobs();

      // Get all resumes with worker information
      const resumes = await storage.getResumes();

      // Generate match scores
      const matches = [];

      for (const job of jobs) {
        for (const resume of resumes) {
          // Get worker information
          const worker = await storage.getUser(resume.worker_id);
          if (!worker) continue;

          // Calculate match score
          const score = calculateMatchScore(job, resume, worker);

          matches.push({
            job,
            worker: {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            },
            resume,
            score
          });
        }
      }

      res.json(matches);
    } catch (error) {
      console.error("Error calculating match scores:", error);
      res.status(500).json({ message: "Failed to calculate match scores" });
    }
  });

  // Helper function to extract contact information from resume text
  function extractContactInfo(resumeText: string) {
    if (!resumeText) return {};

    const contact: any = {};

    // Email extraction (improved pattern)
    const emailMatch = resumeText.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
    if (emailMatch) contact.email = emailMatch[0];

    // Phone extraction (multiple patterns)
    const phonePatterns = [
      /(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/,
      /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/,
      /\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/
    ];

    for (const pattern of phonePatterns) {
      const phoneMatch = resumeText.match(pattern);
      if (phoneMatch) {
        contact.phone = phoneMatch[0];
        break;
      }
    }

    // LinkedIn extraction (improved)
    const linkedinPatterns = [
      /linkedin\.com\/in\/[a-zA-Z0-9-]+/i,
      /linkedin\.com\/pub\/[a-zA-Z0-9-]+/i,
      /www\.linkedin\.com\/in\/[a-zA-Z0-9-]+/i
    ];

    for (const pattern of linkedinPatterns) {
      const linkedinMatch = resumeText.match(pattern);
      if (linkedinMatch) {
        contact.linkedin = linkedinMatch[0].startsWith('http') ? linkedinMatch[0] : `https://${linkedinMatch[0]}`;
        break;
      }
    }

    // GitHub extraction (improved)
    const githubPatterns = [
      /github\.com\/[a-zA-Z0-9-]+/i,
      /www\.github\.com\/[a-zA-Z0-9-]+/i
    ];

    for (const pattern of githubPatterns) {
      const githubMatch = resumeText.match(pattern);
      if (githubMatch) {
        contact.github = githubMatch[0].startsWith('http') ? githubMatch[0] : `https://${githubMatch[0]}`;
        break;
      }
    }

    // Website/Portfolio extraction
    const websitePatterns = [
      /(?:https?:\/\/)?(?:www\.)?[a-zA-Z0-9-]+\.[a-zA-Z]{2,}(?:\/[^\s]*)?/g
    ];

    const websiteMatches = resumeText.match(websitePatterns[0]);
    if (websiteMatches) {
      // Filter out common domains that aren't personal websites
      const personalWebsite = websiteMatches.find(url =>
        !url.includes('linkedin.com') &&
        !url.includes('github.com') &&
        !url.includes('gmail.com') &&
        !url.includes('yahoo.com') &&
        !url.includes('outlook.com') &&
        !url.includes('hotmail.com')
      );

      if (personalWebsite) {
        contact.website = personalWebsite.startsWith('http') ? personalWebsite : `https://${personalWebsite}`;
      }
    }

    // Address extraction (basic pattern)
    const addressMatch = resumeText.match(/(?:Location|Address):\s*([^\n]+)/i);
    if (addressMatch) {
      contact.address = addressMatch[1].trim();
    } else {
      // Try to find city, state pattern
      const cityStateMatch = resumeText.match(/([A-Za-z\s]+),\s*([A-Z]{2})\s*\d{5}/);
      if (cityStateMatch) {
        contact.address = `${cityStateMatch[1]}, ${cityStateMatch[2]}`;
      }
    }

    console.log('📞 Extracted contact info:', contact);
    return contact;
  }

  // Comprehensive match scoring API
  app.get("/api/matches/comprehensive", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    // Allow all authenticated users to access this feature
    // if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can access this feature" });

    try {
      console.log("Calculating comprehensive matches...");
      console.log("User:", req.user?.email, req.user?.role);

      // Get all jobs
      const jobs = await storage.getJobs();
      console.log(`Found ${jobs.length} jobs`);

      if (jobs.length === 0) {
        console.log("No jobs found");
      } else {
        console.log("First job:", jobs[0].title);
      }

      // Get all resumes with worker information
      const resumes = await storage.getResumes();
      console.log(`Found ${resumes.length} resumes`);

      if (resumes.length === 0) {
        console.log("No resumes found");
      } else {
        console.log("First resume:", resumes[0].filename);
      }

      // Generate comprehensive match scores
      const matches = [];

      for (const job of jobs) {
        for (const resume of resumes) {
          // Get worker information
          const worker = await storage.getUser(resume.worker_id);
          if (!worker) continue;

          // Calculate comprehensive match score
          const resumeText = resume.extracted_text || '';

          // Extract job requirements
          const jobSkills = extractSkillsFromJob(job);
          const jobEducation = extractEducationFromJob(job);
          const jobExperience = extractExperienceFromJob(job);

          // Extract resume information
          const resumeSkills = extractSkillsFromResume(resumeText);
          const resumeEducation = extractEducationFromResume(resumeText);
          const resumeExperience = extractExperienceFromResume(resumeText);

          // Calculate matches
          const skillsMatch = calculateSkillsMatch(jobSkills, resumeSkills);
          const experienceMatch = calculateExperienceMatch(jobExperience, resumeExperience);
          const educationMatch = calculateEducationMatch(jobEducation, resumeEducation);
          const industryMatch = calculateIndustryMatch(job.industry || '', resumeText);

          // Calculate overall score
          const overallScore = calculateOverallScore(
            skillsMatch,
            experienceMatch,
            educationMatch,
            industryMatch
          );

          // Extract contact information from resume
          const contactInfo = extractContactInfo(resume.extracted_text || '');

          // Create match object
          const match = {
            job,
            worker: {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            },
            resume,
            contactInfo, // Add contact information to the match
            score: {
              overall: overallScore.score,
              skills: skillsMatch.score,
              experience: experienceMatch.score,
              education: educationMatch.score,
              industry: industryMatch.score,
              details: {
                matchedSkills: skillsMatch.matched,
                missingSkills: skillsMatch.missing,
                experienceMatch: experienceMatch.description,
                educationMatch: educationMatch.description,
                industryMatch: industryMatch.description,
                additionalStrengths: identifyAdditionalStrengths(resumeSkills, jobSkills),
                developmentAreas: identifyDevelopmentAreas(resumeSkills, jobSkills)
              }
            }
          };

          matches.push(match);
        }
      }

      console.log(`Generated ${matches.length} comprehensive matches`);
      res.json(matches);
    } catch (error) {
      console.error("Error calculating comprehensive match scores:", error);
      res.status(500).json({ message: "Failed to calculate comprehensive match scores" });
    }
  });

  // Helper function to calculate match score
  function calculateMatchScore(job: any, resume: any, worker: any) {
    // Extract skills from job and resume
    const jobSkills = job.required_skills
      ? job.required_skills.toLowerCase().split(',').map((s: string) => s.trim())
      : [];

    const resumeText = resume.extracted_text || '';
    const resumeSkills = extractSkillsFromResume(resumeText);

    // Calculate skills match
    const matchedSkills = jobSkills.filter(skill =>
      resumeSkills.some(resumeSkill => resumeSkill.toLowerCase().includes(skill.toLowerCase()))
    );

    const missingSkills = jobSkills.filter(skill =>
      !resumeSkills.some(resumeSkill => resumeSkill.toLowerCase().includes(skill.toLowerCase()))
    );

    const skillsScore = jobSkills.length > 0
      ? Math.round((matchedSkills.length / jobSkills.length) * 100)
      : 50;

    // Calculate experience match
    const experienceMatch = calculateExperienceMatch(job, resumeText);
    const experienceScore = experienceMatch.score;

    // Calculate education match
    const educationMatch = calculateEducationMatch(job, resumeText);
    const educationScore = educationMatch.score;

    // Calculate industry match
    const industryMatch = calculateIndustryMatch(job, resumeText);
    const industryScore = industryMatch.score;

    // Calculate overall score (weighted average)
    const overallScore = Math.round(
      (skillsScore * 0.4) +
      (experienceScore * 0.3) +
      (educationScore * 0.15) +
      (industryScore * 0.15)
    );

    // Generate additional strengths and development areas
    const additionalStrengths = generateStrengths(resumeText, job);
    const developmentAreas = generateDevelopmentAreas(resumeText, job, missingSkills);

    return {
      overall: overallScore,
      skills: skillsScore,
      experience: experienceScore,
      education: educationScore,
      industry: industryScore,
      details: {
        matchedSkills,
        missingSkills,
        experienceMatch: experienceMatch.description,
        educationMatch: educationMatch.description,
        industryMatch: industryMatch.description,
        additionalStrengths,
        developmentAreas
      }
    };
  }

  // Helper function to extract skills from resume text
  function extractSkillsFromResume(resumeText: string) {
    // Common technical skills to look for
    const commonSkills = [
      "JavaScript", "TypeScript", "Python", "Java", "C#", "C++", "Ruby", "PHP", "Swift", "Kotlin",
      "React", "Angular", "Vue", "Node.js", "Express", "Django", "Flask", "Spring", "ASP.NET",
      "SQL", "MongoDB", "PostgreSQL", "MySQL", "Oracle", "Firebase", "AWS", "Azure", "GCP",
      "Docker", "Kubernetes", "CI/CD", "Git", "GitHub", "GitLab", "Agile", "Scrum", "Kanban",
      "HTML", "CSS", "SASS", "LESS", "Bootstrap", "Tailwind", "Material UI", "Redux", "GraphQL",
      "REST API", "Microservices", "DevOps", "TDD", "BDD", "Unit Testing", "Integration Testing",
      "Machine Learning", "AI", "Data Science", "Big Data", "Analytics", "Tableau", "Power BI",
      "Project Management", "Team Leadership", "Communication", "Problem Solving", "Critical Thinking",
      "Marketing", "Sales", "Customer Service", "Finance", "Accounting", "HR", "Operations",
      "Microsoft Office", "Excel", "Word", "PowerPoint", "Outlook", "Google Workspace"
    ];

    // Extract skills from resume text
    const foundSkills = commonSkills.filter(skill =>
      resumeText.toLowerCase().includes(skill.toLowerCase())
    );

    // Add some random skills based on the resume content
    if (resumeText.toLowerCase().includes("management")) {
      foundSkills.push("Leadership");
      foundSkills.push("Team Management");
    }

    if (resumeText.toLowerCase().includes("develop")) {
      foundSkills.push("Software Development");
      foundSkills.push("Problem Solving");
    }

    if (resumeText.toLowerCase().includes("design")) {
      foundSkills.push("UI/UX Design");
      foundSkills.push("Creative Thinking");
    }

    return [...new Set(foundSkills)]; // Remove duplicates
  }

  // Helper function to calculate experience match
  function calculateExperienceMatch(job: any, resumeText: string) {
    const minimumExperience = job.minimum_experience || "";
    let requiredYears = 0;

    // Extract years from minimum experience
    const yearsMatch = minimumExperience.match(/(\d+)/);
    if (yearsMatch) {
      requiredYears = parseInt(yearsMatch[1]);
    }

    // Extract years from resume
    const resumeYearsMatch = resumeText.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of)?\s*(?:experience)/i);
    let resumeYears = 0;
    if (resumeYearsMatch) {
      resumeYears = parseInt(resumeYearsMatch[1]);
    } else {
      // If no explicit years mentioned, estimate based on content
      resumeYears = Math.floor(Math.random() * 10) + 1; // Random 1-10 years for demo
    }

    let score = 0;
    let description = "";

    if (requiredYears === 0) {
      // No specific experience required
      score = 80;
      description = `No specific experience requirement. Candidate has ${resumeYears} years of experience.`;
    } else if (resumeYears >= requiredYears) {
      // Meets or exceeds requirements
      const exceededBy = resumeYears - requiredYears;
      score = Math.min(100, 80 + exceededBy * 5);
      description = `Exceeds required experience by ${exceededBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
    } else {
      // Below requirements
      const shortBy = requiredYears - resumeYears;
      score = Math.max(30, 70 - shortBy * 10);
      description = `Below required experience by ${shortBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
    }

    return { score, description };
  }

  // Helper function to calculate education match
  function calculateEducationMatch(job: any, resumeText: string) {
    // Check for education keywords in resume
    const hasHighSchool = resumeText.toLowerCase().includes("high school");
    const hasAssociate = resumeText.toLowerCase().includes("associate");
    const hasBachelor = resumeText.toLowerCase().includes("bachelor") || resumeText.toLowerCase().includes("bs") || resumeText.toLowerCase().includes("ba");
    const hasMaster = resumeText.toLowerCase().includes("master") || resumeText.toLowerCase().includes("ms") || resumeText.toLowerCase().includes("ma");
    const hasPhD = resumeText.toLowerCase().includes("phd") || resumeText.toLowerCase().includes("doctorate");

    let educationLevel = 0;
    if (hasPhD) educationLevel = 5;
    else if (hasMaster) educationLevel = 4;
    else if (hasBachelor) educationLevel = 3;
    else if (hasAssociate) educationLevel = 2;
    else if (hasHighSchool) educationLevel = 1;

    // For demo purposes, assume job requires bachelor's degree if not specified
    const jobRequiredLevel = 3;

    let score = 0;
    let description = "";

    if (educationLevel >= jobRequiredLevel) {
      score = Math.min(100, 80 + (educationLevel - jobRequiredLevel) * 10);
      description = "Candidate meets or exceeds the education requirements for this position.";
    } else {
      score = Math.max(30, 70 - (jobRequiredLevel - educationLevel) * 20);
      description = "Candidate's education level is below what is typically required for this position.";
    }

    return { score, description };
  }

  // Helper function to calculate industry match
  function calculateIndustryMatch(job: any, resumeText: string) {
    const jobIndustry = job.industry || "";

    // Check if resume mentions the job industry
    const industryMatch = resumeText.toLowerCase().includes(jobIndustry.toLowerCase());

    let score = 0;
    let description = "";

    if (industryMatch) {
      score = 90;
      description = `Candidate has experience in the ${jobIndustry} industry.`;
    } else {
      // Check for related industries
      const relatedIndustries = getRelatedIndustries(jobIndustry);
      const hasRelatedIndustry = relatedIndustries.some(industry =>
        resumeText.toLowerCase().includes(industry.toLowerCase())
      );

      if (hasRelatedIndustry) {
        score = 70;
        description = `Candidate has experience in industries related to ${jobIndustry}.`;
      } else {
        score = 40;
        description = `Candidate does not appear to have experience in the ${jobIndustry} industry.`;
      }
    }

    return { score, description };
  }

  // Helper function to get related industries
  function getRelatedIndustries(industry: string) {
    const industryMap: Record<string, string[]> = {
      "Technology": ["Software", "IT", "Tech", "Computing", "Digital", "Information Technology"],
      "Finance": ["Banking", "Accounting", "Financial Services", "Investment", "Insurance"],
      "Healthcare": ["Medical", "Health", "Pharmaceutical", "Biotech", "Life Sciences"],
      "Retail": ["E-commerce", "Sales", "Consumer Goods", "Merchandising"],
      "Manufacturing": ["Production", "Industrial", "Engineering", "Automotive"],
      "Education": ["Teaching", "Academic", "Training", "E-learning"],
      "Other": []
    };

    return industryMap[industry] || [];
  }

  // Helper function to generate strengths
  function generateStrengths(resumeText: string, job: any) {
    const strengths = [];

    // Check for technical skills
    if (resumeText.toLowerCase().includes("programming") ||
        resumeText.toLowerCase().includes("coding") ||
        resumeText.toLowerCase().includes("software")) {
      strengths.push("Strong technical background in software development");
    }

    // Check for leadership
    if (resumeText.toLowerCase().includes("lead") ||
        resumeText.toLowerCase().includes("manage") ||
        resumeText.toLowerCase().includes("direct")) {
      strengths.push("Leadership experience and team management skills");
    }

    // Check for communication
    if (resumeText.toLowerCase().includes("communicat") ||
        resumeText.toLowerCase().includes("present") ||
        resumeText.toLowerCase().includes("speak")) {
      strengths.push("Excellent communication and presentation skills");
    }

    // Check for problem-solving
    if (resumeText.toLowerCase().includes("problem") ||
        resumeText.toLowerCase().includes("solution") ||
        resumeText.toLowerCase().includes("resolv")) {
      strengths.push("Strong problem-solving and analytical abilities");
    }

    // Add some generic strengths if we don't have enough
    if (strengths.length < 2) {
      strengths.push("Adaptable to new technologies and environments");
      strengths.push("Self-motivated with a strong work ethic");
    }

    return strengths;
  }

  // Helper function to generate development areas
  function generateDevelopmentAreas(resumeText: string, job: any, missingSkills: string[]) {
    const developmentAreas = [];

    // Add missing skills as development areas
    if (missingSkills.length > 0) {
      const skillsToAdd = missingSkills.slice(0, 2);
      skillsToAdd.forEach(skill => {
        developmentAreas.push(`Develop proficiency in ${skill}`);
      });
    }

    // Check for industry experience
    if (!resumeText.toLowerCase().includes(job.industry.toLowerCase())) {
      developmentAreas.push(`Gain more experience in the ${job.industry} industry`);
    }

    // Add some generic development areas if we don't have enough
    if (developmentAreas.length < 2) {
      developmentAreas.push("Enhance leadership and project management capabilities");
      developmentAreas.push("Develop stronger communication and presentation skills");
    }

    return developmentAreas;
  }

  // Get resumes with worker information
  app.get("/api/resumes/with-workers", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      const resumes = await storage.getResumes();

      // Enhance resumes with worker information
      const enhancedResumes = await Promise.all(
        resumes.map(async (resume) => {
          const worker = await storage.getUser(resume.worker_id);
          return {
            resume,
            worker: worker ? {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            } : null
          };
        })
      );

      // Filter out resumes with no worker information
      const validResumes = enhancedResumes.filter(item => item.worker !== null);

      res.json(validResumes);
    } catch (error) {
      console.error("Error fetching resumes with workers:", error);
      res.status(500).json({ message: "Failed to fetch resumes with worker information" });
    }
  });

  // Match scoring API
  app.get("/api/matches/scores", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });

    try {
      // Get all jobs
      const jobs = await storage.getJobs();

      // Get all resumes with worker information
      const resumes = await storage.getResumes();

      // Generate match scores
      const matches = [];

      for (const job of jobs) {
        for (const resume of resumes) {
          // Get worker information
          const worker = await storage.getUser(resume.worker_id);
          if (!worker) continue;

          // Calculate match score
          const score = calculateMatchScore(job, resume, worker);

          matches.push({
            job,
            worker: {
              id: worker.id,
              name: worker.name,
              email: worker.email,
              role: worker.role
            },
            resume,
            score
          });
        }
      }

      res.json(matches);
    } catch (error) {
      console.error("Error calculating match scores:", error);
      res.status(500).json({ message: "Failed to calculate match scores" });
    }
  });

  // Helper function to calculate match score
  function calculateMatchScore(job: any, resume: any, worker: any) {
    // Extract skills from job and resume
    const jobSkills = job.required_skills
      ? job.required_skills.toLowerCase().split(',').map((s: string) => s.trim())
      : [];

    const resumeText = resume.extracted_text || '';
    const resumeSkills = extractSkillsFromResume(resumeText);

    // Calculate skills match
    const matchedSkills = jobSkills.filter(skill =>
      resumeSkills.some(resumeSkill => resumeSkill.toLowerCase().includes(skill.toLowerCase()))
    );

    const missingSkills = jobSkills.filter(skill =>
      !resumeSkills.some(resumeSkill => resumeSkill.toLowerCase().includes(skill.toLowerCase()))
    );

    const skillsScore = jobSkills.length > 0
      ? Math.round((matchedSkills.length / jobSkills.length) * 100)
      : 50;

    // Calculate experience match
    const experienceMatch = calculateExperienceMatch(job, resumeText);
    const experienceScore = experienceMatch.score;

    // Calculate education match
    const educationMatch = calculateEducationMatch(job, resumeText);
    const educationScore = educationMatch.score;

    // Calculate industry match
    const industryMatch = calculateIndustryMatch(job, resumeText);
    const industryScore = industryMatch.score;

    // Calculate overall score (weighted average)
    const overallScore = Math.round(
      (skillsScore * 0.4) +
      (experienceScore * 0.3) +
      (educationScore * 0.15) +
      (industryScore * 0.15)
    );

    // Generate additional strengths and development areas
    const additionalStrengths = generateStrengths(resumeText, job);
    const developmentAreas = generateDevelopmentAreas(resumeText, job, missingSkills);

    return {
      overall: overallScore,
      skills: skillsScore,
      experience: experienceScore,
      education: educationScore,
      industry: industryScore,
      details: {
        matchedSkills,
        missingSkills,
        experienceMatch: experienceMatch.description,
        educationMatch: educationMatch.description,
        industryMatch: industryMatch.description,
        additionalStrengths,
        developmentAreas
      }
    };
  }

  // Helper function to extract skills from resume text
  function extractSkillsFromResume(resumeText: string) {
    // Common technical skills to look for
    const commonSkills = [
      "JavaScript", "TypeScript", "Python", "Java", "C#", "C++", "Ruby", "PHP", "Swift", "Kotlin",
      "React", "Angular", "Vue", "Node.js", "Express", "Django", "Flask", "Spring", "ASP.NET",
      "SQL", "MongoDB", "PostgreSQL", "MySQL", "Oracle", "Firebase", "AWS", "Azure", "GCP",
      "Docker", "Kubernetes", "CI/CD", "Git", "GitHub", "GitLab", "Agile", "Scrum", "Kanban",
      "HTML", "CSS", "SASS", "LESS", "Bootstrap", "Tailwind", "Material UI", "Redux", "GraphQL",
      "REST API", "Microservices", "DevOps", "TDD", "BDD", "Unit Testing", "Integration Testing",
      "Machine Learning", "AI", "Data Science", "Big Data", "Analytics", "Tableau", "Power BI",
      "Project Management", "Team Leadership", "Communication", "Problem Solving", "Critical Thinking",
      "Marketing", "Sales", "Customer Service", "Finance", "Accounting", "HR", "Operations",
      "Microsoft Office", "Excel", "Word", "PowerPoint", "Outlook", "Google Workspace"
    ];

    // Extract skills from resume text
    const foundSkills = commonSkills.filter(skill =>
      resumeText.toLowerCase().includes(skill.toLowerCase())
    );

    // Add some random skills based on the resume content
    if (resumeText.toLowerCase().includes("management")) {
      foundSkills.push("Leadership");
      foundSkills.push("Team Management");
    }

    if (resumeText.toLowerCase().includes("develop")) {
      foundSkills.push("Software Development");
      foundSkills.push("Problem Solving");
    }

    if (resumeText.toLowerCase().includes("design")) {
      foundSkills.push("UI/UX Design");
      foundSkills.push("Creative Thinking");
    }

    return [...new Set(foundSkills)]; // Remove duplicates
  }

  // Helper function to calculate experience match
  function calculateExperienceMatch(job: any, resumeText: string) {
    const minimumExperience = job.minimum_experience || "";
    let requiredYears = 0;

    // Extract years from minimum experience
    const yearsMatch = minimumExperience.match(/(\d+)/);
    if (yearsMatch) {
      requiredYears = parseInt(yearsMatch[1]);
    }

    // Extract years from resume
    let resumeYears = 0;

    if (resumeText && typeof resumeText === 'string') {
      const resumeYearsMatch = resumeText.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of)?\s*(?:experience)/i);
      if (resumeYearsMatch) {
        resumeYears = parseInt(resumeYearsMatch[1]);
      } else {
        // If no explicit years mentioned, estimate based on content
        resumeYears = Math.floor(Math.random() * 10) + 1; // Random 1-10 years for demo
      }
    } else {
      // If resumeText is not a string, use a default value
      resumeYears = 1; // Default to 1 year if no text is available
    }

    let score = 0;
    let description = "";

    if (requiredYears === 0) {
      // No specific experience required
      score = 80;
      description = `No specific experience requirement. Candidate has ${resumeYears} years of experience.`;
    } else if (resumeYears >= requiredYears) {
      // Meets or exceeds requirements
      const exceededBy = resumeYears - requiredYears;
      score = Math.min(100, 80 + exceededBy * 5);
      description = `Exceeds required experience by ${exceededBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
    } else {
      // Below requirements
      const shortBy = requiredYears - resumeYears;
      score = Math.max(30, 70 - shortBy * 10);
      description = `Below required experience by ${shortBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
    }

    return { score, description };
  }

  // Helper function to calculate education match
  function calculateEducationMatch(job: any, resumeText: string) {
    // Check for education keywords in resume
    let hasHighSchool = false;
    let hasAssociate = false;
    let hasBachelor = false;
    let hasMaster = false;
    let hasPhD = false;

    if (resumeText && typeof resumeText === 'string') {
      const lowerText = resumeText.toLowerCase();
      hasHighSchool = lowerText.includes("high school");
      hasAssociate = lowerText.includes("associate");
      hasBachelor = lowerText.includes("bachelor") || lowerText.includes("bs") || lowerText.includes("ba");
      hasMaster = lowerText.includes("master") || lowerText.includes("ms") || lowerText.includes("ma");
      hasPhD = lowerText.includes("phd") || lowerText.includes("doctorate");
    }

    let educationLevel = 0;
    if (hasPhD) educationLevel = 5;
    else if (hasMaster) educationLevel = 4;
    else if (hasBachelor) educationLevel = 3;
    else if (hasAssociate) educationLevel = 2;
    else if (hasHighSchool) educationLevel = 1;

    // For demo purposes, assume job requires bachelor's degree if not specified
    const jobRequiredLevel = 3;

    let score = 0;
    let description = "";

    if (educationLevel >= jobRequiredLevel) {
      score = Math.min(100, 80 + (educationLevel - jobRequiredLevel) * 10);
      description = "Candidate meets or exceeds the education requirements for this position.";
    } else {
      score = Math.max(30, 70 - (jobRequiredLevel - educationLevel) * 20);
      description = "Candidate's education level is below what is typically required for this position.";
    }

    return { score, description };
  }

  // Helper function to calculate industry match
  function calculateIndustryMatch(job: any, resumeText: string) {
    const jobIndustry = job.industry || "";

    if (!jobIndustry) {
      return {
        score: 50,
        description: "No specific industry requirement specified."
      };
    }

    if (!resumeText || typeof resumeText !== 'string') {
      return {
        score: 40,
        description: `Unable to determine if candidate has experience in the ${jobIndustry} industry.`
      };
    }

    // Check if resume mentions the job industry
    const lowerResumeText = resumeText.toLowerCase();
    const industryMatch = lowerResumeText.includes(jobIndustry.toLowerCase());

    let score = 0;
    let description = "";

    if (industryMatch) {
      score = 90;
      description = `Candidate has experience in the ${jobIndustry} industry.`;
    } else {
      // Check for related industries
      const relatedIndustries = getRelatedIndustries(jobIndustry);
      const hasRelatedIndustry = relatedIndustries.some(industry =>
        lowerResumeText.includes(industry.toLowerCase())
      );

      if (hasRelatedIndustry) {
        score = 70;
        description = `Candidate has experience in industries related to ${jobIndustry}.`;
      } else {
        score = 40;
        description = `Candidate does not appear to have experience in the ${jobIndustry} industry.`;
      }
    }

    return { score, description };
  }

  // Helper function to get related industries
  function getRelatedIndustries(industry: string) {
    const industryMap: Record<string, string[]> = {
      "Technology": ["Software", "IT", "Tech", "Computing", "Digital", "Information Technology"],
      "Finance": ["Banking", "Accounting", "Financial Services", "Investment", "Insurance"],
      "Healthcare": ["Medical", "Health", "Pharmaceutical", "Biotech", "Life Sciences"],
      "Retail": ["E-commerce", "Sales", "Consumer Goods", "Merchandising"],
      "Manufacturing": ["Production", "Industrial", "Engineering", "Automotive"],
      "Education": ["Teaching", "Academic", "Training", "E-learning"],
      "Other": []
    };

    return industryMap[industry] || [];
  }

  // Helper function to generate strengths
  function generateStrengths(resumeText: string, job: any) {
    const strengths = [];

    // Check for technical skills
    if (resumeText.toLowerCase().includes("programming") ||
        resumeText.toLowerCase().includes("coding") ||
        resumeText.toLowerCase().includes("software")) {
      strengths.push("Strong technical background in software development");
    }

    // Check for leadership
    if (resumeText.toLowerCase().includes("lead") ||
        resumeText.toLowerCase().includes("manage") ||
        resumeText.toLowerCase().includes("direct")) {
      strengths.push("Leadership experience and team management skills");
    }

    // Check for communication
    if (resumeText.toLowerCase().includes("communicat") ||
        resumeText.toLowerCase().includes("present") ||
        resumeText.toLowerCase().includes("speak")) {
      strengths.push("Excellent communication and presentation skills");
    }

    // Check for problem-solving
    if (resumeText.toLowerCase().includes("problem") ||
        resumeText.toLowerCase().includes("solution") ||
        resumeText.toLowerCase().includes("resolv")) {
      strengths.push("Strong problem-solving and analytical abilities");
    }

    // Add some generic strengths if we don't have enough
    if (strengths.length < 2) {
      strengths.push("Adaptable to new technologies and environments");
      strengths.push("Self-motivated with a strong work ethic");
    }

    return strengths;
  }

  // Helper function to generate development areas
  function generateDevelopmentAreas(resumeText: string, job: any, missingSkills: string[]) {
    const developmentAreas = [];

    // Add missing skills as development areas
    if (missingSkills.length > 0) {
      const skillsToAdd = missingSkills.slice(0, 2);
      skillsToAdd.forEach(skill => {
        developmentAreas.push(`Develop proficiency in ${skill}`);
      });
    }

    // Check for industry experience
    if (!resumeText.toLowerCase().includes(job.industry.toLowerCase())) {
      developmentAreas.push(`Gain more experience in the ${job.industry} industry`);
    }

    // Add some generic development areas if we don't have enough
    if (developmentAreas.length < 2) {
      developmentAreas.push("Enhance leadership and project management capabilities");
      developmentAreas.push("Develop stronger communication and presentation skills");
    }

    return developmentAreas;
  }

  // View resume content route (for in-app viewing)
  app.get("/api/resumes/view/:id", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const resumeId = parseInt(req.params.id);

      // Get the resume
      const resume = await storage.getResume(resumeId);
      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      // Check access permissions
      // Workers can only view their own resume
      // Employers can view any resume
      if (req.user!.role === "worker" && req.user!.id !== resume.worker_id) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Convert relative path to absolute path
      const absolutePath = getAbsolutePath(resume.file_path);
      console.log(`Converting relative path ${resume.file_path} to absolute path ${absolutePath}`);

      // Check if file exists
      if (!fs.existsSync(absolutePath)) {
        console.error(`Resume file not found at ${absolutePath}`);
        return res.status(404).json({ message: "Resume file not found" });
      }

      // For PDF files, we can send the file directly for browser viewing
      if (resume.file_type === 'pdf') {
        // Set appropriate headers for inline viewing
        res.setHeader('Content-disposition', `inline; filename=${resume.filename}`);
        res.setHeader('Content-type', 'application/pdf');

        // Stream the file
        const fileStream = fs.createReadStream(absolutePath);
        fileStream.pipe(res);
        return;
      }

      // For DOCX files, convert to HTML for browser viewing
      if (resume.file_type === 'docx') {
        try {
          const { convertDocxToHtml } = await import('./docx-viewer');
          const htmlContent = await convertDocxToHtml(absolutePath);

          // Set appropriate headers for HTML content
          res.setHeader('Content-Type', 'text/html');
          res.send(htmlContent);
          return;
        } catch (error) {
          console.error('Error converting DOCX to HTML:', error);
          // Fall through to the extracted text option if conversion fails
        }
      }

      // For other file types or if conversion fails, return the extracted text if available
      if (resume.extracted_text) {
        // Return HTML with the extracted text for better viewing
        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>${resume.filename}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 20px;
                color: #333;
              }
              pre {
                white-space: pre-wrap;
                font-family: inherit;
                margin: 0;
              }
              .document-content {
                max-width: 800px;
                margin: 0 auto;
              }
            </style>
          </head>
          <body>
            <div class="document-content">
              <pre>${resume.extracted_text}</pre>
            </div>
          </body>
          </html>
        `;

        res.setHeader('Content-Type', 'text/html');
        res.send(htmlContent);
        return;
      } else {
        return res.status(400).json({
          message: "This file type cannot be viewed in-browser. Please download the file instead.",
          fileType: resume.file_type
        });
      }
    } catch (error) {
      console.error("View resume error:", error);
      return res.status(500).json({ message: "Failed to view resume" });
    }
  });

  // Delete resume route
  app.delete("/api/resumes/:id", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      const resumeId = parseInt(req.params.id);

      // Get the resume
      const resume = await storage.getResume(resumeId);
      if (!resume) {
        return res.status(404).json({ message: "Resume not found" });
      }

      // Workers can only delete their own resume
      if (req.user!.id !== resume.worker_id) {
        return res.status(403).json({ message: "Access denied" });
      }

      // Delete the file
      try {
        // Convert relative path to absolute path
        const absolutePath = getAbsolutePath(resume.file_path);
        console.log(`Converting relative path ${resume.file_path} to absolute path ${absolutePath}`);

        await promisify(fs.unlink)(absolutePath);
        console.log(`Deleted resume file at ${absolutePath}`);
      } catch (error) {
        console.error("Error deleting resume file:", error);
      }

      // Delete the database record
      await storage.deleteResume(resumeId);

      return res.status(200).json({ message: "Resume deleted successfully" });
    } catch (error) {
      console.error("Delete resume error:", error);
      return res.status(500).json({ message: "Failed to delete resume" });
    }
  });

  // Get reskilling suggestions based on resume or job category
  app.get("/api/reskilling-suggestions", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Only workers can get reskilling suggestions
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Only workers can get reskilling suggestions" });
      }

      // Get worker's resume if it exists
      const resume = await storage.getResumeByWorkerId(req.user!.id);

      // Get worker's matches to find job categories they're interested in
      const matches = await storage.getMatchesByWorkerId(req.user!.id);
      const jobIds = matches.map(match => match.job_id);

      let suggestions = [];

      // Check if OpenAI API key is set
      if (!process.env.OPENAI_API_KEY) {
        console.warn("OPENAI_API_KEY is not set, using fallback suggestions");
        suggestions = [
          {
            title: "Web Development Bootcamp",
            description: "Learn modern web development with React, Node.js, and Express.",
            relevance: "High",
            timeCommitment: "12 weeks",
            transferableSkills: ["Problem solving", "Logical thinking", "Attention to detail"]
          },
          {
            title: "Data Science Fundamentals",
            description: "Master data analysis techniques and statistical modeling.",
            relevance: "Medium",
            timeCommitment: "8 weeks",
            transferableSkills: ["Analytical thinking", "Communication", "Research"]
          },
          {
            title: "Project Management Professional (PMP)",
            description: "Industry-recognized certification for project management.",
            relevance: "High",
            timeCommitment: "6 weeks",
            transferableSkills: ["Organization", "Leadership", "Communication"]
          }
        ];
      } else if (resume) {
        // If they have a resume uploaded, analyze it
        try {
          // In a real implementation, we would extract text from PDF or DOCX
          // For this demo, we'll just use a sample text
          const resumeText = "Experienced software developer with 5 years of experience in JavaScript, React, and Node.js. Strong problem-solving skills and team collaboration.";

          // Get job categories they're interested in
          const jobs = await Promise.all(jobIds.map(id => storage.getJob(id)));
          const jobTitles = jobs.filter(job => job).map(job => job!.title);
          const desiredRole = jobTitles.length > 0 ? jobTitles[0] : undefined;

          // Analyze resume with OpenAI
          const analysis = await analyzeResume(resumeText, desiredRole);
          // Ensure suggestions is an array
          suggestions = Array.isArray(analysis.suggestions) ? analysis.suggestions : [];
        } catch (error) {
          console.error("Error analyzing resume:", error);
          // Fall back to default suggestions if analysis fails
          const reskillingPath = await generateReskillingPath(["JavaScript", "Problem Solving"], "Software Developer");
          // Ensure suggestions is always an array
          suggestions = Array.isArray(reskillingPath) ? reskillingPath : [];
        }
      } else {
        // If no resume, use the worker's name and desired job to generate suggestions
        const reskillingPath = await generateReskillingPath(["Problem Solving", "Communication"], "Tech Professional");
        // Ensure suggestions is always an array
        suggestions = Array.isArray(reskillingPath) ? reskillingPath : [];
      }

      return res.json({
        hasResume: !!resume,
        suggestions
      });
    } catch (error) {
      console.error("Reskilling suggestions error:", error);
      return res.status(500).json({ message: "Failed to get reskilling suggestions" });
    }
  });

  // PathCoach Feature Routes

  // 1. Growth Memory Engine - Store and retrieve skilling history
  app.get("/api/pathcoach/skilling-history", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can access their skilling history
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const history = await storage.getSkillingHistory(workerId);

      return res.json(history);
    } catch (error) {
      console.error("Get skilling history error:", error);
      return res.status(500).json({ message: "Failed to get skilling history" });
    }
  });

  app.post("/api/pathcoach/skilling-history", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can save their skilling history
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const { suggestions } = req.body;

      if (!suggestions) {
        return res.status(400).json({ message: "Suggestions are required" });
      }

      const history = await storage.saveSkillingHistory({
        worker_id: workerId,
        suggestions
      });

      return res.status(201).json(history);
    } catch (error) {
      console.error("Save skilling history error:", error);
      return res.status(500).json({ message: "Failed to save skilling history" });
    }
  });

  // 2. Career Quantum Leap Generator
  app.get("/api/pathcoach/career-transitions", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can access their career transition plans
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const plans = await storage.getCareerTransitionPlans(workerId);

      return res.json(plans);
    } catch (error) {
      console.error("Get career transitions error:", error);
      return res.status(500).json({ message: "Failed to get career transitions" });
    }
  });

  app.post("/api/pathcoach/career-transitions", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can save their career transition plans
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const { current_role, dream_role, transition_plan } = req.body;

      if (!dream_role || !transition_plan) {
        return res.status(400).json({ message: "Dream role and transition plan are required" });
      }

      const plan = await storage.saveCareerTransitionPlan({
        worker_id: workerId,
        current_role,
        dream_role,
        transition_plan
      });

      return res.status(201).json(plan);
    } catch (error) {
      console.error("Save career transition error:", error);
      return res.status(500).json({ message: "Failed to save career transition" });
    }
  });

  // 3. Skill Context Analyzer - Worker Profile
  app.get("/api/pathcoach/worker-profile", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can access their profile
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const profile = await storage.getWorkerProfile(workerId);

      if (!profile) {
        return res.status(404).json({ message: "Profile not found" });
      }

      return res.json(profile);
    } catch (error) {
      console.error("Get worker profile error:", error);
      return res.status(500).json({ message: "Failed to get worker profile" });
    }
  });

  app.post("/api/pathcoach/worker-profile", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can save their profile
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const { skills_context } = req.body;

      const profile = await storage.createOrUpdateWorkerProfile({
        worker_id: workerId,
        skills_context
      });

      return res.status(201).json(profile);
    } catch (error) {
      console.error("Save worker profile error:", error);
      return res.status(500).json({ message: "Failed to save worker profile" });
    }
  });

  // 4. GPT Reflection Journal
  app.get("/api/pathcoach/reflection-journals", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can access their journals
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const journals = await storage.getReflectionJournals(workerId);

      return res.json(journals);
    } catch (error) {
      console.error("Get reflection journals error:", error);
      return res.status(500).json({ message: "Failed to get reflection journals" });
    }
  });

  app.post("/api/pathcoach/reflection-journals", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can save their journals
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const { prompt, entry } = req.body;

      if (!prompt || !entry) {
        return res.status(400).json({ message: "Prompt and entry are required" });
      }

      const journal = await storage.saveReflectionJournal({
        worker_id: workerId,
        prompt,
        entry
      });

      return res.status(201).json(journal);
    } catch (error) {
      console.error("Save reflection journal error:", error);
      return res.status(500).json({ message: "Failed to save reflection journal" });
    }
  });

  // 5. Workplace Soul Match - Worker Values
  app.get("/api/pathcoach/worker-values", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can access their values
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const values = await storage.getWorkerValues(workerId);

      if (!values) {
        return res.status(404).json({ message: "Values not found" });
      }

      return res.json(values);
    } catch (error) {
      console.error("Get worker values error:", error);
      return res.status(500).json({ message: "Failed to get worker values" });
    }
  });

  app.post("/api/pathcoach/worker-values", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can save their values
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const { values_match_enabled, core_values, mission_statement } = req.body;

      const values = await storage.saveWorkerValues({
        worker_id: workerId,
        values_match_enabled,
        core_values,
        mission_statement
      });

      return res.status(201).json(values);
    } catch (error) {
      console.error("Save worker values error:", error);
      return res.status(500).json({ message: "Failed to save worker values" });
    }
  });

  // 6. AI Mentor Constellations
  app.get("/api/pathcoach/mentor-preferences", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can access their mentor preferences
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const preferences = await storage.getMentorPreference(workerId);

      if (!preferences) {
        return res.status(404).json({ message: "Mentor preferences not found" });
      }

      return res.json(preferences);
    } catch (error) {
      console.error("Get mentor preferences error:", error);
      return res.status(500).json({ message: "Failed to get mentor preferences" });
    }
  });

  app.post("/api/pathcoach/mentor-preferences", async (req, res) => {
    if (!req.isAuthenticated()) {
      return res.status(401).json({ message: "Not authenticated" });
    }

    try {
      // Only workers can save their mentor preferences
      if (req.user!.role !== "worker") {
        return res.status(403).json({ message: "Access denied" });
      }

      const workerId = req.user!.id;
      const { mentor_tone } = req.body;

      // Validate mentor tone
      if (mentor_tone && !['rebel', 'leader', 'hacker'].includes(mentor_tone)) {
        return res.status(400).json({ message: "Invalid mentor tone" });
      }

      const preferences = await storage.saveMentorPreference({
        worker_id: workerId,
        mentor_tone
      });

      return res.status(201).json(preferences);
    } catch (error) {
      console.error("Save mentor preferences error:", error);
      return res.status(500).json({ message: "Failed to save mentor preferences" });
    }
  });

  // Market Analysis route
  app.post("/api/market-analysis", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        console.log("Market analysis: User not authenticated");
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Only employers and admins can use market analysis
      if (req.user!.role !== "employer" && req.user!.role !== "admin") {
        console.log(`Market analysis: Access denied for role ${req.user!.role}`);
        return res.status(403).json({ message: "Access denied" });
      }

      const { field } = req.body;

      if (!field) {
        console.log("Market analysis: Field is required");
        return res.status(400).json({ message: "Field is required" });
      }

      // Log the request
      console.log(`Market analysis requested for field: ${field}`);

      // Call OpenAI to analyze the market
      console.log("Calling analyzeMarket function...");
      const analysis = await analyzeMarket(field);
      console.log("Analysis received:", JSON.stringify(analysis).substring(0, 100) + "...");

      // Return the analysis
      return res.json(analysis);
    } catch (error) {
      console.error("Market analysis error:", error);
      return res.status(500).json({ message: "Failed to analyze market" });
    }
  });

  // Test route
  app.get("/api/test", (req, res) => {
    res.json({ message: "API is working!" });
  });

  // Admin routes
  // Admin dashboard statistics
  app.get("/api/admin/stats/platform", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // Get all users
      // Since there's no direct method to get all users, we'll collect them manually
      const users = [];
      // Start with a reasonable number of user IDs to check
      for (let i = 1; i <= 100; i++) {
        const user = await storage.getUser(i);
        if (user) {
          users.push(user);
        }
      }

      // Get all jobs
      const jobs = await storage.getJobs();

      // Get all matches (applications)
      const matches = await storage.getMatches();

      // Get all resumes
      const resumes = await storage.getResumes();

      // Get approved domains
      const domains = await storage.getApprovedDomains();

      // Calculate statistics
      const stats = {
        total_users: users.length,
        employers: users.filter(user => user.role === 'employer').length,
        workers: users.filter(user => user.role === 'worker').length,
        admin_users: users.filter(user => user.role === 'admin').length,
        total_jobs: jobs.length,
        total_applications: matches.length,
        total_resumes: resumes.length,
        active_users_last_30_days: users.length, // Placeholder - would need login tracking
        new_users_last_30_days: users.filter(user => {
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return new Date(user.created_at) >= thirtyDaysAgo;
        }).length,
        verified_domains: domains.length
      };

      res.json(stats);
    } catch (error) {
      console.error("Error fetching platform statistics:", error);
      res.status(500).json({ message: "Failed to fetch platform statistics" });
    }
  });

  // Admin user statistics
  app.get("/api/admin/stats/users", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // Get all users
      // Since there's no direct method to get all users, we'll collect them manually
      const users = [];
      // Start with a reasonable number of user IDs to check
      for (let i = 1; i <= 100; i++) {
        const user = await storage.getUser(i);
        if (user) {
          users.push(user);
        }
      }

      // Calculate statistics
      const stats = {
        total: users.length,
        verified: users.filter(user => user.is_verified).length,
        unverified: users.filter(user => !user.is_verified).length,
        active_last_30_days: users.length, // Placeholder - would need login tracking
        inactive: 0, // Placeholder - would need login tracking
        employers: users.filter(user => user.role === 'employer').length,
        workers: users.filter(user => user.role === 'worker').length,
        admins: users.filter(user => user.role === 'admin').length
      };

      res.json(stats);
    } catch (error) {
      console.error("Error fetching user statistics:", error);
      res.status(500).json({ message: "Failed to fetch user statistics" });
    }
  });

  // Admin user management
  app.get("/api/admin/users", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // Get pagination parameters
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;

      // Use cache for users list with 2-minute TTL
      const cacheKey = 'admin:users';
      const allUsers = await cache.getOrSet(cacheKey, async () => {
        console.log('Cache miss for users, fetching from database');
        // Get all users
        // Since there's no direct method to get all users, we'll collect them manually
        const users = [];
        // Start with a reasonable number of user IDs to check
        for (let i = 1; i <= 200; i++) {
          const user = await storage.getUser(i);
          if (user) {
            users.push(user);
          }
        }

        // Add status field (placeholder - would need login tracking)
        return users.map(user => ({
          ...user,
          status: 'active' as 'active' | 'inactive' | 'suspended',
          last_login: new Date().toISOString() // Placeholder
        }));
      }, 2 * 60 * 1000); // 2 minute cache TTL

      // Apply filters if provided
      let filteredUsers = [...allUsers];

      const role = req.query.role as string;
      if (role && role !== 'all') {
        filteredUsers = filteredUsers.filter(user => user.role === role);
      }

      const verified = req.query.verified as string;
      if (verified === 'true') {
        filteredUsers = filteredUsers.filter(user => user.is_verified);
      } else if (verified === 'false') {
        filteredUsers = filteredUsers.filter(user => !user.is_verified);
      }

      // Apply pagination
      const totalUsers = filteredUsers.length;
      const totalPages = Math.ceil(totalUsers / limit);
      const paginatedUsers = filteredUsers.slice(offset, offset + limit);

      res.json({
        users: paginatedUsers,
        pagination: {
          total: totalUsers,
          page,
          limit,
          totalPages
        }
      });
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).json({ message: "Failed to fetch users" });
    }
  });

  // Admin domain management
  app.get("/api/admin/domains", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // Use cache for domains with 5-minute TTL
      const cacheKey = 'admin:domains';
      const enhancedDomains = await cache.getOrSet(cacheKey, async () => {
        console.log('Cache miss for domains, fetching from database');
        // Get all approved domains
        const domains = await storage.getApprovedDomains();

        // Get all users to count verified workers per domain
        // Since there's no direct method to get all users, we'll collect them manually
        const users = [];
        // Start with a reasonable number of user IDs to check
        for (let i = 1; i <= 100; i++) {
          const user = await storage.getUser(i);
          if (user) {
            users.push(user);
          }
        }

        // Enhance domains with worker counts and map field names
        return domains.map(domain => {
          const domainWorkers = users.filter(user =>
            user.role === 'worker' &&
            user.is_verified &&
            user.email.endsWith(`@${domain.domain}`)
          );

          return {
            ...domain,
            domain_name: domain.domain, // Map domain to domain_name for frontend compatibility
            verified_workers_count: domainWorkers.length
          };
        });
      }, 5 * 60 * 1000); // 5 minute cache TTL

      res.json(enhancedDomains);
    } catch (error) {
      console.error("Error fetching domains:", error);
      res.status(500).json({ message: "Failed to fetch domains" });
    }
  });

  // Admin add domain
  app.post("/api/admin/domains", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const { domain_name, description, is_active } = req.body;

      if (!domain_name) {
        return res.status(400).json({ message: "Domain name is required" });
      }

      // Check if domain already exists
      const existingDomain = await storage.getApprovedDomainByName(domain_name);
      if (existingDomain) {
        return res.status(400).json({ message: "Domain already exists" });
      }

      // Create new domain
      const newDomain = await storage.createApprovedDomain({
        domain: domain_name, // Map domain_name to domain for database compatibility
        description,
        is_active: is_active !== undefined ? is_active : true
      });

      // Add domain_name field for frontend compatibility
      newDomain.domain_name = newDomain.domain;

      // Invalidate domains cache
      cache.delete('admin:domains');

      res.status(201).json(newDomain);
    } catch (error) {
      console.error("Error adding domain:", error);
      res.status(500).json({ message: "Failed to add domain" });
    }
  });

  // Admin update domain
  app.put("/api/admin/domains/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const domainId = parseInt(req.params.id);
      const { domain_name, description, is_active } = req.body;

      // Check if domain exists
      const existingDomain = await storage.getApprovedDomain(domainId);
      if (!existingDomain) {
        return res.status(404).json({ message: "Domain not found" });
      }

      // Update domain
      const updatedDomain = await storage.updateApprovedDomain(domainId, {
        domain: domain_name, // Map domain_name to domain for database compatibility
        description,
        is_active
      });

      // Add domain_name field for frontend compatibility
      if (updatedDomain) {
        updatedDomain.domain_name = updatedDomain.domain;
      }

      // Invalidate domains cache
      cache.delete('admin:domains');

      res.json(updatedDomain);
    } catch (error) {
      console.error("Error updating domain:", error);
      res.status(500).json({ message: "Failed to update domain" });
    }
  });

  // Admin delete domain
  app.delete("/api/admin/domains/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const domainId = parseInt(req.params.id);

      // Check if domain exists
      const existingDomain = await storage.getApprovedDomain(domainId);
      if (!existingDomain) {
        return res.status(404).json({ message: "Domain not found" });
      }

      // Delete domain
      await storage.deleteApprovedDomain(domainId);

      // Invalidate domains cache
      cache.delete('admin:domains');

      res.json({ message: "Domain deleted successfully" });
    } catch (error) {
      console.error("Error deleting domain:", error);
      res.status(500).json({ message: "Failed to delete domain" });
    }
  });

  // Admin bulk import domains
  app.post("/api/admin/domains/bulk-import", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const { domains } = req.body;

      if (!domains || !Array.isArray(domains) || domains.length === 0) {
        return res.status(400).json({ message: "Domains list is required" });
      }

      // Get existing domains to avoid duplicates
      const existingDomains = await storage.getApprovedDomains();
      const existingDomainNames = existingDomains.map(d => d.domain.toLowerCase());

      // Track import results
      let imported = 0;
      let skipped = 0;

      // Process each domain
      for (const domain of domains) {
        // Skip if domain already exists
        if (existingDomainNames.includes(domain.toLowerCase())) {
          skipped++;
          continue;
        }

        // Create new domain
        await storage.createApprovedDomain({
          domain: domain, // Use domain field for database compatibility
          is_active: true
        });

        imported++;
      }

      res.json({ message: "Domains imported successfully", imported, skipped });
    } catch (error) {
      console.error("Error importing domains:", error);
      res.status(500).json({ message: "Failed to import domains" });
    }
  });

  // Admin user operations
  app.delete("/api/admin/users/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const userId = parseInt(req.params.id);

      // Check if user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Prevent deleting self
      if (userId === req.user!.id) {
        return res.status(400).json({ message: "Cannot delete your own account" });
      }

      // Delete user
      try {
        if (typeof storage.deleteUser === 'function') {
          await storage.deleteUser(userId);
        } else {
          // Mark as deleted by updating user
          await storage.updateUser(userId, {
            is_deleted: true,
            email: `deleted_${userId}@deleted.com` // Prevent login
          });
        }
      } catch (error) {
        console.error(`Error deleting user ${userId}:`, error);
        // Try the fallback method
        await storage.updateUser(userId, {
          is_deleted: true,
          email: `deleted_${userId}@deleted.com` // Prevent login
        });
      }

      // Invalidate users cache
      cache.delete('admin:users');

      res.json({ message: "User deleted successfully" });
    } catch (error) {
      console.error("Error deleting user:", error);
      res.status(500).json({ message: "Failed to delete user" });
    }
  });

  // Get single user for editing
  app.get("/api/admin/users/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const userId = parseInt(req.params.id);
      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Add status field (placeholder - would need login tracking)
      const enhancedUser = {
        ...user,
        status: 'active' as 'active' | 'inactive' | 'suspended',
        last_login: new Date().toISOString() // Placeholder
      };

      res.json(enhancedUser);
    } catch (error) {
      console.error("Error fetching user:", error);
      res.status(500).json({ message: "Failed to fetch user" });
    }
  });

  // Update user
  app.put("/api/admin/users/:id", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const userId = parseInt(req.params.id);
      const userData = req.body;

      // Check if user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Update user
      const updatedUser = await storage.updateUser(userId, userData);

      // Invalidate users cache
      cache.delete('admin:users');

      res.json(updatedUser);
    } catch (error) {
      console.error("Error updating user:", error);
      res.status(500).json({ message: "Failed to update user" });
    }
  });

  // Admin verify user
  app.put("/api/admin/users/:id/verify", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const userId = parseInt(req.params.id);

      // Check if user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // Update user verification status
      const updatedUser = await storage.updateUser(userId, { is_verified: true });

      // Invalidate users cache
      cache.delete('admin:users');

      res.json(updatedUser);
    } catch (error) {
      console.error("Error verifying user:", error);
      res.status(500).json({ message: "Failed to verify user" });
    }
  });

  // Get user permissions
  app.get("/api/admin/users/:id/permissions", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const userId = parseInt(req.params.id);
      const user = await storage.getUser(userId);

      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // In a real implementation, we would fetch the user's permissions from the database
      // For now, we'll return some default permissions based on the user's role
      const permissions = [
        { id: 'view_dashboard', name: 'View Dashboard', description: 'Can view the main dashboard', checked: true },
        { id: 'manage_jobs', name: 'Manage Jobs', description: 'Can create, edit and delete jobs', checked: user.role === 'admin' || user.role === 'employer' },
        { id: 'manage_workers', name: 'Manage Workers', description: 'Can view and manage worker profiles', checked: user.role === 'admin' },
        { id: 'manage_employers', name: 'Manage Employers', description: 'Can view and manage employer accounts', checked: user.role === 'admin' },
        { id: 'view_analytics', name: 'View Analytics', description: 'Can access analytics and reports', checked: user.role === 'admin' || user.role === 'employer' },
        { id: 'manage_content', name: 'Manage Content', description: 'Can edit website content', checked: user.role === 'admin' },
        { id: 'manage_settings', name: 'Manage Settings', description: 'Can change system settings', checked: user.role === 'admin' },
        { id: 'view_logs', name: 'View Logs', description: 'Can view system logs', checked: user.role === 'admin' },
      ];

      res.json(permissions);
    } catch (error) {
      console.error("Error fetching user permissions:", error);
      res.status(500).json({ message: "Failed to fetch user permissions" });
    }
  });

  // Update user permissions
  app.put("/api/admin/users/:id/permissions", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const userId = parseInt(req.params.id);
      const permissions = req.body;

      // Check if user exists
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      // In a real implementation, we would update the user's permissions in the database
      // For now, we'll just return success

      res.json({ message: "Permissions updated successfully" });
    } catch (error) {
      console.error("Error updating user permissions:", error);
      res.status(500).json({ message: "Failed to update user permissions" });
    }
  });

  // Admin system logs
  app.get("/api/admin/logs", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // Get system logs
      const logs = await storage.getSystemLogs();

      // Apply filters if provided
      let filteredLogs = [...logs];

      const level = req.query.level as string;
      if (level && level !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.level === level);
      }

      const source = req.query.source as string;
      if (source && source !== 'all') {
        filteredLogs = filteredLogs.filter(log => log.source === source);
      }

      // Limit results if requested
      const limit = parseInt(req.query.limit as string) || 100;
      filteredLogs = filteredLogs.slice(0, limit);

      res.json(filteredLogs);
    } catch (error) {
      console.error("Error fetching system logs:", error);
      res.status(500).json({ message: "Failed to fetch system logs" });
    }
  });

  // Admin add system log
  app.post("/api/admin/logs", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const { level, message, source, metadata } = req.body;

      if (!level || !message || !source) {
        return res.status(400).json({ message: "Level, message, and source are required" });
      }

      // Create new log entry
      const newLog = await storage.addSystemLog({
        level,
        message,
        source,
        user_id: req.user!.id,
        metadata
      });

      res.status(201).json(newLog);
    } catch (error) {
      console.error("Error adding system log:", error);
      res.status(500).json({ message: "Failed to add system log" });
    }
  });

  // Admin system settings
  app.get("/api/admin/settings", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // For now, return some default settings
      // In a real implementation, these would be stored in the database
      const settings = {
        site_name: "PathLink",
        maintenance_mode: false,
        user_registration_enabled: true,
        default_user_role: "worker",
        email_notifications_enabled: true,
        resume_file_types_allowed: ["pdf", "docx"],
        max_resume_file_size_mb: 5,
        job_listing_expiry_days: 30,
        enable_worker_verification: true,
        enable_employer_verification: false,
        system_version: "1.0.0"
      };

      // Add a small delay to simulate database access (for development only)
      // Remove this in production
      await new Promise(resolve => setTimeout(resolve, 500));

      res.json(settings);
    } catch (error) {
      console.error("Error fetching system settings:", error);
      res.status(500).json({ message: "Failed to fetch system settings" });
    }
  });

  // Update system settings
  app.put("/api/admin/settings", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const updatedSettings = req.body;

      // In a real implementation, these would be stored in the database
      // For now, we'll just return the updated settings

      // Add a small delay to simulate database access (for development only)
      // Remove this in production
      await new Promise(resolve => setTimeout(resolve, 500));

      // Log the update
      await storage.addSystemLog({
        level: "info",
        message: "System settings updated",
        source: "admin",
        user_id: req.user!.id,
        metadata: { updated_by: req.user!.name }
      });

      res.json(updatedSettings);
    } catch (error) {
      console.error("Error updating system settings:", error);
      res.status(500).json({ message: "Failed to update system settings" });
    }
  });

  // Public content endpoint
  app.get("/api/content/:slug", async (req, res) => {
    try {
      const slug = req.params.slug;

      // Get content items
      const contentItems = getContentItems();

      // Find the content item with the matching slug
      const contentItem = contentItems.find(item => item.slug === slug);

      if (!contentItem) {
        return res.status(404).json({ message: "Content not found" });
      }

      res.json(contentItem);
    } catch (error) {
      console.error("Error fetching content by slug:", error);
      res.status(500).json({ message: "Failed to fetch content" });
    }
  });

  // Admin content management
  app.get("/api/admin/content", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      res.json(getContentItems());
    } catch (error) {
      console.error("Error fetching content:", error);
      res.status(500).json({ message: "Failed to fetch content" });
    }
  });

  // Helper function to get content items
  function getContentItems() {
      // For now, return some sample content items
      // In a real implementation, these would be stored in the database
      const contentItems = [
        {
          id: 1,
          title: "Welcome to PathLink",
          type: "page",
          slug: "welcome",
          content: `<h1 class="text-3xl font-bold mb-6">Welcome to PathLink</h1>
          <p class="text-lg mb-4">PathLink is the AI-powered platform for connecting workers with opportunities in today's rapidly changing job market.</p>

          <h2 class="text-2xl font-bold mt-8 mb-4">Our Mission</h2>
          <p class="mb-4">At PathLink, we believe that every worker deserves a clear path to meaningful employment. Our platform uses advanced AI technology to match workers with jobs based on their skills, experience, and potential.</p>

          <h2 class="text-2xl font-bold mt-8 mb-4">For Workers</h2>
          <p class="mb-4">Whether you're looking for a new opportunity or facing a career transition, PathLink provides:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2">AI-powered job matching based on your unique skills</li>
            <li class="mb-2">Personalized career guidance and skill development recommendations</li>
            <li class="mb-2">Direct connections with employers who value your abilities</li>
            <li class="mb-2">A supportive community of professionals in transition</li>
          </ul>

          <h2 class="text-2xl font-bold mt-8 mb-4">For Employers</h2>
          <p class="mb-4">Finding the right talent shouldn't be a challenge. PathLink helps employers:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2">Discover qualified candidates with transferable skills</li>
            <li class="mb-2">Reduce hiring time and costs</li>
            <li class="mb-2">Build diverse teams with fresh perspectives</li>
            <li class="mb-2">Support responsible workforce transitions</li>
          </ul>

          <div class="bg-blue-50 p-6 rounded-lg mt-8">
            <h3 class="text-xl font-bold mb-3">Join PathLink Today</h3>
            <p class="mb-4">Take the first step toward your next opportunity or find your next great hire.</p>
            <p><a href="/auth" class="text-blue-600 hover:underline font-medium">Sign up now</a> to experience the future of work.</p>
          </div>`,
          created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          updated_at: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
          published: true
        },
        {
          id: 2,
          title: "How It Works",
          type: "page",
          slug: "how-it-works",
          content: `<h1 class="text-3xl font-bold mb-6">How PathLink Works</h1>
          <p class="text-lg mb-6">PathLink uses advanced AI technology to create meaningful connections between workers and employers. Here's how our platform transforms the job search and hiring process.</p>

          <div class="border-l-4 border-blue-500 pl-4 py-2 mb-8">
            <p class="italic">"PathLink doesn't just match resumes to job descriptions—it understands the potential in every worker and helps employers see beyond traditional hiring criteria."</p>
          </div>

          <h2 class="text-2xl font-bold mt-8 mb-4">For Workers</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="bg-gray-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">1. Create Your Profile</h3>
              <p>Upload your resume and complete your profile. Our AI analyzes your experience, skills, and potential to create a comprehensive talent profile.</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">2. Discover Opportunities</h3>
              <p>Receive personalized job recommendations based on your skills and potential, not just your past job titles.</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">3. Skill Development</h3>
              <p>Get personalized recommendations for skills to develop based on your career goals and market demand.</p>
            </div>

            <div class="bg-gray-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">4. Connect with Employers</h3>
              <p>Apply to positions with employers who are already interested in your unique skill set.</p>
            </div>
          </div>

          <h2 class="text-2xl font-bold mt-8 mb-4">For Employers</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div class="bg-blue-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">1. Post Opportunities</h3>
              <p>Create detailed job listings that focus on skills and competencies rather than just experience.</p>
            </div>

            <div class="bg-blue-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">2. AI-Powered Matching</h3>
              <p>Our algorithm identifies candidates with the right skills and potential, including those from non-traditional backgrounds.</p>
            </div>

            <div class="bg-blue-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">3. Review Candidates</h3>
              <p>Evaluate matched candidates with detailed skill profiles and potential assessments.</p>
            </div>

            <div class="bg-blue-50 p-6 rounded-lg">
              <h3 class="text-xl font-bold mb-3">4. Streamlined Hiring</h3>
              <p>Connect directly with promising candidates and manage the hiring process efficiently through our platform.</p>
            </div>
          </div>

          <h2 class="text-2xl font-bold mt-8 mb-4">Our AI Technology</h2>
          <p class="mb-4">PathLink's proprietary AI goes beyond keyword matching to understand:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2"><strong>Skill Transferability:</strong> How skills from one industry can apply to another</li>
            <li class="mb-2"><strong>Learning Potential:</strong> A worker's capacity to acquire new skills</li>
            <li class="mb-2"><strong>Career Trajectories:</strong> Common and possible career paths across industries</li>
            <li class="mb-2"><strong>Market Trends:</strong> Emerging skill demands and industry shifts</li>
          </ul>

          <div class="bg-green-50 p-6 rounded-lg mt-8">
            <h3 class="text-xl font-bold mb-3">Ready to Experience PathLink?</h3>
            <p class="mb-4">Join thousands of workers and employers already transforming their approach to careers and hiring.</p>
            <p><a href="/auth" class="text-green-600 hover:underline font-medium">Create your account today</a> and discover the PathLink difference.</p>
          </div>`,
          created_at: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
          updated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
          published: true
        },
        {
          id: 3,
          title: "Privacy Policy",
          type: "legal",
          slug: "privacy-policy",
          content: `<h1 class="text-3xl font-bold mb-6">Privacy Policy</h1>
          <p class="text-sm text-gray-500 mb-6">Last Updated: March 31, 2024</p>

          <p class="mb-4">At PathLink, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our platform.</p>

          <h2 class="text-2xl font-bold mt-8 mb-4">Information We Collect</h2>

          <h3 class="text-xl font-bold mt-6 mb-3">Personal Information</h3>
          <p class="mb-4">We may collect personal information that you provide directly to us, including:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2">Contact information (name, email address, phone number)</li>
            <li class="mb-2">Account credentials</li>
            <li class="mb-2">Professional information (resume, work history, skills)</li>
            <li class="mb-2">Educational background</li>
            <li class="mb-2">Profile information and preferences</li>
          </ul>

          <h3 class="text-xl font-bold mt-6 mb-3">Usage Information</h3>
          <p class="mb-4">We may automatically collect certain information about how you access and use the PathLink platform, including:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2">Log data (IP address, browser type, pages visited)</li>
            <li class="mb-2">Device information</li>
            <li class="mb-2">Location information</li>
            <li class="mb-2">Usage patterns and preferences</li>
          </ul>

          <h2 class="text-2xl font-bold mt-8 mb-4">How We Use Your Information</h2>
          <p class="mb-4">We use the information we collect to:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2">Provide, maintain, and improve the PathLink platform</li>
            <li class="mb-2">Process and complete transactions</li>
            <li class="mb-2">Send you technical notices, updates, and administrative messages</li>
            <li class="mb-2">Respond to your comments, questions, and requests</li>
            <li class="mb-2">Provide customer service</li>
            <li class="mb-2">Communicate with you about products, services, offers, and events</li>
            <li class="mb-2">Monitor and analyze trends, usage, and activities</li>
            <li class="mb-2">Detect, investigate, and prevent fraudulent transactions and other illegal activities</li>
            <li class="mb-2">Personalize and improve your experience</li>
            <li class="mb-2">Facilitate contests, sweepstakes, and promotions</li>
          </ul>

          <h2 class="text-2xl font-bold mt-8 mb-4">Information Sharing</h2>
          <p class="mb-4">We may share your information with:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2"><strong>Employers and Workers:</strong> To facilitate job matching and hiring processes</li>
            <li class="mb-2"><strong>Service Providers:</strong> Third parties that perform services on our behalf</li>
            <li class="mb-2"><strong>Business Transfers:</strong> In connection with a merger, acquisition, or sale of assets</li>
            <li class="mb-2"><strong>Legal Requirements:</strong> To comply with law, regulation, legal process, or governmental request</li>
          </ul>

          <h2 class="text-2xl font-bold mt-8 mb-4">Data Security</h2>
          <p class="mb-4">We implement appropriate technical and organizational measures to protect the security of your personal information. However, no security system is impenetrable, and we cannot guarantee the security of our systems.</p>

          <h2 class="text-2xl font-bold mt-8 mb-4">Your Rights and Choices</h2>
          <p class="mb-4">Depending on your location, you may have certain rights regarding your personal information, including:</p>
          <ul class="list-disc pl-6 mb-6">
            <li class="mb-2">Accessing, correcting, or deleting your personal information</li>
            <li class="mb-2">Objecting to our processing of your personal information</li>
            <li class="mb-2">Requesting restriction of processing</li>
            <li class="mb-2">Data portability</li>
            <li class="mb-2">Withdrawing consent</li>
          </ul>

          <h2 class="text-2xl font-bold mt-8 mb-4">Contact Us</h2>
          <p class="mb-4">If you have any questions about this Privacy Policy, please contact us at:</p>
          <p class="mb-2">Email: <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></p>
          <p>Address: 123 Main Street, Suite 456, San Francisco, CA 94105</p>`,
          created_at: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
          updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
          published: true
        }
      ];

      return contentItems;
  }

  // Admin analytics
  app.get("/api/admin/analytics", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      // Get all users, jobs, and matches
      // Since there's no direct method to get all users, we'll collect them manually
      const users = [];
      // Start with a reasonable number of user IDs to check
      for (let i = 1; i <= 100; i++) {
        const user = await storage.getUser(i);
        if (user) {
          users.push(user);
        }
      }
      const jobs = await storage.getJobs();
      const matches = await storage.getMatches();

      // Calculate analytics data
      const now = new Date();
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      const ninetyDaysAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);

      // User growth
      const userGrowth = {
        total: users.length,
        last_30_days: users.filter(user => new Date(user.created_at) >= thirtyDaysAgo).length,
        last_90_days: users.filter(user => new Date(user.created_at) >= ninetyDaysAgo).length,
        by_role: {
          worker: users.filter(user => user.role === 'worker').length,
          employer: users.filter(user => user.role === 'employer').length,
          admin: users.filter(user => user.role === 'admin').length
        }
      };

      // Job metrics
      const jobMetrics = {
        total: jobs.length,
        last_30_days: jobs.filter(job => job.created_at && new Date(job.created_at) >= thirtyDaysAgo).length,
        by_industry: Object.entries(jobs.reduce((acc, job) => {
          acc[job.industry] = (acc[job.industry] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)).map(([industry, count]) => ({ industry, count }))
      };

      // Match metrics
      const matchMetrics = {
        total: matches.length,
        by_status: {
          pending: matches.filter(match => match.status === 'pending').length,
          matched: matches.filter(match => match.status === 'matched').length,
          rejected: matches.filter(match => match.status === 'rejected').length,
          interview_scheduled: matches.filter(match => match.status === 'interview_scheduled').length,
          accepted: matches.filter(match => match.status === 'accepted').length
        },
        success_rate: matches.filter(match => match.status === 'accepted').length / matches.length || 0
      };

      res.json({
        user_growth: userGrowth,
        job_metrics: jobMetrics,
        match_metrics: matchMetrics
      });
    } catch (error) {
      console.error("Error fetching analytics:", error);
      res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });

  // Admin bulk user actions
  app.post("/api/admin/users/bulk-action", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "admin") return res.status(403).json({ message: "Admin access required" });

    try {
      const { action, userIds } = req.body;

      if (!action || !userIds || !Array.isArray(userIds) || userIds.length === 0) {
        return res.status(400).json({ message: "Action and user IDs are required" });
      }

      // Process based on action type
      switch (action) {
        case 'verify':
          for (const userId of userIds) {
            // Skip if user is the current admin
            if (userId === req.user!.id) continue;

            await storage.updateUser(userId, { is_verified: true });
          }
          break;

        case 'deactivate':
          // This would require a status field in the user model
          // For now, we'll just return success
          break;

        case 'delete':
          // Since deleteUser might not be available in all storage implementations,
          // we'll use a workaround by setting a 'deleted' flag
          for (const userId of userIds) {
            // Skip if user is the current admin
            if (userId === req.user!.id) continue;

            // Try to use deleteUser if available, otherwise mark as deleted
            try {
              if (typeof storage.deleteUser === 'function') {
                await storage.deleteUser(userId);
              } else {
                // Mark as deleted by updating user
                await storage.updateUser(userId, {
                  is_deleted: true,
                  email: `deleted_${userId}@deleted.com` // Prevent login
                });
              }
            } catch (error) {
              console.error(`Error deleting user ${userId}:`, error);
              // Try the fallback method
              await storage.updateUser(userId, {
                is_deleted: true,
                email: `deleted_${userId}@deleted.com` // Prevent login
              });
            }
          }
          break;

        default:
          return res.status(400).json({ message: "Invalid action" });
      }

      res.json({ message: "Bulk action completed successfully" });
    } catch (error) {
      console.error("Error performing bulk action:", error);
      res.status(500).json({ message: "Failed to perform bulk action" });
    }
  });

  // Statistics routes
  app.get("/api/statistics/jobs", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can view statistics" });

    try {
      // Get all jobs for the employer
      const employerJobs = await storage.getJobsByEmployer(req.user!.id);

      if (employerJobs.length === 0) {
        return res.json([]);
      }

      // Get all matches
      const allMatches = await storage.getMatches();

      // Calculate statistics for each job
      const jobStats = employerJobs.map(job => {
        // Filter matches for this job
        const jobMatches = allMatches.filter(match => match.job_id === job.id);

        // Count applications by status
        const pending = jobMatches.filter(match => match.status === 'pending').length;
        const rejected = jobMatches.filter(match => match.status === 'rejected').length;
        const interview_scheduled = jobMatches.filter(match => match.status === 'interview_scheduled').length;
        const accepted = jobMatches.filter(match => match.status === 'accepted').length;
        const matched = jobMatches.filter(match => match.status === 'matched').length;

        return {
          job_id: job.id,
          job_title: job.title,
          total_applications: jobMatches.length,
          pending,
          rejected,
          interview_scheduled,
          accepted,
          matched
        };
      });

      res.json(jobStats);
    } catch (error) {
      console.error("Error fetching job statistics:", error);
      res.status(500).json({ message: "Failed to fetch job statistics" });
    }
  });

  app.get("/api/statistics/overall", async (req, res) => {
    if (!req.isAuthenticated()) return res.status(401).json({ message: "Not authenticated" });
    if (req.user!.role !== "employer") return res.status(403).json({ message: "Only employers can view statistics" });

    try {
      // Get all jobs for the employer
      const employerJobs = await storage.getJobsByEmployer(req.user!.id);
      const jobIds = employerJobs.map(job => job.id);

      // Get all matches
      const allMatches = await storage.getMatches();

      // Filter matches for employer's jobs
      const employerMatches = allMatches.filter(match => jobIds.includes(match.job_id));

      // Count applications by status
      const pending = employerMatches.filter(match => match.status === 'pending').length;
      const rejected = employerMatches.filter(match => match.status === 'rejected').length;
      const interview_scheduled = employerMatches.filter(match => match.status === 'interview_scheduled').length;
      const accepted = employerMatches.filter(match => match.status === 'accepted').length;
      const matched = employerMatches.filter(match => match.status === 'matched').length;

      // Calculate overall statistics
      const overallStats = {
        total_applications: employerMatches.length,
        pending,
        rejected,
        interview_scheduled,
        accepted,
        matched,
        total_jobs: employerJobs.length,
        avg_applications_per_job: employerJobs.length > 0 ? employerMatches.length / employerJobs.length : 0
      };

      res.json(overallStats);
    } catch (error) {
      console.error("Error fetching overall statistics:", error);
      res.status(500).json({ message: "Failed to fetch overall statistics" });
    }
  });

  // Test match creation route
  app.get("/api/test-match", async (req, res) => {
    try {
      if (!req.isAuthenticated()) {
        return res.status(401).json({ message: "Not authenticated" });
      }

      // Get the first job
      const jobs = await storage.getJobs();
      if (jobs.length === 0) {
        return res.status(404).json({ message: "No jobs found" });
      }

      const job = jobs[0];

      // Create a match with a pending status
      try {
        // Create the match directly with the database client
        const client = await pool.connect();
        try {
          await client.query('BEGIN');

          const insertQuery = `
            INSERT INTO matches (worker_id, job_id, status, match_score, match_date)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *
          `;

          const values = [
            req.user!.id,
            job.id,
            'pending',
            Math.floor(Math.random() * 30) + 70, // Simple random score between 70-100
            new Date()
          ];

          const result = await client.query(insertQuery, values);
          await client.query('COMMIT');

          return res.status(201).json({
            message: "Test match created successfully",
            match: result.rows[0]
          });
        } catch (dbError) {
          await client.query('ROLLBACK');
          console.error('Database error:', dbError);
          return res.status(500).json({ message: "Database error", error: dbError instanceof Error ? dbError.message : String(dbError) });
        } finally {
          client.release();
        }
      } catch (dbError) {
        console.error('Database operation error:', dbError);
        return res.status(500).json({ message: "Database error when creating match", error: dbError instanceof Error ? dbError.message : String(dbError) });
      }
    } catch (error) {
      console.error("Error creating test match:", error);
      res.status(500).json({ message: "Failed to create test match", error: error instanceof Error ? error.message : String(error) });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
