# Setting Up Restricted GitHub Codespace Permissions

## Repository Settings Configuration

To properly restrict Codespace access, you need to configure these settings in your GitHub repository:

### 1. Repository Access Settings

Go to **Settings** → **General** → **Permissions**:

- ✅ **Restrict pushes that create files**: Enable
- ✅ **Restrict pushes that create or update GitHub Actions workflow files**: Enable  
- ✅ **Restrict pushes that create or update GitHub Actions workflow files to users with maintain permissions**: Enable

### 2. Branch Protection Rules

Go to **Settings** → **Branches** → **Add rule**:

**Rule settings for `main` branch:**
- ✅ **Require pull request reviews before merging**
- ✅ **Require status checks to pass before merging**
- ✅ **Require branches to be up to date before merging**
- ✅ **Restrict pushes that create files**
- ✅ **Do not allow bypassing the above settings**

### 3. Codespace Settings

Go to **Settings** → **Codespaces**:

- ✅ **Enable Codespaces for this repository**
- ✅ **Restrict base image to organization-approved images**
- ✅ **Set timeout**: 30 minutes (to prevent long-running sessions)
- ✅ **Restrict machine types**: 2-core, 4-core maximum

### 4. Actions Permissions

Go to **Settings** → **Actions** → **General**:

- ✅ **Allow actions and reusable workflows**: Selected actions and reusable workflows
- ✅ **Allow actions created by GitHub**: Enable
- ✅ **Allow actions by Marketplace verified creators**: Enable
- ✅ **Allow specified actions and reusable workflows**: Add specific actions only

### 5. Secrets and Variables

Go to **Settings** → **Secrets and variables** → **Codespaces**:

**Add these secrets for development:**
- `DEV_DATABASE_URL`: Development database connection
- `DEV_SESSION_SECRET`: Development session secret
- `DEV_OPENAI_API_KEY`: Development OpenAI key (optional)

**Important**: Never add production secrets to Codespace environment!

### 6. Collaborator Permissions

Go to **Settings** → **Manage access**:

For external contributors:
- ✅ **Role**: Read
- ✅ **Can create Codespaces**: Yes
- ✅ **Can create pull requests**: Yes
- ❌ **Can push to main**: No
- ❌ **Can manage settings**: No

### 7. Repository Visibility

- ✅ **Private repository** (recommended for proprietary code)
- ✅ **Restrict Codespace creation to collaborators only**

## Additional Security Measures

### 1. .gitignore Configuration

Ensure your `.gitignore` includes:
```
# Environment files
.env
.env.local
.env.production

# Sensitive files
*.key
*.pem
*.p12
config/production.json

# Database files
*.db
*.sqlite

# Logs with potential sensitive data
logs/
*.log
```

### 2. Pre-commit Hooks

Consider adding pre-commit hooks to prevent sensitive data commits:

```bash
# Install pre-commit
npm install --save-dev pre-commit

# Add to package.json
"pre-commit": [
  "check-secrets",
  "lint"
]
```

### 3. Regular Security Audits

- Review Codespace usage logs monthly
- Audit pull requests for sensitive data
- Monitor for unusual activity patterns
- Update security configurations as needed

## User Instructions

Share these instructions with Codespace users:

### ✅ Allowed Actions:
1. Create and edit code files
2. Run development servers locally
3. Create feature branches
4. Submit pull requests
5. Test functionality within Codespace

### ❌ Prohibited Actions:
1. Downloading repository files
2. Copying code outside environment
3. Accessing production systems
4. Sharing credentials or API keys
5. Bypassing security restrictions

### 🚨 Security Violations:
Any attempts to:
- Extract proprietary code
- Access production systems
- Share credentials
- Bypass restrictions

Will result in immediate access revocation and potential legal action.

## Monitoring and Compliance

### Regular Checks:
1. **Weekly**: Review Codespace usage logs
2. **Monthly**: Audit pull request history
3. **Quarterly**: Update security configurations
4. **Annually**: Full security assessment

### Compliance Documentation:
- Maintain access logs
- Document security incidents
- Track user permissions changes
- Regular security training records

---

**Note**: These settings provide a good baseline for security, but always consult with your security team for organization-specific requirements.
