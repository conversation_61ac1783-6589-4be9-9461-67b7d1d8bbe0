{"version": "5", "dialect": "pg", "id": "a3e9c8f7-d1b2-4c3a-b4d5-e6f7g8h9i0j1", "prevId": "0002_snapshot", "tables": {"user_profiles": {"name": "user_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false}, "years_of_experience": {"name": "years_of_experience", "type": "integer", "primaryKey": false, "notNull": false}, "skills": {"name": "skills", "type": "text", "primaryKey": false, "notNull": false}, "profile_picture": {"name": "profile_picture", "type": "text", "primaryKey": false, "notNull": false}, "pending_email": {"name": "pending_email", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_user_profiles_user_id": {"name": "idx_user_profiles_user_id", "columns": ["user_id"], "isUnique": true}}, "foreignKeys": {"user_profiles_user_id_users_id_fk": {"name": "user_profiles_user_id_users_id_fk", "tableFrom": "user_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}}