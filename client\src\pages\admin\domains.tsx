import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { PaginationControls } from '@/components/pagination-controls';
import {
  Search,
  Plus,
  Trash2,
  Loader2,
  Globe,
  CheckCircle,
  XCircle,
  RefreshCw,
  Upload,
  Download,
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface Domain {
  id: number;
  domain_name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  description?: string;
  verified_workers_count: number;
}

const ApprovedDomains: React.FC = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddDomainDialog, setShowAddDomainDialog] = useState(false);
  const [showDeleteDomainDialog, setShowDeleteDomainDialog] = useState(false);
  const [domainToDelete, setDomainToDelete] = useState<Domain | null>(null);
  const [newDomainName, setNewDomainName] = useState('');
  const [newDomainDescription, setNewDomainDescription] = useState('');
  const [newDomainActive, setNewDomainActive] = useState(true);
  const [bulkImportDialogOpen, setBulkImportDialogOpen] = useState(false);
  const [bulkDomains, setBulkDomains] = useState('');

  // Fetch domains
  const { data: domains, isLoading } = useQuery({
    queryKey: ['/api/admin/domains'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/domains');
      if (!res.ok) throw new Error('Failed to fetch domains');
      return res.json() as Promise<Domain[]>;
    },
  });

  // Add domain mutation
  const addDomainMutation = useMutation({
    mutationFn: async (domainData: { domain_name: string; description?: string; is_active: boolean }) => {
      const res = await apiRequest('POST', '/api/admin/domains', domainData);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to add domain');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/domains'] });
      toast({
        title: 'Domain Added',
        description: 'The domain has been successfully added to the approved list.',
      });
      setShowAddDomainDialog(false);
      setNewDomainName('');
      setNewDomainDescription('');
      setNewDomainActive(true);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add domain',
        variant: 'destructive',
      });
    },
  });

  // Delete domain mutation
  const deleteDomainMutation = useMutation({
    mutationFn: async (domainId: number) => {
      const res = await apiRequest('DELETE', `/api/admin/domains/${domainId}`);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete domain');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/domains'] });
      toast({
        title: 'Domain Deleted',
        description: 'The domain has been successfully removed from the approved list.',
      });
      setShowDeleteDomainDialog(false);
      setDomainToDelete(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete domain',
        variant: 'destructive',
      });
    },
  });

  // Toggle domain status mutation
  const toggleDomainStatusMutation = useMutation({
    mutationFn: async ({ domainId, isActive }: { domainId: number; isActive: boolean }) => {
      const res = await apiRequest('PUT', `/api/admin/domains/${domainId}`, { is_active: isActive });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to update domain status');
      }
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/domains'] });
      toast({
        title: 'Domain Updated',
        description: 'The domain status has been successfully updated.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update domain status',
        variant: 'destructive',
      });
    },
  });

  // Bulk import domains mutation
  const bulkImportDomainsMutation = useMutation({
    mutationFn: async (domainsText: string) => {
      const domainsList = domainsText
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);

      const res = await apiRequest('POST', '/api/admin/domains/bulk-import', { domains: domainsList });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to import domains');
      }
      return res.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/domains'] });
      toast({
        title: 'Domains Imported',
        description: `Successfully imported ${data.imported} domains. ${data.skipped || 0} domains were skipped.`,
      });
      setBulkImportDialogOpen(false);
      setBulkDomains('');
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to import domains',
        variant: 'destructive',
      });
    },
  });

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Filter domains based on search query
  const filteredDomains = domains
    ? domains.filter((domain) =>
        domain.domain_name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  // Calculate pagination
  const totalItems = filteredDomains.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDomains.slice(indexOfFirstItem, indexOfLastItem);

  // Handle add domain
  const handleAddDomain = () => {
    if (!newDomainName) {
      toast({
        title: 'Missing Information',
        description: 'Please enter a domain name.',
        variant: 'destructive',
      });
      return;
    }

    // Basic domain validation
    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
    if (!domainRegex.test(newDomainName)) {
      toast({
        title: 'Invalid Domain',
        description: 'Please enter a valid domain name (e.g., example.com).',
        variant: 'destructive',
      });
      return;
    }

    addDomainMutation.mutate({
      domain_name: newDomainName,
      description: newDomainDescription || undefined,
      is_active: newDomainActive,
    });
  };

  // Handle delete domain
  const handleDeleteDomain = (domain: Domain) => {
    setDomainToDelete(domain);
    setShowDeleteDomainDialog(true);
  };

  const confirmDeleteDomain = () => {
    if (domainToDelete) {
      deleteDomainMutation.mutate(domainToDelete.id);
    }
  };

  // Handle toggle domain status
  const handleToggleDomainStatus = (domain: Domain) => {
    toggleDomainStatusMutation.mutate({
      domainId: domain.id,
      isActive: !domain.is_active,
    });
  };

  // Handle bulk import
  const handleBulkImport = () => {
    if (!bulkDomains.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please enter at least one domain name.',
        variant: 'destructive',
      });
      return;
    }

    bulkImportDomainsMutation.mutate(bulkDomains);
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">Approved Domains</h1>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => setBulkImportDialogOpen(true)}>
              <Upload className="h-4 w-4 mr-2" />
              Bulk Import
            </Button>
            <Button onClick={() => setShowAddDomainDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Domain
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search domains..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button variant="outline" onClick={() => setSearchQuery('')} className="flex items-center">
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>

        {/* Domains Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading domains...</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[300px]">Domain Name</TableHead>
                    <TableHead className="w-[120px]">Status</TableHead>
                    <TableHead className="w-[150px]">Verified Workers</TableHead>
                    <TableHead className="w-[200px]">Added On</TableHead>
                    <TableHead className="w-[300px]">Description</TableHead>
                    <TableHead className="w-[150px] text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentItems.length > 0 ? (
                    currentItems.map((domain) => (
                      <TableRow key={domain.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <Globe className="h-4 w-4 mr-2 text-gray-500" />
                            {domain.domain_name}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={domain.is_active}
                              onCheckedChange={() => handleToggleDomainStatus(domain)}
                              aria-label={`Toggle ${domain.domain_name} status`}
                            />
                            <Badge
                              className={
                                domain.is_active
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                              }
                            >
                              {domain.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>{domain.verified_workers_count}</TableCell>
                        <TableCell>{new Date(domain.created_at).toLocaleDateString()}</TableCell>
                        <TableCell className="max-w-[300px] truncate">
                          {domain.description || '-'}
                        </TableCell>
                        <TableCell className="text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteDomain(domain)}
                            className="text-red-600 hover:text-red-800 hover:bg-red-100"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                          <Globe className="h-12 w-12 mb-2 opacity-20" />
                          <p>No domains found</p>
                          {searchQuery && (
                            <p className="text-sm">Try adjusting your search</p>
                          )}
                          <Button
                            variant="outline"
                            className="mt-4"
                            onClick={() => setShowAddDomainDialog(true)}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Domain
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>

        {/* Pagination Controls */}
        {filteredDomains.length > 0 && (
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={totalItems}
            onPageChange={setCurrentPage}
            onItemsPerPageChange={(value) => {
              setItemsPerPage(value);
              setCurrentPage(1); // Reset to first page when changing items per page
            }}
            className="mt-4"
          />
        )}

        {/* Export Button */}
        <div className="flex justify-end mt-6">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Domains
          </Button>
        </div>
      </div>

      {/* Add Domain Dialog */}
      <Dialog open={showAddDomainDialog} onOpenChange={setShowAddDomainDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Approved Domain</DialogTitle>
            <DialogDescription>
              Add a new domain to the approved list. Workers with email addresses from approved domains can register as verified workers.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="domain-name">Domain Name</Label>
              <Input
                id="domain-name"
                placeholder="example.com"
                value={newDomainName}
                onChange={(e) => setNewDomainName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="domain-description">Description (Optional)</Label>
              <Textarea
                id="domain-description"
                placeholder="Company or organization description"
                value={newDomainDescription}
                onChange={(e) => setNewDomainDescription(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="domain-active"
                checked={newDomainActive}
                onCheckedChange={setNewDomainActive}
              />
              <Label htmlFor="domain-active">Active</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddDomainDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddDomain} disabled={addDomainMutation.isPending}>
              {addDomainMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Add Domain
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Domain Dialog */}
      <Dialog open={showDeleteDomainDialog} onOpenChange={setShowDeleteDomainDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Domain</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the domain "{domainToDelete?.domain_name}"? This will prevent new workers with this domain from registering as verified workers.
              {domainToDelete?.verified_workers_count > 0 && (
                <p className="mt-2 text-amber-600 dark:text-amber-400">
                  Warning: There are currently {domainToDelete.verified_workers_count} verified workers with this domain.
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDomainDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteDomain}
              disabled={deleteDomainMutation.isPending}
            >
              {deleteDomainMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Bulk Import Dialog */}
      <Dialog open={bulkImportDialogOpen} onOpenChange={setBulkImportDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Bulk Import Domains</DialogTitle>
            <DialogDescription>
              Enter one domain per line to add multiple domains at once.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <Textarea
              placeholder="example.com
company.org
organization.net"
              value={bulkDomains}
              onChange={(e) => setBulkDomains(e.target.value)}
              className="min-h-[200px]"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setBulkImportDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleBulkImport}
              disabled={bulkImportDomainsMutation.isPending}
            >
              {bulkImportDomainsMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Import Domains
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default ApprovedDomains;
