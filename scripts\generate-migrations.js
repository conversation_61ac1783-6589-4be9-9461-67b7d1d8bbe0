/**
 * <PERSON><PERSON>t to generate database migrations
 * Run with: node scripts/generate-migrations.js
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
require('dotenv').config();

// Check if DATABASE_URL is set
if (!process.env.DATABASE_URL) {
  console.error('ERROR: DATABASE_URL environment variable is not set.');
  console.error('Please set it in your .env file or environment variables.');
  process.exit(1);
}

// Create the drizzle directory if it doesn't exist
const drizzleDir = path.join(__dirname, '..', 'drizzle');
if (!fs.existsSync(drizzleDir)) {
  fs.mkdirSync(drizzleDir, { recursive: true });
  console.log('Created drizzle directory for migrations');
}

try {
  // Run the drizzle-kit generate command
  console.log('Generating database migrations...');
  execSync('npx drizzle-kit generate:pg', { 
    stdio: 'inherit',
    env: process.env
  });
  
  console.log('Migrations generated successfully!');
} catch (error) {
  console.error('Error generating migrations:', error.message);
  process.exit(1);
}
