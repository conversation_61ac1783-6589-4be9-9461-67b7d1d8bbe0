import { Pool } from 'pg';

import dotenv from 'dotenv';
dotenv.config();

const connectionString = process.env.DATABASE_URL;

async function resetMatchToWithdrawn() {
  const pool = new Pool({ connectionString });
  try {
    console.log('=== RESETTING MATCH 28 TO WITHDRAWN STATUS ===');
    
    // Reset match 28 to withdrawn status
    const updateResult = await pool.query(
      'UPDATE matches SET status = $1 WHERE id = $2 RETURNING *',
      ['withdrawn', 28]
    );
    
    if (updateResult.rows.length > 0) {
      const match = updateResult.rows[0];
      console.log('✅ Match 28 reset to withdrawn:', {
        id: match.id,
        worker_id: match.worker_id,
        job_id: match.job_id,
        status: match.status,
        match_score: match.match_score,
        match_date: match.match_date
      });
    } else {
      console.log('❌ Failed to reset match 28');
    }
    
  } catch (error) {
    console.error('Reset failed:', error.message);
  } finally {
    await pool.end();
  }
}

resetMatchToWithdrawn();
