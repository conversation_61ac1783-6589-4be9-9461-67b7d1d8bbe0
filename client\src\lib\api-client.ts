// API client for server communication
// This replaces the Supabase client with direct API calls to our server

// Authentication functions
export const signUp = async (email: string, password: string) => {
  try {
    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const signIn = async (email: string, password: string) => {
  try {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const signOut = async () => {
  try {
    await fetch('/api/auth/logout', { method: 'POST' });
    return { error: null };
  } catch (error: any) {
    return { error };
  }
};

export const resetPassword = async (email: string) => {
  try {
    const response = await fetch('/api/auth/forgot-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email })
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

// User functions
export const getCurrentUser = async () => {
  try {
    const response = await fetch('/api/auth/me');
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const getUserProfile = async () => {
  try {
    const response = await fetch('/api/profile');
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const updateUserProfile = async (updates: any) => {
  try {
    const response = await fetch('/api/profile', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

// Job functions
export const getJobs = async () => {
  try {
    const response = await fetch('/api/jobs');
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const getJob = async (jobId: number) => {
  try {
    const response = await fetch(`/api/jobs/${jobId}`);
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const createJob = async (job: any) => {
  try {
    const response = await fetch('/api/jobs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(job)
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

// Resume functions
export const getResumes = async () => {
  try {
    const response = await fetch('/api/resumes');
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    console.error('Error fetching resumes:', error);
    return { data: null, error };
  }
};

export const getResume = async (resumeId: number) => {
  try {
    const response = await fetch(`/api/resumes/${resumeId}`);
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    console.error('Error fetching resume:', error);
    return { data: null, error };
  }
};

export const uploadResume = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('resumeFile', file);

    const response = await fetch('/api/resumes', {
      method: 'POST',
      body: formData
    });

    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    console.error('Error uploading resume:', error);
    return { data: null, error };
  }
};

// Match functions
export const getMatches = async () => {
  try {
    const response = await fetch('/api/matches');
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const getComprehensiveMatches = async () => {
  try {
    // Add cache-busting parameter with random number
    const timestamp = new Date().getTime();
    const random = Math.random();
    const response = await fetch(`/api/matches/comprehensive?t=${timestamp}&r=${random}`, {
      cache: 'no-cache',
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    console.error('Error fetching comprehensive matches:', error);
    return { data: null, error };
  }
};

export const createMatch = async (jobId: number) => {
  try {
    const response = await fetch('/api/matches', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ job_id: jobId })
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

// Surplus Employee functions
export const getSurplusEmployees = async () => {
  try {
    const response = await fetch('/api/surplus-employees');
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const createSurplusEmployee = async (employee: any) => {
  try {
    const response = await fetch('/api/surplus-employees', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(employee)
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const updateSurplusEmployee = async (id: number, updates: any) => {
  try {
    const response = await fetch(`/api/surplus-employees/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(updates)
    });
    const data = await response.json();
    return { data, error: null };
  } catch (error: any) {
    return { data: null, error };
  }
};

export const deleteSurplusEmployee = async (id: number) => {
  try {
    const response = await fetch(`/api/surplus-employees/${id}`, {
      method: 'DELETE'
    });
    const data = await response.json();
    return { error: null };
  } catch (error: any) {
    return { error };
  }
};
