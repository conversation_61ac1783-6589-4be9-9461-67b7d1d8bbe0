const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function testAuthentication() {
  try {
    console.log('🔌 Testing authentication with Neon database...');
    const client = await pool.connect();
    
    // Test credentials
    const testCredentials = [
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' },
      { email: '<EMAIL>', password: 'password123' }
    ];
    
    for (const cred of testCredentials) {
      console.log(`\n🔍 Testing login for: ${cred.email}`);
      
      // Get user from database
      const userResult = await client.query('SELECT * FROM users WHERE email = $1', [cred.email]);
      
      if (userResult.rows.length === 0) {
        console.log('❌ User not found');
        continue;
      }
      
      const user = userResult.rows[0];
      console.log(`👤 User found: ${user.name} (${user.role})`);
      
      // Test password
      const isPasswordValid = await bcrypt.compare(cred.password, user.password);
      
      if (isPasswordValid) {
        console.log('✅ Password correct - Login should work!');
      } else {
        console.log('❌ Password incorrect - Updating password...');
        
        // Update password
        const hashedPassword = await bcrypt.hash(cred.password, 10);
        await client.query('UPDATE users SET password = $1 WHERE email = $2', [hashedPassword, cred.email]);
        console.log('✅ Password updated to "password123"');
      }
    }
    
    client.release();
    await pool.end();
    console.log('\n✅ Authentication test complete!');
    console.log('🎯 All users should now be able to login with password: password123');
    
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
  }
}

testAuthentication();
