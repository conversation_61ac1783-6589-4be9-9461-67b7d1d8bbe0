import React, { useEffect, Suspense, lazy } from "react";
import { Switch, Route, Redirect } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider, useAuth } from "@/hooks/use-auth";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { ProtectedRoute } from "./lib/protected-route";
import { Loading } from "@/components/loading";
import { HelmetProvider } from "react-helmet-async";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Lazy load components for better performance
const HomePage = lazy(() => import("@/pages/home-page"));
const LandingPage = lazy(() => import("@/pages/landing-page"));
const NotFound = lazy(() => import("@/pages/not-found"));
const AuthPage = lazy(() => import("@/pages/auth-page"));
const AuthCallback = lazy(() => import("@/pages/auth-callback"));
const Dashboard = lazy(() => import("@/pages/dashboard"));
const PathCoachPage = lazy(() => import("@/pages/pathcoach-page"));
const AnalyticsPage = lazy(() => import("@/pages/analytics-page"));
const StatisticsPage = lazy(() => import("@/pages/statistics"));
const ContentPage = lazy(() => import("@/pages/content-page"));
const ProfilePage = lazy(() => import("@/pages/profile"));
const SettingsPage = lazy(() => import("@/pages/settings"));
const ViewMatchesPage = lazy(() => import("@/pages/view-matches"));
const BrowseJobsPage = lazy(() => import("@/pages/browse-jobs"));
const PricingPage = lazy(() => import("@/pages/pricing"));
const ContactPage = lazy(() => import("@/pages/Contact"));

// Lazy load admin pages
const AdminDashboard = lazy(() => import("@/pages/admin/dashboard"));
const AdminUsers = lazy(() => import("@/pages/admin/users"));
const AdminUserEdit = lazy(() => import("@/pages/admin/user-edit"));
const AdminUserPermissions = lazy(() => import("@/pages/admin/user-permissions"));
const AdminDomains = lazy(() => import("@/pages/admin/domains"));
const AdminContent = lazy(() => import("@/pages/admin/content"));
const AdminSettings = lazy(() => import("@/pages/admin/settings"));
const AdminLogs = lazy(() => import("@/pages/admin/logs"));

function AuthenticatedRedirect() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loading fullScreen={false} message="Authenticating..." />
      </div>
    );
  }

  return user ? <Redirect to="/dashboard" /> : <Redirect to="/login" />;
}

// Loading component for suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <Loading fullScreen={false} message="Loading page..." />
  </div>
);

function Router() {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <PageLoader />;
  }

  return (
    <Suspense fallback={<PageLoader />}>
      <Switch>
        {/* Public routes */}
        <Route path="/" component={LandingPage} />
        <Route path="/login" component={AuthPage} />
        <Route path="/auth" component={AuthPage} />
        <Route path="/auth/callback" component={AuthCallback} />

        {/* Content pages */}
        <Route path="/welcome" component={ContentPage} />
        <Route path="/how-it-works" component={ContentPage} />
        <Route path="/privacy-policy" component={ContentPage} />
        <Route path="/pricing" component={PricingPage} />
        <Route path="/contact" component={ContactPage} />

        {/* If user is logged in and tries to access the home page, redirect to dashboard */}
        <Route path="/home">
          {user ? <Redirect to="/dashboard" /> : <HomePage />}
        </Route>

        {/* Protected routes */}
        <ProtectedRoute path="/dashboard" component={Dashboard} />
        <ProtectedRoute path="/pathcoach" component={PathCoachPage} />
        <ProtectedRoute path="/pathcoach-page" component={PathCoachPage} />
        <ProtectedRoute path="/analytics" component={AnalyticsPage} />
        <ProtectedRoute path="/statistics" component={StatisticsPage} />
        <ProtectedRoute path="/matches" component={ViewMatchesPage} />
        <ProtectedRoute path="/browse-jobs" component={BrowseJobsPage} />
        <ProtectedRoute path="/profile" component={ProfilePage} />
        <ProtectedRoute path="/settings" component={SettingsPage} />

        {/* Admin Routes */}
        <ProtectedRoute path="/admin" exact component={AdminDashboard} />
        <ProtectedRoute path="/admin/users" exact component={AdminUsers} />
        <ProtectedRoute path="/admin/users/:id" component={AdminUserEdit} />
        <ProtectedRoute path="/admin/users/:id/permissions" component={AdminUserPermissions} />
        <ProtectedRoute path="/admin/domains" exact component={AdminDomains} />
        <ProtectedRoute path="/admin/content" exact component={AdminContent} />
        <ProtectedRoute path="/admin/system" exact component={AdminSettings} />
        <ProtectedRoute path="/admin/logs" exact component={AdminLogs} />

        <Route component={NotFound} />
      </Switch>
    </Suspense>
  );
}

// Simplified tooltip remover that won't interfere with the main UI
function DashboardTooltipRemover() {
  // We're disabling this functionality as it might be causing UI issues
  return null;
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="light">
        <HelmetProvider>
          <AuthProvider>
            <DashboardTooltipRemover />
            {/* Remove random key to prevent constant remounting */}
            <Router />
            <Toaster />
            <ToastContainer position="top-right" autoClose={5000} />
          </AuthProvider>
        </HelmetProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
