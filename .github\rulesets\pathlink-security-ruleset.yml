# PathLink Repository Security Ruleset Configuration
# This file defines comprehensive security rules for the PathLink repository

name: "PathLink Security & Protection Ruleset"
description: "Comprehensive security rules to protect PathLink source code and enforce development workflows"

# Target Configuration
target: branch
enforcement: active

# Branch Conditions - Apply to protected branches
conditions:
  ref_name:
    include:
      - "refs/heads/main"
      - "refs/heads/master" 
      - "refs/heads/production"
      - "refs/heads/staging"
      - "refs/heads/release/*"
    exclude:
      - "refs/heads/dependabot/*"

# Security and Protection Rules
rules:
  # Pull Request Requirements
  - type: pull_request
    parameters:
      # Require PR reviews before merging
      required_approving_review_count: 1
      # Dismiss stale reviews when new commits are pushed
      dismiss_stale_reviews_on_push: true
      # Require code owner approval
      require_code_owner_review: true
      # Require approval of most recent reviewable push
      require_last_push_approval: true
      # Require conversation resolution before merging
      required_review_thread_resolution: true
      # Restrict who can dismiss reviews
      restrict_review_dismissals:
        users: []
        teams: []
        apps: []

  # Required Status Checks
  - type: required_status_checks
    parameters:
      # Require branches to be up to date before merging
      strict_required_status_checks_policy: true
      required_status_checks:
        - context: "security-check"
          integration_id: null
        - context: "codespace-security"
          integration_id: null
        - context: "build"
          integration_id: null
        - context: "test"
          integration_id: null
        - context: "lint"
          integration_id: null

  # Prevent Force Pushes
  - type: non_fast_forward
    parameters: {}

  # Require Linear History
  - type: required_linear_history
    parameters: {}

  # Require Signed Commits
  - type: required_signatures
    parameters: {}

  # Restrict Direct Pushes
  - type: restrict_pushes
    parameters:
      # Only allow pushes through pull requests
      restrict_pushes_to_admins: false

  # Prevent Branch Creation
  - type: restrict_creations
    parameters: {}

  # Restrict Branch Updates
  - type: restrict_updates
    parameters:
      # Prevent force pushes and deletions
      update_allows_fetch_and_merge: false

  # Prevent Branch Deletion
  - type: restrict_deletions
    parameters: {}

  # File Path Restrictions
  - type: file_path_restriction
    parameters:
      restricted_file_paths:
        # Prevent modification of security files
        - ".github/rulesets/*"
        - ".github/workflows/security-*.yml"
        - ".devcontainer/security-*.sh"
        - ".devcontainer/Dockerfile"
        - "CODESPACE_SECURITY_*.md"
        # Prevent committing sensitive files
        - ".env"
        - ".env.*"
        - "*.key"
        - "*.pem"
        - "*.p12"
        - "*.pfx"
        - "config/production.json"
        - "secrets.json"
        - "credentials.json"

  # File Extension Restrictions
  - type: file_extension_restriction
    parameters:
      restricted_file_extensions:
        # Prevent executable files
        - ".exe"
        - ".bat"
        - ".cmd"
        - ".com"
        - ".scr"
        - ".msi"
        # Prevent archive files that could contain source code
        - ".zip"
        - ".tar"
        - ".tar.gz"
        - ".rar"
        - ".7z"
        # Prevent database files
        - ".db"
        - ".sqlite"
        - ".mdb"

  # Maximum File Size (prevent large file uploads)
  - type: max_file_size
    parameters:
      max_file_size: 10485760  # 10MB limit

  # Required Workflows
  - type: required_workflows
    parameters:
      required_workflows:
        - path: ".github/workflows/security-check.yml"
          ref: "refs/heads/main"
        - path: ".github/workflows/codespace-security.yml"
          ref: "refs/heads/main"

# Bypass Actors (who can bypass these rules)
bypass_actors:
  # Repository administrators can bypass in emergency situations
  - actor_type: RepositoryRole
    actor_id: 1  # Admin role
    bypass_mode: pull_request  # Can bypass but still need PR

  # Specific users who can bypass (repository owner)
  - actor_type: User
    actor_id: peterdimian
    bypass_mode: always

# Additional Security Configurations
security_settings:
  # Prevent forking
  allow_forking: false
  
  # Require two-factor authentication
  require_2fa: true
  
  # Restrict repository visibility changes
  restrict_visibility_changes: true
  
  # Enable vulnerability alerts
  enable_vulnerability_alerts: true
  
  # Enable security advisories
  enable_security_advisories: true
  
  # Restrict package publishing
  restrict_package_publishing: true

# Codespace Specific Restrictions
codespace_restrictions:
  # Limit codespace creation to collaborators only
  restrict_codespace_creation: true
  
  # Require approval for codespace creation
  require_codespace_approval: true
  
  # Limit codespace machine types
  allowed_machine_types:
    - "basicLinux32gb"
    - "standardLinux32gb"
  
  # Set maximum codespace timeout
  max_idle_timeout: 30  # minutes
  
  # Restrict codespace access
  restrict_codespace_access:
    - "no_download"
    - "no_copy"
    - "monitored_terminal"

# Webhook Notifications for Security Events
webhooks:
  security_events:
    - event: "push_blocked"
      url: "https://api.pathlink.com/webhooks/security"
    - event: "rule_violation"
      url: "https://api.pathlink.com/webhooks/violations"
    - event: "bypass_used"
      url: "https://api.pathlink.com/webhooks/bypass"

# Compliance and Audit Settings
compliance:
  # Enable audit logging
  enable_audit_log: true
  
  # Retention period for audit logs
  audit_log_retention: 365  # days
  
  # Export audit logs
  audit_log_export:
    enabled: true
    format: "json"
    destination: "s3://pathlink-audit-logs/"

# Notification Settings
notifications:
  # Notify on rule violations
  rule_violations:
    - type: "email"
      recipients: ["<EMAIL>"]
    - type: "slack"
      webhook: "https://hooks.slack.com/services/..."
  
  # Notify on bypass usage
  bypass_usage:
    - type: "email"
      recipients: ["<EMAIL>"]
