import { useState } from "react";
import { useCareerTransitions } from "@/hooks/use-career-transitions";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Save, Target, ArrowRight, Star, StepForward } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { CareerTransitionPlan } from "@shared/schema";

// Define the transition plan type
interface TransitionPlan {
  step1: string;
  step2: string;
  step3: string;
  [key: string]: string;
}

export function CareerQuantumLeapGenerator() {
  const { careerPlans, isLoading, createCareerPlanMutation } = useCareerTransitions();
  
  const [currentRole, setCurrentRole] = useState<string>("");
  const [dreamRole, setDreamRole] = useState<string>("");
  const [step1, setStep1] = useState<string>("");
  const [step2, setStep2] = useState<string>("");
  const [step3, setStep3] = useState<string>("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    createCareerPlanMutation.mutate({
      current_role: currentRole,
      dream_role: dreamRole,
      transition_plan: {
        step1,
        step2,
        step3
      }
    });
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Career Quantum Leap Generator
          </CardTitle>
          <CardDescription>Loading your career plans...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Target className="h-5 w-5" />
          Career Quantum Leap Generator
        </CardTitle>
        <CardDescription>
          Map your journey from your current role to your dream career
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="current-role">Current Role</Label>
              <Input
                id="current-role"
                placeholder="Frontend Developer"
                value={currentRole}
                onChange={(e) => setCurrentRole(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2 flex items-end">
              <div className="hidden md:flex items-center text-gray-400 mx-2 pb-2">
                <ArrowRight className="h-5 w-5" />
              </div>
              <div className="flex-1 space-y-2">
                <Label htmlFor="dream-role">Dream Role</Label>
                <Input
                  id="dream-role"
                  placeholder="Chief Technology Officer"
                  value={dreamRole}
                  onChange={(e) => setDreamRole(e.target.value)}
                  required
                />
              </div>
            </div>
          </div>

          <div className="p-4 bg-muted rounded-lg space-y-4">
            <h4 className="font-medium">Transition Plan Steps</h4>
            
            <div className="space-y-2">
              <Label htmlFor="step1" className="flex items-center gap-2">
                <StepForward className="h-4 w-4" /> Step 1
              </Label>
              <Textarea
                id="step1"
                placeholder="First action step to move toward your dream role..."
                value={step1}
                onChange={(e) => setStep1(e.target.value)}
                className="resize-none"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="step2" className="flex items-center gap-2">
                <StepForward className="h-4 w-4" /> Step 2
              </Label>
              <Textarea
                id="step2"
                placeholder="Second action step to move toward your dream role..."
                value={step2}
                onChange={(e) => setStep2(e.target.value)}
                className="resize-none"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="step3" className="flex items-center gap-2">
                <StepForward className="h-4 w-4" /> Step 3
              </Label>
              <Textarea
                id="step3"
                placeholder="Third action step to move toward your dream role..."
                value={step3}
                onChange={(e) => setStep3(e.target.value)}
                className="resize-none"
                required
              />
            </div>
          </div>

          <Button 
            type="submit" 
            className="w-full md:w-auto"
            disabled={createCareerPlanMutation.isPending}
          >
            {createCareerPlanMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save My Career Plan
              </>
            )}
          </Button>
        </form>
        
        {careerPlans && careerPlans.length > 0 && (
          <div className="mt-8">
            <h4 className="font-medium text-lg mb-4">Your Career Transition Plans</h4>
            <Accordion type="single" collapsible className="space-y-4">
              {careerPlans.map((plan: CareerTransitionPlan, index: number) => (
                <AccordionItem key={index} value={`plan-${index}`} className="border rounded-lg p-2">
                  <AccordionTrigger className="py-2 px-4 hover:no-underline">
                    <div className="flex items-center gap-2 text-left">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <div>
                        <span className="font-medium">{plan.current_role || "Current Role"}</span>
                        <ArrowRight className="inline-block mx-2 h-4 w-4" />
                        <span className="font-medium">{plan.dream_role}</span>
                        <span className="block text-xs text-muted-foreground mt-1">
                          Created: {plan.created_at ? new Date(plan.created_at).toLocaleDateString() : "N/A"}
                        </span>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pt-2 pb-4">
                    <div className="space-y-3">
                      {Object.entries(plan.transition_plan as TransitionPlan).map(([key, value], i) => (
                        <div key={key} className="flex gap-3">
                          <div className="flex-shrink-0 w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                            {i + 1}
                          </div>
                          <p className="text-sm">{value}</p>
                        </div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        )}
      </CardContent>
    </Card>
  );
}