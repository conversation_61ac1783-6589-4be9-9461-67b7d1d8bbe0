# PathLink Netlify Deployment Guide

*Copyright © 2025 <PERSON>. All Rights Reserved.*

This guide provides detailed instructions for deploying the PathLink application to Netlify, a modern platform for serverless deployment.

## Prerequisites

- A Netlify account
- Git repository with your PathLink code
- Neon PostgreSQL database

## Deployment Steps

### 1. Connect Your Repository to Netlify

1. Log in to your Netlify account
2. Click "New site from Git"
3. Select your Git provider (GitHub, GitLab, or Bitbucket)
4. Select your PathLink repository
5. Configure build settings:
   - Build command: `npm run build`
   - Publish directory: `dist/public`

### 2. Configure Environment Variables

In the Netlify dashboard, go to Site settings > Environment variables and add the following:

- `DATABASE_URL`: Your Neon database connection string
- `SESSION_SECRET`: A secure random string for session management
- `OPENAI_API_KEY`: Your OpenAI API key (if using OpenAI services)
- `SENDGRID_API_KEY`: Your SendGrid API key (for email functionality)
- `NODE_ENV`: Set to `production`

### 3. Configure Netlify Functions

The project is already set up with Netlify Functions configuration in the `netlify.toml` file.

### 4. Deploy Your Site

1. Click "Deploy site" in the Netlify dashboard
2. Wait for the build and deployment to complete
3. Your site will be available at a Netlify subdomain (e.g., `your-site-name.netlify.app`)

### 5. Set Up a Custom Domain (Optional)

1. In the Netlify dashboard, go to Site settings > Domain management
2. Click "Add custom domain"
3. Follow the instructions to configure your domain

## File Storage in Production

For production deployment, you should use a cloud storage service like AWS S3 for storing resume files. The current implementation uses Netlify's temporary file storage, which is not suitable for long-term storage.

To implement AWS S3 storage:

1. Create an AWS S3 bucket
2. Add the following environment variables to Netlify:
   - `S3_BUCKET`: Your S3 bucket name
   - `S3_REGION`: AWS region (e.g., `us-east-1`)
   - `AWS_ACCESS_KEY_ID`: Your AWS access key
   - `AWS_SECRET_ACCESS_KEY`: Your AWS secret key

3. Update the file upload code to use S3 instead of local storage

## Troubleshooting

### Database Connection Issues

- Ensure your Neon database is configured to accept connections from Netlify's IP ranges
- Check that your DATABASE_URL environment variable is correctly set

### Function Execution Timeout

Netlify Functions have execution limits:

- Regular functions: 10 seconds
- Background functions: 26 seconds

If your functions are timing out, consider optimizing your code or using background functions for complex operations.

### Module Format Issues

If you encounter issues with ESM/CJS module formats:

1. Make sure your Netlify functions use CommonJS format (`require()` instead of `import`)
2. Avoid using `import.meta.url` in Netlify functions
3. Avoid top-level await in Netlify functions
4. Make sure all dependencies are properly listed in the main package.json

### Missing Environment Variables

If your application is not working correctly, check that all required environment variables are set in the Netlify dashboard.

## Monitoring and Logs

To view logs and monitor your application:

1. In the Netlify dashboard, go to Functions
2. Click on a function to view its logs
3. Use the "Live" tab to see real-time logs

## Additional Resources

- [Netlify Documentation](https://docs.netlify.com/)
- [Netlify Functions Documentation](https://docs.netlify.com/functions/overview/)
- [Netlify CLI Documentation](https://docs.netlify.com/cli/get-started/)
- [Netlify Environment Variables](https://docs.netlify.com/configure-builds/environment-variables/)

## Demo Accounts

For testing purposes, the following demo accounts are available:

| Role     | Email                      | Password     |
|----------|----------------------------|--------------|
| Admin    | `<EMAIL>`       | admin123     |
| Employer | `<EMAIL>`    | employer123  |
| Worker   | `<EMAIL>`      | worker123    |

These accounts are pre-configured in the Netlify Functions and can be used to test the application without setting up a database.

## Support

For any deployment issues, please contact [<EMAIL>](mailto:<EMAIL>) for assistance.
