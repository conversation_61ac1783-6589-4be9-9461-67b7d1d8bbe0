const { chromium } = require('playwright');
const fs = require('fs');

class EmployerUITestSuite {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5000';
  }

  async log(test, status, details = '', screenshot = null) {
    const result = {
      test,
      status,
      details,
      screenshot,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [Employer] ${test}: ${status} ${details}`);
  }

  async setup() {
    console.log('🚀 Setting up browser for Employer UI testing...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 1000
    });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1280, height: 720 });
    console.log('✅ Browser ready for Employer testing');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Employer UI testing complete');
    }
  }

  async takeScreenshot(name) {
    const filename = `screenshots/employer-${name}-${Date.now()}.png`;
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    await this.page.screenshot({ path: filename, fullPage: true });
    return filename;
  }

  async loginAsEmployer() {
    console.log('\n🔐 LOGGING IN AS EMPLOYER...');

    try {
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(3000);

      // Look for email and password inputs
      const emailInput = this.page.locator('input[type="email"]').first();
      const passwordInput = this.page.locator('input[type="password"]').first();
      
      if (await emailInput.isVisible() && await passwordInput.isVisible()) {
        // Use employer credentials
        await emailInput.fill('<EMAIL>');
        await passwordInput.fill('password123');
        
        const screenshot = await this.takeScreenshot('employer-login-form');
        await this.log('Employer Login Form', 'PASS', 'Employer credentials entered', screenshot);
        
        // Click login button
        const loginButton = this.page.locator('button:has-text("Sign In")').first();
        await loginButton.click();
        
        // Wait for navigation
        await this.page.waitForTimeout(4000);
        
        const currentUrl = this.page.url();
        if (currentUrl.includes('/dashboard')) {
          const dashboardScreenshot = await this.takeScreenshot('employer-dashboard');
          await this.log('Employer Login Success', 'PASS', 'Successfully logged in as employer', dashboardScreenshot);
          return true;
        } else {
          await this.log('Employer Login Success', 'FAIL', `Redirected to: ${currentUrl}`);
          return false;
        }
      } else {
        await this.log('Employer Login Form', 'FAIL', 'Login form not found');
        return false;
      }
    } catch (error) {
      await this.log('Employer Login', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  }

  async testEmployerDashboard() {
    console.log('\n📊 TESTING EMPLOYER DASHBOARD...');

    try {
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForTimeout(3000);

      // Check for employer-specific content
      const pageContent = await this.page.textContent('body');
      
      // Look for employer-specific terms
      const employerTerms = ['job', 'candidate', 'applicant', 'hire', 'posting', 'talent'];
      const foundTerms = employerTerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundTerms.length > 0) {
        await this.log('Employer Dashboard Content', 'PASS', `Found employer terms: ${foundTerms.join(', ')}`);
      } else {
        await this.log('Employer Dashboard Content', 'WARN', 'No employer-specific content detected');
      }

      // Check for dashboard widgets/cards
      const cards = await this.page.locator('.card, [class*="card"], .widget, [class*="widget"]').count();
      const buttons = await this.page.locator('button').count();

      if (cards > 0 || buttons > 0) {
        await this.log('Dashboard Components', 'PASS', `Found ${cards} cards and ${buttons} buttons`);
      } else {
        await this.log('Dashboard Components', 'WARN', 'No dashboard components detected');
      }

      const screenshot = await this.takeScreenshot('employer-dashboard-full');
      await this.log('Dashboard Screenshot', 'PASS', 'Dashboard screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Dashboard Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testJobManagement() {
    console.log('\n💼 TESTING JOB MANAGEMENT...');

    try {
      // Test job posting/management page
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForTimeout(2000);

      // Look for job-related buttons or links
      const jobButtons = [
        'Post Job',
        'Create Job',
        'Add Job',
        'New Job',
        'Manage Jobs',
        'View Jobs'
      ];

      let jobButtonFound = false;
      for (const buttonText of jobButtons) {
        const button = this.page.locator(`text=${buttonText}`);
        if (await button.isVisible()) {
          await this.log('Job Management Button', 'PASS', `Found: ${buttonText}`);
          jobButtonFound = true;
          
          // Try clicking the button
          try {
            await button.click();
            await this.page.waitForTimeout(2000);
            await this.log('Job Button Click', 'PASS', `Successfully clicked ${buttonText}`);
          } catch (clickError) {
            await this.log('Job Button Click', 'WARN', `Could not click ${buttonText}`);
          }
          break;
        }
      }

      if (!jobButtonFound) {
        await this.log('Job Management Button', 'WARN', 'No job management buttons found');
      }

      // Check for job listings or job-related content
      const pageText = await this.page.textContent('body');
      if (pageText.includes('Frontend Developer') || pageText.includes('job') || pageText.includes('position')) {
        await this.log('Job Content', 'PASS', 'Job-related content found');
      } else {
        await this.log('Job Content', 'WARN', 'No job content detected');
      }

      const screenshot = await this.takeScreenshot('job-management');
      await this.log('Job Management Screenshot', 'PASS', 'Job management screenshot captured', screenshot);

    } catch (error) {
      await this.log('Job Management Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testCandidateManagement() {
    console.log('\n👥 TESTING CANDIDATE MANAGEMENT...');

    try {
      // Look for candidate/applicant management features
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForTimeout(2000);

      // Look for candidate-related buttons or sections
      const candidateButtons = [
        'View Candidates',
        'Applicants',
        'Applications',
        'Candidates',
        'View Applications',
        'Manage Candidates'
      ];

      let candidateFeatureFound = false;
      for (const buttonText of candidateButtons) {
        const element = this.page.locator(`text=${buttonText}`);
        if (await element.isVisible()) {
          await this.log('Candidate Management Feature', 'PASS', `Found: ${buttonText}`);
          candidateFeatureFound = true;
          break;
        }
      }

      if (!candidateFeatureFound) {
        await this.log('Candidate Management Feature', 'WARN', 'No candidate management features found');
      }

      // Check for candidate/applicant data
      const pageText = await this.page.textContent('body');
      const candidateTerms = ['applicant', 'candidate', 'resume', 'application', 'contact'];
      const foundCandidateTerms = candidateTerms.filter(term => 
        pageText.toLowerCase().includes(term.toLowerCase())
      );

      if (foundCandidateTerms.length > 0) {
        await this.log('Candidate Content', 'PASS', `Found candidate terms: ${foundCandidateTerms.join(', ')}`);
      } else {
        await this.log('Candidate Content', 'WARN', 'No candidate-related content found');
      }

      const screenshot = await this.takeScreenshot('candidate-management');
      await this.log('Candidate Management Screenshot', 'PASS', 'Candidate management screenshot captured', screenshot);

    } catch (error) {
      await this.log('Candidate Management Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerMatches() {
    console.log('\n🎯 TESTING EMPLOYER MATCHES...');

    try {
      // Test matches/recommendations for employers
      const matchesUrls = [
        `${this.baseUrl}/matches`,
        `${this.baseUrl}/view-matches`,
        `${this.baseUrl}/dashboard`
      ];

      for (const url of matchesUrls) {
        await this.page.goto(url);
        await this.page.waitForTimeout(2000);

        const pageText = await this.page.textContent('body');
        
        // Look for matching-related content
        const matchTerms = ['match', 'recommend', 'suitable', 'compatible', 'score'];
        const foundMatchTerms = matchTerms.filter(term => 
          pageText.toLowerCase().includes(term.toLowerCase())
        );

        if (foundMatchTerms.length > 0) {
          await this.log('Employer Matches Content', 'PASS', `Found match terms: ${foundMatchTerms.join(', ')}`);
          break;
        }
      }

      // Look for match-related buttons
      const matchButtons = [
        'View Matches',
        'See Recommendations',
        'Find Candidates',
        'Match Score'
      ];

      for (const buttonText of matchButtons) {
        const button = this.page.locator(`text=${buttonText}`);
        if (await button.isVisible()) {
          await this.log('Match Feature Button', 'PASS', `Found: ${buttonText}`);
          break;
        }
      }

      const screenshot = await this.takeScreenshot('employer-matches');
      await this.log('Employer Matches Screenshot', 'PASS', 'Employer matches screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Matches Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerAICoach() {
    console.log('\n🤖 TESTING EMPLOYER AI COACH...');

    try {
      await this.page.goto(`${this.baseUrl}/path-coach`);
      await this.page.waitForTimeout(4000);

      // Check if AI Coach loads for employers
      const pageText = await this.page.textContent('body');
      
      // Look for employer-specific AI Coach content
      const employerAITerms = ['hiring', 'recruitment', 'talent', 'interview', 'candidate'];
      const foundAITerms = employerAITerms.filter(term => 
        pageText.toLowerCase().includes(term.toLowerCase())
      );

      if (foundAITerms.length > 0) {
        await this.log('Employer AI Coach Content', 'PASS', `Found employer AI terms: ${foundAITerms.join(', ')}`);
      } else {
        await this.log('Employer AI Coach Content', 'WARN', 'No employer-specific AI content detected');
      }

      // Look for chat interface
      const chatInput = this.page.locator('input[placeholder*="Ask"], textarea[placeholder*="Ask"], input[placeholder*="message"]');
      
      if (await chatInput.isVisible()) {
        await this.log('AI Chat Interface', 'PASS', 'Chat interface available for employers');
        
        // Test sending an employer-specific message
        await chatInput.fill('How can I improve my hiring process?');
        
        const sendButton = this.page.locator('button:has-text("Send"), button[type="submit"]').first();
        if (await sendButton.isVisible()) {
          await sendButton.click();
          await this.page.waitForTimeout(3000);
          await this.log('Employer AI Chat Test', 'PASS', 'Successfully sent employer query to AI');
        }
      } else {
        await this.log('AI Chat Interface', 'FAIL', 'Chat interface not found');
      }

      const screenshot = await this.takeScreenshot('employer-ai-coach');
      await this.log('Employer AI Coach Screenshot', 'PASS', 'Employer AI Coach screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer AI Coach Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerProfile() {
    console.log('\n👤 TESTING EMPLOYER PROFILE...');

    try {
      await this.page.goto(`${this.baseUrl}/profile`);
      await this.page.waitForTimeout(3000);

      // Check for employer profile fields
      const inputs = await this.page.locator('input, textarea, select').count();
      
      if (inputs > 0) {
        await this.log('Employer Profile Form', 'PASS', `Found ${inputs} form fields`);
        
        // Look for employer-specific fields
        const pageText = await this.page.textContent('body');
        const employerFields = ['company', 'organization', 'business', 'industry', 'size'];
        const foundFields = employerFields.filter(field => 
          pageText.toLowerCase().includes(field.toLowerCase())
        );

        if (foundFields.length > 0) {
          await this.log('Employer Profile Fields', 'PASS', `Found employer fields: ${foundFields.join(', ')}`);
        } else {
          await this.log('Employer Profile Fields', 'WARN', 'No employer-specific fields detected');
        }
      } else {
        await this.log('Employer Profile Form', 'FAIL', 'No profile form found');
      }

      const screenshot = await this.takeScreenshot('employer-profile');
      await this.log('Employer Profile Screenshot', 'PASS', 'Employer profile screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Profile Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async runEmployerTests() {
    console.log('🚀 STARTING COMPREHENSIVE EMPLOYER UI TESTING...\n');
    console.log('This will test all employer-specific features in PathLink.\n');

    try {
      await this.setup();

      // Login as employer first
      const loginSuccess = await this.loginAsEmployer();
      
      if (loginSuccess) {
        await this.testEmployerDashboard();
        await this.testJobManagement();
        await this.testCandidateManagement();
        await this.testEmployerMatches();
        await this.testEmployerAICoach();
        await this.testEmployerProfile();
      } else {
        console.log('❌ Could not login as employer - skipping employer-specific tests');
      }

    } catch (error) {
      console.error('Employer UI testing failed:', error);
    } finally {
      await this.cleanup();
    }

    // Generate comprehensive report
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const warningTests = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 EMPLOYER UI TEST SUMMARY:');
    console.log('='.repeat(60));
    console.log(`✅ PASSED: ${passedTests}`);
    console.log(`❌ FAILED: ${failedTests}`);
    console.log(`⚠️  WARNINGS: ${warningTests}`);
    console.log(`📈 EMPLOYER UI SUCCESS RATE: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));

    // Categorize results
    const categories = {};
    this.results.forEach(result => {
      const category = result.test.split(' ')[0];
      if (!categories[category]) {
        categories[category] = { pass: 0, fail: 0, warn: 0 };
      }
      categories[category][result.status.toLowerCase()]++;
    });

    console.log('\n📋 EMPLOYER FEATURES BREAKDOWN:');
    Object.entries(categories).forEach(([category, stats]) => {
      const total = stats.pass + stats.fail + stats.warn;
      const successRate = ((stats.pass / total) * 100).toFixed(1);
      console.log(`${category}: ${stats.pass}/${total} (${successRate}%)`);
    });

    // Save detailed report
    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        warningTests,
        successRate: (passedTests / totalTests) * 100
      },
      categories,
      results: this.results,
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('employer-ui-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed employer UI test report saved to employer-ui-test-report.json');
    console.log('📸 Employer screenshots saved in the screenshots/ directory');

    // Final recommendations
    console.log('\n🎯 EMPLOYER UI RECOMMENDATIONS:');
    if (passedTests / totalTests >= 0.8) {
      console.log('   ✅ Employer features are working excellently!');
      console.log('   ✅ All major employer functionality is operational');
      console.log('   ✅ Ready for employer user testing');
    } else if (passedTests / totalTests >= 0.6) {
      console.log('   ⚠️  Employer features are mostly functional');
      console.log('   🔧 Some employer features need attention');
      console.log('   📈 Consider improving employer-specific workflows');
    } else {
      console.log('   ❌ Employer features need significant work');
      console.log('   🔧 Critical employer functionality issues');
      console.log('   📋 Review failed tests for employer improvements');
    }

    return report;
  }
}

// Run employer UI tests
const tester = new EmployerUITestSuite();
tester.runEmployerTests().catch(console.error);
