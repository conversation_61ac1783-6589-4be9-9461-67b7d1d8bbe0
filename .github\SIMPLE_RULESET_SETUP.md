# Simple GitHub Ruleset Setup for PathLink

## 🎯 Quick Setup Guide

This is a simplified guide to set up essential repository protection rules manually in GitHub.

## 📋 Step-by-Step Instructions

### 1. Navigate to Repository Settings
1. Go to: `https://github.com/peterdimian/PathLink`
2. Click **Settings** tab
3. Click **Rules** → **Rulesets** in left sidebar

### 2. Create New Ruleset
1. Click **New ruleset** button
2. Enter name: `PathLink Protection`
3. Set **Enforcement status** to **Active**

### 3. Configure Target Branches
1. In **Target branches** section:
   - Click **Add target**
   - Select **Include by name**
   - Enter `main` (or `master` if that's your default branch)
   - Click **Add inclusion**

### 4. Add Essential Rules

#### Rule 1: Require Pull Requests
1. Click **Add rule** → **Require a pull request before merging**
2. Configure:
   - **Required approvals**: `1`
   - ✅ Check **Dismiss stale PR reviews when new commits are pushed**
   - ✅ Check **Require review from code owners**
   - ✅ Check **Require conversation resolution before merging**

#### Rule 2: Require Status Checks
1. Click **Add rule** → **Require status checks to pass before merging**
2. Configure:
   - ✅ Check **Require branches to be up to date before merging**
   - In **Status checks** field, add: `security-check`

#### Rule 3: Block Force Pushes
1. Click **Add rule** → **Block force pushes**
2. No additional configuration needed

#### Rule 4: Require Linear History
1. Click **Add rule** → **Require linear history**
2. No additional configuration needed

#### Rule 5: Restrict Deletions
1. Click **Add rule** → **Restrict deletions**
2. No additional configuration needed

### 5. Save Ruleset
1. Click **Create** button at the bottom
2. Confirm the ruleset is **Active**

## ✅ What This Achieves

### Protection Enabled:
- ✅ **No direct pushes** to main branch
- ✅ **All changes require PR** with 1 approval
- ✅ **Security checks must pass** before merging
- ✅ **No force pushes** allowed
- ✅ **Linear commit history** enforced
- ✅ **Branch cannot be deleted**

### Security Benefits:
- ✅ **Code review required** for all changes
- ✅ **Automated security scanning** before merge
- ✅ **Audit trail** of all changes
- ✅ **Prevents accidental** direct commits
- ✅ **Maintains clean history**

## 🧪 Test the Setup

### Test 1: Try Direct Push (Should Fail)
```bash
# This should be blocked
git checkout main
echo "test" > test.txt
git add test.txt
git commit -m "Direct commit test"
git push origin main
```
**Expected**: ❌ Push rejected - requires pull request

### Test 2: Create PR (Should Work)
```bash
# This should work
git checkout -b test-branch
echo "test" > test.txt
git add test.txt
git commit -m "Test commit"
git push origin test-branch
# Then create PR via GitHub UI
```
**Expected**: ✅ PR created successfully

## 🔧 Additional Security (Optional)

### Enable Branch Protection (Legacy)
If rulesets don't work, use classic branch protection:

1. Go to **Settings** → **Branches**
2. Click **Add rule**
3. Enter branch name: `main`
4. Enable:
   - ✅ Require a pull request before merging
   - ✅ Require status checks to pass before merging
   - ✅ Require branches to be up to date before merging
   - ✅ Require linear history
   - ✅ Include administrators

### Repository Security Settings
1. Go to **Settings** → **Security & analysis**
2. Enable:
   - ✅ Dependency graph
   - ✅ Dependabot alerts
   - ✅ Dependabot security updates
   - ✅ Secret scanning

## 🚨 Troubleshooting

### "Invalid actor" Error
- Remove any bypass actors from the configuration
- Use empty bypass actors array: `"bypass_actors": []`

### Status Check Not Found
- The `security-check` status will appear after first workflow run
- You can add it later after the workflow runs once

### Can't Import JSON
- Use manual setup instead of JSON import
- GitHub's ruleset format is still evolving

### Need Emergency Access
- Repository admins can temporarily disable rulesets
- Go to **Settings** → **Rules** → **Rulesets**
- Change enforcement to **Disabled** temporarily

## 📞 Support

If you encounter issues:
1. Check GitHub's [ruleset documentation](https://docs.github.com/en/repositories/configuring-branches-and-merges-in-your-repository/managing-rulesets)
2. Use classic branch protection as fallback
3. Contact GitHub support for ruleset-specific issues

## ✅ Verification

After setup, verify these work:
- [ ] Direct pushes to main are blocked
- [ ] PRs can be created on feature branches
- [ ] Security workflow runs on PRs
- [ ] PRs require approval before merge
- [ ] Force pushes are blocked

---

**Setup Time**: ~5 minutes  
**Protection Level**: High  
**Maintenance**: Minimal
