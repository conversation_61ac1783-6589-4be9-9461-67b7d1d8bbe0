#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 PathLink AWS Lambda Deployment');
console.log('=================================\n');

// AWS CLI path
const AWS_CLI = '"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe"';

function createLambdaHandler() {
  console.log('📝 Creating Lambda handler...');
  
  const handlerContent = `const serverless = require('serverless-http');
const express = require('express');
const cors = require('cors');
const { neon } = require('@neondatabase/serverless');

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL);

const app = express();

// Configure CORS
app.use(cors({
  origin: ['https://pathlink.netlify.app', 'http://localhost:5000'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Test database connection
app.get('/api/health', async (req, res) => {
  try {
    const result = await sql\`SELECT 1 as test\`;
    res.json({ 
      status: 'healthy', 
      database: 'connected',
      timestamp: new Date().toISOString() 
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'error', 
      database: 'disconnected',
      error: error.message 
    });
  }
});

// Get all jobs
app.get('/api/jobs', async (req, res) => {
  try {
    const jobs = await sql\`
      SELECT id, title, company, location, industry,
             salary_range, description, required_skills, created_at, employer_id
      FROM jobs
      ORDER BY created_at DESC
    \`;
    res.json(jobs);
  } catch (error) {
    console.error('Error getting jobs:', error);
    res.status(500).json({ error: 'Failed to get jobs' });
  }
});

// User authentication
app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    const users = await sql\`
      SELECT u.id, u.email, u.name, u.role, u.password,
             up.profile_picture as "profilePicture",
             up.position, up.location, up.bio, up.company,
             up.years_of_experience as "yearsOfExperience", up.skills
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.email = \${email}
      LIMIT 1
    \`;
    
    if (users.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    const user = users[0];
    
    // Simple password check (in production, use proper hashing)
    if (user.password !== password) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Remove password from response
    delete user.password;
    
    res.json({ user, message: 'Login successful' });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Get user profile
app.get('/api/profile/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const users = await sql\`
      SELECT u.id, u.email, u.name, u.role,
             up.profile_picture as "profilePicture",
             up.position, up.location, up.bio, up.company,
             up.years_of_experience as "yearsOfExperience", up.skills
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.id = \${userId}
      LIMIT 1
    \`;
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(users[0]);
  } catch (error) {
    console.error('Error getting profile:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Get resumes
app.get('/api/resumes', async (req, res) => {
  try {
    const resumes = await sql\`
      SELECT r.*, u.id as worker_id, u.name as worker_name, u.email as worker_email
      FROM resumes r
      JOIN users u ON r.worker_id = u.id
      WHERE u.role = 'worker'
      ORDER BY r.upload_date DESC
    \`;

    const formattedResumes = resumes.map(row => ({
      resume: {
        id: row.id,
        filename: row.filename,
        file_path: row.file_path,
        file_size: row.file_size,
        file_type: row.file_type,
        extracted_text: row.extracted_text,
        upload_date: row.upload_date,
        user_id: row.worker_id
      },
      worker: {
        id: row.worker_id,
        name: row.worker_name,
        email: row.worker_email,
        role: 'worker'
      }
    }));

    res.json(formattedResumes);
  } catch (error) {
    console.error('Error getting resumes:', error);
    res.status(500).json({ error: 'Failed to get resumes' });
  }
});

// Get applications
app.get('/api/applications/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const applications = await sql\`
      SELECT m.id, m.worker_id as user_id, m.job_id, m.status, m.match_date as created_at,
             j.title, j.company, j.location, j.salary_range as salary
      FROM matches m
      JOIN jobs j ON m.job_id = j.id
      WHERE m.worker_id = \${userId}
      ORDER BY m.match_date DESC
    \`;
    
    res.json(applications);
  } catch (error) {
    console.error('Error getting applications:', error);
    res.status(500).json({ error: 'Failed to get applications' });
  }
});

// Apply for job
app.post('/api/apply', async (req, res) => {
  try {
    const { user_id, job_id } = req.body;
    
    const result = await sql\`
      INSERT INTO matches (worker_id, job_id, status, match_score, match_date)
      VALUES (\${user_id}, \${job_id}, 'pending', 85, NOW())
      RETURNING id, worker_id as user_id, job_id, status, match_date as created_at
    \`;
    
    res.json(result[0]);
  } catch (error) {
    console.error('Error applying for job:', error);
    res.status(500).json({ error: 'Failed to apply for job' });
  }
});

// Catch all other routes
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Export the serverless handler
module.exports.handler = serverless(app);
`;

  fs.writeFileSync('lambda-handler.js', handlerContent);
  console.log('✅ Lambda handler created');
}

function createLambdaPackage() {
  console.log('📦 Creating Lambda deployment package...');
  
  try {
    // Create a temporary directory for Lambda package
    if (!fs.existsSync('lambda-package')) {
      fs.mkdirSync('lambda-package');
    }
    
    // Copy handler
    fs.copyFileSync('lambda-handler.js', 'lambda-package/index.js');
    
    // Create package.json for Lambda
    const lambdaPackageJson = {
      "name": "pathlink-lambda",
      "version": "1.0.0",
      "main": "index.js",
      "dependencies": {
        "serverless-http": "^3.2.0",
        "express": "^4.21.2",
        "cors": "^2.8.5",
        "@neondatabase/serverless": "^0.10.4"
      }
    };
    
    fs.writeFileSync('lambda-package/package.json', JSON.stringify(lambdaPackageJson, null, 2));
    
    // Install dependencies in lambda package
    execSync('npm install', { cwd: 'lambda-package', stdio: 'inherit' });
    
    // Create zip file
    execSync('powershell Compress-Archive -Path "lambda-package/*" -DestinationPath "pathlink-lambda.zip" -Force', { stdio: 'inherit' });
    
    console.log('✅ Lambda package created');
    return 'pathlink-lambda.zip';
  } catch (error) {
    console.log('❌ Failed to create Lambda package:', error.message);
    return null;
  }
}

function createLambdaFunction(packagePath) {
  console.log('🔧 Creating Lambda function...');
  
  const functionName = 'pathlink-api';
  
  try {
    // Check if function exists
    try {
      execSync(`${AWS_CLI} lambda get-function --function-name ${functionName}`, { stdio: 'pipe' });
      console.log('✅ Lambda function already exists, updating...');
      
      // Update function code
      execSync(`${AWS_CLI} lambda update-function-code --function-name ${functionName} --zip-file fileb://${packagePath}`, { stdio: 'inherit' });
      
      // Update environment variables
      const envVars = {
        "Variables": {
          "NODE_ENV": "production",
          "DATABASE_URL": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
          "SESSION_SECRET": "pathlink-production-secret-2024",
          "JWT_SECRET": "pathlink-jwt-secret-2024",
          "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
          "SENDGRID_API_KEY": "SG.1234567890abcdefghijklmnopqrstuvwxyz"
        }
      };
      
      fs.writeFileSync('env-vars.json', JSON.stringify(envVars));
      execSync(`${AWS_CLI} lambda update-function-configuration --function-name ${functionName} --environment file://env-vars.json`, { stdio: 'inherit' });
      
      return functionName;
    } catch (e) {
      // Function doesn't exist, create it
    }
    
    // Create IAM role for Lambda
    const roleName = 'pathlink-lambda-role';
    const trustPolicy = {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Principal": {
            "Service": "lambda.amazonaws.com"
          },
          "Action": "sts:AssumeRole"
        }
      ]
    };
    
    fs.writeFileSync('trust-policy.json', JSON.stringify(trustPolicy));
    
    try {
      execSync(`${AWS_CLI} iam create-role --role-name ${roleName} --assume-role-policy-document file://trust-policy.json`, { stdio: 'pipe' });
      execSync(`${AWS_CLI} iam attach-role-policy --role-name ${roleName} --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole`, { stdio: 'pipe' });
      console.log('✅ IAM role created');
    } catch (roleError) {
      console.log('⚠️ IAM role might already exist');
    }
    
    // Get account ID
    const accountId = execSync(`${AWS_CLI} sts get-caller-identity --query Account --output text`, { encoding: 'utf8' }).trim();
    const roleArn = `arn:aws:iam::${accountId}:role/${roleName}`;
    
    // Wait a moment for role to be ready
    console.log('⏳ Waiting for IAM role to be ready...');
    execSync('timeout /t 10 /nobreak', { stdio: 'pipe' });
    
    // Create Lambda function
    execSync(`${AWS_CLI} lambda create-function --function-name ${functionName} --runtime nodejs18.x --role ${roleArn} --handler index.handler --zip-file fileb://${packagePath} --timeout 30 --memory-size 512`, { stdio: 'inherit' });
    
    // Set environment variables
    const envVars = {
      "Variables": {
        "NODE_ENV": "production",
        "DATABASE_URL": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
        "SESSION_SECRET": "pathlink-production-secret-2024",
        "JWT_SECRET": "pathlink-jwt-secret-2024",
        "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
        "SENDGRID_API_KEY": "SG.1234567890abcdefghijklmnopqrstuvwxyz"
      }
    };
    
    fs.writeFileSync('env-vars.json', JSON.stringify(envVars));
    execSync(`${AWS_CLI} lambda update-function-configuration --function-name ${functionName} --environment file://env-vars.json`, { stdio: 'inherit' });
    
    console.log('✅ Lambda function created');
    return functionName;
  } catch (error) {
    console.log('❌ Failed to create Lambda function:', error.message);
    return null;
  }
}

function createAPIGateway(functionName) {
  console.log('🌐 Creating API Gateway...');
  
  const apiName = 'pathlink-api';
  
  try {
    // Create REST API
    const createResult = execSync(`${AWS_CLI} apigateway create-rest-api --name ${apiName} --description "PathLink API"`, { encoding: 'utf8' });
    const api = JSON.parse(createResult);
    const apiId = api.id;
    
    console.log(`✅ API Gateway created: ${apiId}`);
    
    // Get root resource
    const resourcesResult = execSync(`${AWS_CLI} apigateway get-resources --rest-api-id ${apiId}`, { encoding: 'utf8' });
    const resources = JSON.parse(resourcesResult);
    const rootResourceId = resources.items[0].id;
    
    // Create proxy resource
    const proxyResult = execSync(`${AWS_CLI} apigateway create-resource --rest-api-id ${apiId} --parent-id ${rootResourceId} --path-part "{proxy+}"`, { encoding: 'utf8' });
    const proxyResource = JSON.parse(proxyResult);
    const proxyResourceId = proxyResource.id;
    
    // Create ANY method
    execSync(`${AWS_CLI} apigateway put-method --rest-api-id ${apiId} --resource-id ${proxyResourceId} --http-method ANY --authorization-type NONE`, { stdio: 'pipe' });
    
    // Get account ID and region
    const accountId = execSync(`${AWS_CLI} sts get-caller-identity --query Account --output text`, { encoding: 'utf8' }).trim();
    const region = 'us-east-1';
    
    // Set up integration
    const lambdaUri = `arn:aws:apigateway:${region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${region}:${accountId}:function:${functionName}/invocations`;
    
    execSync(`${AWS_CLI} apigateway put-integration --rest-api-id ${apiId} --resource-id ${proxyResourceId} --http-method ANY --type AWS_PROXY --integration-http-method POST --uri ${lambdaUri}`, { stdio: 'pipe' });
    
    // Add permission for API Gateway to invoke Lambda
    execSync(`${AWS_CLI} lambda add-permission --function-name ${functionName} --statement-id apigateway-invoke --action lambda:InvokeFunction --principal apigateway.amazonaws.com --source-arn "arn:aws:execute-api:${region}:${accountId}:${apiId}/*/*"`, { stdio: 'pipe' });
    
    // Deploy API
    execSync(`${AWS_CLI} apigateway create-deployment --rest-api-id ${apiId} --stage-name prod`, { stdio: 'inherit' });
    
    const apiUrl = `${apiId}.execute-api.${region}.amazonaws.com/prod`;
    console.log(`✅ API Gateway deployed: https://${apiUrl}`);
    
    return apiUrl;
  } catch (error) {
    console.log('❌ Failed to create API Gateway:', error.message);
    return null;
  }
}

function updateNetlifyConfig(apiUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace AWS redirects with API Gateway
    netlifyConfig = netlifyConfig.replace(
      /# Redirect API calls to AWS Elastic Beanstalk[\s\S]*?force = true/g,
      `# Redirect API calls to AWS Lambda
[[redirects]]
  from = "/api/*"
  to = "https://${apiUrl}/:splat"
  status = 200
  force = true`
    );
    
    netlifyConfig = netlifyConfig.replace(
      /# Redirect upload calls to AWS[\s\S]*?force = true/g,
      `# Redirect upload calls to AWS Lambda
[[redirects]]
  from = "/upload/*"
  to = "https://${apiUrl}/upload/:splat"
  status = 200
  force = true`
    );
    
    netlifyConfig = netlifyConfig.replace(
      /# Health check redirect to AWS[\s\S]*?force = true/g,
      `# Health check redirect to AWS Lambda
[[redirects]]
  from = "/health"
  to = "https://${apiUrl}/health"
  status = 200
  force = true`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated');
    console.log(`🔗 API calls will redirect to: https://${apiUrl}`);
    
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log(`Please manually update netlify.toml with: https://${apiUrl}`);
  }
}

async function main() {
  console.log('Starting PathLink AWS Lambda deployment...\n');
  
  // Step 1: Create Lambda handler
  createLambdaHandler();
  
  // Step 2: Create deployment package
  const packagePath = createLambdaPackage();
  if (!packagePath) {
    console.log('\n❌ Failed to create deployment package.');
    process.exit(1);
  }
  
  // Step 3: Create Lambda function
  const functionName = createLambdaFunction(packagePath);
  if (!functionName) {
    console.log('\n❌ Failed to create Lambda function.');
    process.exit(1);
  }
  
  // Step 4: Create API Gateway
  const apiUrl = createAPIGateway(functionName);
  if (!apiUrl) {
    console.log('\n❌ Failed to create API Gateway.');
    process.exit(1);
  }
  
  // Step 5: Update Netlify configuration
  updateNetlifyConfig(apiUrl);
  
  console.log('\n🎉 DEPLOYMENT COMPLETE!');
  console.log('======================');
  console.log('✅ Backend deployed to AWS Lambda');
  console.log('✅ API Gateway configured');
  console.log('✅ Netlify configuration updated');
  console.log(`\n🌍 Your API: https://${apiUrl}`);
  console.log(`🔍 Health check: https://${apiUrl}/health`);
  console.log(`🔌 API test: https://${apiUrl}/api/health`);
  
  // Cleanup
  try {
    fs.unlinkSync('lambda-handler.js');
    fs.unlinkSync('pathlink-lambda.zip');
    fs.unlinkSync('trust-policy.json');
    fs.unlinkSync('env-vars.json');
    execSync('rmdir /s /q lambda-package', { stdio: 'pipe' });
    console.log('✅ Cleaned up temporary files');
  } catch (e) {
    // Ignore cleanup errors
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
