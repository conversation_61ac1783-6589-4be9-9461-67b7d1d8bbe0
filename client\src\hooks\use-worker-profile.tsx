import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { WorkerProfile } from "@shared/schema";

export function useWorkerProfile() {
  const { toast } = useToast();
  
  const {
    data: workerProfile,
    isLoading,
    error,
  } = useQuery<WorkerProfile>({
    queryKey: ["/api/pathcoach/worker-profile"],
    staleTime: 300000, // 5 minutes
  });
  
  const createProfileMutation = useMutation({
    mutationFn: async (profileData: { skills_context: any }) => {
      const res = await apiRequest("POST", "/api/pathcoach/worker-profile", profileData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Your skills profile has been saved.",
      });
      // Invalidate the worker profile query to refetch data
      queryClient.invalidateQueries({ queryKey: ["/api/pathcoach/worker-profile"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to save profile: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    workerProfile,
    isLoading,
    error,
    createProfileMutation,
  };
}