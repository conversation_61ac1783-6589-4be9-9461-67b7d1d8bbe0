# GitHub Ruleset Setup Guide for PathLink

## 🔒 Repository Security Ruleset Implementation

This guide will help you implement comprehensive GitHub repository rules to protect your PathLink source code and enforce secure development workflows.

## 📋 Prerequisites

- Repository admin access to PathLink
- GitHub Pro, Team, or Enterprise account (required for advanced rulesets)
- Understanding of your development workflow

## 🛠️ Step-by-Step Setup

### 1. Access Repository Settings

1. Go to your PathLink repository on GitHub
2. Click **Settings** tab
3. Navigate to **Rules** → **Rulesets** in the left sidebar

### 2. Create New Ruleset

1. Click **New ruleset** button
2. Choose **Branch ruleset** (most comprehensive protection)
3. Enter ruleset name: `PathLink Security & Protection Ruleset`

### 3. Configure Ruleset Scope

**Enforcement Status:**
- ✅ Set to **Active** (enforces rules immediately)

**Target Branches:**
- ✅ Include: `main`, `master`, `production`, `staging`, `release/*`
- ✅ Exclude: `dependabot/*` (allow automated dependency updates)

### 4. Configure Branch Protection Rules

#### **Pull Request Requirements:**
```
✅ Require a pull request before merging
✅ Require approvals: 1
✅ Dismiss stale PR reviews when new commits are pushed
✅ Require review from code owners
✅ Require approval of the most recent reviewable push
✅ Require conversation resolution before merging
```

#### **Status Check Requirements:**
```
✅ Require status checks to pass before merging
✅ Require branches to be up to date before merging

Required Status Checks:
- security-check
- codespace-security  
- build
- test
- lint
```

#### **Additional Restrictions:**
```
✅ Require signed commits
✅ Require linear history
✅ Restrict pushes that create files
✅ Restrict force pushes
✅ Restrict deletions
```

### 5. Configure File and Path Restrictions

#### **Restricted File Paths:**
Add these patterns to prevent modification of security files:
```
.github/rulesets/*
.github/workflows/security-*.yml
.devcontainer/security-*.sh
.devcontainer/Dockerfile
CODESPACE_SECURITY_*.md
.env
.env.*
*.key
*.pem
*.p12
*.pfx
config/production.json
secrets.json
credentials.json
```

#### **Restricted File Extensions:**
Block these file types:
```
.exe, .bat, .cmd, .com, .scr, .msi
.zip, .tar, .tar.gz, .rar, .7z
.db, .sqlite, .mdb
```

#### **File Size Limits:**
- ✅ Maximum file size: **10MB**

### 6. Configure Bypass Settings

**Bypass Actors:**
- ✅ Repository Administrators (emergency access only)
- ✅ Specific users: Repository owner
- ✅ Bypass mode: **Pull request** (still requires PR even for admins)

### 7. Advanced Security Settings

#### **Repository Security:**
1. Go to **Settings** → **Security**
2. Enable these features:
   ```
   ✅ Vulnerability alerts
   ✅ Security advisories
   ✅ Dependency graph
   ✅ Dependabot alerts
   ✅ Dependabot security updates
   ```

#### **Codespace Restrictions:**
1. Go to **Settings** → **Codespaces**
2. Configure:
   ```
   ✅ Restrict codespace creation to collaborators
   ✅ Set timeout: 30 minutes
   ✅ Restrict machine types: Basic/Standard only
   ```

### 8. Webhook Configuration (Optional)

For advanced monitoring, set up webhooks:
1. Go to **Settings** → **Webhooks**
2. Add webhook URL: `https://api.pathlink.com/webhooks/security`
3. Select events:
   ```
   ✅ Push
   ✅ Pull requests
   ✅ Repository
   ✅ Security advisory
   ```

## 🧪 Testing the Ruleset

### Test 1: Direct Push Prevention
```bash
# This should be blocked
git push origin main
```
**Expected Result:** ❌ Push rejected - requires pull request

### Test 2: Sensitive File Detection
```bash
# This should be blocked
echo "secret=abc123" > .env
git add .env
git commit -m "Add env file"
git push
```
**Expected Result:** ❌ Commit rejected - sensitive file detected

### Test 3: Large File Prevention
```bash
# This should be blocked
dd if=/dev/zero of=largefile.bin bs=1M count=15
git add largefile.bin
git commit -m "Add large file"
```
**Expected Result:** ❌ Commit rejected - file too large

### Test 4: Security Workflow
```bash
# This should trigger security checks
git checkout -b feature/test-security
echo "console.log('test');" > test.js
git add test.js
git commit -m "Add test file"
git push origin feature/test-security
# Create PR - should trigger security workflow
```
**Expected Result:** ✅ Security checks run and pass

## 📊 Monitoring and Compliance

### View Ruleset Activity
1. Go to **Insights** → **Pulse**
2. Check **Security** tab for violations
3. Review **Actions** tab for workflow results

### Audit Logs
1. Go to **Settings** → **Audit log**
2. Filter by:
   - `action:protected_branch`
   - `action:repo.ruleset`
   - `action:git.push_protection`

### Security Alerts
Monitor these locations for security events:
- **Security** tab → **Security advisories**
- **Pull requests** → Failed status checks
- **Actions** → Security workflow failures

## 🚨 Violation Response

### When Rules Are Violated:
1. **Automatic blocking** of the violating action
2. **Notification** to repository administrators
3. **Audit log entry** with full details
4. **Required remediation** before proceeding

### Common Violations and Solutions:

#### Direct Push to Protected Branch:
```
❌ Problem: Direct push to main branch
✅ Solution: Create pull request instead
```

#### Sensitive File Committed:
```
❌ Problem: .env file in commit
✅ Solution: Remove file, add to .gitignore
```

#### Large File Upload:
```
❌ Problem: File exceeds 10MB limit
✅ Solution: Use Git LFS or external storage
```

#### Unsigned Commits:
```
❌ Problem: Commit not signed
✅ Solution: Enable commit signing in Git
```

## 🔧 Maintenance

### Regular Reviews:
- **Monthly**: Review ruleset effectiveness
- **Quarterly**: Update restricted file patterns
- **Annually**: Audit bypass usage and permissions

### Updates:
- Monitor GitHub's new security features
- Update status check requirements as CI/CD evolves
- Adjust file size limits based on project needs

## 📞 Support

### For Ruleset Issues:
- Check **Actions** tab for detailed error messages
- Review **Audit log** for violation details
- Contact repository administrator for bypass requests

### Emergency Bypass:
- Repository administrators can bypass rules
- Document reason for bypass in commit message
- Review and remove bypass after emergency resolved

## ✅ Verification Checklist

After setup, verify these protections are active:

- [ ] Direct pushes to main branch are blocked
- [ ] Pull requests require approval
- [ ] Security workflows run on all PRs
- [ ] Sensitive files are automatically detected and blocked
- [ ] Large files are rejected
- [ ] Restricted file extensions are blocked
- [ ] Codespace security is enforced
- [ ] Audit logging is enabled
- [ ] Webhook notifications are working (if configured)

---

**Security Status**: 🔒 **MAXIMUM PROTECTION ACTIVE**  
**Implementation Date**: January 2025  
**Next Review**: Quarterly
