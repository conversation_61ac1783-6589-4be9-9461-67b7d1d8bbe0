const serverless = require('serverless-http');
const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create Express app
const app = express();

// Configure multer storage for Netlify Functions
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // In Netlify Functions, we need to use the /tmp directory for temporary file storage
    const uploadDir = '/tmp/uploads/resumes';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, 'resume-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage });

// Configure routes
app.post('/api/upload/resume', upload.single('resumeFile'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // Get file information
    const { originalname, path: filePath, size } = req.file;

    // In a production environment, you would upload this file to a cloud storage service
    // like AWS S3 and store the URL in your database

    // For now, we'll just return the file information
    res.json({
      message: 'File uploaded successfully',
      file: {
        name: originalname,
        path: filePath,
        size
      }
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    res.status(500).json({ message: 'Failed to upload file' });
  }
});

// Create serverless handler
const handler = serverless(app);

// Export the serverless handler
module.exports = {
  handler: async (event, context) => {
    // Log request details
    console.log('Upload request path:', event.path);
    console.log('Upload request method:', event.httpMethod);

    return await handler(event, context);
  }
};
