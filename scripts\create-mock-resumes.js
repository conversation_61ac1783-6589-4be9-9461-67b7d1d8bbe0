/**
 * This script creates mock resume data and adds it to the server's memory storage
 * for search functionality testing.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Sample resume data
const sampleResumes = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    skills: ["JavaScript", "React", "Node.js", "TypeScript", "MongoDB"],
    experience: "5 years of frontend development experience at tech startups",
    education: "Bachelor's in Computer Science, Stanford University",
    industry: "Technology"
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    skills: ["Python", "Django", "PostgreSQL", "AWS", "Docker"],
    experience: "7 years as a backend developer with focus on scalable systems",
    education: "Master's in Software Engineering, MIT",
    industry: "Technology"
  },
  {
    name: "<PERSON>",
    email: "micha<PERSON>.<EMAIL>",
    skills: ["Java", "Spring Boot", "Microservices", "Kubernetes", "CI/CD"],
    experience: "10 years in enterprise software development",
    education: "Bachelor's in Computer Engineering, UC Berkeley",
    industry: "Technology"
  },
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    skills: ["Data Analysis", "SQL", "Python", "Tableau", "Excel"],
    experience: "6 years as a data analyst in the finance sector",
    education: "Master's in Business Analytics, NYU",
    industry: "Finance"
  },
  {
    name: "David Wilson",
    email: "<EMAIL>",
    skills: ["UX/UI Design", "Figma", "Adobe XD", "HTML/CSS", "User Research"],
    experience: "8 years designing user interfaces for web and mobile applications",
    education: "Bachelor's in Graphic Design, RISD",
    industry: "Design"
  },
  {
    name: "Jennifer Lee",
    email: "<EMAIL>",
    skills: ["Product Management", "Agile", "Scrum", "User Stories", "Roadmapping"],
    experience: "9 years managing digital products from conception to launch",
    education: "MBA, Harvard Business School",
    industry: "Product Management"
  },
  {
    name: "Robert Taylor",
    email: "<EMAIL>",
    skills: ["DevOps", "AWS", "Terraform", "Docker", "Kubernetes"],
    experience: "7 years implementing CI/CD pipelines and cloud infrastructure",
    education: "Bachelor's in Computer Science, Georgia Tech",
    industry: "Technology"
  },
  {
    name: "Lisa Martinez",
    email: "<EMAIL>",
    skills: ["Machine Learning", "Python", "TensorFlow", "Data Science", "NLP"],
    experience: "5 years developing machine learning models for recommendation systems",
    education: "PhD in Computer Science, Stanford University",
    industry: "Artificial Intelligence"
  },
  {
    name: "James Wilson",
    email: "<EMAIL>",
    skills: ["iOS Development", "Swift", "Objective-C", "UIKit", "SwiftUI"],
    experience: "6 years developing iOS applications",
    education: "Bachelor's in Computer Science, University of Michigan",
    industry: "Technology"
  },
  {
    name: "Sophia Garcia",
    email: "<EMAIL>",
    skills: ["Android Development", "Kotlin", "Java", "Jetpack Compose", "Firebase"],
    experience: "5 years as an Android developer",
    education: "Master's in Mobile Computing, University of Toronto",
    industry: "Technology"
  }
];

// Function to generate resume content
function generateResumeContent(resume) {
  return `
# ${resume.name}
Email: ${resume.email}

## Skills
${resume.skills.join(', ')}

## Experience
${resume.experience}

## Education
${resume.education}

## Industry
${resume.industry}

## Additional Information
Passionate professional with a strong background in ${resume.industry.toLowerCase()}.
Experienced in ${resume.skills.slice(0, 3).join(', ')}, and other technologies.
Looking for opportunities to leverage my expertise in ${resume.skills[0]} and ${resume.skills[1]}.

References available upon request.
`;
}

// Create the uploads/resumes directory if it doesn't exist
const resumesDir = path.join(__dirname, '..', 'uploads', 'resumes');
if (!fs.existsSync(resumesDir)) {
  fs.mkdirSync(resumesDir, { recursive: true });
}

// Create sample resume files
for (let i = 0; i < sampleResumes.length; i++) {
  const resume = sampleResumes[i];
  const content = generateResumeContent(resume);
  const filename = `${resume.name.replace(/\s+/g, '_')}_Resume.txt`;
  const filePath = path.join(resumesDir, filename);
  
  fs.writeFileSync(filePath, content);
  console.log(`Created sample resume: ${filePath}`);
}

console.log(`Created ${sampleResumes.length} sample resumes in ${resumesDir}`);
console.log('You can now start the server and search for resumes using keywords like:');
console.log('- JavaScript, React, Python, Java');
console.log('- Frontend, Backend, DevOps, Machine Learning');
console.log('- Technology, Finance, Design, Product Management');
