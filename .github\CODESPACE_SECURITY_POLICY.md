# PathLink Codespace Security Policy

## 🔒 Security Overview

This GitHub Codespace environment is configured with comprehensive security restrictions to protect proprietary source code and prevent unauthorized data extraction.

## 🚫 Restricted Operations

### Completely Blocked Commands:
- **File Transfer**: `wget`, `curl`, `scp`, `rsync`, `ftp`, `sftp`
- **Network Tools**: `nc`, `netcat`, `telnet`
- **Archive Operations**: `tar`, `zip`, `unzip`, `gzip`, `gunzip`
- **Git Operations**: `git clone`, `git archive`, `git bundle`

### Monitored Operations:
- File access to sensitive files (`.env`, `.key`, `.pem`, `config.json`)
- Git operations (only push/pull/commit/status allowed)
- Terminal commands and file operations

## ✅ Allowed Operations

### Development Activities:
- ✅ **Code Editing**: Full access to edit source code files
- ✅ **NPM Commands**: `npm install`, `npm run dev`, `npm test`, etc.
- ✅ **Git Version Control**: `git add`, `git commit`, `git push`, `git pull`, `git status`
- ✅ **Pull Requests**: Create and manage pull requests through GitHub interface
- ✅ **Development Servers**: Run local development servers on forwarded ports

### File Operations:
- ✅ **Read Files**: View source code, documentation, configuration files
- ✅ **Edit Files**: Modify existing files and create new files
- ✅ **Directory Navigation**: Browse project structure

## 🔍 Security Monitoring

### Active Monitoring:
- **Command Execution**: All terminal commands are monitored
- **File Access**: Sensitive file access attempts are logged
- **Network Activity**: Outbound connections are restricted
- **Security Events**: All blocked operations are logged with timestamps

### Logging:
- **Location**: `/var/log/pathlink-security.log`
- **Content**: User actions, timestamps, blocked operations
- **Retention**: Logs are maintained for security audit purposes

## 🚨 Security Violations

### Automatic Actions:
1. **Command Blocking**: Restricted commands are immediately blocked
2. **Event Logging**: All violation attempts are logged with user and timestamp
3. **Warning Display**: Clear security warnings are shown to users
4. **Session Monitoring**: Repeated violations may trigger additional restrictions

### Violation Examples:
```bash
# These commands will be BLOCKED:
wget https://example.com/file.zip          # ❌ BLOCKED
curl -O https://api.example.com/data       # ❌ BLOCKED  
git clone https://github.com/user/repo     # ❌ BLOCKED
tar -czf backup.tar.gz ./src               # ❌ BLOCKED
scp file.txt user@server:/path             # ❌ BLOCKED

# These commands are ALLOWED:
git add .                                  # ✅ ALLOWED
git commit -m "Update feature"             # ✅ ALLOWED
npm run dev                                # ✅ ALLOWED
code src/components/App.tsx                # ✅ ALLOWED
```

## 🛡️ Technical Implementation

### Container Security:
- **Restricted User**: Non-root user with limited privileges
- **Removed Utilities**: Download tools removed from container
- **Command Aliases**: Dangerous commands aliased to security warnings
- **File Permissions**: Sensitive files protected with restricted access

### VS Code Restrictions:
- **Extension Limitations**: Only approved extensions installed
- **Command Palette**: Restricted command suggestions
- **File Explorer**: Hidden sensitive files and directories
- **Terminal**: Monitored command execution

### Network Security:
- **Port Forwarding**: Only development ports (3000, 5000, 8888) allowed
- **Outbound Connections**: Restricted to necessary development services
- **Download Prevention**: All download utilities disabled

## 📋 User Guidelines

### DO:
- ✅ Edit source code files to implement features
- ✅ Run development commands (`npm run dev`, `npm test`)
- ✅ Use git for version control (add, commit, push, pull)
- ✅ Create pull requests for code review
- ✅ Test your changes locally using development servers
- ✅ Follow the established development workflow

### DON'T:
- ❌ Attempt to download or copy source code
- ❌ Try to bypass security restrictions
- ❌ Access sensitive configuration files
- ❌ Use file transfer or archiving tools
- ❌ Clone additional repositories
- ❌ Attempt to extract project files

## 🔧 Security Commands

### Check Security Status:
```bash
/usr/local/bin/security-monitor status
```

### View Security Log:
```bash
/usr/local/bin/security-monitor log
```

### Test Command Permissions:
```bash
/usr/local/bin/security-monitor check [command]
```

## 📞 Support & Violations

### For Technical Support:
- Create an issue in the repository
- Contact the project maintainer
- Use the GitHub Discussions feature

### Security Violation Reporting:
- All violations are automatically logged
- Repeated violations will be reviewed by security team
- Serious violations may result in access revocation

### Appeal Process:
- Contact project maintainer with justification
- Provide business case for additional access
- Security team will review and respond within 24 hours

## 🔄 Policy Updates

This security policy may be updated to address new security requirements or threats. Users will be notified of significant changes through:
- Repository notifications
- Codespace environment updates
- Direct communication for critical changes

## ⚖️ Legal Notice

By using this Codespace environment, you agree to:
- Comply with all security restrictions
- Not attempt to bypass or circumvent security measures
- Report any security vulnerabilities discovered
- Use the environment solely for authorized development purposes

Violation of this security policy may result in:
- Immediate access revocation
- Legal action for intellectual property theft
- Termination of development privileges

---

**Last Updated**: January 2025  
**Policy Version**: 2.0  
**Contact**: PathLink Security Team
