{"summary": {"Setup": {"total": 1, "passed": 1, "failed": 0, "warnings": 0}, "Performance": {"total": 5, "passed": 5, "failed": 0, "warnings": 0}, "Security": {"total": 8, "passed": 8, "failed": 0, "warnings": 0}, "Validation": {"total": 5, "passed": 5, "failed": 0, "warnings": 0}, "Error Handling": {"total": 2, "passed": 1, "failed": 1, "warnings": 0}, "Concurrency": {"total": 1, "passed": 1, "failed": 0, "warnings": 0}}, "totalTests": 22, "totalPassed": 21, "totalFailed": 1, "totalWarnings": 0, "overallSuccessRate": 95.45454545454545, "results": [{"category": "Setup", "test": "Authentication", "status": "PASS", "details": "Successfully logged in", "metrics": {}, "timestamp": "2025-06-06T03:10:57.591Z"}, {"category": "Performance", "test": "/api/user Response Time", "status": "PASS", "details": "74ms", "metrics": {"responseTime": 74}, "timestamp": "2025-06-06T03:10:57.698Z"}, {"category": "Performance", "test": "/api/jobs Response Time", "status": "PASS", "details": "86ms", "metrics": {"responseTime": 86}, "timestamp": "2025-06-06T03:10:57.819Z"}, {"category": "Performance", "test": "/api/matches Response Time", "status": "PASS", "details": "84ms", "metrics": {"responseTime": 84}, "timestamp": "2025-06-06T03:10:57.933Z"}, {"category": "Performance", "test": "/api/profile Response Time", "status": "PASS", "details": "91ms", "metrics": {"responseTime": 91}, "timestamp": "2025-06-06T03:10:58.056Z"}, {"category": "Performance", "test": "/api/jobs/browse Response Time", "status": "PASS", "details": "80ms", "metrics": {"responseTime": 80}, "timestamp": "2025-06-06T03:10:58.167Z"}, {"category": "Security", "test": "/api/user Auth Protection", "status": "PASS", "details": "Properly protected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.170Z"}, {"category": "Security", "test": "/api/jobs Auth Protection", "status": "PASS", "details": "Properly protected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.171Z"}, {"category": "Security", "test": "/api/matches Auth Protection", "status": "PASS", "details": "Properly protected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.174Z"}, {"category": "Security", "test": "/api/profile Auth Protection", "status": "PASS", "details": "Properly protected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.176Z"}, {"category": "Security", "test": "SQL Injection Protection", "status": "PASS", "details": "Payload rejected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.297Z"}, {"category": "Security", "test": "SQL Injection Protection", "status": "PASS", "details": "Payload rejected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.429Z"}, {"category": "Security", "test": "SQL Injection Protection", "status": "PASS", "details": "Payload rejected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.547Z"}, {"category": "Security", "test": "SQL Injection Protection", "status": "PASS", "details": "Payload rejected", "metrics": {}, "timestamp": "2025-06-06T03:10:58.672Z"}, {"category": "Validation", "test": "Invalid Email Rejection", "status": "PASS", "details": "Rejected: invalid-email", "metrics": {}, "timestamp": "2025-06-06T03:10:58.792Z"}, {"category": "Validation", "test": "Invalid Email Rejection", "status": "PASS", "details": "Rejected: @domain.com", "metrics": {}, "timestamp": "2025-06-06T03:10:58.933Z"}, {"category": "Validation", "test": "Invalid Email Rejection", "status": "PASS", "details": "Rejected: user@", "metrics": {}, "timestamp": "2025-06-06T03:10:59.050Z"}, {"category": "Validation", "test": "Invalid Email Rejection", "status": "PASS", "details": "Rejected: <EMAIL>", "metrics": {}, "timestamp": "2025-06-06T03:10:59.166Z"}, {"category": "Validation", "test": "Empty Message Rejection", "status": "PASS", "details": "Empty message rejected", "metrics": {}, "timestamp": "2025-06-06T03:10:59.255Z"}, {"category": "Erro<PERSON>", "test": "404 Not Found", "status": "FAIL", "details": "Status: 200", "metrics": {}, "timestamp": "2025-06-06T03:10:59.359Z"}, {"category": "Erro<PERSON>", "test": "Malformed JSON", "status": "PASS", "details": "Malformed JSON rejected", "metrics": {}, "timestamp": "2025-06-06T03:10:59.365Z"}, {"category": "Concurrency", "test": "Simultaneous Requests", "status": "PASS", "details": "10/10 successful, avg: 251ms", "metrics": {}, "timestamp": "2025-06-06T03:10:59.759Z"}], "timestamp": "2025-06-06T03:10:59.760Z"}