option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: pathlink-production-secret-2024
    JWT_SECRET: pathlink-jwt-secret-2024
    OPENAI_API_KEY: ********************************************************************************************************************************************************************
    SENDGRID_API_KEY: SG.1234567890abcdefghijklmnopqrstuvwxyz
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.19.0
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.micro
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
