const MemoryStore = require('memorystore');
const session = require('express-session');

// Configure session store for Netlify Functions
const configureStorage = async () => {
  // Create memory store for sessions
  const MemoryStoreSession = MemoryStore(session);
  const sessionStore = new MemoryStoreSession({
    checkPeriod: 86400000 // prune expired entries every 24h
  });

  // Create a simple storage object for Netlify Functions
  const storage = {
    sessionStore,
    getJobs: async () => [],
    getResumes: async () => [],
    getResumesWithWorkers: async () => []
  };

  return storage;
};

module.exports = { configureStorage };
