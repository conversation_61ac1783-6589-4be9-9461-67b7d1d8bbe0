option_settings:
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.17.0
    GzipCompression: true
  aws:elasticbeanstalk:container:nodejs:staticfiles:
    /: dist
    /assets: dist/assets
    /static: dist/static
  aws:elasticbeanstalk:environment:process:default:
    HealthCheckPath: /health
    Port: 8080
    Protocol: HTTP
  aws:elasticbeanstalk:healthreporting:system:
    SystemType: enhanced
    HealthCheckSuccessThreshold: Ok
    HealthCheckURL: /health

commands:
  01_node_install:
    command: |
      curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
      . ~/.nvm/nvm.sh
      nvm install 18.17.0
      nvm use 18.17.0
      nvm alias default 18.17.0

container_commands:
  01_npm_install:
    command: "npm ci --production"
  02_build_app:
    command: "npm run build"
