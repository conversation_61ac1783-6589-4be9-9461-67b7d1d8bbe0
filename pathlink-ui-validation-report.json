{"summary": {"totalTests": 27, "passedTests": 19, "failedTests": 1, "warningTests": 7, "successRate": 70.37037037037037}, "categories": {"PathLink": {"pass": 1, "fail": 0, "warn": 0}, "Interactive": {"pass": 1, "fail": 0, "warn": 0}, "Landing": {"pass": 0, "fail": 1, "warn": 0}, "Form": {"pass": 2, "fail": 0, "warn": 1}, "Login": {"pass": 1, "fail": 0, "warn": 0}, "Dashboard": {"pass": 1, "fail": 0, "warn": 1}, "Browse": {"pass": 1, "fail": 0, "warn": 1}, "Path": {"pass": 1, "fail": 0, "warn": 1}, "Profile": {"pass": 1, "fail": 0, "warn": 1}, "Contact": {"pass": 2, "fail": 0, "warn": 0}, "Headers": {"pass": 1, "fail": 0, "warn": 0}, "Buttons": {"pass": 1, "fail": 0, "warn": 0}, "Links": {"pass": 1, "fail": 0, "warn": 0}, "Images": {"pass": 1, "fail": 0, "warn": 0}, "Forms": {"pass": 0, "fail": 0, "warn": 1}, "Inputs": {"pass": 0, "fail": 0, "warn": 1}, "Navigation": {"pass": 1, "fail": 0, "warn": 0}, "Responsive": {"pass": 1, "fail": 0, "warn": 0}, "Clickable": {"pass": 1, "fail": 0, "warn": 0}, "Element": {"pass": 1, "fail": 0, "warn": 0}}, "results": [{"test": "PathLink Branding", "status": "PASS", "details": "PathLink branding found", "screenshot": null, "timestamp": "2025-06-06T03:23:22.182Z"}, {"test": "Interactive Elements", "status": "PASS", "details": "Found 15 buttons and 7 links", "screenshot": null, "timestamp": "2025-06-06T03:23:22.190Z"}, {"test": "Landing Page Test", "status": "FAIL", "details": "Error: locator.isVisible: Error: strict mode violation: locator('text=Get Started') resolved to 2 elements:\n    1) <button class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground h-11 rounded-md px-8 bg-[#1C2A42] hover:bg-[#1C2A42]/90\">Get Started</button> aka getByRole('button', { name: 'Get Started', exact: true })\n    2) <button class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 text-primary-foreground h-10 px-4 py-2 mt-6 bg-[#1C2A42] hover:bg-[#1C2A42]/90\">Get Started Today</button> aka getByRole('button', { name: 'Get Started Today' })\n\nCall log:\n\u001b[2m    - checking visibility of locator('text=Get Started')\u001b[22m\n", "screenshot": null, "timestamp": "2025-06-06T03:23:22.213Z"}, {"test": "Form Inputs Found", "status": "PASS", "details": "Email: 0, Password: 1, Text: 0, Total: 2", "screenshot": "screenshots/pathlink-auth-page-1749180206300.png", "timestamp": "2025-06-06T03:23:26.411Z"}, {"test": "Form Buttons", "status": "PASS", "details": "Found 9 buttons (1 submit)", "screenshot": null, "timestamp": "2025-06-06T03:23:26.414Z"}, {"test": "Login Form Content", "status": "PASS", "details": "Login-related content found", "screenshot": null, "timestamp": "2025-06-06T03:23:26.416Z"}, {"test": "Dashboard Route", "status": "WARN", "details": "Redirected to http://localhost:5000/auth", "screenshot": null, "timestamp": "2025-06-06T03:23:29.490Z"}, {"test": "Dashboard Content", "status": "PASS", "details": "Page has content (364 chars)", "screenshot": null, "timestamp": "2025-06-06T03:23:29.498Z"}, {"test": "Browse Jobs Route", "status": "WARN", "details": "Redirected to http://localhost:5000/auth", "screenshot": null, "timestamp": "2025-06-06T03:23:32.566Z"}, {"test": "Browse Jobs Content", "status": "PASS", "details": "Page has content (364 chars)", "screenshot": null, "timestamp": "2025-06-06T03:23:32.574Z"}, {"test": "Path Coach Route", "status": "PASS", "details": "Successfully navigated to /path-coach", "screenshot": null, "timestamp": "2025-06-06T03:23:35.647Z"}, {"test": "Path Coach Content", "status": "WARN", "details": "Page has minimal content", "screenshot": null, "timestamp": "2025-06-06T03:23:35.654Z"}, {"test": "Profile Route", "status": "WARN", "details": "Redirected to http://localhost:5000/auth", "screenshot": null, "timestamp": "2025-06-06T03:23:38.722Z"}, {"test": "Profile Content", "status": "PASS", "details": "Page has content (364 chars)", "screenshot": null, "timestamp": "2025-06-06T03:23:38.729Z"}, {"test": "Contact Route", "status": "PASS", "details": "Successfully navigated to /contact", "screenshot": null, "timestamp": "2025-06-06T03:23:41.809Z"}, {"test": "Contact Content", "status": "PASS", "details": "Page has content (485 chars)", "screenshot": null, "timestamp": "2025-06-06T03:23:41.817Z"}, {"test": "Headers Component", "status": "PASS", "details": "Found 19 headers", "screenshot": null, "timestamp": "2025-06-06T03:23:45.890Z"}, {"test": "Buttons Component", "status": "PASS", "details": "Found 15 buttons", "screenshot": null, "timestamp": "2025-06-06T03:23:45.894Z"}, {"test": "Links Component", "status": "PASS", "details": "Found 7 links", "screenshot": null, "timestamp": "2025-06-06T03:23:45.899Z"}, {"test": "Images Component", "status": "PASS", "details": "Found 3 images", "screenshot": null, "timestamp": "2025-06-06T03:23:45.901Z"}, {"test": "Forms Component", "status": "WARN", "details": "No forms found", "screenshot": null, "timestamp": "2025-06-06T03:23:45.907Z"}, {"test": "Inputs Component", "status": "WARN", "details": "No inputs found", "screenshot": null, "timestamp": "2025-06-06T03:23:45.909Z"}, {"test": "Navigation Component", "status": "PASS", "details": "Found 1 navigation", "screenshot": null, "timestamp": "2025-06-06T03:23:45.914Z"}, {"test": "Responsive Design", "status": "PASS", "details": "Responsive design classes detected", "screenshot": null, "timestamp": "2025-06-06T03:23:45.924Z"}, {"test": "Clickable Elements", "status": "PASS", "details": "Found 22 clickable elements", "screenshot": null, "timestamp": "2025-06-06T03:23:50.024Z"}, {"test": "<PERSON><PERSON>lick Test", "status": "PASS", "details": "Successfully clicked an element", "screenshot": null, "timestamp": "2025-06-06T03:23:52.085Z"}, {"test": "Form Interactions", "status": "WARN", "details": "No forms or inputs found for interaction testing", "screenshot": null, "timestamp": "2025-06-06T03:23:52.093Z"}], "timestamp": "2025-06-06T03:23:52.620Z"}