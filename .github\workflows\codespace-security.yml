name: Codespace Security Check

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ main ]

jobs:
  security-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check for sensitive data
      run: |
        echo "🔍 Checking for sensitive data in source code..."

        # Define directories to exclude from security checks
        EXCLUDE_DIRS="-path ./node_modules -o -path ./.git -o -path ./dist -o -path ./build -o -path ./.netlify -o -path ./aws-deploy"

        # Check for potential OpenAI API keys in source code only (excluding build directories)
        if find . \( $EXCLUDE_DIRS \) -prune -o \( -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" \) -print | xargs grep -l "sk-proj-" 2>/dev/null; then
          echo "❌ Found potential OpenAI API key in source code"
          exit 1
        fi

        # Check for SendGrid API keys in source code only (excluding build directories)
        if find . \( $EXCLUDE_DIRS \) -prune -o \( -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" \) -print | xargs grep -l "SG\.[A-Za-z0-9]" 2>/dev/null; then
          echo "❌ Found potential SendGrid API key in source code"
          exit 1
        fi

        # Check for hardcoded database credentials in source code (excluding build directories and config files)
        if find . \( $EXCLUDE_DIRS \) -prune -o \( -name "*.ts" -o -name "*.js" -o -name "*.tsx" -o -name "*.jsx" \) -print | xargs grep -l "postgresql://.*:.*@.*\..*\..*\..*/" 2>/dev/null; then
          echo "❌ Found potential hardcoded database credentials in source code"
          exit 1
        fi

        # Check for .env files that shouldn't be committed
        if find . -name ".env" -not -path "./.git/*" | head -1 | grep -q "."; then
          echo "❌ Found .env file in repository - this should not be committed"
          exit 1
        fi

        echo "✅ No sensitive data found in source code"
    
    - name: Validate Codespace configuration
      run: |
        echo "🔧 Validating Codespace configuration..."
        
        if [ ! -f ".devcontainer/devcontainer.json" ]; then
          echo "❌ Missing devcontainer.json"
          exit 1
        fi
        
        if [ ! -f ".env.example" ]; then
          echo "❌ Missing .env.example"
          exit 1
        fi
        
        echo "✅ Codespace configuration is valid"
    
    - name: Check PR permissions
      if: github.event_name == 'pull_request'
      run: |
        echo "🔒 Checking PR permissions..."
        echo "PR Author: ${{ github.event.pull_request.user.login }}"
        echo "PR Base: ${{ github.event.pull_request.base.ref }}"
        echo "PR Head: ${{ github.event.pull_request.head.ref }}"
        echo "✅ PR security check complete"
