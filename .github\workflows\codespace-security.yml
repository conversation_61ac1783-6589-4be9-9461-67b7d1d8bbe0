name: Codespace Security Check

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ main ]

jobs:
  security-check:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check for sensitive data
      run: |
        echo "🔍 Checking for sensitive data in commits..."
        
        # Check for potential secrets in code
        if grep -r "sk-proj-" . --exclude-dir=.git --exclude-dir=node_modules; then
          echo "❌ Found potential OpenAI API key in code"
          exit 1
        fi
        
        if grep -r "SG\." . --exclude-dir=.git --exclude-dir=node_modules --exclude="*.example"; then
          echo "❌ Found potential SendGrid API key in code"
          exit 1
        fi
        
        if grep -r "postgresql://.*:.*@" . --exclude-dir=.git --exclude-dir=node_modules --exclude="*.example" --exclude="*.md"; then
          echo "❌ Found potential database credentials in code"
          exit 1
        fi
        
        echo "✅ No sensitive data found in commits"
    
    - name: Validate Codespace configuration
      run: |
        echo "🔧 Validating Codespace configuration..."
        
        if [ ! -f ".devcontainer/devcontainer.json" ]; then
          echo "❌ Missing devcontainer.json"
          exit 1
        fi
        
        if [ ! -f ".env.example" ]; then
          echo "❌ Missing .env.example"
          exit 1
        fi
        
        echo "✅ Codespace configuration is valid"
    
    - name: Check PR permissions
      if: github.event_name == 'pull_request'
      run: |
        echo "🔒 Checking PR permissions..."
        echo "PR Author: ${{ github.event.pull_request.user.login }}"
        echo "PR Base: ${{ github.event.pull_request.base.ref }}"
        echo "PR Head: ${{ github.event.pull_request.head.ref }}"
        echo "✅ PR security check complete"
