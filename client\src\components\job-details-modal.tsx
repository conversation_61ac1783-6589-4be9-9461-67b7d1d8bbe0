import { <PERSON><PERSON>, DialogContent, DialogDescription, <PERSON>alog<PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Briefcase, MapPin, Building, Clock, Calendar, Tag, CheckSquare } from "lucide-react";

interface Job {
  id: number;
  title: string;
  description: string;
  industry: string;
  location: string;
  employer_id: number;
  required_skills?: string;
  minimum_experience?: string;
  preferred_backgrounds?: string;
  work_model?: string;
  availability_needs?: string;
  language_requirements?: string;
  culture_fit_keywords?: string;
  salary_range?: string;
  diversity_goals?: string;
}

interface JobDetailsModalProps {
  open: boolean;
  onClose: () => void;
  job: Job;
}

export default function JobDetailsModal({ open, onClose, job }: JobDetailsModalProps) {
  // Parse skills from comma-separated string
  const skills = job.required_skills ? job.required_skills.split(",").map(s => s.trim()) : [];
  
  // Parse preferred backgrounds
  const backgrounds = job.preferred_backgrounds ? job.preferred_backgrounds.split(",").map(b => b.trim()) : [];
  
  // Parse culture fit keywords
  const cultureKeywords = job.culture_fit_keywords ? job.culture_fit_keywords.split(",").map(k => k.trim()) : [];

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-[#1C2A42]">{job.title}</DialogTitle>
          <DialogDescription className="flex flex-wrap gap-2 items-center">
            <Badge variant="outline" className="flex items-center">
              <Building className="h-3 w-3 mr-1" />
              {job.industry}
            </Badge>
            <Badge variant="outline" className="flex items-center">
              <MapPin className="h-3 w-3 mr-1" />
              {job.location}
            </Badge>
            {job.work_model && (
              <Badge variant="outline" className="flex items-center">
                <Briefcase className="h-3 w-3 mr-1" />
                {job.work_model}
              </Badge>
            )}
            {job.salary_range && (
              <Badge variant="outline" className="flex items-center">
                <Tag className="h-3 w-3 mr-1" />
                {job.salary_range}
              </Badge>
            )}
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <div className="space-y-6">
            {/* Job Description */}
            <div>
              <h3 className="text-lg font-medium mb-2">Job Description</h3>
              <p className="text-gray-700 whitespace-pre-line">{job.description}</p>
            </div>

            <Separator />

            {/* Required Skills */}
            <div>
              <h3 className="text-lg font-medium mb-2">Required Skills</h3>
              {skills.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {skills.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="bg-blue-50 text-blue-700 hover:bg-blue-100">
                      {skill}
                    </Badge>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">No specific skills listed</p>
              )}
            </div>

            <Separator />

            {/* Experience and Background */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-md font-medium mb-2 flex items-center">
                    <Clock className="h-4 w-4 mr-2" />
                    Experience Required
                  </h3>
                  <p className="text-gray-700">{job.minimum_experience || "Not specified"}</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-md font-medium mb-2 flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    Availability
                  </h3>
                  <p className="text-gray-700">{job.availability_needs || "Not specified"}</p>
                </CardContent>
              </Card>
            </div>

            {/* Preferred Backgrounds */}
            {backgrounds.length > 0 && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-2">Preferred Backgrounds</h3>
                  <ul className="list-disc list-inside text-gray-700">
                    {backgrounds.map((background, index) => (
                      <li key={index}>{background}</li>
                    ))}
                  </ul>
                </div>
              </>
            )}

            {/* Language Requirements */}
            {job.language_requirements && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-2">Language Requirements</h3>
                  <p className="text-gray-700">{job.language_requirements}</p>
                </div>
              </>
            )}

            {/* Culture Fit */}
            {cultureKeywords.length > 0 && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-2">Culture Fit</h3>
                  <div className="flex flex-wrap gap-2">
                    {cultureKeywords.map((keyword, index) => (
                      <Badge key={index} variant="outline" className="bg-purple-50 text-purple-700 hover:bg-purple-100">
                        {keyword}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Diversity Goals */}
            {job.diversity_goals && (
              <>
                <Separator />
                <div>
                  <h3 className="text-lg font-medium mb-2">Diversity & Inclusion</h3>
                  <p className="text-gray-700">{job.diversity_goals}</p>
                </div>
              </>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
