// <PERSON><PERSON><PERSON> to create the user_profiles table in the database
import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

async function createUserProfilesTable() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // SQL to create the user_profiles table
    const sql = `
      -- Add user_profiles table
      CREATE TABLE IF NOT EXISTS "user_profiles" (
        "id" serial PRIMARY KEY NOT NULL,
        "user_id" integer NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
        "position" text,
        "location" text,
        "bio" text,
        "company" text,
        "years_of_experience" integer,
        "skills" text,
        "profile_picture" text,
        "pending_email" text,
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL
      );

      -- Add unique constraint on user_id
      CREATE UNIQUE INDEX IF NOT EXISTS "idx_user_profiles_user_id" ON "user_profiles"("user_id");
    `;

    console.log('Creating user_profiles table...');
    await client.query(sql);
    console.log('user_profiles table created successfully');

    // Check if the table was created
    const result = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_profiles'
      );
    `);

    if (result.rows[0].exists) {
      console.log('Verified: user_profiles table exists');
    } else {
      console.error('Failed to create user_profiles table');
    }

  } catch (error) {
    console.error('Error creating user_profiles table:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

createUserProfilesTable();
