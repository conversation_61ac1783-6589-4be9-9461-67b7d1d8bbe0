const { chromium } = require('playwright');
const fs = require('fs');

class EmployerComprehensiveTest {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5000';
  }

  async log(test, status, details = '', screenshot = null) {
    const result = {
      test,
      status,
      details,
      screenshot,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [Employer] ${test}: ${status} ${details}`);
  }

  async setup() {
    console.log('🚀 Setting up comprehensive employer testing...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 1000
    });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1280, height: 720 });
    console.log('✅ Browser ready for comprehensive employer testing');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Comprehensive employer testing complete');
    }
  }

  async takeScreenshot(name) {
    const filename = `screenshots/employer-${name}-${Date.now()}.png`;
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    await this.page.screenshot({ path: filename, fullPage: true });
    return filename;
  }

  async loginAsEmployer() {
    console.log('\n🔐 LOGGING IN AS EMPLOYER...');

    try {
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(3000);

      // Use the correct input selectors based on debug results
      const allInputs = await this.page.locator('input').all();
      
      if (allInputs.length >= 2) {
        // Fill employer credentials
        await allInputs[0].fill('<EMAIL>');
        await allInputs[1].fill('password123');
        
        const screenshot = await this.takeScreenshot('employer-login-form');
        await this.log('Employer Login Form', 'PASS', 'Employer credentials entered', screenshot);
        
        // Click Sign In button
        const signInButton = this.page.locator('button:has-text("Sign In")');
        await signInButton.click();
        
        await this.page.waitForTimeout(4000);
        
        const currentUrl = this.page.url();
        if (currentUrl.includes('/dashboard')) {
          const dashboardScreenshot = await this.takeScreenshot('employer-dashboard');
          await this.log('Employer Login Success', 'PASS', 'Successfully logged in as employer', dashboardScreenshot);
          return true;
        } else {
          await this.log('Employer Login Success', 'FAIL', `Redirected to: ${currentUrl}`);
          return false;
        }
      } else {
        await this.log('Employer Login Form', 'FAIL', 'Insufficient input fields');
        return false;
      }
    } catch (error) {
      await this.log('Employer Login', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  }

  async testEmployerDashboard() {
    console.log('\n📊 TESTING EMPLOYER DASHBOARD...');

    try {
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForTimeout(3000);

      // Check page content
      const pageContent = await this.page.textContent('body');
      
      // Look for employer-specific content
      const employerTerms = ['job', 'candidate', 'applicant', 'hire', 'posting', 'talent', 'recruitment'];
      const foundTerms = employerTerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundTerms.length > 0) {
        await this.log('Employer Dashboard Content', 'PASS', `Found employer terms: ${foundTerms.join(', ')}`);
      } else {
        await this.log('Employer Dashboard Content', 'WARN', 'No employer-specific content detected');
      }

      // Check for interactive elements
      const buttons = await this.page.locator('button').count();
      const links = await this.page.locator('a').count();
      const cards = await this.page.locator('[class*="card"], .card, [class*="widget"]').count();

      await this.log('Dashboard Interactive Elements', 'PASS', `${buttons} buttons, ${links} links, ${cards} cards`);

      // Look for specific employer actions
      const employerActions = [
        'Post Job',
        'Create Job',
        'View Applications',
        'Manage Jobs',
        'Find Candidates',
        'View Matches'
      ];

      let actionsFound = 0;
      for (const action of employerActions) {
        const element = this.page.locator(`text=${action}`);
        if (await element.isVisible()) {
          actionsFound++;
          await this.log('Employer Action Available', 'PASS', `Found: ${action}`);
        }
      }

      if (actionsFound === 0) {
        await this.log('Employer Actions', 'WARN', 'No specific employer actions found');
      }

      const screenshot = await this.takeScreenshot('dashboard-full');
      await this.log('Dashboard Screenshot', 'PASS', 'Dashboard screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Dashboard Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testJobManagement() {
    console.log('\n💼 TESTING JOB MANAGEMENT FEATURES...');

    try {
      // Check current page for job-related content
      const pageContent = await this.page.textContent('body');
      
      // Look for existing jobs
      if (pageContent.includes('Frontend Developer') || pageContent.includes('job')) {
        await this.log('Job Listings Present', 'PASS', 'Job content found on dashboard');
      } else {
        await this.log('Job Listings Present', 'WARN', 'No job content visible');
      }

      // Test job creation/management buttons
      const jobButtons = [
        'Post Job',
        'Create Job',
        'Add Job',
        'New Job',
        'Manage Jobs'
      ];

      let jobButtonFound = false;
      for (const buttonText of jobButtons) {
        const button = this.page.locator(`text=${buttonText}`);
        if (await button.isVisible()) {
          await this.log('Job Management Button', 'PASS', `Found: ${buttonText}`);
          jobButtonFound = true;
          
          // Try clicking the button
          try {
            await button.click();
            await this.page.waitForTimeout(2000);
            await this.log('Job Button Interaction', 'PASS', `Successfully clicked ${buttonText}`);
            
            // Take screenshot after clicking
            await this.takeScreenshot(`job-${buttonText.toLowerCase().replace(' ', '-')}`);
          } catch (clickError) {
            await this.log('Job Button Interaction', 'WARN', `Could not interact with ${buttonText}`);
          }
          break;
        }
      }

      if (!jobButtonFound) {
        await this.log('Job Management Buttons', 'WARN', 'No job management buttons found');
      }

      const screenshot = await this.takeScreenshot('job-management');
      await this.log('Job Management Screenshot', 'PASS', 'Job management screenshot captured', screenshot);

    } catch (error) {
      await this.log('Job Management Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testCandidateViewing() {
    console.log('\n👥 TESTING CANDIDATE/APPLICANT VIEWING...');

    try {
      // Look for candidate/applicant related content
      const pageContent = await this.page.textContent('body');
      
      const candidateTerms = ['applicant', 'candidate', 'resume', 'application', 'contact', 'Peter Dimian'];
      const foundCandidateTerms = candidateTerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundCandidateTerms.length > 0) {
        await this.log('Candidate Content', 'PASS', `Found candidate terms: ${foundCandidateTerms.join(', ')}`);
      } else {
        await this.log('Candidate Content', 'WARN', 'No candidate-related content found');
      }

      // Look for candidate management buttons
      const candidateButtons = [
        'View Candidates',
        'View Applications',
        'Applicants',
        'Applications',
        'Contact Candidate',
        'View Resume'
      ];

      let candidateButtonFound = false;
      for (const buttonText of candidateButtons) {
        const button = this.page.locator(`text=${buttonText}`);
        if (await button.isVisible()) {
          await this.log('Candidate Management Button', 'PASS', `Found: ${buttonText}`);
          candidateButtonFound = true;
          
          // Try clicking the button
          try {
            await button.click();
            await this.page.waitForTimeout(2000);
            await this.log('Candidate Button Interaction', 'PASS', `Successfully clicked ${buttonText}`);
          } catch (clickError) {
            await this.log('Candidate Button Interaction', 'WARN', `Could not interact with ${buttonText}`);
          }
          break;
        }
      }

      if (!candidateButtonFound) {
        await this.log('Candidate Management Buttons', 'WARN', 'No candidate management buttons found');
      }

      const screenshot = await this.takeScreenshot('candidate-management');
      await this.log('Candidate Management Screenshot', 'PASS', 'Candidate management screenshot captured', screenshot);

    } catch (error) {
      await this.log('Candidate Viewing Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerMatches() {
    console.log('\n🎯 TESTING EMPLOYER MATCHING FEATURES...');

    try {
      // Test matches page
      await this.page.goto(`${this.baseUrl}/view-matches`);
      await this.page.waitForTimeout(3000);

      const pageContent = await this.page.textContent('body');
      
      // Look for matching-related content
      const matchTerms = ['match', 'recommend', 'suitable', 'compatible', 'score', 'rating'];
      const foundMatchTerms = matchTerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundMatchTerms.length > 0) {
        await this.log('Employer Matches Content', 'PASS', `Found match terms: ${foundMatchTerms.join(', ')}`);
      } else {
        await this.log('Employer Matches Content', 'WARN', 'No matching content detected');
      }

      // Check for match-related buttons
      const matchButtons = [
        'View Matches',
        'See Recommendations',
        'Find Candidates',
        'Match Score',
        'View Details'
      ];

      for (const buttonText of matchButtons) {
        const button = this.page.locator(`text=${buttonText}`);
        if (await button.isVisible()) {
          await this.log('Match Feature Button', 'PASS', `Found: ${buttonText}`);
        }
      }

      const screenshot = await this.takeScreenshot('employer-matches');
      await this.log('Employer Matches Screenshot', 'PASS', 'Employer matches screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Matches Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerAICoach() {
    console.log('\n🤖 TESTING EMPLOYER AI COACH...');

    try {
      await this.page.goto(`${this.baseUrl}/path-coach`);
      await this.page.waitForTimeout(4000);

      const pageContent = await this.page.textContent('body');
      
      // Look for employer-specific AI content
      const employerAITerms = ['hiring', 'recruitment', 'talent', 'interview', 'candidate', 'employer'];
      const foundAITerms = employerAITerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundAITerms.length > 0) {
        await this.log('Employer AI Coach Content', 'PASS', `Found employer AI terms: ${foundAITerms.join(', ')}`);
      } else {
        await this.log('Employer AI Coach Content', 'WARN', 'No employer-specific AI content detected');
      }

      // Test chat interface
      const chatInput = this.page.locator('input[placeholder*="Ask"], textarea[placeholder*="Ask"], input[placeholder*="message"]');
      
      if (await chatInput.isVisible()) {
        await this.log('AI Chat Interface', 'PASS', 'Chat interface available for employers');
        
        // Test sending an employer-specific message
        await chatInput.fill('How can I improve my hiring process and find better candidates?');
        
        const sendButton = this.page.locator('button:has-text("Send"), button[type="submit"]').first();
        if (await sendButton.isVisible()) {
          await sendButton.click();
          await this.page.waitForTimeout(5000);
          await this.log('Employer AI Chat Test', 'PASS', 'Successfully sent employer query to AI');
          
          // Check for AI response
          const updatedContent = await this.page.textContent('body');
          if (updatedContent.length > pageContent.length) {
            await this.log('AI Response Received', 'PASS', 'AI responded to employer query');
          } else {
            await this.log('AI Response Received', 'WARN', 'No clear AI response detected');
          }
        }
      } else {
        await this.log('AI Chat Interface', 'FAIL', 'Chat interface not found');
      }

      const screenshot = await this.takeScreenshot('employer-ai-coach');
      await this.log('Employer AI Coach Screenshot', 'PASS', 'Employer AI Coach screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer AI Coach Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerProfile() {
    console.log('\n👤 TESTING EMPLOYER PROFILE...');

    try {
      await this.page.goto(`${this.baseUrl}/profile`);
      await this.page.waitForTimeout(3000);

      // Check for profile form
      const inputs = await this.page.locator('input, textarea, select').count();
      
      if (inputs > 0) {
        await this.log('Employer Profile Form', 'PASS', `Found ${inputs} form fields`);
        
        // Check for employer-specific content
        const pageContent = await this.page.textContent('body');
        const employerFields = ['company', 'organization', 'business', 'industry', 'position', 'location'];
        const foundFields = employerFields.filter(field => 
          pageContent.toLowerCase().includes(field.toLowerCase())
        );

        if (foundFields.length > 0) {
          await this.log('Employer Profile Fields', 'PASS', `Found employer fields: ${foundFields.join(', ')}`);
        } else {
          await this.log('Employer Profile Fields', 'WARN', 'No employer-specific fields clearly identified');
        }

        // Test profile editing
        const firstInput = this.page.locator('input').first();
        if (await firstInput.isVisible()) {
          await firstInput.fill('Test Employer Company');
          await this.log('Profile Editing', 'PASS', 'Profile form is editable');
        }
      } else {
        await this.log('Employer Profile Form', 'FAIL', 'No profile form found');
      }

      const screenshot = await this.takeScreenshot('employer-profile');
      await this.log('Employer Profile Screenshot', 'PASS', 'Employer profile screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Profile Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async runComprehensiveEmployerTests() {
    console.log('🚀 STARTING COMPREHENSIVE EMPLOYER UI TESTING...\n');
    console.log('This will thoroughly test all employer features in PathLink.\n');

    try {
      await this.setup();

      // Login as employer
      const loginSuccess = await this.loginAsEmployer();
      
      if (loginSuccess) {
        await this.testEmployerDashboard();
        await this.testJobManagement();
        await this.testCandidateViewing();
        await this.testEmployerMatches();
        await this.testEmployerAICoach();
        await this.testEmployerProfile();
      } else {
        console.log('❌ Could not login as employer - cannot test employer features');
      }

    } catch (error) {
      console.error('Comprehensive employer testing failed:', error);
    } finally {
      await this.cleanup();
    }

    // Generate comprehensive report
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const warningTests = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 COMPREHENSIVE EMPLOYER UI TEST SUMMARY:');
    console.log('='.repeat(70));
    console.log(`✅ PASSED: ${passedTests}`);
    console.log(`❌ FAILED: ${failedTests}`);
    console.log(`⚠️  WARNINGS: ${warningTests}`);
    console.log(`📈 EMPLOYER UI SUCCESS RATE: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('='.repeat(70));

    // Categorize results
    const categories = {};
    this.results.forEach(result => {
      const category = result.test.split(' ')[0];
      if (!categories[category]) {
        categories[category] = { pass: 0, fail: 0, warn: 0 };
      }
      categories[category][result.status.toLowerCase()]++;
    });

    console.log('\n📋 EMPLOYER FEATURES DETAILED BREAKDOWN:');
    Object.entries(categories).forEach(([category, stats]) => {
      const total = stats.pass + stats.fail + stats.warn;
      const successRate = ((stats.pass / total) * 100).toFixed(1);
      console.log(`${category}: ${stats.pass}/${total} (${successRate}%)`);
    });

    // Save detailed report
    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        warningTests,
        successRate: (passedTests / totalTests) * 100
      },
      categories,
      results: this.results,
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('employer-comprehensive-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed employer test report saved to employer-comprehensive-test-report.json');
    console.log('📸 All employer screenshots saved in the screenshots/ directory');

    // Final assessment
    console.log('\n🎯 EMPLOYER FEATURES ASSESSMENT:');
    if (passedTests / totalTests >= 0.8) {
      console.log('   ✅ Employer features are working excellently!');
      console.log('   ✅ All major employer functionality is operational');
      console.log('   ✅ Ready for employer user testing and production');
      console.log('   ✅ Employers can effectively manage jobs and candidates');
    } else if (passedTests / totalTests >= 0.6) {
      console.log('   ⚠️  Employer features are mostly functional');
      console.log('   🔧 Some employer features may need minor improvements');
      console.log('   📈 Consider enhancing employer-specific workflows');
      console.log('   ✅ Core employer functionality is working');
    } else {
      console.log('   ❌ Employer features need significant attention');
      console.log('   🔧 Critical employer functionality issues detected');
      console.log('   📋 Review failed tests for immediate improvements');
    }

    return report;
  }
}

// Run comprehensive employer tests
const tester = new EmployerComprehensiveTest();
tester.runComprehensiveEmployerTests().catch(console.error);
