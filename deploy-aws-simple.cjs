#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 PathLink AWS Deployment (Simple)');
console.log('===================================\n');

// AWS CLI and EB CLI paths
const AWS_CLI = '"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe"';
const EB_CLI = 'eb'; // Should be in PATH after pip install

function setupDeploymentFiles() {
  console.log('📁 Setting up deployment files...');
  
  // Create .ebextensions directory
  if (!fs.existsSync('.ebextensions')) {
    fs.mkdirSync('.ebextensions');
  }

  // Environment configuration
  const envConfig = `option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: pathlink-production-secret-2024
    JWT_SECRET: pathlink-jwt-secret-2024
    OPENAI_API_KEY: ********************************************************************************************************************************************************************
    SENDGRID_API_KEY: SG.1234567890abcdefghijklmnopqrstuvwxyz
  aws:elasticbeanstalk:container:nodejs:
    NodeCommand: "npm start"
    NodeVersion: 18.19.0
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.micro
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
`;

  fs.writeFileSync('.ebextensions/environment.config', envConfig);
  
  // Procfile
  fs.writeFileSync('Procfile', 'web: npm start');
  
  // .ebignore
  const ebignoreContent = `node_modules/
.git/
.env
*.log
.DS_Store
screenshots/
test-*.json
*-test-report.json
.vscode/
.idea/
*.md
`;
  fs.writeFileSync('.ebignore', ebignoreContent);
  
  // Update package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (!packageJson.engines) packageJson.engines = {};
  packageJson.engines.node = '18.x';
  packageJson.engines.npm = '9.x';
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  
  console.log('✅ Deployment files configured');
}

function buildApplication() {
  console.log('\n🔨 Building application...');
  try {
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

function testAWSConnection() {
  console.log('\n🧪 Testing AWS connection...');
  try {
    const result = execSync(`${AWS_CLI} sts get-caller-identity`, { encoding: 'utf8' });
    const identity = JSON.parse(result);
    console.log('✅ AWS connection successful');
    console.log(`   Account: ${identity.Account}`);
    console.log(`   User: ${identity.Arn}`);
    return true;
  } catch (error) {
    console.log('❌ AWS connection failed:', error.message);
    return false;
  }
}

function deployToElasticBeanstalk() {
  console.log('\n🌐 Deploying to AWS Elastic Beanstalk...');
  
  try {
    // Initialize EB if not already done
    if (!fs.existsSync('.elasticbeanstalk')) {
      console.log('Initializing Elastic Beanstalk...');
      execSync(`${EB_CLI} init pathlink --region us-east-1 --platform "Node.js 18 running on 64bit Amazon Linux 2"`, { 
        stdio: 'inherit' 
      });
    }
    
    // Check if environment exists
    let environmentExists = false;
    try {
      const status = execSync(`${EB_CLI} status`, { stdio: 'pipe' }).toString();
      environmentExists = status.includes('Environment details');
      console.log('✅ Environment already exists');
    } catch (e) {
      console.log('Creating new environment...');
    }
    
    if (!environmentExists) {
      console.log('Creating environment...');
      execSync(`${EB_CLI} create pathlink-production --instance-type t3.micro --region us-east-1`, { 
        stdio: 'inherit' 
      });
    }
    
    // Deploy
    console.log('Deploying application...');
    execSync(`${EB_CLI} deploy`, { stdio: 'inherit' });
    
    // Get application URL
    const status = execSync(`${EB_CLI} status`, { stdio: 'pipe' }).toString();
    const urlMatch = status.match(/CNAME:\s*(.+)/);
    
    if (urlMatch) {
      const appUrl = urlMatch[1].trim();
      console.log(`\n✅ Deployment successful!`);
      console.log(`🌍 Application URL: https://${appUrl}`);
      
      // Update Netlify configuration
      updateNetlifyConfig(appUrl);
      
      return appUrl;
    }
    
    return null;
  } catch (error) {
    console.log('❌ Deployment failed:', error.message);
    console.log('\nTrying to get more information...');
    
    try {
      console.log('\n📋 EB Status:');
      execSync(`${EB_CLI} status`, { stdio: 'inherit' });
    } catch (statusError) {
      console.log('Could not get EB status');
    }
    
    try {
      console.log('\n📋 EB Logs:');
      execSync(`${EB_CLI} logs`, { stdio: 'inherit' });
    } catch (logsError) {
      console.log('Could not get EB logs');
    }
    
    return null;
  }
}

function updateNetlifyConfig(awsUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace any existing AWS URLs
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/[a-zA-Z0-9-]+\.us-east-1\.elasticbeanstalk\.com/g,
      `https://${awsUrl}`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated');
    console.log(`🔗 API calls will redirect to: https://${awsUrl}`);
    
    // Show updated redirects
    console.log('\n📋 Updated redirects:');
    console.log(`   /api/* → https://${awsUrl}/api/*`);
    console.log(`   /upload/* → https://${awsUrl}/upload/*`);
    console.log(`   /health → https://${awsUrl}/health`);
    
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log(`Please manually update netlify.toml with: https://${awsUrl}`);
  }
}

function testDeployment(appUrl) {
  console.log('\n🧪 Testing deployment...');
  console.log(`🔍 Health check: https://${appUrl}/health`);
  console.log(`🌐 Application: https://${appUrl}`);
  console.log(`🔌 API test: https://${appUrl}/api/health`);
}

async function main() {
  console.log('Starting PathLink AWS deployment...\n');
  
  // Step 1: Test AWS connection
  if (!testAWSConnection()) {
    console.log('\n❌ AWS connection failed. Please check credentials.');
    process.exit(1);
  }
  
  // Step 2: Setup deployment files
  setupDeploymentFiles();
  
  // Step 3: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed.');
    process.exit(1);
  }
  
  // Step 4: Deploy to AWS
  const appUrl = deployToElasticBeanstalk();
  if (!appUrl) {
    console.log('\n❌ Deployment failed.');
    process.exit(1);
  }
  
  // Step 5: Test deployment
  testDeployment(appUrl);
  
  console.log('\n🎉 DEPLOYMENT COMPLETE!');
  console.log('======================');
  console.log('✅ Backend deployed to AWS Elastic Beanstalk');
  console.log('✅ Netlify configuration updated');
  console.log('✅ API redirects configured');
  console.log('\n📋 Next Steps:');
  console.log('1. Deploy frontend to Netlify (git push)');
  console.log('2. Test the complete application');
  console.log('3. Configure custom domain (optional)');
  console.log('\n🔧 Useful Commands:');
  console.log('- eb status: Check application status');
  console.log('- eb logs: View application logs');
  console.log('- eb deploy: Deploy updates');
  console.log(`\n🌍 Your application: https://${appUrl}`);
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
