/**
 * This script tests the connection to Supabase PostgreSQL database
 * using the direct connection string from the .env file.
 */

import pg from 'pg';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

// Load environment variables
dotenv.config();

const { Pool } = pg;

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testSupabaseConnection() {
  console.log('Testing Supabase PostgreSQL connection...');
  
  if (!process.env.DATABASE_URL) {
    console.error('ERROR: DATABASE_URL environment variable is not set.');
    process.exit(1);
  }
  
  console.log('Using connection string:', process.env.DATABASE_URL.replace(/:[^:]*@/, ':****@'));
  
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false // Required for Supabase PostgreSQL
    }
  });
  
  try {
    console.log('Connecting to database...');
    const client = await pool.connect();
    console.log('Connected to database successfully!');
    
    // Test a simple query
    console.log('Running test query...');
    const result = await client.query('SELECT NOW() as current_time');
    console.log('Database time:', result.rows[0].current_time);
    
    // List tables in the database
    console.log('Listing database tables...');
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `);
    
    if (tablesResult.rows.length === 0) {
      console.log('No tables found in the database.');
    } else {
      console.log('Tables in the database:');
      tablesResult.rows.forEach((row, index) => {
        console.log(`  ${index + 1}. ${row.table_name}`);
      });
    }
    
    client.release();
    await pool.end();
    
    console.log('Database connection test completed successfully!');
    return true;
  } catch (error) {
    console.error('Error connecting to Supabase PostgreSQL:', error.message);
    if (error.message.includes('password authentication failed')) {
      console.error('HINT: Check your database username and password in the DATABASE_URL');
    } else if (error.message.includes('connect ETIMEDOUT')) {
      console.error('HINT: Check your network connection or database host address');
    } else if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.error('HINT: Check if the database name in your connection string exists');
    }
    
    try {
      await pool.end();
    } catch (e) {
      // Ignore errors when closing the pool
    }
    
    return false;
  }
}

// Run the test
testSupabaseConnection().then(success => {
  if (success) {
    console.log('✅ Supabase PostgreSQL connection test passed!');
    process.exit(0);
  } else {
    console.error('❌ Supabase PostgreSQL connection test failed!');
    process.exit(1);
  }
}).catch(error => {
  console.error('Unexpected error:', error);
  process.exit(1);
});
