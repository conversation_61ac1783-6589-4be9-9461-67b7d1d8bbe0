-- Create admin user <NAME_EMAIL>
-- The password is 'password' hashed with bcrypt
INSERT INTO users (email, password, name, role, is_verified, original_role, created_at)
VALUES (
    '<EMAIL>', 
    '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 
    'System Administrator', 
    'admin',
    TRUE,
    'admin',
    NOW()
)
ON CONFLICT (email) DO UPDATE SET
    password = '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu',
    name = 'System Administrator',
    role = 'admin',
    is_verified = TRUE,
    original_role = 'admin';

-- Add some approved email domains if they don't exist
INSERT INTO approved_domains (domain, description, is_active, created_at)
VALUES 
    ('google.com', 'Google Inc.', TRUE, NOW()),
    ('microsoft.com', 'Microsoft Corporation', TRUE, NOW()),
    ('apple.com', 'Apple Inc.', TRUE, NOW()),
    ('amazon.com', 'Amazon', TRUE, NOW()),
    ('meta.com', 'Meta (Facebook)', TRUE, NOW()),
    ('ibm.com', 'IBM', TRUE, NOW()),
    ('intel.com', 'Intel Corporation', TRUE, NOW()),
    ('cisco.com', 'Cisco Systems', TRUE, NOW()),
    ('oracle.com', 'Oracle Corporation', TRUE, NOW()),
    ('salesforce.com', 'Salesforce', TRUE, NOW()),
    ('pathlink.com', 'PathLink', TRUE, NOW())
ON CONFLICT (domain) DO NOTHING;
