import { Pool } from 'pg';

const connectionString = 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

async function verifyReapply() {
  const pool = new Pool({ connectionString });
  try {
    console.log('=== VERIFYING REAPPLY FUNCTIONALITY ===');
    
    // Check the current status of match 28
    const result = await pool.query('SELECT * FROM matches WHERE id = $1', [28]);
    
    if (result.rows.length > 0) {
      const match = result.rows[0];
      console.log('Match 28 current status:', {
        id: match.id,
        worker_id: match.worker_id,
        job_id: match.job_id,
        status: match.status,
        match_score: match.match_score,
        match_date: match.match_date,
        created_at: match.created_at,
        updated_at: match.updated_at
      });
      
      if (match.status === 'pending') {
        console.log('✅ REAPPLY SUCCESS: Match status changed from withdrawn to pending');
      } else if (match.status === 'withdrawn') {
        console.log('❌ REAPPLY NOT COMPLETED: Match is still withdrawn');
      } else {
        console.log(`ℹ️  MATCH STATUS: ${match.status}`);
      }
    } else {
      console.log('❌ Match 28 not found');
    }
    
  } catch (error) {
    console.error('Verification failed:', error.message);
  } finally {
    await pool.end();
  }
}

verifyReapply();
