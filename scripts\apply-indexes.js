import { Client } from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function applyIndexes() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '..', 'drizzle', '0001_add_indexes.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Applying indexes...');
    await client.query(sql);
    console.log('Indexes applied successfully');

  } catch (error) {
    console.error('Error applying indexes:', error);
  } finally {
    await client.end();
  }
}

applyIndexes().catch(console.error);
