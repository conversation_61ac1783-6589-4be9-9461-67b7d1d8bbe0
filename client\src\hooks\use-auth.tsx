import { createContext, ReactNode, useContext, useEffect } from "react";
import { useLocation } from "wouter";
import {
  useQuery,
  useMutation,
  UseMutationResult,
} from "@tanstack/react-query";
import { insertUserSchema, User as SelectUser } from "@shared/schema";
import { getQueryFn, apiRequest, queryClient } from "../lib/queryClient";
import { useToast } from "@/hooks/use-toast";

type AuthUser = Omit<SelectUser, "password">;

type AuthContextType = {
  user: AuthUser | null;
  isLoading: boolean;
  error: Error | null;
  loginMutation: UseMutationResult<AuthUser, Error, LoginData>;
  logoutMutation: UseMutationResult<void, Error, void>;
  registerMutation: UseMutationResult<AuthUser, Error, RegisterData>;
};

type LoginData = {
  email: string;
  password: string;
};

type RegisterData = {
  name: string;
  email: string;
  password: string;
  role: "worker" | "employer";
};

export const AuthContext = createContext<AuthContextType | null>(null);
export function AuthProvider({ children }: { children: ReactNode }) {
  const { toast } = useToast();
  const [_, navigate] = useLocation();

  const {
    data: user,
    error,
    isLoading,
    refetch,
  } = useQuery<AuthUser | undefined, Error>({
    queryKey: ["/api/user"],
    queryFn: getQueryFn({ on401: "returnNull" }),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const loginMutation = useMutation({
    mutationFn: async (credentials: LoginData) => {
      try {
        console.log("Logging in with:", credentials);
        const res = await apiRequest("POST", "/api/login", credentials);

        if (!res.ok) {
          const text = await res.text();
          let errorMessage;
          try {
            const errorData = JSON.parse(text);
            errorMessage = errorData.message || "Login failed";
          } catch {
            errorMessage = text || "Login failed";
          }
          throw new Error(errorMessage);
        }

        return await res.json();
      } catch (error: any) {
        console.error("Login error:", error);
        throw error;
      }
    },
    onSuccess: (userData: any) => {
      console.log("Login successful:", userData);

      // Store JWT token if provided
      if (userData.token) {
        localStorage.setItem('authToken', userData.token);
        console.log("JWT token stored in localStorage");
        // Remove token from userData before storing in query cache
        const { token, ...userDataWithoutToken } = userData;
        queryClient.setQueryData(["/api/user"], userDataWithoutToken);
      } else {
        queryClient.setQueryData(["/api/user"], userData);
      }

      toast({
        title: "Login successful",
        description: `Welcome back, ${userData.name}!`,
      });
      navigate("/dashboard");
    },
    onError: (error: Error) => {
      console.error("Login error in mutation:", error);
      toast({
        title: "Login failed",
        description: error.message || "Invalid email or password",
        variant: "destructive",
      });
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (data: RegisterData) => {
      try {
        console.log("Registering with:", data);
        const res = await apiRequest("POST", "/api/register", data);

        if (!res.ok) {
          const text = await res.text();
          let errorMessage;
          try {
            const errorData = JSON.parse(text);
            errorMessage = errorData.message || "Registration failed";
          } catch {
            errorMessage = text || "Registration failed";
          }
          throw new Error(errorMessage);
        }

        return await res.json();
      } catch (error: any) {
        console.error("Registration error:", error);
        throw error;
      }
    },
    onSuccess: (user: AuthUser) => {
      console.log("Registration successful:", user);
      queryClient.setQueryData(["/api/user"], user);
      toast({
        title: "Registration successful",
        description: `Welcome to PathLink, ${user.name}!`,
      });
      navigate("/dashboard");
    },
    onError: (error: Error) => {
      console.error("Registration error in mutation:", error);
      toast({
        title: "Registration failed",
        description: error.message || "Could not create account",
        variant: "destructive",
      });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("POST", "/api/logout");
    },
    onSuccess: () => {
      // Clear JWT token from localStorage
      localStorage.removeItem('authToken');
      console.log("JWT token removed from localStorage");

      queryClient.setQueryData(["/api/user"], null);
      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
      navigate("/auth");
    },
    onError: (error: Error) => {
      toast({
        title: "Logout failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return (
    <AuthContext.Provider
      value={{
        user: user ?? null,
        isLoading,
        error,
        loginMutation,
        logoutMutation,
        registerMutation,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
