import { useState, useRef } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload, Camera, Trash2 } from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface ProfilePictureUploadProps {
  profilePicture: string;
  name: string;
  onPictureUpdate: (url: string) => void;
}

export function ProfilePictureUpload({ 
  profilePicture, 
  name, 
  onPictureUpdate 
}: ProfilePictureUploadProps) {
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please upload an image file (JPEG, PNG, etc.)",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Profile picture must be less than 5MB",
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('profilePicture', file);

      const response = await apiRequest("POST", "/api/profile/picture", formData, {
        headers: {
          // Don't set Content-Type here, it will be set automatically with the boundary
        },
      });

      if (!response.ok) {
        throw new Error("Failed to upload profile picture");
      }

      const data = await response.json();
      onPictureUpdate(data.profilePicture);

      toast({
        title: "Profile Picture Updated",
        description: "Your profile picture has been successfully updated.",
      });
    } catch (error) {
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "An error occurred while uploading your profile picture.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemovePicture = async () => {
    if (!profilePicture) return;
    
    setIsUploading(true);
    
    try {
      // Call API to remove profile picture
      const response = await apiRequest("DELETE", "/api/profile/picture");
      
      if (!response.ok) {
        throw new Error("Failed to remove profile picture");
      }
      
      onPictureUpdate("");
      
      toast({
        title: "Profile Picture Removed",
        description: "Your profile picture has been removed.",
      });
    } catch (error) {
      toast({
        title: "Removal Failed",
        description: error instanceof Error ? error.message : "An error occurred while removing your profile picture.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="relative group">
        <Avatar className="h-24 w-24 border-2 border-primary/10">
          <AvatarImage src={profilePicture || ""} alt={name} />
          <AvatarFallback className="text-lg font-medium">
            {getInitials(name)}
          </AvatarFallback>
        </Avatar>
        
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex gap-2">
            <Button 
              size="icon" 
              variant="ghost" 
              className="h-8 w-8 rounded-full bg-white/20 hover:bg-white/30 text-white"
              onClick={triggerFileInput}
              disabled={isUploading}
            >
              <Camera className="h-4 w-4" />
            </Button>
            
            {profilePicture && (
              <Button 
                size="icon" 
                variant="ghost" 
                className="h-8 w-8 rounded-full bg-white/20 hover:bg-white/30 text-white"
                onClick={handleRemovePicture}
                disabled={isUploading}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
        
        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full">
            <div className="h-5 w-5 animate-spin rounded-full border-2 border-white border-t-transparent" />
          </div>
        )}
      </div>
      
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />
      
      <Button 
        variant="outline" 
        size="sm"
        className="flex items-center gap-2"
        onClick={triggerFileInput}
        disabled={isUploading}
      >
        <Upload className="h-4 w-4" />
        Change Picture
      </Button>
    </div>
  );
}
