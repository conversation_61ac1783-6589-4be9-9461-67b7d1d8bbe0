import React, { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { useAuth } from "@/hooks/use-auth";
import { useTheme } from "@/components/ui/theme-provider";
import { Logo } from "@/components/logo";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Moon, Sun, BarChart2, Lightbulb, Shield, LayoutDashboard,
  User, LogOut, Settings, Menu, ChevronDown, Info, Phone,
  CreditCard, Home, HelpCircle, Target, TrendingUp, Briefcase, Bot
} from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { motion } from "framer-motion";
import { apiRequest } from "@/lib/queryClient";

interface SharedNavbarProps {
  transparent?: boolean;
}

export const SharedNavbar: React.FC<SharedNavbarProps> = ({ transparent = false }) => {
  const { user, logoutMutation } = useAuth();
  const [location] = useLocation();
  const { theme, setTheme } = useTheme();
  const [profilePicture, setProfilePicture] = useState<string | null>(null);

  // Fetch user profile to get profile picture
  useEffect(() => {
    if (user) {
      fetchProfilePicture();
    }
  }, [user]);

  const fetchProfilePicture = async () => {
    try {
      const response = await apiRequest("GET", "/api/profile");
      if (response.ok) {
        const data = await response.json();
        if (data.profilePicture) {
          setProfilePicture(data.profilePicture);
        }
      }
    } catch (error) {
      console.error("Error fetching profile picture:", error);
    }
  };

  const handleLogout = async () => {
    await logoutMutation.mutateAsync();
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const isActive = (path: string) => location === path;

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      ?.split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2) || "U";
  };

  return (
    <motion.nav
      className={`${transparent ? 'bg-transparent' : 'bg-[#1C2A42] dark:bg-gray-800'} text-white p-4 shadow-md z-50`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 100, damping: 15 }}
    >
      <div className="container mx-auto flex justify-between items-center">
        <div className="flex items-center">
          <Link href="/">
            <div className="flex items-center cursor-pointer">
              <Logo className="h-14 w-auto" />
            </div>
          </Link>
        </div>

        <div className="flex items-center">
          {/* Mobile Menu Button - Only visible on mobile */}
          <div className="md:hidden mr-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-white">
                  <Menu className="h-6 w-6" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-56">
                <DropdownMenuItem asChild>
                  <Link href="/">
                    <div className="flex items-center w-full">
                      <Home className="mr-2 h-4 w-4" />
                      <span>Home</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                {user && (
                  <>
                    <DropdownMenuItem asChild>
                      <Link href="/dashboard">
                        <div className="flex items-center w-full">
                          <LayoutDashboard className="mr-2 h-4 w-4" />
                          <span>Dashboard</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/pathcoach">
                        <div className="flex items-center w-full">
                          <Lightbulb className="mr-2 h-4 w-4" />
                          <span>PathCoach</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/ai-coach">
                        <div className="flex items-center w-full">
                          <Bot className="mr-2 h-4 w-4" />
                          <span>AI Coach</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/browse-jobs">
                        <div className="flex items-center w-full">
                          <Briefcase className="mr-2 h-4 w-4" />
                          <span>Browse Jobs</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/matches">
                        <div className="flex items-center w-full">
                          <Target className="mr-2 h-4 w-4" />
                          <span>Match Analysis</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    {user?.role === 'employer' && (
                      <DropdownMenuItem asChild>
                        <Link href="/statistics">
                          <div className="flex items-center w-full">
                            <TrendingUp className="mr-2 h-4 w-4" />
                            <span>Job Statistics</span>
                          </div>
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem asChild>
                      <Link href="/analytics">
                        <div className="flex items-center w-full">
                          <BarChart2 className="mr-2 h-4 w-4" />
                          <span>Analytics</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    {user?.role === 'admin' && (
                      <DropdownMenuItem asChild>
                        <Link href="/admin">
                          <div className="flex items-center w-full">
                            <Shield className="mr-2 h-4 w-4" />
                            <span>Admin</span>
                          </div>
                        </Link>
                      </DropdownMenuItem>
                    )}
                  </>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/pricing">
                    <div className="flex items-center w-full">
                      <CreditCard className="mr-2 h-4 w-4" />
                      <span>Pricing</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/how-it-works">
                    <div className="flex items-center w-full">
                      <Info className="mr-2 h-4 w-4" />
                      <span>How It Works</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/contact">
                    <div className="flex items-center w-full">
                      <Phone className="mr-2 h-4 w-4" />
                      <span>Contact</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/welcome">
                    <div className="flex items-center w-full">
                      <HelpCircle className="mr-2 h-4 w-4" />
                      <span>About Us</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                {user && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/profile">
                        <div className="flex items-center w-full">
                          <User className="mr-2 h-4 w-4" />
                          <span>Profile</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/settings">
                        <div className="flex items-center w-full">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Settings</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Desktop Navigation - Hidden on mobile */}
          <div className="hidden md:flex space-x-1 mr-4">
            {/* Primary Links - Always visible */}
            <Link href="/">
              <Button variant="ghost" size="sm" className={`flex items-center px-3 ${isActive('/') ? 'bg-white/20' : ''}`}>
                <Home className="h-4 w-4 mr-2" />
                Home
              </Button>
            </Link>

            {/* Dashboard - Only show for authenticated users */}
            {user && (
              <Link href="/dashboard">
                <Button variant="ghost" size="sm" className={`flex items-center px-3 ${isActive('/dashboard') ? 'bg-white/20' : ''}`}>
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
            )}

            {/* Tools Dropdown - Only show for authenticated users */}
            {user && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="flex items-center px-3">
                    <span className="mr-1">Tools</span>
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem asChild>
                    <Link href="/pathcoach">
                      <div className="flex items-center w-full">
                        <Lightbulb className="mr-2 h-4 w-4" />
                        <span>PathCoach</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/ai-coach">
                      <div className="flex items-center w-full">
                        <Bot className="mr-2 h-4 w-4" />
                        <span>AI Coach</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/browse-jobs">
                      <div className="flex items-center w-full">
                        <Briefcase className="mr-2 h-4 w-4" />
                        <span>Browse Jobs</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/matches">
                      <div className="flex items-center w-full">
                        <Target className="mr-2 h-4 w-4" />
                        <span>Match Analysis</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                  {user?.role === 'employer' && (
                    <DropdownMenuItem asChild>
                      <Link href="/statistics">
                        <div className="flex items-center w-full">
                          <TrendingUp className="mr-2 h-4 w-4" />
                          <span>Job Statistics</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem asChild>
                    <Link href="/analytics">
                      <div className="flex items-center w-full">
                        <BarChart2 className="mr-2 h-4 w-4" />
                        <span>Analytics</span>
                      </div>
                    </Link>
                  </DropdownMenuItem>
                  {user?.role === 'admin' && (
                    <DropdownMenuItem asChild>
                      <Link href="/admin">
                        <div className="flex items-center w-full">
                          <Shield className="mr-2 h-4 w-4" />
                          <span>Admin</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* More Dropdown for secondary links */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="flex items-center px-3">
                  <span className="mr-1">More</span>
                  <ChevronDown className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href="/pricing">
                    <div className="flex items-center w-full">
                      <CreditCard className="mr-2 h-4 w-4" />
                      <span>Pricing</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/how-it-works">
                    <div className="flex items-center w-full">
                      <Info className="mr-2 h-4 w-4" />
                      <span>How It Works</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/contact">
                    <div className="flex items-center w-full">
                      <Phone className="mr-2 h-4 w-4" />
                      <span>Contact</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/welcome">
                    <div className="flex items-center w-full">
                      <HelpCircle className="mr-2 h-4 w-4" />
                      <span>About Us</span>
                    </div>
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Right Side Controls */}
          <div className="flex items-center space-x-2">
            {/* Theme Toggle Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleTheme}
              className="rounded-full bg-white/10 hover:bg-white/20"
              aria-label="Toggle theme"
            >
              {theme === "dark" ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </Button>

            {/* User Menu (if logged in) */}
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={profilePicture || undefined} alt={user.name || "User"} />
                      <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">{user.name}</p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem asChild>
                      <Link href="/profile">
                        <div className="flex items-center w-full">
                          <User className="mr-2 h-4 w-4" />
                          <span>Profile</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link href="/settings">
                        <div className="flex items-center w-full">
                          <Settings className="mr-2 h-4 w-4" />
                          <span>Settings</span>
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} disabled={logoutMutation.isPending}>
                    {logoutMutation.isPending ? (
                      <div className="flex items-center">
                        <div className="w-4 h-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        <span>Logging out...</span>
                      </div>
                    ) : (
                      <>
                        <LogOut className="mr-2 h-4 w-4" />
                        <span>Log out</span>
                      </>
                    )}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              /* Sign In Button (if not logged in) */
              <Link href="/auth">
                <Button
                  variant="secondary"
                  size="sm"
                  className="bg-white text-[#1C2A42] hover:bg-white/90"
                >
                  Sign In
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default SharedNavbar;
