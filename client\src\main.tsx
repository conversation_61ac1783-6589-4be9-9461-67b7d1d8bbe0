import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";

// Add global CSS variables for the theme
document.documentElement.style.setProperty('--midnight-blueprint', '#1C2A42');
document.documentElement.style.setProperty('--desert-alloy', '#C1B7AA');
document.documentElement.style.setProperty('--ivory-signal', '#EAE6E1');

console.log("Using Neon database instead of Supabase");

createRoot(document.getElementById("root")!).render(<App />);
