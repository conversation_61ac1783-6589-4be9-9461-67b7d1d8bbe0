@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Remove excessive Dashboard tooltip removal styles that might be causing issues */
[role="tooltip"],
div[role="dialog"] {
  z-index: 50;
}

/* Ensure the root element is visible */
#root {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 100vh !important;
}

/* Ensure root children are visible */
#root > * {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
}

/* Custom loader animation */
.loader {
  --c: no-repeat linear-gradient(#6100ee 0 0);
  background: var(--c), var(--c), #d7b8fc;
  background-size: 60% 100%;
  animation: loaderAnimation 3s infinite;
  border-radius: 2px;
  margin: 0 auto; /* Center horizontally */
}

@keyframes loaderAnimation {
  0%   {transform: translateX(-150%) translateY(0); will-change: transform;}
  66%  {transform: translateX(250%) translateY(0); will-change: transform;}
  100% {transform: translateX(250%) translateY(250%); will-change: transform;}
}

/* Loading spinner fallback (for compatibility) */
.loading-spinner {
  animation: spin 1s linear infinite;
  border: 2px solid #d7b8fc;
  border-top-color: #6100ee;
  border-radius: 50%;
  height: 1.5rem;
  width: 1.5rem;
  will-change: transform;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}