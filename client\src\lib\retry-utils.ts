/**
 * Utility functions for handling retries and rate limiting
 */

// Store the last attempt timestamps for different operations
const lastAttempts: Record<string, number> = {};

// Store the number of consecutive failures for different operations
const failureCount: Record<string, number> = {};

// Maximum number of retries
const MAX_RETRIES = 3;

// Base delay in milliseconds
const BASE_DELAY = 1000;

/**
 * Check if an operation should be rate limited based on previous attempts
 * @param operation - The name of the operation (e.g., 'login', 'signup')
 * @param minInterval - Minimum interval between attempts in milliseconds
 * @returns Whether the operation should be rate limited
 */
export function shouldRateLimit(operation: string, minInterval = 2000): boolean {
  const now = Date.now();
  const lastAttempt = lastAttempts[operation] || 0;
  
  // Calculate dynamic interval based on failure count
  const count = failureCount[operation] || 0;
  const dynamicInterval = count > 0 ? minInterval * Math.pow(2, count - 1) : minInterval;
  
  // Check if enough time has passed since the last attempt
  return now - lastAttempt < dynamicInterval;
}

/**
 * Record an attempt for an operation
 * @param operation - The name of the operation
 * @param success - Whether the attempt was successful
 */
export function recordAttempt(operation: string, success: boolean): void {
  lastAttempts[operation] = Date.now();
  
  if (success) {
    // Reset failure count on success
    failureCount[operation] = 0;
  } else {
    // Increment failure count on failure
    failureCount[operation] = (failureCount[operation] || 0) + 1;
  }
}

/**
 * Get the recommended wait time before the next attempt
 * @param operation - The name of the operation
 * @returns The recommended wait time in milliseconds
 */
export function getWaitTime(operation: string): number {
  const count = failureCount[operation] || 0;
  return count > 0 ? BASE_DELAY * Math.pow(2, count - 1) : 0;
}

/**
 * Execute a function with exponential backoff retry logic
 * @param operation - The name of the operation
 * @param fn - The function to execute
 * @param maxRetries - Maximum number of retries (default: 3)
 * @returns The result of the function
 */
export async function withRetry<T>(
  operation: string,
  fn: () => Promise<T>,
  maxRetries = MAX_RETRIES
): Promise<T> {
  let retries = 0;
  
  while (true) {
    try {
      // Check if we should rate limit
      if (shouldRateLimit(operation)) {
        const waitTime = getWaitTime(operation);
        console.log(`Rate limiting ${operation}, waiting ${waitTime}ms before retry`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
      
      // Execute the function
      const result = await fn();
      
      // Record successful attempt
      recordAttempt(operation, true);
      
      return result;
    } catch (error: any) {
      // Record failed attempt
      recordAttempt(operation, false);
      
      // Check if we've reached the maximum number of retries
      if (retries >= maxRetries || !isRetryableError(error)) {
        throw error;
      }
      
      // Increment retry count
      retries++;
      
      // Calculate delay using exponential backoff
      const delay = BASE_DELAY * Math.pow(2, retries - 1);
      console.log(`Retrying ${operation} after ${delay}ms (attempt ${retries} of ${maxRetries})`);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

/**
 * Check if an error is retryable
 * @param error - The error to check
 * @returns Whether the error is retryable
 */
function isRetryableError(error: any): boolean {
  // Rate limiting errors are retryable
  if (error.message?.includes('Too many') || error.message?.includes('rate limit')) {
    return true;
  }
  
  // Network errors are retryable
  if (error.name === 'NetworkError' || error.message?.includes('network')) {
    return true;
  }
  
  // Server errors (5xx) are retryable
  if (error.status >= 500 && error.status < 600) {
    return true;
  }
  
  // Other errors are not retryable
  return false;
}
