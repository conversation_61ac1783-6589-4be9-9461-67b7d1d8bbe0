const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function debugPassword() {
  try {
    console.log('🔍 Debugging <NAME_EMAIL>...');
    const client = await pool.connect();
    
    const email = '<EMAIL>';
    const testPassword = 'password123';
    
    // Get user from database
    const userResult = await client.query('SELECT * FROM users WHERE email = $1', [email]);
    
    if (userResult.rows.length === 0) {
      console.log('❌ User not found');
      client.release();
      await pool.end();
      return;
    }
    
    const user = userResult.rows[0];
    console.log('👤 User found:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    console.log('🔐 Password hash in database:', user.password);
    console.log('🔐 Hash starts with:', user.password.substring(0, 10));
    console.log('🔐 Hash length:', user.password.length);
    
    // Test different passwords
    const testPasswords = ['password123', 'password', 'Password123', 'PASSWORD123'];
    
    for (const pwd of testPasswords) {
      console.log(`\n🧪 Testing password: "${pwd}"`);
      try {
        const isValid = await bcrypt.compare(pwd, user.password);
        console.log(`   Result: ${isValid ? '✅ MATCH' : '❌ No match'}`);
      } catch (error) {
        console.log(`   Error: ${error.message}`);
      }
    }
    
    // Let's also try to create a new hash and test it
    console.log('\n🔧 Creating new hash for "password123"...');
    const newHash = await bcrypt.hash('password123', 10);
    console.log('🔐 New hash:', newHash);
    
    const testNewHash = await bcrypt.compare('password123', newHash);
    console.log('✅ New hash test:', testNewHash ? 'WORKS' : 'FAILED');
    
    client.release();
    await pool.end();
    console.log('\n✅ Debug complete');
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

debugPassword();
