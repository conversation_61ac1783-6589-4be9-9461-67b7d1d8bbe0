<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Page</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
      background-color: #f8f8f8;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #1C2A42;
    }
    .logo {
      color: #1C2A42;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="logo">PathLink</div>
    <h1>This is a test page</h1>
    <p>If you can see this, static HTML is working correctly.</p>
    <p>The main application might be having issues with React rendering or JavaScript execution.</p>
    <p><a href="/">Return to main app</a></p>
  </div>
</body>
</html>