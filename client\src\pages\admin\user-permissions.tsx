import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useLocation } from 'wouter';
import AdminLayout from '@/components/admin-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Save, Loader2, Shield } from 'lucide-react';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  is_verified: boolean;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  checked: boolean;
}

export default function UserPermissionsPage() {
  const { id } = useParams<{ id: string }>();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<Permission[]>([
    // Default permissions - these would normally come from the API
    { id: 'view_dashboard', name: 'View Dashboard', description: 'Can view the main dashboard', checked: false },
    { id: 'manage_jobs', name: 'Manage Jobs', description: 'Can create, edit and delete jobs', checked: false },
    { id: 'manage_workers', name: 'Manage Workers', description: 'Can view and manage worker profiles', checked: false },
    { id: 'manage_employers', name: 'Manage Employers', description: 'Can view and manage employer accounts', checked: false },
    { id: 'view_analytics', name: 'View Analytics', description: 'Can access analytics and reports', checked: false },
    { id: 'manage_content', name: 'Manage Content', description: 'Can edit website content', checked: false },
    { id: 'manage_settings', name: 'Manage Settings', description: 'Can change system settings', checked: false },
    { id: 'view_logs', name: 'View Logs', description: 'Can view system logs', checked: false },
  ]);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/admin/users/${id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch user');
        }
        
        const userData = await response.json();
        setUser(userData);
        
        // In a real implementation, we would fetch the user's permissions here
        // For now, we'll simulate it based on the user's role
        if (userData.role === 'admin') {
          setPermissions(prev => prev.map(p => ({ ...p, checked: true })));
        } else if (userData.role === 'employer') {
          setPermissions(prev => prev.map(p => ({ 
            ...p, 
            checked: ['view_dashboard', 'manage_jobs', 'view_analytics'].includes(p.id)
          })));
        } else {
          // Worker role
          setPermissions(prev => prev.map(p => ({ 
            ...p, 
            checked: ['view_dashboard'].includes(p.id)
          })));
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        toast({
          title: 'Error',
          description: 'Failed to load user data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchUser();
    }
  }, [id, toast]);

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    setPermissions(prev => 
      prev.map(permission => 
        permission.id === permissionId 
          ? { ...permission, checked } 
          : permission
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      
      // In a real implementation, we would send the permissions to the API
      // For now, we'll just simulate a successful update
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: 'Success',
        description: 'User permissions updated successfully',
      });
      
      // Navigate back to users list
      setLocation('/admin/users');
    } catch (error) {
      console.error('Error updating permissions:', error);
      toast({
        title: 'Error',
        description: 'Failed to update permissions',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-8 flex items-center justify-center min-h-[60vh]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout>
        <div className="container mx-auto py-8">
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
            <h2 className="text-lg font-semibold">User Not Found</h2>
            <p>The requested user could not be found.</p>
            <Button 
              variant="outline" 
              className="mt-4"
              onClick={() => setLocation('/admin/users')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Users
            </Button>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="container mx-auto py-8">
        <div className="flex items-center mb-6">
          <Button 
            variant="ghost" 
            onClick={() => setLocation('/admin/users')}
            className="mr-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">User Permissions</h1>
        </div>

        <Card className="max-w-2xl mx-auto">
          <CardHeader>
            <div className="flex items-center">
              <Shield className="h-5 w-5 mr-2 text-primary" />
              <div>
                <CardTitle>Permissions for {user.name}</CardTitle>
                <CardDescription>
                  Role: {user.role.charAt(0).toUpperCase() + user.role.slice(1)} | 
                  Email: {user.email}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent>
              <div className="space-y-4">
                {permissions.map((permission) => (
                  <div key={permission.id} className="flex items-start space-x-3 p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800">
                    <Checkbox
                      id={permission.id}
                      checked={permission.checked}
                      onCheckedChange={(checked) => 
                        handlePermissionChange(permission.id, checked === true)
                      }
                    />
                    <div className="space-y-1">
                      <Label
                        htmlFor={permission.id}
                        className="font-medium"
                      >
                        {permission.name}
                      </Label>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {permission.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <Button 
                variant="outline" 
                type="button"
                onClick={() => setLocation('/admin/users')}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save Permissions
                  </>
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      </div>
    </AdminLayout>
  );
}
