import React, { useRef, useEffect, useState } from "react";
import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowDown, Mail, Heart, Brain, ChevronUp, Play } from "lucide-react";
import { motion, useInView, useAnimation, AnimatePresence } from "framer-motion";
import { Progress } from "@/components/ui/progress";
import { Logo } from "@/components/logo";

import logoImg from "@/assets/logo.jpeg";
import teamImg from "@/assets/IMG_2675.jpeg";

// Network Node component
interface NodeProps {
  x: number;
  y: number;
  size?: number;
  delay?: number;
}

const NetworkNode: React.FC<NodeProps> = ({ x, y, size = 4, delay = 0 }) => (
  <motion.div
    className="absolute rounded-full bg-[#1C2A42] shadow-lg"
    style={{
      left: `${x}%`,
      top: `${y}%`,
      width: `${size}px`,
      height: `${size}px`,
    }}
    initial={{ opacity: 0, scale: 0 }}
    animate={{
      opacity: [0.4, 0.8, 0.4],
      scale: [1, 1.3, 1],
      boxShadow: [
        "0 0 0px rgba(28, 42, 66, 0.3)",
        "0 0 20px rgba(28, 42, 66, 0.6)",
        "0 0 0px rgba(28, 42, 66, 0.3)"
      ]
    }}
    transition={{
      duration: 3,
      repeat: Infinity,
      ease: "easeInOut",
      delay: delay
    }}
  />
);

// Connection Line component
interface ConnectionProps {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  delay?: number;
}

const ConnectionLine: React.FC<ConnectionProps> = ({ x1, y1, x2, y2, delay = 0 }) => {
  const length = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
  const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

  return (
    <motion.div
      className="absolute origin-left"
      style={{
        left: `${x1}%`,
        top: `${y1}%`,
        width: `${length}%`,
        height: '2px',
        transform: `rotate(${angle}deg)`,
        background: 'linear-gradient(90deg, rgba(28, 42, 66, 0.3), rgba(229, 222, 255, 0.6), rgba(28, 42, 66, 0.3))',
        boxShadow: '0 0 4px rgba(229, 222, 255, 0.4)'
      }}
      initial={{ scaleX: 0, opacity: 0 }}
      animate={{
        scaleX: [0, 1, 0],
        opacity: [0, 0.7, 0]
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut",
        delay: delay
      }}
    />
  );
};

// Network Background component
const NetworkBackground: React.FC = () => {
  const nodes = [
    { x: 10, y: 20, size: 6, delay: 0 },
    { x: 25, y: 15, size: 4, delay: 0.5 },
    { x: 40, y: 25, size: 5, delay: 1 },
    { x: 60, y: 10, size: 4, delay: 1.5 },
    { x: 75, y: 30, size: 6, delay: 2 },
    { x: 90, y: 20, size: 4, delay: 2.5 },
    { x: 15, y: 60, size: 5, delay: 0.3 },
    { x: 35, y: 70, size: 4, delay: 0.8 },
    { x: 55, y: 65, size: 6, delay: 1.3 },
    { x: 80, y: 75, size: 4, delay: 1.8 },
    { x: 20, y: 85, size: 5, delay: 0.6 },
    { x: 70, y: 90, size: 4, delay: 1.1 }
  ];

  const connections = [
    { x1: 10, y1: 20, x2: 25, y2: 15, delay: 0 },
    { x1: 25, y1: 15, x2: 40, y2: 25, delay: 0.5 },
    { x1: 40, y1: 25, x2: 60, y2: 10, delay: 1 },
    { x1: 60, y1: 10, x2: 75, y2: 30, delay: 1.5 },
    { x1: 75, y1: 30, x2: 90, y2: 20, delay: 2 },
    { x1: 15, y1: 60, x2: 35, y2: 70, delay: 0.3 },
    { x1: 35, y1: 70, x2: 55, y2: 65, delay: 0.8 },
    { x1: 55, y1: 65, x2: 80, y2: 75, delay: 1.3 },
    { x1: 20, y1: 85, x2: 70, y2: 90, delay: 0.6 },
    { x1: 10, y1: 20, x2: 15, y2: 60, delay: 0.2 },
    { x1: 40, y1: 25, x2: 35, y2: 70, delay: 0.7 },
    { x1: 75, y1: 30, x2: 80, y2: 75, delay: 1.2 }
  ];

  return (
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      {/* Animated gradient overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-[#E5DEFF]/10 via-transparent to-[#1C2A42]/5"
        animate={{
          background: [
            "linear-gradient(45deg, rgba(229, 222, 255, 0.1), transparent, rgba(28, 42, 66, 0.05))",
            "linear-gradient(45deg, rgba(28, 42, 66, 0.05), transparent, rgba(229, 222, 255, 0.1))",
            "linear-gradient(45deg, rgba(229, 222, 255, 0.1), transparent, rgba(28, 42, 66, 0.05))"
          ]
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      {/* Connection lines */}
      {connections.map((connection, index) => (
        <ConnectionLine
          key={`connection-${index}`}
          x1={connection.x1}
          y1={connection.y1}
          x2={connection.x2}
          y2={connection.y2}
          delay={connection.delay}
        />
      ))}

      {/* Network nodes */}
      {nodes.map((node, index) => (
        <NetworkNode
          key={`node-${index}`}
          x={node.x}
          y={node.y}
          size={node.size}
          delay={node.delay}
        />
      ))}

      {/* Floating particles for extra ambiance */}
      {Array.from({ length: 8 }).map((_, i) => (
        <motion.div
          key={`particle-${i}`}
          className="absolute w-1 h-1 bg-[#E5DEFF] rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -30, 0],
            opacity: [0, 0.6, 0],
            scale: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 4 + Math.random() * 2,
            repeat: Infinity,
            ease: "easeInOut",
            delay: Math.random() * 3
          }}
        />
      ))}
    </div>
  );
};

export default function LandingPage() {
  const whyPathLinkRef = useRef<HTMLDivElement>(null);
  const builtForHumansRef = useRef<HTMLDivElement>(null);
  const foundersRef = useRef<HTMLDivElement>(null);
  const pathCoachRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);

  // Animation controls for sections
  const controls = {
    hero: useAnimation(),
    why: useAnimation(),
    how: useAnimation(),
    humans: useAnimation(),
    founders: useAnimation(),
    pathCoach: useAnimation(),
    who: useAnimation(),
    contact: useAnimation()
  };

  // Check if sections are in view
  const isInView = {
    why: useInView(whyPathLinkRef, { once: false, amount: 0.3 }),
    humans: useInView(builtForHumansRef, { once: false, amount: 0.3 }),
    founders: useInView(foundersRef, { once: false, amount: 0.3 }),
    pathCoach: useInView(pathCoachRef, { once: false, amount: 0.3 }),
    contact: useInView(contactRef, { once: false, amount: 0.3 })
  };

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 500) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }

      // Progress bar logic
      const scrollTop = window.scrollY;
      const docHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;
      setScrollProgress(progress);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Animate sections when they come into view
  useEffect(() => {
    if (isInView.why) controls.why.start({ opacity: 1, y: 0, transition: { duration: 0.5 } });
    if (isInView.humans) controls.humans.start({ opacity: 1, y: 0, transition: { duration: 0.5 } });
    if (isInView.founders) controls.founders.start({ opacity: 1, y: 0, transition: { duration: 0.5 } });
    if (isInView.pathCoach) controls.pathCoach.start({ opacity: 1, y: 0, transition: { duration: 0.5 } });
    if (isInView.contact) controls.contact.start({ opacity: 1, y: 0, transition: { duration: 0.5 } });
  }, [isInView, controls]);

  // Start hero animation on load
  useEffect(() => {
    controls.hero.start({ opacity: 1, y: 0, transition: { duration: 0.8 } });
    controls.how.start({ opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.2 } });
    controls.who.start({ opacity: 1, y: 0, transition: { duration: 0.5, delay: 0.2 } });
  }, [controls]);

  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Scroll Progress Bar */}
      <div className="fixed top-0 left-0 w-full z-50">
        <Progress value={scrollProgress} className="h-1 bg-gray-200 [&_.bg-primary]:bg-[#1C2A42] transition-all duration-200" />
      </div>

      <motion.header
        className="py-6 px-8 sticky top-0 bg-white/80 backdrop-blur-sm border-b z-10"
        initial={{ y: -100 }}
        animate={{ y: 0 }}
        transition={{ type: "spring", stiffness: 100, damping: 15 }}
      >
        <div className="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-center">
          <motion.div
            className="flex items-center gap-3 mb-4 md:mb-0"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <Logo className="h-10 w-auto" />
            <h1 className="text-2xl md:text-3xl font-bold text-[#1C2A42]">
              PathLink
            </h1>
          </motion.div>
          <motion.nav
            className="flex flex-wrap justify-center gap-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {[
              { ref: whyPathLinkRef, label: "Why PathLink" },
              { ref: builtForHumansRef, label: "Built for Humans" },
              { ref: foundersRef, label: "Founder's Pledge" },
              { ref: pathCoachRef, label: "PathCoach Edge" },
              { ref: contactRef, label: "Contact" }
            ].map((item, index) => (
              <motion.button
                key={index}
                type="button"
                onClick={() => scrollToSection(item.ref)}
                className="text-sm text-gray-600 hover:text-[#1C2A42] transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.3 + (index * 0.1) }}
              >
                {item.label}
              </motion.button>
            ))}
            <Link to="/pricing">
              <motion.button
                type="button"
                className="text-sm text-[#1C2A42] font-medium hover:text-[#1C2A42]/80 transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: 0.8 }}
              >
                Pricing
              </motion.button>
            </Link>
          </motion.nav>
        </div>
      </motion.header>

      <main>
        {/* Hero Section */}
        <section className="bg-[#EAE6E1] py-20 px-8 relative overflow-hidden">
          {/* Network Background */}
          <NetworkBackground />

          <motion.div
            className="max-w-4xl mx-auto text-center relative z-10"
            initial={{ opacity: 0, y: 30 }}
            animate={controls.hero}
          >
            <motion.h2
              className="text-4xl md:text-5xl font-bold mb-6 text-[#1C2A42]"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              Transforming Workforce Mobility
            </motion.h2>

            <motion.p
              className="text-xl mb-10 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
            >
              PathLink is a revolutionary workforce redistribution platform that connects surplus employees
              to high-demand roles using AI, skill analytics, and career guidance.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row justify-center gap-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.6 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link to="/auth">
                  <Button size="lg" className="bg-[#1C2A42] hover:bg-[#1C2A42]/90">
                    Get Started
                  </Button>
                </Link>
              </motion.div>

              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Link to="/auth">
                  <Button size="lg" variant="outline" className="border-[#1C2A42] text-[#1C2A42]">
                    Join the Movement
                  </Button>
                </Link>
              </motion.div>
            </motion.div>

            <motion.div
              className="mt-16"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.7, delay: 0.8 }}
            >
              <motion.button
                onClick={() => scrollToSection(whyPathLinkRef)}
                aria-label="Scroll down"
                className="inline-flex bg-white p-3 rounded-full shadow"
                type="button"
                animate={{ y: [0, 10, 0] }}
                transition={{
                  duration: 1.5,
                  repeat: Infinity,
                  repeatType: "loop"
                }}
                whileHover={{ scale: 1.1 }}
              >
                <ArrowDown className="h-6 w-6 text-[#1C2A42]" />
              </motion.button>
            </motion.div>
          </motion.div>
        </section>

        {/* Showcase Section */}
        <section className="py-12 px-8 bg-white flex flex-col items-center">
          <motion.h3
            className="text-2xl font-bold mb-6 text-[#1C2A42] flex items-center gap-2"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.5 }}
            transition={{ duration: 0.5 }}
          >
            <Play className="h-7 w-7 text-[#1C2A42]" /> PathLink in Action
          </motion.h3>
          <div className="w-full max-w-4xl relative">
            <motion.div
              className="relative rounded-xl shadow-2xl overflow-hidden bg-[#EAE6E1]/40"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.7 }}
              whileHover={{ scale: 1.02 }}
            >
              {/* Placeholder for Video */}
              <div className="flex flex-col items-center justify-center min-h-[300px] bg-gradient-to-br from-[#E5DEFF]/30 to-[#1C2A42]/10 rounded-xl">
                <img
                  src={logoImg}
                  alt="PathLink Logo"
                  className="w-32 h-32 object-contain mb-6 rounded-lg shadow-lg"
                />
                <h4 className="text-2xl font-bold text-[#1C2A42] mb-4">
                  Experience PathLink
                </h4>
                <p className="text-lg text-gray-600 max-w-md text-center">
                  Discover how our AI-powered platform revolutionizes workforce mobility and connects talent with opportunity.
                </p>
                <Link to="/auth">
                  <Button className="mt-6 bg-[#1C2A42] hover:bg-[#1C2A42]/90">
                    Get Started Today
                  </Button>
                </Link>
              </div>

              {/* Gradient overlay for better presentation */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-t from-[#1C2A42]/10 via-transparent to-transparent pointer-events-none rounded-xl"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              />
            </motion.div>

            {/* Description */}
            <motion.p
              className="text-center text-gray-600 mt-4 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              Watch how PathLink revolutionizes workforce mobility through intelligent matching,
              AI-powered analytics, and seamless career transitions.
            </motion.p>
          </div>
        </section>

        {/* Why PathLink Section */}
        <section ref={whyPathLinkRef} className="py-20 px-8 bg-white relative overflow-hidden">
          {/* Subtle Network Background */}
          <div className="absolute inset-0 opacity-30">
            <NetworkBackground />
          </div>
          <motion.div
            className="max-w-4xl mx-auto relative z-10"
            initial={{ opacity: 0, y: 50 }}
            animate={controls.why}
          >
            <motion.h2
              className="text-3xl font-bold mb-8 text-[#1C2A42] text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{ duration: 0.5 }}
            >
              Why PathLink Exists
            </motion.h2>

            <motion.div
              className="bg-[#EAE6E1]/30 p-8 rounded-lg shadow-sm relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              {/* Animated background elements */}
              <motion.div
                className="absolute -right-16 -top-16 w-32 h-32 rounded-full bg-[#1C2A42]/5"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.3, 0.5, 0.3]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              />
              <motion.div
                className="absolute -left-16 -bottom-16 w-32 h-32 rounded-full bg-[#1C2A42]/5"
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.2, 0.4, 0.2]
                }}
                transition={{
                  duration: 10,
                  repeat: Infinity,
                  repeatType: "reverse",
                  delay: 1
                }}
              />

              <motion.p
                className="text-lg mb-6 relative z-10"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                PathLink was created with a bold mission: to end layoffs as we know them.
                In today's rapidly evolving economy, skill gaps and workforce misalignments
                cause unnecessary disruption and human suffering.
              </motion.p>

              <motion.p
                className="text-lg mb-6 relative z-10"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                We believe talent should flow to where it's needed most, and workers deserve
                the opportunity to grow into new roles rather than facing unemployment.
              </motion.p>

              <motion.p
                className="text-lg relative z-10"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                Our platform addresses three critical problems:
              </motion.p>

              <motion.ul
                className="list-disc list-inside mt-4 space-y-2 text-lg relative z-10"
              >
                {["Eliminating inefficient labor markets that waste human potential",
                  "Closing skill gaps through targeted reskilling recommendations",
                  "Redistributing opportunity more equitably across industries and regions"
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true, amount: 0.5 }}
                    transition={{ duration: 0.5, delay: 0.6 + (index * 0.1) }}
                  >
                    {item}
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
          </motion.div>
        </section>

        {/* How It Works Section */}
        <section className="py-20 px-8 bg-gray-50 relative">
          <motion.div
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={controls.how}
          >
            <motion.h2
              className="text-3xl font-bold mb-12 text-[#1C2A42] text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{ duration: 0.5 }}
            >
              How It Works
            </motion.h2>

            <motion.div className="space-y-8">
              {[
                {
                  number: 1,
                  title: "Upload Jobs or Surplus Talent",
                  description: "Easily list job openings or surplus employees who need placement. Our system catalogs skills, experience, and potential while preserving worker dignity and employer needs."
                },
                {
                  number: 2,
                  title: "AI Matches the Right Workers",
                  description: "Our intelligent algorithm finds perfect fits based on skills and experience. The platform identifies hidden potential and transferable abilities that traditional recruitment often misses."
                },
                {
                  number: 3,
                  title: "PathCoach Guides the Journey",
                  description: "Receive personalized guidance and recommendations for successful transitions. Our PathCoach system supports both workers and employers throughout the placement process."
                }
              ].map((step, index) => (
                <motion.div
                  key={index}
                  className="bg-white p-6 rounded-lg shadow-sm flex flex-col md:flex-row gap-6 items-center"
                  initial={{ opacity: 0, x: -30 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, amount: 0.5 }}
                  transition={{ duration: 0.5, delay: 0.2 + (index * 0.2) }}
                  whileHover={{
                    scale: 1.02,
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
                  }}
                >
                  <motion.div
                    className="h-16 w-16 bg-[#1C2A42] text-white rounded-full flex items-center justify-center font-bold text-2xl flex-shrink-0"
                    initial={{ scale: 0.8 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{
                      type: "spring",
                      stiffness: 200,
                      damping: 10,
                      delay: 0.3 + (index * 0.2)
                    }}
                    whileHover={{ scale: 1.1 }}
                  >
                    {step.number}
                  </motion.div>
                  <div>
                    <motion.h3
                      className="text-xl font-bold mb-2"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: 0.4 + (index * 0.2) }}
                    >
                      {step.title}
                    </motion.h3>
                    <motion.p
                      className="text-gray-600"
                      initial={{ opacity: 0 }}
                      whileInView={{ opacity: 1 }}
                      viewport={{ once: true }}
                      transition={{ duration: 0.3, delay: 0.5 + (index * 0.2) }}
                    >
                      {step.description}
                    </motion.p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </section>

        {/* Built for Humans Section */}
        <section ref={builtForHumansRef} className="py-20 px-8 bg-white relative">
          <motion.div
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 50 }}
            animate={controls.humans}
          >
            <motion.h2
              className="text-3xl font-bold mb-8 text-[#1C2A42] text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.5 }}
              transition={{ duration: 0.5 }}
            >
              Built for Humans, Not Algorithms
            </motion.h2>

            <motion.div
              className="bg-[#EAE6E1]/30 p-8 rounded-lg shadow-sm relative overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.3 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <motion.div
                className="flex justify-center mb-8"
                initial={{ scale: 0.5, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                viewport={{ once: true }}
                transition={{
                  type: "spring",
                  stiffness: 200,
                  damping: 10,
                  delay: 0.3
                }}
              >
                <motion.div
                  animate={{
                    scale: [1, 1.1, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                >
                  <Heart className="h-16 w-16 text-[#1C2A42]" />
                </motion.div>
              </motion.div>

              <motion.p
                className="text-lg mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                While we leverage cutting-edge AI, PathLink puts people at the center of everything we do.
                Unlike cold automation or keyword-matching systems, we focus on human growth, potential,
                and dignity.
              </motion.p>

              <motion.p
                className="text-lg mb-6"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.5, delay: 0.5 }}
              >
                Our platform recognizes that careers are journeys, not just transactions. We celebrate
                the uniquely human qualities that make each worker special – adaptability, creativity,
                emotional intelligence, and the capacity to learn and grow.
              </motion.p>

              <motion.p
                className="text-lg"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.5 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                PathLink is designed to:
              </motion.p>

              <motion.ul
                className="list-disc list-inside mt-4 space-y-2 text-lg"
              >
                {["See potential where traditional systems see only keywords",
                  "Honor the dignity of work and workers in transition",
                  "Cultivate human connections in an increasingly automated world"
                ].map((item, index) => (
                  <motion.li
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true, amount: 0.5 }}
                    transition={{ duration: 0.5, delay: 0.7 + (index * 0.1) }}
                  >
                    {item}
                  </motion.li>
                ))}
              </motion.ul>
            </motion.div>
          </motion.div>
        </section>

        {/* Founder's Pledge Section */}
        <section ref={foundersRef} className="py-20 px-8 bg-gray-50">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-[#1C2A42] text-center">Founder's Pledge</h2>
            <div className="bg-white p-8 rounded-lg shadow-sm">
              <blockquote className="text-lg italic mb-6 border-l-4 border-[#1C2A42] pl-4">
                "We believe that no worker should face uncertainty alone, and no business should
                lose valuable talent due to market shifts. PathLink exists to create a more humane,
                efficient, and opportunity-rich labor ecosystem for everyone."
              </blockquote>
              <p className="text-lg mb-6">
                Our leadership team is committed to building PathLink as a long-term force for
                positive workforce evolution. We pledge to:
              </p>
              <ul className="list-disc list-inside mt-4 space-y-2 text-lg">
                <li>Always prioritize human dignity in our technology decisions</li>
                <li>Build features based on real worker and employer needs, not hypotheticals</li>
                <li>Measure success by lives improved, not just transactions processed</li>
                <li>Create sustainable paths to rewarding work for people at all career stages</li>
              </ul>
            </div>
          </div>
        </section>

        {/* PathCoach Edge Section */}
        <section ref={pathCoachRef} className="py-20 px-8 bg-white">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-8 text-[#1C2A42] text-center">The PathCoach Edge</h2>
            <div className="bg-[#EAE6E1]/30 p-8 rounded-lg shadow-sm">
              <div className="flex justify-center mb-8">
                <Brain className="h-16 w-16 text-[#1C2A42]" />
              </div>
              <p className="text-lg mb-6">
                What truly sets PathLink apart is our PathCoach system – personalized guidance that
                transforms the career transition experience. PathCoach doesn't just suggest jobs;
                it provides comprehensive support for sustainable career growth.
              </p>
              <p className="text-lg mb-6">
                PathCoach offers:
              </p>
              <ul className="list-disc list-inside mt-4 space-y-4 text-lg">
                <li>
                  <strong>Personalized Reskilling Recommendations:</strong> Targeted suggestions
                  based on your unique experience and the skills needed for high-demand roles.
                </li>
                <li>
                  <strong>Mindset Support:</strong> Tools and guidance to build resilience and
                  confidence during career transitions.
                </li>
                <li>
                  <strong>Growth Tracking:</strong> Visualization of your skill development and
                  career trajectory over time.
                </li>
                <li>
                  <strong>Transformation Stories:</strong> Real examples of successful career pivots
                  to inspire your journey.
                </li>
              </ul>
            </div>
          </div>
        </section>

        {/* Who It's For Section */}
        <section className="py-20 px-8 bg-gray-50">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold mb-12 text-[#1C2A42] text-center">Who It's For</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-4 text-[#1C2A42]">Employers & HR Teams</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="h-5 w-5 bg-[#1C2A42] text-white rounded-full flex items-center justify-center text-xs mr-2 mt-0.5">✓</div>
                    <p>Find qualified candidates faster while reducing costs</p>
                  </li>
                  <li className="flex items-start">
                    <div className="h-5 w-5 bg-[#1C2A42] text-white rounded-full flex items-center justify-center text-xs mr-2 mt-0.5">✓</div>
                    <p>Minimize costly layoffs through strategic talent redistribution</p>
                  </li>
                  <li className="flex items-start">
                    <div className="h-5 w-5 bg-[#1C2A42] text-white rounded-full flex items-center justify-center text-xs mr-2 mt-0.5">✓</div>
                    <p>Build your reputation as an ethical employer committed to worker wellbeing</p>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-4 text-[#1C2A42]">Transitioning Workers</h3>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <div className="h-5 w-5 bg-[#1C2A42] text-white rounded-full flex items-center justify-center text-xs mr-2 mt-0.5">✓</div>
                    <p>Navigate career changes with intelligent matching</p>
                  </li>
                  <li className="flex items-start">
                    <div className="h-5 w-5 bg-[#1C2A42] text-white rounded-full flex items-center justify-center text-xs mr-2 mt-0.5">✓</div>
                    <p>Receive personalized reskilling guidance and support</p>
                  </li>
                  <li className="flex items-start">
                    <div className="h-5 w-5 bg-[#1C2A42] text-white rounded-full flex items-center justify-center text-xs mr-2 mt-0.5">✓</div>
                    <p>Connect to opportunities that value your full potential, not just your past</p>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section ref={contactRef} className="py-20 px-8 bg-white">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-8 text-[#1C2A42]">Get in Touch</h2>
            <div className="bg-[#EAE6E1]/30 p-8 rounded-lg shadow-sm">
              <div className="flex justify-center mb-6">
                <Mail className="h-12 w-12 text-[#1C2A42]" />
              </div>
              <p className="text-lg mb-8">
                Have questions about how PathLink can help your organization or career?
                We're here to help transform the way talent moves in the economy.
              </p>
              <div className="flex flex-col items-center">
                <p className="text-lg font-medium mb-2">Contact us at:</p>
                <a
                  href="mailto:<EMAIL>"
                  className="text-[#1C2A42] font-bold text-xl hover:underline"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
            <div className="mt-12">
              <Link to="/auth">
                <Button className="bg-[#1C2A42]">
                  Join PathLink Today
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Scroll to top button */}
      <AnimatePresence>
        {showScrollTop && (
          <motion.button
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 bg-[#1C2A42] text-white p-3 rounded-full shadow-lg z-50"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            type="button"
            aria-label="Scroll to top"
          >
            <ChevronUp className="h-6 w-6" />
          </motion.button>
        )}
      </AnimatePresence>

      <footer className="py-12 px-8 bg-[#1C2A42] text-white">
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <Logo className="h-8 w-auto" />
                <h3 className="text-xl font-bold">PathLink</h3>
              </div>
              <p className="text-gray-300">
                AI-powered labor redistribution platform transforming workforce mobility.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li><button type="button" onClick={() => scrollToSection(whyPathLinkRef)} className="text-gray-300 hover:text-white transition-colors duration-200">Why PathLink</button></li>
                <li><button type="button" onClick={() => scrollToSection(builtForHumansRef)} className="text-gray-300 hover:text-white transition-colors duration-200">Built for Humans</button></li>
                <li><button type="button" onClick={() => scrollToSection(foundersRef)} className="text-gray-300 hover:text-white transition-colors duration-200">Founder's Pledge</button></li>
                <li><button type="button" onClick={() => scrollToSection(pathCoachRef)} className="text-gray-300 hover:text-white transition-colors duration-200">PathCoach Edge</button></li>
                <li><Link to="/pricing" className="text-gray-300 hover:text-white transition-colors duration-200">Pricing</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-medium mb-4">Contact</h3>
              <p className="text-gray-300 mb-2"><EMAIL></p>
              <p className="text-gray-300">123 Innovation Way, Tech Corridor</p>
            </div>
          </div>
          <motion.div
            className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <p>© 2025 PathLink. All rights reserved.</p>
          </motion.div>
        </div>
      </footer>
    </div>
  );
}