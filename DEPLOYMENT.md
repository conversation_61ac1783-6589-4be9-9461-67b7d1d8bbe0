# PathLink Deployment Guide

*Copyright © 2025 <PERSON>. All Rights Reserved.*

This guide provides detailed instructions for deploying the PathLink application to various platforms.

> **Note:** For Netlify deployment instructions, please see the [Netlify Deployment Guide](NETLIFY_DEPLOYMENT.md).

## Deploying to Render

Render is the recommended platform for deploying PathLink due to its ease of use and excellent support for full-stack Node.js applications with PostgreSQL databases.

### Prerequisites

- A [Render account](https://render.com) (free tier is sufficient for testing)
- Your PathLink codebase in a GitHub repository
- Required environment variables (see below)

### Step 1: Prepare Your Repository

Ensure your repository contains the following files:

- `render.yaml` (for Render Blueprint)
- `Procfile` (for process management)
- Proper `package.json` scripts (build, start)

### Step 2: Connect Your Repository to Render

1. Log in to your Render dashboard
2. Click "New" and select "Blueprint"
3. Connect your GitHub account if you haven't already
4. Select your PathLink repository
5. <PERSON><PERSON> will detect the `render.yaml` configuration and suggest services to deploy
6. Review the configuration and click "Apply"

### Step 3: Configure Environment Variables

Set up the following environment variables in the Render dashboard:

- `DATABASE_URL`: Your PostgreSQL connection string
  - You can use an existing Neon database or create a new PostgreSQL database on Render
- `SESSION_SECRET`: A secure random string for session encryption
- `OPENAI_API_KEY`: Your OpenAI API key
- `SENDGRID_API_KEY`: Your SendGrid API key

### Step 4: Deploy Your Application

1. Click "Create Web Service" to start the deployment process
2. Render will build and deploy your application
3. Once deployment is complete, you can access your application at the provided URL

### Step 5: Run Database Migrations

After deployment, you need to run database migrations:

1. Go to your web service dashboard
2. Click on "Shell"
3. Run the following command:

   ```bash
   npm run db:migrate
   ```

## Deploying to Vercel

Vercel is another excellent option for deploying PathLink, especially if you prefer a platform optimized for frontend applications.

### Prerequisites

- A [Vercel account](https://vercel.com) (free tier is sufficient for testing)
- Your PathLink codebase in a GitHub repository
- A PostgreSQL database (Neon recommended)

### Step 1: Prepare Your Repository

Ensure your repository contains a `vercel.json` file:

```json
{
  "version": 2,
  "builds": [
    {
      "src": "server/index.ts",
      "use": "@vercel/node"
    },
    {
      "src": "client/package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/api/(.*)",
      "dest": "server/index.ts"
    },
    {
      "src": "/(.*)",
      "dest": "client/dist/$1"
    }
  ]
}
```

### Step 2: Deploy to Vercel

1. Log in to your Vercel dashboard
2. Click "New Project"
3. Import your GitHub repository
4. Configure the project:
   - Framework Preset: Other
   - Root Directory: ./
   - Build Command: npm run build
   - Output Directory: client/dist
5. Add environment variables:
   - `DATABASE_URL`
   - `SESSION_SECRET`
   - `OPENAI_API_KEY`
   - `SENDGRID_API_KEY`
6. Click "Deploy"

### Step 3: Run Database Migrations

After deployment, you need to run database migrations:

1. Install Vercel CLI: `npm i -g vercel`
2. Log in to Vercel CLI: `vercel login`
3. Run migrations: `vercel run npm run db:migrate`

## Deploying to Railway

Railway is a developer-friendly platform that makes deploying applications straightforward.

### Prerequisites

- A [Railway account](https://railway.app) (free tier is sufficient for testing)
- Your PathLink codebase in a GitHub repository

### Step 1: Deploy to Railway

1. Log in to your Railway dashboard
2. Click "New Project"
3. Select "Deploy from GitHub repo"
4. Select your PathLink repository
5. Railway will automatically detect your Node.js app
6. Add a PostgreSQL database:
   - Click "New"
   - Select "Database" → "PostgreSQL"
7. Add environment variables:
   - `DATABASE_URL` (Railway will automatically connect your database)
   - `SESSION_SECRET`
   - `OPENAI_API_KEY`
   - `SENDGRID_API_KEY`
8. Deploy your application

### Step 2: Run Database Migrations

After deployment, you need to run database migrations:

1. Go to your project dashboard
2. Click on "Deployments"
3. Click on your latest deployment
4. Click "Shell"
5. Run: `npm run db:migrate`

## Deploying to Fly.io

Fly.io allows you to deploy applications globally with minimal configuration.

### Prerequisites

- A [Fly.io account](https://fly.io) (free tier is sufficient for testing)
- Fly CLI installed: `curl -L https://fly.io/install.sh | sh`
- Your PathLink codebase in a GitHub repository

### Step 1: Prepare Your Repository

Ensure your repository contains a `fly.toml` file.

### Step 2: Deploy to Fly.io

1. Log in to Fly CLI: `fly auth login`
2. Initialize your app: `fly launch`
3. Create a PostgreSQL database: `fly postgres create`
4. Attach the database: `fly postgres attach --app pathlink <database-name>`
5. Set environment variables:
   - `SESSION_SECRET`
   - `OPENAI_API_KEY`
   - `SENDGRID_API_KEY`
6. Deploy your application: `fly deploy`

### Step 3: Run Database Migrations

After deployment, you need to run database migrations:

1. Open a shell to your app: `fly ssh console`
2. Run: `npm run db:migrate`

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues:

1. Verify your `DATABASE_URL` is correct
2. Ensure your database is accessible from your deployment platform
3. Check if your database requires SSL (most cloud databases do)
4. For Neon databases, make sure you're using the correct connection string format

### Build Failures

If your build fails:

1. Check the build logs for specific errors
2. Ensure all dependencies are properly listed in `package.json`
3. Verify your build script is correct
4. Make sure your Node.js version is compatible (v18+ recommended)

### Runtime Errors

If your application crashes after deployment:

1. Check the application logs
2. Verify all environment variables are set correctly
3. Ensure database migrations have been run
4. Check for any platform-specific issues

## Monitoring and Maintenance

After deployment, regularly monitor your application:

1. Set up alerts for application errors
2. Monitor database performance
3. Regularly update dependencies
4. Back up your database regularly

For any deployment issues, please contact [<EMAIL>](mailto:<EMAIL>) for assistance.
