import { useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/hooks/use-auth";

export default function HomePage() {
  const { user } = useAuth();
  const [_, setLocation] = useLocation();

  useEffect(() => {
    // Redirect to dashboard if authenticated
    if (user) {
      setLocation("/dashboard");
    } else {
      setLocation("/auth");
    }
  }, [user, setLocation]);

  return null;
}
