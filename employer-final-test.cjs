const { chromium } = require('playwright');
const fs = require('fs');

class EmployerFinalTest {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5000';
  }

  async log(test, status, details = '', screenshot = null) {
    const result = {
      test,
      status,
      details,
      screenshot,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [Employer] ${test}: ${status} ${details}`);
  }

  async setup() {
    console.log('🚀 Setting up final employer testing...');
    this.browser = await chromium.launch({ 
      headless: false,
      slowMo: 1500
    });
    this.page = await this.browser.newPage();
    await this.page.setViewportSize({ width: 1280, height: 720 });
    console.log('✅ Browser ready for final employer testing');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Final employer testing complete');
    }
  }

  async takeScreenshot(name) {
    const filename = `screenshots/employer-final-${name}-${Date.now()}.png`;
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    await this.page.screenshot({ path: filename, fullPage: true });
    return filename;
  }

  async loginAsEmployer() {
    console.log('\n🔐 LOGGING IN AS EMPLOYER...');

    try {
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(3000);

      // Use the specific input approach that worked in debug
      const allInputs = await this.page.locator('input').all();
      
      if (allInputs.length >= 2) {
        await allInputs[0].fill('<EMAIL>');
        await allInputs[1].fill('password123');
        
        const screenshot = await this.takeScreenshot('employer-login-filled');
        await this.log('Employer Login Form', 'PASS', 'Employer credentials entered', screenshot);
        
        // Use the specific submit button (type="submit")
        const submitButton = this.page.locator('button[type="submit"]');
        await submitButton.click();
        
        await this.page.waitForTimeout(4000);
        
        const currentUrl = this.page.url();
        if (currentUrl.includes('/dashboard')) {
          const dashboardScreenshot = await this.takeScreenshot('employer-dashboard-success');
          await this.log('Employer Login Success', 'PASS', 'Successfully logged in as employer', dashboardScreenshot);
          return true;
        } else {
          await this.log('Employer Login Success', 'FAIL', `Redirected to: ${currentUrl}`);
          return false;
        }
      } else {
        await this.log('Employer Login Form', 'FAIL', 'Insufficient input fields');
        return false;
      }
    } catch (error) {
      await this.log('Employer Login', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  }

  async testEmployerDashboardFeatures() {
    console.log('\n📊 TESTING EMPLOYER DASHBOARD FEATURES...');

    try {
      await this.page.goto(`${this.baseUrl}/dashboard`);
      await this.page.waitForTimeout(3000);

      const pageContent = await this.page.textContent('body');
      
      // Test 1: Check for employer-specific content
      const employerTerms = ['job', 'candidate', 'applicant', 'hire', 'posting', 'talent'];
      const foundTerms = employerTerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundTerms.length > 0) {
        await this.log('Employer Dashboard Content', 'PASS', `Found: ${foundTerms.join(', ')}`);
      } else {
        await this.log('Employer Dashboard Content', 'WARN', 'No employer-specific content detected');
      }

      // Test 2: Check for interactive elements
      const buttons = await this.page.locator('button').count();
      const links = await this.page.locator('a').count();

      await this.log('Dashboard Interactivity', 'PASS', `${buttons} buttons, ${links} links available`);

      // Test 3: Look for job management features
      const jobKeywords = ['Frontend Developer', 'job', 'position', 'posting'];
      const hasJobContent = jobKeywords.some(keyword => 
        pageContent.toLowerCase().includes(keyword.toLowerCase())
      );

      if (hasJobContent) {
        await this.log('Job Management Content', 'PASS', 'Job-related content found');
      } else {
        await this.log('Job Management Content', 'WARN', 'No job content detected');
      }

      const screenshot = await this.takeScreenshot('dashboard-features');
      await this.log('Dashboard Features Screenshot', 'PASS', 'Dashboard features captured', screenshot);

    } catch (error) {
      await this.log('Employer Dashboard Features Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerNavigation() {
    console.log('\n🧭 TESTING EMPLOYER NAVIGATION...');

    const employerPages = [
      { name: 'Dashboard', path: '/dashboard' },
      { name: 'View Matches', path: '/view-matches' },
      { name: 'Path Coach', path: '/path-coach' },
      { name: 'Profile', path: '/profile' }
    ];

    for (const page of employerPages) {
      try {
        await this.page.goto(`${this.baseUrl}${page.path}`);
        await this.page.waitForTimeout(2000);

        const currentUrl = this.page.url();
        const pageContent = await this.page.textContent('body');

        if (currentUrl.includes(page.path) || currentUrl.includes('/dashboard')) {
          await this.log(`${page.name} Navigation`, 'PASS', `Successfully accessed ${page.path}`);
          
          // Check if page has meaningful content
          if (pageContent.length > 100) {
            await this.log(`${page.name} Content`, 'PASS', `Page has content (${pageContent.length} chars)`);
          } else {
            await this.log(`${page.name} Content`, 'WARN', 'Page has minimal content');
          }
        } else {
          await this.log(`${page.name} Navigation`, 'WARN', `Redirected to ${currentUrl}`);
        }

      } catch (error) {
        await this.log(`${page.name} Navigation Test`, 'FAIL', `Error: ${error.message}`);
      }
    }
  }

  async testEmployerAICoach() {
    console.log('\n🤖 TESTING EMPLOYER AI COACH...');

    try {
      await this.page.goto(`${this.baseUrl}/path-coach`);
      await this.page.waitForTimeout(4000);

      const pageContent = await this.page.textContent('body');
      
      // Check for employer-specific AI content
      const employerAITerms = ['hiring', 'recruitment', 'talent', 'interview', 'employer'];
      const foundAITerms = employerAITerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundAITerms.length > 0) {
        await this.log('Employer AI Content', 'PASS', `Found: ${foundAITerms.join(', ')}`);
      } else {
        await this.log('Employer AI Content', 'WARN', 'No employer-specific AI content');
      }

      // Test chat interface
      const chatInputs = await this.page.locator('input, textarea').count();
      
      if (chatInputs > 0) {
        await this.log('AI Chat Interface', 'PASS', `Found ${chatInputs} input fields`);
        
        // Try to send a message
        const firstInput = this.page.locator('input, textarea').first();
        if (await firstInput.isVisible()) {
          await firstInput.fill('How can I improve my hiring process?');
          await this.log('AI Chat Input', 'PASS', 'Successfully entered employer query');
          
          // Look for send button
          const buttons = await this.page.locator('button').all();
          for (const button of buttons) {
            const buttonText = await button.textContent();
            if (buttonText && buttonText.toLowerCase().includes('send')) {
              await button.click();
              await this.page.waitForTimeout(3000);
              await this.log('AI Chat Send', 'PASS', 'Successfully sent message');
              break;
            }
          }
        }
      } else {
        await this.log('AI Chat Interface', 'WARN', 'No chat input found');
      }

      const screenshot = await this.takeScreenshot('ai-coach');
      await this.log('AI Coach Screenshot', 'PASS', 'AI Coach screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer AI Coach Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerMatches() {
    console.log('\n🎯 TESTING EMPLOYER MATCHES...');

    try {
      await this.page.goto(`${this.baseUrl}/view-matches`);
      await this.page.waitForTimeout(3000);

      const pageContent = await this.page.textContent('body');
      
      // Check for matching content
      const matchTerms = ['match', 'candidate', 'worker', 'score', 'recommend'];
      const foundMatchTerms = matchTerms.filter(term => 
        pageContent.toLowerCase().includes(term.toLowerCase())
      );

      if (foundMatchTerms.length > 0) {
        await this.log('Employer Matches Content', 'PASS', `Found: ${foundMatchTerms.join(', ')}`);
      } else {
        await this.log('Employer Matches Content', 'WARN', 'No matching content detected');
      }

      // Check for interactive elements
      const buttons = await this.page.locator('button').count();
      if (buttons > 0) {
        await this.log('Matches Interactivity', 'PASS', `${buttons} interactive buttons found`);
      } else {
        await this.log('Matches Interactivity', 'WARN', 'No interactive elements found');
      }

      const screenshot = await this.takeScreenshot('matches');
      await this.log('Matches Screenshot', 'PASS', 'Matches screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Matches Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testEmployerProfile() {
    console.log('\n👤 TESTING EMPLOYER PROFILE...');

    try {
      await this.page.goto(`${this.baseUrl}/profile`);
      await this.page.waitForTimeout(3000);

      // Check for profile form
      const inputs = await this.page.locator('input, textarea, select').count();
      
      if (inputs > 0) {
        await this.log('Employer Profile Form', 'PASS', `Found ${inputs} form fields`);
        
        // Test editing capability
        const firstInput = this.page.locator('input').first();
        if (await firstInput.isVisible()) {
          await firstInput.fill('Test Employer Profile');
          await this.log('Profile Editing', 'PASS', 'Profile form is editable');
        }
      } else {
        await this.log('Employer Profile Form', 'WARN', 'No profile form found');
      }

      const screenshot = await this.takeScreenshot('profile');
      await this.log('Profile Screenshot', 'PASS', 'Profile screenshot captured', screenshot);

    } catch (error) {
      await this.log('Employer Profile Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async runFinalEmployerTests() {
    console.log('🚀 STARTING FINAL EMPLOYER UI TESTING...\n');
    console.log('This will comprehensively test all employer features.\n');

    try {
      await this.setup();

      // Login as employer
      const loginSuccess = await this.loginAsEmployer();
      
      if (loginSuccess) {
        await this.testEmployerDashboardFeatures();
        await this.testEmployerNavigation();
        await this.testEmployerAICoach();
        await this.testEmployerMatches();
        await this.testEmployerProfile();
      } else {
        console.log('❌ Could not login as employer - testing limited features');
      }

    } catch (error) {
      console.error('Final employer testing failed:', error);
    } finally {
      await this.cleanup();
    }

    // Generate final report
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const warningTests = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 FINAL EMPLOYER UI TEST SUMMARY:');
    console.log('='.repeat(70));
    console.log(`✅ PASSED: ${passedTests}`);
    console.log(`❌ FAILED: ${failedTests}`);
    console.log(`⚠️  WARNINGS: ${warningTests}`);
    console.log(`📈 EMPLOYER UI SUCCESS RATE: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('='.repeat(70));

    // Detailed breakdown
    const categories = {};
    this.results.forEach(result => {
      const category = result.test.split(' ')[0];
      if (!categories[category]) {
        categories[category] = { pass: 0, fail: 0, warn: 0 };
      }
      categories[category][result.status.toLowerCase()]++;
    });

    console.log('\n📋 EMPLOYER FEATURES BREAKDOWN:');
    Object.entries(categories).forEach(([category, stats]) => {
      const total = stats.pass + stats.fail + stats.warn;
      const successRate = ((stats.pass / total) * 100).toFixed(1);
      console.log(`${category}: ${stats.pass}/${total} (${successRate}%)`);
    });

    // Save report
    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        warningTests,
        successRate: (passedTests / totalTests) * 100
      },
      categories,
      results: this.results,
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('employer-final-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Final employer test report saved to employer-final-test-report.json');
    console.log('📸 All screenshots saved in the screenshots/ directory');

    // Final assessment
    console.log('\n🎯 FINAL EMPLOYER ASSESSMENT:');
    if (passedTests / totalTests >= 0.75) {
      console.log('   ✅ EMPLOYER FEATURES ARE WORKING EXCELLENTLY!');
      console.log('   ✅ All major employer functionality is operational');
      console.log('   ✅ Employers can effectively use PathLink');
      console.log('   ✅ Ready for employer user testing and production');
    } else if (passedTests / totalTests >= 0.5) {
      console.log('   ⚠️  EMPLOYER FEATURES ARE MOSTLY FUNCTIONAL');
      console.log('   🔧 Some areas may need minor improvements');
      console.log('   ✅ Core employer functionality is working');
      console.log('   📈 Consider addressing warnings for better UX');
    } else {
      console.log('   ❌ EMPLOYER FEATURES NEED ATTENTION');
      console.log('   🔧 Critical issues need to be addressed');
      console.log('   📋 Review failed tests for improvements');
    }

    return report;
  }
}

// Run final employer tests
const tester = new EmployerFinalTest();
tester.runFinalEmployerTests().catch(console.error);
