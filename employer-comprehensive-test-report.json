{"summary": {"totalTests": 2, "passedTests": 1, "failedTests": 1, "warningTests": 0, "successRate": 50}, "categories": {"Employer": {"pass": 1, "fail": 1, "warn": 0}}, "results": [{"test": "Employer Login Form", "status": "PASS", "details": "Employer credentials entered", "screenshot": "screenshots/employer-employer-login-form-1749181355412.png", "timestamp": "2025-06-06T03:42:35.529Z"}, {"test": "Employer <PERSON>", "status": "FAIL", "details": "Error: locator.click: Error: strict mode violation: locator('button:has-text(\"Sign In\")') resolved to 5 elements:\n    1) <button type=\"submit\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full\">Sign In</button> aka getByRole('button', { name: 'Sign In' })\n    2) <button type=\"button\" class=\"whitespace-nowrap rounded-md font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground flex items-center gap-1 sm:gap-2 border transition-colors duration-200 px-2 sm:px-4 py-2 min-h-[40px] bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 text-g…>…</button> aka getByRole('button', { name: 'Continue with Google' })\n    3) <button type=\"button\" class=\"whitespace-nowrap rounded-md font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground flex items-center gap-1 sm:gap-2 border transition-colors duration-200 px-2 sm:px-4 py-2 min-h-[40px] bg-[#1877F2] hover:bg-[#166FE5] text-white border-[#1877F2] w-full justif…>…</button> aka getByRole('button', { name: 'Continue with Facebook' })\n    4) <button type=\"button\" class=\"whitespace-nowrap rounded-md font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground flex items-center gap-1 sm:gap-2 border transition-colors duration-200 px-2 sm:px-4 py-2 min-h-[40px] bg-black dark:bg-black hover:bg-gray-900 dark:hover:bg-gray-900 text-whit…>…</button> aka getByRole('button', { name: 'Continue with Apple' })\n    5) <button type=\"button\" class=\"whitespace-nowrap rounded-md font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 hover:text-accent-foreground flex items-center gap-1 sm:gap-2 border transition-colors duration-200 px-2 sm:px-4 py-2 min-h-[40px] bg-[#0A66C2] hover:bg-[#004182] text-white border-[#0A66C2] w-full justif…>…</button> aka getByRole('button', { name: 'Continue with LinkedIn' })\n\nCall log:\n\u001b[2m  - waiting for locator('button:has-text(\"Sign In\")')\u001b[22m\n", "screenshot": null, "timestamp": "2025-06-06T03:42:36.547Z"}], "timestamp": "2025-06-06T03:42:36.853Z"}