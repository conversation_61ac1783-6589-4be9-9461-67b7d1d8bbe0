/**
 * Match Analysis Helper Functions
 *
 * This file contains functions for analyzing job listings and resumes
 * to calculate match scores and extract relevant information.
 */

/**
 * Extract skills from a job listing
 */
export function extractSkillsFromJob(job: any): string[] {
  const skills: string[] = [];

  // Extract skills from required_skills field using enhanced NLP extraction
  if (job.required_skills) {
    const extractedSkills = extractSkillsFromText(job.required_skills);
    skills.push(...extractedSkills);
  }

  // Extract skills from job description using NLP techniques
  if (job.description) {
    const extractedSkills = extractSkillsFromText(job.description);
    skills.push(...extractedSkills);
  }

  // COMPREHENSIVE filter for ALL non-contributory terms that employers commonly use
  const filterWords = new Set([
    // Basic words
    'join', 'you', 'responsibilities', 'requirements', 'qualifications', 'preferred', 'nice',
    'have', 'must', 'should', 'will', 'can', 'able', 'experience', 'years', 'year', 'strong',
    'excellent', 'good', 'solid', 'proven', 'demonstrated', 'working', 'knowledge', 'understanding',
    'familiarity', 'proficiency', 'skills', 'ability', 'capabilities', 'background', 'expertise',

    // Job level descriptors
    'user-friendly', 'backend', 'frontend', 'full-stack', 'senior', 'junior', 'mid-level',
    'entry-level', 'lead', 'principal', 'architect', 'engineer', 'developer', 'analyst',
    'manager', 'director', 'coordinator', 'specialist', 'consultant', 'administrator',

    // Common words
    'and', 'or', 'with', 'in', 'of', 'for', 'to', 'the', 'a', 'an', 'is', 'are', 'be', 'been',
    'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
    'may', 'might', 'must', 'shall', 'can', 'cannot', 'plus', 'including', 'such', 'as',
    'like', 'similar', 'related', 'relevant', 'applicable', 'appropriate', 'suitable',
    'required', 'preferred', 'desired', 'ideal', 'bonus', 'advantage', 'asset', 'less',
    'more', 'most', 'some', 'any', 'all', 'each', 'every', 'other', 'another', 'both',
    'either', 'neither', 'none', 'few', 'many', 'much', 'several', 'various', 'different',

    // Employer filler words that add no value
    'proficient', 'frameworks', 'libraries', 'tools', 'technologies', 'platforms', 'systems',
    'applications', 'software', 'development', 'programming', 'coding', 'building', 'creating',
    'designing', 'implementing', 'maintaining', 'testing', 'debugging', 'optimizing',
    'collaboration', 'communication', 'teamwork', 'leadership', 'management', 'planning',
    'cross-browser', 'compatibility', 'responsive', 'design', 'version', 'control', 'apis',
    'restful', 'web', 'mobile', 'desktop', 'client', 'server', 'database', 'storage',

    // Additional employer buzzwords
    'innovative', 'cutting-edge', 'state-of-the-art', 'best-practices', 'industry-standard',
    'scalable', 'robust', 'efficient', 'effective', 'reliable', 'secure', 'performance',
    'optimization', 'integration', 'implementation', 'deployment', 'maintenance', 'support',
    'troubleshooting', 'problem-solving', 'analytical', 'detail-oriented', 'self-motivated',
    'team-player', 'fast-paced', 'dynamic', 'environment', 'opportunity', 'growth',
    'career', 'professional', 'passionate', 'dedicated', 'committed', 'enthusiastic',

    // Technical buzzwords that aren't specific skills
    'stack', 'end-to-end', 'full-cycle', 'lifecycle', 'methodology', 'practices', 'standards',
    'protocols', 'procedures', 'processes', 'workflows', 'pipelines', 'architecture',
    'infrastructure', 'ecosystem', 'environment', 'platform-agnostic', 'cross-platform',
    'multi-platform', 'enterprise', 'commercial', 'open-source', 'proprietary',

    // Experience-related terms
    'hands-on', 'practical', 'real-world', 'production', 'commercial', 'enterprise-level',
    'large-scale', 'high-volume', 'mission-critical', 'business-critical', 'customer-facing',
    'user-facing', 'public-facing', 'internal', 'external', 'third-party', 'vendor',

    // Soft skill descriptors
    'interpersonal', 'organizational', 'multitasking', 'prioritization', 'time-management',
    'project-management', 'stakeholder', 'client-facing', 'customer-service', 'business',
    'technical', 'functional', 'non-functional', 'operational', 'strategic', 'tactical'
  ]);

  // Remove duplicates and filter out non-contributory words
  const uniqueSkills = [...new Set(skills)]
    .filter(skill => skill && skill.length > 1)
    .filter(skill => !filterWords.has(skill.toLowerCase()));

  console.log(`🚀 ENHANCED ALGORITHM IS WORKING! Job: "${job.title}"`);
  console.log(`🔍 Raw skills extracted: ${skills.length} skills`);
  console.log(`✅ Filtered skills: ${uniqueSkills.length} skills`);
  console.log(`📋 Final job skills: ${uniqueSkills.join(', ')}`);

  return uniqueSkills;
}

/**
 * Extract skills from resume text
 */
export function extractSkillsFromResume(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return []; // Return empty array if text is not a string
  }

  // Use NLP techniques to extract skills from resume text
  return extractSkillsFromText(text);
}

/**
 * Extract skills from text using advanced NLP techniques and custom keyword extraction
 */
export function extractSkillsFromText(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return []; // Return empty array if text is not a string
  }

  const lowerText = text.toLowerCase();
  const foundSkills: string[] = [];

  // 1. Extract skills using comprehensive skill patterns
  const skillPatterns = getComprehensiveSkillPatterns();

  for (const category of Object.keys(skillPatterns)) {
    for (const skill of skillPatterns[category]) {
      if (lowerText.includes(skill.toLowerCase())) {
        foundSkills.push(skill);
      }
    }
  }

  // 2. Extract custom keywords using NLP patterns
  const customSkills = extractCustomKeywords(text);
  foundSkills.push(...customSkills);

  // 3. Extract acronyms and abbreviations
  const acronyms = extractAcronyms(text);
  foundSkills.push(...acronyms);

  // 4. Extract compound skills (e.g., "React.js", "Node.js")
  const compoundSkills = extractCompoundSkills(text);
  foundSkills.push(...compoundSkills);

  // 5. Extract version-specific skills (e.g., "Python 3.9", "Java 11")
  const versionSkills = extractVersionSpecificSkills(text);
  foundSkills.push(...versionSkills);

  // Remove duplicates and return
  return [...new Set(foundSkills)];
}

/**
 * Get comprehensive skill patterns for all industries and categories
 */
function getComprehensiveSkillPatterns(): Record<string, string[]> {
  return {
    // Programming Languages
    programmingLanguages: [
      "JavaScript", "TypeScript", "Python", "Java", "C++", "C#", "C", "Ruby", "PHP", "Swift",
      "Kotlin", "Go", "Rust", "Scala", "R", "MATLAB", "Perl", "Objective-C", "Dart", "F#",
      "Haskell", "Clojure", "Erlang", "Elixir", "Julia", "Lua", "Shell", "Bash", "PowerShell"
    ],

    // Web Technologies
    webTechnologies: [
      "HTML", "CSS", "SASS", "SCSS", "LESS", "Stylus", "Bootstrap", "Tailwind CSS", "Bulma",
      "Material-UI", "Ant Design", "Chakra UI", "jQuery", "React", "Angular", "Vue.js", "Svelte",
      "Next.js", "Nuxt.js", "Gatsby", "Express.js", "Koa.js", "Fastify", "NestJS", "Django",
      "Flask", "FastAPI", "Spring Boot", "ASP.NET", "Laravel", "Symfony", "CodeIgniter", "Rails"
    ],

    // Databases
    databases: [
      "MySQL", "PostgreSQL", "SQLite", "Oracle", "SQL Server", "MongoDB", "CouchDB", "Cassandra",
      "Redis", "Elasticsearch", "Neo4j", "DynamoDB", "Firebase", "Supabase", "PlanetScale",
      "InfluxDB", "TimescaleDB", "MariaDB", "Amazon RDS", "Google Cloud SQL"
    ],

    // Cloud & DevOps
    cloudDevOps: [
      "AWS", "Azure", "Google Cloud", "GCP", "Docker", "Kubernetes", "Jenkins", "GitLab CI",
      "GitHub Actions", "CircleCI", "Travis CI", "Terraform", "Ansible", "Chef", "Puppet",
      "Vagrant", "Nginx", "Apache", "Load Balancing", "Microservices", "Serverless", "Lambda",
      "CloudFormation", "ARM Templates", "Helm", "Istio", "Prometheus", "Grafana", "ELK Stack"
    ],

    // Data Science & AI
    dataScience: [
      "Machine Learning", "Deep Learning", "Neural Networks", "TensorFlow", "PyTorch", "Keras",
      "Scikit-learn", "Pandas", "NumPy", "Matplotlib", "Seaborn", "Plotly", "Jupyter", "Apache Spark",
      "Hadoop", "Kafka", "Airflow", "MLflow", "Kubeflow", "Data Mining", "Statistics", "A/B Testing",
      "Computer Vision", "NLP", "Natural Language Processing", "OpenCV", "NLTK", "spaCy", "Transformers"
    ],

    // Mobile Development
    mobile: [
      "iOS", "Android", "React Native", "Flutter", "Xamarin", "Ionic", "Cordova", "PhoneGap",
      "Swift", "Objective-C", "Kotlin", "Java", "Dart", "UIKit", "SwiftUI", "Jetpack Compose",
      "Core Data", "Realm", "SQLite", "Firebase", "Push Notifications", "App Store", "Google Play"
    ],

    // Design & UX
    design: [
      "UI/UX Design", "User Experience", "User Interface", "Figma", "Sketch", "Adobe XD",
      "InVision", "Zeplin", "Marvel", "Principle", "Framer", "Photoshop", "Illustrator",
      "After Effects", "Wireframing", "Prototyping", "User Research", "Usability Testing",
      "Design Systems", "Accessibility", "WCAG", "Information Architecture", "Interaction Design"
    ],

    // Business & Management
    business: [
      "Project Management", "Agile", "Scrum", "Kanban", "Lean", "Six Sigma", "PMP", "PRINCE2",
      "Jira", "Confluence", "Trello", "Asana", "Monday.com", "Slack", "Microsoft Teams",
      "Leadership", "Team Management", "Strategic Planning", "Business Analysis", "Requirements Gathering",
      "Stakeholder Management", "Risk Management", "Change Management", "Process Improvement"
    ],

    // Marketing & Sales
    marketing: [
      "Digital Marketing", "SEO", "SEM", "Google Ads", "Facebook Ads", "LinkedIn Ads", "Content Marketing",
      "Email Marketing", "Social Media Marketing", "Influencer Marketing", "Affiliate Marketing",
      "Marketing Automation", "HubSpot", "Salesforce", "Marketo", "Mailchimp", "Google Analytics",
      "Google Tag Manager", "A/B Testing", "Conversion Optimization", "CRM", "Lead Generation"
    ],

    // Finance & Accounting
    finance: [
      "Financial Analysis", "Financial Modeling", "Budgeting", "Forecasting", "Accounting",
      "Bookkeeping", "Tax Preparation", "Audit", "Compliance", "Risk Management", "Investment Analysis",
      "Portfolio Management", "Trading", "Derivatives", "Fixed Income", "Equity Research",
      "QuickBooks", "SAP", "Oracle Financials", "Excel", "VBA", "SQL", "Python", "R"
    ],

    // Healthcare
    healthcare: [
      "Electronic Health Records", "EHR", "EMR", "HIPAA", "Medical Coding", "ICD-10", "CPT",
      "Clinical Research", "FDA Regulations", "GCP", "Medical Device", "Pharmaceutical",
      "Biotechnology", "Laboratory", "Radiology", "Nursing", "Patient Care", "Telemedicine",
      "Healthcare Analytics", "Population Health", "Quality Improvement", "Clinical Trials"
    ],

    // Soft Skills
    softSkills: [
      "Communication", "Teamwork", "Leadership", "Problem Solving", "Critical Thinking",
      "Time Management", "Adaptability", "Creativity", "Work Ethic", "Interpersonal Skills",
      "Decision Making", "Conflict Resolution", "Negotiation", "Presentation", "Public Speaking",
      "Customer Service", "Attention to Detail", "Organization", "Multitasking", "Flexibility",
      "Emotional Intelligence", "Collaboration", "Innovation", "Analytical Thinking", "Strategic Thinking"
    ]
  };
}

/**
 * Extract custom keywords using NLP patterns
 */
function extractCustomKeywords(text: string): string[] {
  const keywords: string[] = [];

  // Pattern 1: Extract capitalized words that might be technologies or tools
  const capitalizedWords = text.match(/\b[A-Z][a-zA-Z0-9]*(?:\.[a-zA-Z0-9]+)*\b/g) || [];
  keywords.push(...capitalizedWords.filter(word => word.length > 2));

  // Pattern 2: Extract words followed by common tech suffixes
  const techSuffixes = ['.js', '.py', '.rb', '.php', '.go', '.rs', '.kt', '.swift'];
  for (const suffix of techSuffixes) {
    const regex = new RegExp(`\\b\\w+\\${suffix.replace('.', '\\.')}\\b`, 'gi');
    const matches = text.match(regex) || [];
    keywords.push(...matches);
  }

  // Pattern 3: Extract words in quotes (often indicating specific tools or technologies)
  const quotedWords = text.match(/"([^"]+)"/g) || [];
  keywords.push(...quotedWords.map(word => word.replace(/"/g, '')));

  // Pattern 4: Extract words with common tech patterns
  const techPatterns = [
    /\b\w+(?:API|SDK|CLI|IDE|CRM|ERP|CMS|LMS)\b/gi,
    /\b(?:micro|macro|multi|cross|full|back|front|end|stack)\w+/gi,
    /\b\w+(?:ware|soft|tech|sys|app|web|mobile|cloud|data|base|server|client)\b/gi
  ];

  for (const pattern of techPatterns) {
    const matches = text.match(pattern) || [];
    keywords.push(...matches);
  }

  return keywords;
}

/**
 * Extract acronyms and abbreviations
 */
function extractAcronyms(text: string): string[] {
  // Extract 2-6 letter uppercase acronyms
  const acronyms = text.match(/\b[A-Z]{2,6}\b/g) || [];

  // Filter out common words that aren't acronyms
  const commonWords = ['THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BUT', 'HIS', 'HAS', 'HAD'];
  return acronyms.filter(acronym => !commonWords.includes(acronym));
}

/**
 * Extract compound skills (skills with dots, hyphens, or slashes)
 */
function extractCompoundSkills(text: string): string[] {
  const compounds: string[] = [];

  // Pattern for dotted skills (React.js, Node.js, Vue.js)
  const dottedSkills = text.match(/\b\w+\.\w+\b/g) || [];
  compounds.push(...dottedSkills);

  // Pattern for hyphenated skills (full-stack, cross-platform)
  const hyphenatedSkills = text.match(/\b\w+-\w+(?:-\w+)*\b/g) || [];
  compounds.push(...hyphenatedSkills);

  // Pattern for slashed skills (HTML/CSS, CI/CD)
  const slashedSkills = text.match(/\b\w+\/\w+(?:\/\w+)*\b/g) || [];
  compounds.push(...slashedSkills);

  return compounds;
}

/**
 * Extract version-specific skills
 */
function extractVersionSpecificSkills(text: string): string[] {
  const versionSkills: string[] = [];

  // Pattern for version numbers (Python 3.9, Java 11, .NET 5)
  const versionPattern = /\b(\w+(?:\.\w+)?)\s+(?:v?(\d+(?:\.\d+)*)|(\d+(?:\.\d+)*))\b/gi;
  const matches = text.matchAll(versionPattern);

  for (const match of matches) {
    const skill = match[1];
    const version = match[2] || match[3];
    if (skill && version) {
      versionSkills.push(`${skill} ${version}`);
      versionSkills.push(skill); // Also add the base skill
    }
  }

  return versionSkills;
}

/**
 * Extract education requirements from a job listing
 */
export function extractEducationFromJob(job: any): string {
  // Extract education from job description
  const description = job.description?.toLowerCase() || '';

  if (description.includes('phd') || description.includes('doctorate')) {
    return 'PhD';
  } else if (description.includes('master') || description.includes('ms') || description.includes('ma')) {
    return 'Master';
  } else if (description.includes('bachelor') || description.includes('bs') || description.includes('ba')) {
    return 'Bachelor';
  } else if (description.includes('associate')) {
    return 'Associate';
  } else if (description.includes('high school')) {
    return 'High School';
  }

  return 'Not Specified';
}

/**
 * Extract education information from resume text
 */
export function extractEducationFromResume(text: string): string {
  if (!text || typeof text !== 'string') {
    return 'Not Specified'; // Default if text is not a string
  }

  // Extract education from resume text
  const lowerText = text.toLowerCase();

  if (lowerText.includes('phd') || lowerText.includes('doctorate')) {
    return 'PhD';
  } else if (lowerText.includes('master') || lowerText.includes('ms') || lowerText.includes('ma')) {
    return 'Master';
  } else if (lowerText.includes('bachelor') || lowerText.includes('bs') || lowerText.includes('ba')) {
    return 'Bachelor';
  } else if (lowerText.includes('associate')) {
    return 'Associate';
  } else if (lowerText.includes('high school')) {
    return 'High School';
  }

  return 'Not Specified';
}

/**
 * Extract experience requirements from a job listing
 */
export function extractExperienceFromJob(job: any): number {
  // Extract years from minimum_experience field
  if (job.minimum_experience) {
    const yearsMatch = job.minimum_experience.match(/(\d+)/);
    if (yearsMatch) {
      return parseInt(yearsMatch[1]);
    }
  }

  // Extract years from job description
  if (job.description) {
    const yearsMatch = job.description.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of)?\s*(?:experience)/i);
    if (yearsMatch) {
      return parseInt(yearsMatch[1]);
    }
  }

  return 0; // Default to 0 years if not specified
}

/**
 * Extract experience information from resume text
 */
export function extractExperienceFromResume(text: string): number {
  if (!text || typeof text !== 'string') {
    return 0; // Default to 0 years if text is not a string
  }

  // Extract years from resume text
  const yearsMatch = text.match(/(\d+)\s*(?:years?|yrs?)\s*(?:of)?\s*(?:experience)/i);
  if (yearsMatch) {
    return parseInt(yearsMatch[1]);
  }

  return 0; // Default to 0 years if not specified
}

/**
 * Calculate skills match between job requirements and resume skills using advanced matching
 */
export function calculateSkillsMatch(jobSkills: string[], resumeSkills: string[]) {
  const matchedSkills: string[] = [];
  const partialMatches: Array<{job: string, resume: string, confidence: number}> = [];
  const missingSkills: string[] = [];

  // Normalize skills for better matching
  const normalizedJobSkills = jobSkills.map(skill => normalizeSkill(skill));
  const normalizedResumeSkills = resumeSkills.map(skill => normalizeSkill(skill));

  for (const jobSkill of jobSkills) {
    const normalizedJobSkill = normalizeSkill(jobSkill);
    let bestMatch = null;
    let bestConfidence = 0;

    for (const resumeSkill of resumeSkills) {
      const normalizedResumeSkill = normalizeSkill(resumeSkill);
      const confidence = calculateSkillSimilarity(normalizedJobSkill, normalizedResumeSkill);

      if (confidence > bestConfidence) {
        bestMatch = resumeSkill;
        bestConfidence = confidence;
      }
    }

    if (bestConfidence >= 0.8) {
      // High confidence match
      matchedSkills.push(jobSkill);
    } else if (bestConfidence >= 0.5) {
      // Partial match
      partialMatches.push({
        job: jobSkill,
        resume: bestMatch!,
        confidence: bestConfidence
      });
    } else {
      // No match found
      missingSkills.push(jobSkill);
    }
  }

  // Calculate weighted score considering partial matches
  const exactMatchScore = matchedSkills.length;
  const partialMatchScore = partialMatches.reduce((sum, match) => sum + match.confidence, 0);
  const totalPossibleScore = jobSkills.length;

  const score = totalPossibleScore > 0
    ? Math.round(((exactMatchScore + partialMatchScore) / totalPossibleScore) * 100)
    : 50;

  return {
    score,
    matched: matchedSkills,
    partialMatches,
    missing: missingSkills,
    description: generateSkillsMatchDescription(matchedSkills, partialMatches, missingSkills, jobSkills.length)
  };
}

/**
 * Normalize skill names for better matching
 */
function normalizeSkill(skill: string): string {
  return skill
    .toLowerCase()
    .replace(/[^\w\s]/g, '') // Remove special characters
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

/**
 * Calculate similarity between two skills using multiple techniques
 */
function calculateSkillSimilarity(skill1: string, skill2: string): number {
  // Exact match
  if (skill1 === skill2) return 1.0;

  // Check if one skill contains the other
  if (skill1.includes(skill2) || skill2.includes(skill1)) {
    const longer = skill1.length > skill2.length ? skill1 : skill2;
    const shorter = skill1.length <= skill2.length ? skill1 : skill2;
    return shorter.length / longer.length;
  }

  // Check for common abbreviations and synonyms
  const synonyms = getSkillSynonyms();
  if (synonyms[skill1] && synonyms[skill1].includes(skill2)) return 0.9;
  if (synonyms[skill2] && synonyms[skill2].includes(skill1)) return 0.9;

  // Levenshtein distance for similar spellings
  const distance = levenshteinDistance(skill1, skill2);
  const maxLength = Math.max(skill1.length, skill2.length);
  const similarity = 1 - (distance / maxLength);

  // Only consider it a match if similarity is above threshold
  return similarity > 0.7 ? similarity : 0;
}

/**
 * Get skill synonyms and abbreviations
 */
function getSkillSynonyms(): Record<string, string[]> {
  return {
    'javascript': ['js', 'ecmascript', 'es6', 'es2015', 'es2020'],
    'typescript': ['ts'],
    'python': ['py'],
    'artificial intelligence': ['ai', 'machine learning', 'ml'],
    'machine learning': ['ml', 'artificial intelligence', 'ai'],
    'natural language processing': ['nlp'],
    'user interface': ['ui'],
    'user experience': ['ux'],
    'ui/ux': ['user interface', 'user experience', 'ui', 'ux'],
    'cascading style sheets': ['css'],
    'hypertext markup language': ['html'],
    'structured query language': ['sql'],
    'application programming interface': ['api'],
    'software development kit': ['sdk'],
    'command line interface': ['cli'],
    'integrated development environment': ['ide'],
    'customer relationship management': ['crm'],
    'enterprise resource planning': ['erp'],
    'content management system': ['cms'],
    'learning management system': ['lms'],
    'amazon web services': ['aws'],
    'google cloud platform': ['gcp', 'google cloud'],
    'microsoft azure': ['azure'],
    'continuous integration': ['ci'],
    'continuous deployment': ['cd'],
    'ci/cd': ['continuous integration', 'continuous deployment'],
    'react': ['reactjs', 'react.js'],
    'angular': ['angularjs', 'angular.js'],
    'vue': ['vuejs', 'vue.js'],
    'node': ['nodejs', 'node.js'],
    'express': ['expressjs', 'express.js'],
    'mongodb': ['mongo'],
    'postgresql': ['postgres'],
    'mysql': ['my sql'],
    'github': ['git hub'],
    'gitlab': ['git lab'],
    'docker': ['containerization'],
    'kubernetes': ['k8s', 'container orchestration']
  };
}

/**
 * Calculate Levenshtein distance between two strings
 */
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Generate a detailed description of skills matching
 */
function generateSkillsMatchDescription(
  matched: string[],
  partialMatches: Array<{job: string, resume: string, confidence: number}>,
  missing: string[],
  totalRequired: number
): string {
  let description = `Matches ${matched.length} out of ${totalRequired} required skills exactly.`;

  if (partialMatches.length > 0) {
    description += ` Has ${partialMatches.length} partial skill matches.`;
  }

  if (missing.length > 0) {
    description += ` Missing ${missing.length} required skills.`;
  }

  return description;
}

/**
 * Calculate experience match between job requirements and resume experience
 */
export function calculateExperienceMatch(requiredYears: number, resumeYears: number) {
  let score = 0;
  let description = "";

  if (requiredYears === 0) {
    // No specific experience required
    score = 80;
    description = `No specific experience requirement. Candidate has ${resumeYears} years of experience.`;
  } else if (resumeYears >= requiredYears) {
    // Meets or exceeds requirements
    const exceededBy = resumeYears - requiredYears;
    score = Math.min(100, 80 + exceededBy * 5);
    description = `Exceeds required experience by ${exceededBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
  } else {
    // Below requirements
    const shortBy = requiredYears - resumeYears;
    score = Math.max(30, 70 - shortBy * 10);
    description = `Below required experience by ${shortBy} years. Required: ${requiredYears} years, Candidate: ${resumeYears} years.`;
  }

  return { score, description };
}

/**
 * Calculate education match between job requirements and resume education
 */
export function calculateEducationMatch(requiredEducation: string, resumeEducation: string) {
  // Education levels in ascending order
  const educationLevels = [
    'Not Specified',
    'High School',
    'Associate',
    'Bachelor',
    'Master',
    'PhD'
  ];

  // Get indices for comparison
  const requiredIndex = educationLevels.indexOf(requiredEducation);
  const resumeIndex = educationLevels.indexOf(resumeEducation);

  let score = 0;
  let description = "";

  if (requiredIndex === 0) {
    // No specific education required
    score = 80;
    description = `No specific education requirement. Candidate has ${resumeEducation} degree.`;
  } else if (resumeIndex >= requiredIndex) {
    // Meets or exceeds requirements
    score = 100;
    description = `Meets or exceeds education requirements. Required: ${requiredEducation}, Candidate: ${resumeEducation}.`;
  } else {
    // Below requirements
    score = Math.max(30, 70 - (requiredIndex - resumeIndex) * 20);
    description = `Below education requirements. Required: ${requiredEducation}, Candidate: ${resumeEducation}.`;
  }

  return { score, description };
}

/**
 * Calculate industry match between job industry and resume text
 */
export function calculateIndustryMatch(jobIndustry: string, resumeText: string) {
  if (!jobIndustry) {
    return {
      score: 50,
      description: "No specific industry requirement specified."
    };
  }

  if (!resumeText || typeof resumeText !== 'string') {
    return {
      score: 40,
      description: `Unable to determine if candidate has experience in the ${jobIndustry} industry.`
    };
  }

  // Check if resume mentions the job industry
  const industryMatch = resumeText.toLowerCase().includes(jobIndustry.toLowerCase());

  let score = 0;
  let description = "";

  if (industryMatch) {
    score = 90;
    description = `Candidate has experience in the ${jobIndustry} industry.`;
  } else {
    // Check for related industries
    const relatedIndustries = getRelatedIndustries(jobIndustry);
    const hasRelatedIndustry = relatedIndustries.some(industry =>
      resumeText.toLowerCase().includes(industry.toLowerCase())
    );

    if (hasRelatedIndustry) {
      score = 70;
      description = `Candidate has experience in industries related to ${jobIndustry}.`;
    } else {
      score = 40;
      description = `Candidate does not appear to have experience in the ${jobIndustry} industry.`;
    }
  }

  return { score, description };
}

/**
 * Get related industries for a given industry with comprehensive mapping
 */
export function getRelatedIndustries(industry: string): string[] {
  // Comprehensive map of industries to related industries and keywords
  const industryMap: Record<string, string[]> = {
    'Technology': [
      'Software', 'IT', 'Tech', 'Information Technology', 'Computer', 'Digital', 'SaaS', 'Cloud',
      'Cybersecurity', 'Data', 'Analytics', 'AI', 'Machine Learning', 'Blockchain', 'IoT',
      'Mobile', 'Web Development', 'DevOps', 'Agile', 'Startup', 'Innovation'
    ],
    'Finance': [
      'Banking', 'Investment', 'Financial Services', 'Insurance', 'Accounting', 'Fintech',
      'Trading', 'Asset Management', 'Wealth Management', 'Credit', 'Loans', 'Mortgage',
      'Risk Management', 'Compliance', 'Audit', 'Tax', 'Treasury', 'Capital Markets'
    ],
    'Healthcare': [
      'Medical', 'Health', 'Pharmaceutical', 'Biotech', 'Life Sciences', 'Clinical',
      'Hospital', 'Nursing', 'Therapy', 'Diagnostics', 'Medical Device', 'Telemedicine',
      'Health Tech', 'Wellness', 'Mental Health', 'Public Health', 'Research'
    ],
    'Education': [
      'Teaching', 'Academic', 'School', 'University', 'Training', 'Learning', 'EdTech',
      'Curriculum', 'Instruction', 'E-learning', 'Online Education', 'Corporate Training',
      'Professional Development', 'Certification', 'Assessment'
    ],
    'Manufacturing': [
      'Production', 'Industrial', 'Factory', 'Engineering', 'Automotive', 'Aerospace',
      'Electronics', 'Machinery', 'Assembly', 'Quality Control', 'Supply Chain',
      'Logistics', 'Operations', 'Lean Manufacturing', 'Six Sigma'
    ],
    'Retail': [
      'E-commerce', 'Sales', 'Consumer Goods', 'Merchandising', 'Fashion', 'Apparel',
      'Food & Beverage', 'Grocery', 'Department Store', 'Specialty Retail', 'Wholesale',
      'Distribution', 'Inventory Management', 'Customer Service'
    ],
    'Marketing': [
      'Advertising', 'PR', 'Communications', 'Media', 'Digital Marketing', 'Social Media',
      'Content Marketing', 'SEO', 'SEM', 'Brand Management', 'Market Research',
      'Campaign Management', 'Lead Generation', 'Growth Marketing', 'Influencer Marketing'
    ],
    'Consulting': [
      'Professional Services', 'Business Services', 'Advisory', 'Strategy', 'Management Consulting',
      'IT Consulting', 'Financial Advisory', 'HR Consulting', 'Operations Consulting',
      'Change Management', 'Business Analysis', 'Process Improvement'
    ],
    'Hospitality': [
      'Tourism', 'Hotel', 'Restaurant', 'Travel', 'Leisure', 'Entertainment', 'Events',
      'Catering', 'Food Service', 'Resort', 'Gaming', 'Recreation', 'Customer Experience'
    ],
    'Construction': [
      'Building', 'Architecture', 'Engineering', 'Real Estate', 'Infrastructure', 'Civil',
      'Residential', 'Commercial', 'Project Management', 'Contracting', 'Development',
      'Property Management', 'Urban Planning'
    ],
    'Energy': [
      'Oil & Gas', 'Renewable Energy', 'Solar', 'Wind', 'Utilities', 'Power Generation',
      'Energy Efficiency', 'Sustainability', 'Environmental', 'Green Technology'
    ],
    'Transportation': [
      'Logistics', 'Supply Chain', 'Shipping', 'Freight', 'Aviation', 'Maritime',
      'Rail', 'Trucking', 'Delivery', 'Warehousing', 'Distribution'
    ],
    'Government': [
      'Public Sector', 'Federal', 'State', 'Local Government', 'Military', 'Defense',
      'Public Administration', 'Policy', 'Regulatory', 'Non-profit', 'NGO'
    ],
    'Legal': [
      'Law', 'Attorney', 'Lawyer', 'Legal Services', 'Litigation', 'Corporate Law',
      'Intellectual Property', 'Compliance', 'Regulatory', 'Paralegal'
    ],
    'Media': [
      'Broadcasting', 'Publishing', 'Journalism', 'Content Creation', 'Film', 'Television',
      'Radio', 'Digital Media', 'Social Media', 'Gaming', 'Entertainment'
    ],
    'Agriculture': [
      'Farming', 'Food Production', 'Agribusiness', 'Livestock', 'Crop', 'Agricultural Technology',
      'Food Safety', 'Sustainability', 'Organic', 'Biotechnology'
    ]
  };

  // Find the best matching industry (case-insensitive, partial matching)
  const normalizedIndustry = industry.toLowerCase();

  // First, try exact match
  for (const [key, values] of Object.entries(industryMap)) {
    if (key.toLowerCase() === normalizedIndustry) {
      return values;
    }
  }

  // Then, try partial match
  for (const [key, values] of Object.entries(industryMap)) {
    if (key.toLowerCase().includes(normalizedIndustry) || normalizedIndustry.includes(key.toLowerCase())) {
      return values;
    }
  }

  // Finally, check if the industry appears in any of the related terms
  for (const [key, values] of Object.entries(industryMap)) {
    if (values.some(value => value.toLowerCase().includes(normalizedIndustry) || normalizedIndustry.includes(value.toLowerCase()))) {
      return values;
    }
  }

  // If no match found, return empty array
  return [];
}

/**
 * Calculate overall match score based on individual scores
 */
export function calculateOverallScore(
  skillsMatch: { score: number },
  experienceMatch: { score: number },
  educationMatch: { score: number },
  industryMatch: { score: number }
) {
  // Calculate weighted average
  const overallScore = Math.round(
    (skillsMatch.score * 0.4) +
    (experienceMatch.score * 0.3) +
    (educationMatch.score * 0.15) +
    (industryMatch.score * 0.15)
  );

  return { score: overallScore };
}

/**
 * Identify additional strengths from resume skills
 */
export function identifyAdditionalStrengths(resumeSkills: string[], jobSkills: string[]): string[] {
  // Find skills in resume that are not required by the job but might be valuable
  const additionalSkills = resumeSkills.filter(skill =>
    !jobSkills.some(jobSkill => jobSkill.toLowerCase() === skill.toLowerCase())
  );

  // Limit to top 5 additional skills
  return additionalSkills.slice(0, 5);
}

/**
 * Identify development areas based on missing skills
 */
export function identifyDevelopmentAreas(resumeSkills: string[], jobSkills: string[]): string[] {
  // Find skills required by the job that are not in the resume
  const missingSkills = jobSkills.filter(skill =>
    !resumeSkills.some(resumeSkill => resumeSkill.toLowerCase() === skill.toLowerCase())
  );

  // Convert to development areas
  const developmentAreas = missingSkills.map(skill => `Learn ${skill}`);

  // Limit to top 5 development areas
  return developmentAreas.slice(0, 5);
}
