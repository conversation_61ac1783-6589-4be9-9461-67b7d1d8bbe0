{"summary": {"totalTests": 23, "passedTests": 17, "failedTests": 0, "warningTests": 6, "successRate": 73.91304347826086}, "categories": {"Employer": {"pass": 4, "fail": 0, "warn": 2}, "Dashboard": {"pass": 4, "fail": 0, "warn": 0}, "Job": {"pass": 1, "fail": 0, "warn": 0}, "View": {"pass": 1, "fail": 0, "warn": 1}, "Path": {"pass": 1, "fail": 0, "warn": 1}, "Profile": {"pass": 4, "fail": 0, "warn": 0}, "AI": {"pass": 1, "fail": 0, "warn": 1}, "Matches": {"pass": 1, "fail": 0, "warn": 1}}, "results": [{"test": "Employer Login Form", "status": "PASS", "details": "Employer credentials entered", "screenshot": "screenshots/employer-final-employer-login-filled-1749181446693.png", "timestamp": "2025-06-06T03:44:06.812Z"}, {"test": "Employer Login Success", "status": "PASS", "details": "Successfully logged in as employer", "screenshot": "screenshots/employer-final-employer-dashboard-success-1749181452378.png", "timestamp": "2025-06-06T03:44:12.521Z"}, {"test": "Employer Dashboard Content", "status": "PASS", "details": "Found: job, candidate, posting", "screenshot": null, "timestamp": "2025-06-06T03:44:18.341Z"}, {"test": "Dashboard Interactivity", "status": "PASS", "details": "12 buttons, 3 links available", "screenshot": null, "timestamp": "2025-06-06T03:44:18.345Z"}, {"test": "Job Management Content", "status": "PASS", "details": "Job-related content found", "screenshot": null, "timestamp": "2025-06-06T03:44:18.345Z"}, {"test": "Dashboard Features Screenshot", "status": "PASS", "details": "Dashboard features captured", "screenshot": "screenshots/employer-final-dashboard-features-1749181458345.png", "timestamp": "2025-06-06T03:44:18.434Z"}, {"test": "Dashboard Navigation", "status": "PASS", "details": "Successfully accessed /dashboard", "screenshot": null, "timestamp": "2025-06-06T03:44:22.505Z"}, {"test": "Dashboard Content", "status": "PASS", "details": "Page has content (675 chars)", "screenshot": null, "timestamp": "2025-06-06T03:44:22.506Z"}, {"test": "View Matches Navigation", "status": "PASS", "details": "Successfully accessed /view-matches", "screenshot": null, "timestamp": "2025-06-06T03:44:27.328Z"}, {"test": "View Matches Content", "status": "WARN", "details": "Page has minimal content", "screenshot": null, "timestamp": "2025-06-06T03:44:27.329Z"}, {"test": "Path Coach Navigation", "status": "PASS", "details": "Successfully accessed /path-coach", "screenshot": null, "timestamp": "2025-06-06T03:44:31.696Z"}, {"test": "Path Coach Content", "status": "WARN", "details": "Page has minimal content", "screenshot": null, "timestamp": "2025-06-06T03:44:31.697Z"}, {"test": "Profile Navigation", "status": "PASS", "details": "Successfully accessed /profile", "screenshot": null, "timestamp": "2025-06-06T03:44:35.748Z"}, {"test": "Profile Content", "status": "PASS", "details": "Page has content (403 chars)", "screenshot": null, "timestamp": "2025-06-06T03:44:35.748Z"}, {"test": "Employer AI Content", "status": "WARN", "details": "No employer-specific AI content", "screenshot": null, "timestamp": "2025-06-06T03:44:42.181Z"}, {"test": "AI Chat Interface", "status": "WARN", "details": "No chat input found", "screenshot": null, "timestamp": "2025-06-06T03:44:42.183Z"}, {"test": "AI Coach <PERSON>", "status": "PASS", "details": "AI Coach screenshot captured", "screenshot": "screenshots/employer-final-ai-coach-1749181482183.png", "timestamp": "2025-06-06T03:44:42.248Z"}, {"test": "Employer Matches Content", "status": "WARN", "details": "No matching content detected", "screenshot": null, "timestamp": "2025-06-06T03:44:47.404Z"}, {"test": "Matches Interactivity", "status": "WARN", "details": "No interactive elements found", "screenshot": null, "timestamp": "2025-06-06T03:44:47.406Z"}, {"test": "Matches Screenshot", "status": "PASS", "details": "Matches screenshot captured", "screenshot": "screenshots/employer-final-matches-1749181487406.png", "timestamp": "2025-06-06T03:44:47.483Z"}, {"test": "Employer Profile Form", "status": "PASS", "details": "Found 1 form fields", "screenshot": null, "timestamp": "2025-06-06T03:44:52.590Z"}, {"test": "Profile Editing", "status": "PASS", "details": "Profile form is editable", "screenshot": null, "timestamp": "2025-06-06T03:44:54.116Z"}, {"test": "Profile Screenshot", "status": "PASS", "details": "Profile screenshot captured", "screenshot": "screenshots/employer-final-profile-1749181494117.png", "timestamp": "2025-06-06T03:44:54.605Z"}], "timestamp": "2025-06-06T03:44:54.939Z"}