import React from 'react';
import { useAuth } from '@/hooks/useAuth';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ShieldCheck, ShieldAlert } from 'lucide-react';

/**
 * VerificationStatus component displays the verification status for workers
 * Employers are automatically verified
 */
export function VerificationStatus() {
  const { user } = useAuth();

  // Only show for workers
  if (!user || user.role !== 'worker') {
    return null;
  }

  // If verified, show success message
  if (user.is_verified) {
    return (
      <Alert className="mb-4 bg-green-50 border-green-200">
        <ShieldCheck className="h-4 w-4 text-green-600" />
        <AlertTitle className="text-green-800">Verified Account</AlertTitle>
        <AlertDescription className="text-green-700">
          Your account has been verified. You have full access to all worker features.
        </AlertDescription>
      </Alert>
    );
  }

  // If not verified, show warning
  return (
    <Alert className="mb-4 bg-amber-50 border-amber-200">
      <ShieldAlert className="h-4 w-4 text-amber-600" />
      <AlertTitle className="text-amber-800">Verification Required</AlertTitle>
      <AlertDescription className="text-amber-700">
        Your account requires verification. Please use a work email from an approved company domain to access all worker features.
      </AlertDescription>
    </Alert>
  );
}
