import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('📦 Creating AWS deployment package...');

// Create deployment directory
if (!fs.existsSync('aws-deploy')) {
  fs.mkdirSync('aws-deploy');
}

// Copy files individually
if (fs.existsSync('package.json')) {
  fs.copyFileSync('package.json', 'aws-deploy/package.json');
}
if (fs.existsSync('Procfile')) {
  fs.copyFileSync('Procfile', 'aws-deploy/Procfile');
}
if (fs.existsSync('dist')) {
  execSync('xcopy "dist" "aws-deploy\\dist" /E /I /Y', { stdio: 'inherit' });
}
if (fs.existsSync('.ebextensions')) {
  execSync('xcopy ".ebextensions" "aws-deploy\\.ebextensions" /E /I /Y', { stdio: 'inherit' });
}

console.log('✅ Deployment package ready in aws-deploy/');
console.log('📋 Next steps:');
console.log('1. Zip the aws-deploy folder');
console.log('2. Upload to AWS Elastic Beanstalk console');
console.log('3. Deploy manually through AWS console');