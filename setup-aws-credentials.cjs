#!/usr/bin/env node

const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');

console.log('🔑 AWS Credentials Setup Script');
console.log('================================\n');

// Path to the CSV file
const csvPath = 'C:\\Users\\<USER>\\OneDrive\\Desktop\\PathLink\\pathlink-deployer_accessKeys.csv';

function readCredentialsFromCSV() {
  try {
    console.log('📄 Reading credentials from CSV file...');
    
    if (!fs.existsSync(csvPath)) {
      console.log('❌ CSV file not found at:', csvPath);
      console.log('Please make sure the file exists and try again.');
      return null;
    }

    const csvContent = fs.readFileSync(csvPath, 'utf8');
    console.log('✅ CSV file found and read successfully');
    
    // Parse CSV content - handle BOM and different formats
    const cleanContent = csvContent.replace(/^\uFEFF/, ''); // Remove BOM
    const lines = cleanContent.trim().split('\n');

    if (lines.length < 2) {
      console.log('❌ CSV file appears to be empty or malformed');
      return null;
    }

    // Skip header line, get data line
    const dataLine = lines[1];

    // Try different parsing approaches
    let accessKeyId, secretAccessKey;

    // Method 1: Standard CSV with commas
    if (dataLine.includes(',')) {
      const parts = dataLine.split(',');
      accessKeyId = parts[0];
      secretAccessKey = parts[1];
    }
    // Method 2: Tab separated
    else if (dataLine.includes('\t')) {
      const parts = dataLine.split('\t');
      accessKeyId = parts[0];
      secretAccessKey = parts[1];
    }
    // Method 3: Space separated
    else {
      const parts = dataLine.split(/\s+/);
      accessKeyId = parts[0];
      secretAccessKey = parts[1];
    }

    if (!accessKeyId || !secretAccessKey) {
      console.log('❌ Could not parse access keys from CSV');
      console.log('CSV content preview:', csvContent.substring(0, 200));
      console.log('Data line:', dataLine);
      return null;
    }

    console.log('✅ Credentials parsed successfully');
    console.log('👤 Username: pathlink-deployer');
    console.log('🔑 Access Key ID:', accessKeyId);
    console.log('🔐 Secret Key:', secretAccessKey.substring(0, 10) + '...');

    return {
      username: 'pathlink-deployer',
      accessKeyId: accessKeyId.trim(),
      secretAccessKey: secretAccessKey.trim()
    };

  } catch (error) {
    console.log('❌ Error reading CSV file:', error.message);
    return null;
  }
}

function configureAWSCLI(credentials) {
  try {
    console.log('\n🔧 Configuring AWS CLI...');

    // Check if AWS CLI is installed
    try {
      execSync('aws --version', { stdio: 'pipe' });
      console.log('✅ AWS CLI is installed');
    } catch (error) {
      console.log('❌ AWS CLI is not installed');
      console.log('Please install AWS CLI first: https://aws.amazon.com/cli/');
      return false;
    }

    // Configure AWS CLI using environment variables approach
    const configCommands = [
      `aws configure set aws_access_key_id ${credentials.accessKeyId}`,
      `aws configure set aws_secret_access_key ${credentials.secretAccessKey}`,
      `aws configure set default.region us-east-1`,
      `aws configure set default.output json`
    ];

    console.log('⚙️ Setting AWS configuration...');
    
    for (const command of configCommands) {
      execSync(command, { stdio: 'pipe' });
    }

    console.log('✅ AWS CLI configured successfully');
    return true;

  } catch (error) {
    console.log('❌ Error configuring AWS CLI:', error.message);
    return false;
  }
}

function testAWSConfiguration() {
  try {
    console.log('\n🧪 Testing AWS configuration...');
    
    const result = execSync('aws sts get-caller-identity', { stdio: 'pipe' }).toString();
    const identity = JSON.parse(result);
    
    console.log('✅ AWS configuration test successful!');
    console.log('👤 User ID:', identity.UserId);
    console.log('🏢 Account:', identity.Account);
    console.log('🔗 ARN:', identity.Arn);
    
    return true;

  } catch (error) {
    console.log('❌ AWS configuration test failed:', error.message);
    return false;
  }
}

function main() {
  console.log('Starting AWS credentials setup...\n');

  // Step 1: Read credentials from CSV
  const credentials = readCredentialsFromCSV();
  if (!credentials) {
    console.log('\n❌ Failed to read credentials from CSV file');
    console.log('Please check the file path and format');
    return;
  }

  // Step 2: Configure AWS CLI
  const configSuccess = configureAWSCLI(credentials);
  if (!configSuccess) {
    console.log('\n❌ Failed to configure AWS CLI');
    return;
  }

  // Step 3: Test configuration
  const testSuccess = testAWSConfiguration();
  if (!testSuccess) {
    console.log('\n❌ AWS configuration test failed');
    return;
  }

  // Step 4: Success message
  console.log('\n🎉 AWS CREDENTIALS SETUP COMPLETE!');
  console.log('=====================================');
  console.log('✅ CSV file read successfully');
  console.log('✅ AWS CLI configured');
  console.log('✅ Configuration tested and working');
  console.log('\n🚀 You can now run the deployment script:');
  console.log('   node deploy-to-aws.cjs');
  console.log('\n📋 Your AWS configuration:');
  console.log('   Region: us-east-1');
  console.log('   Output: json');
  console.log('   User: pathlink-deployer');
}

// Run the setup
if (require.main === module) {
  main();
}

module.exports = { readCredentialsFromCSV, configureAWSCLI, testAWSConfiguration };
