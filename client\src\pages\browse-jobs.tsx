import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { Search, MapPin, Building, Calendar, DollarSign, Users, Filter, X, Briefcase, Clock, Star, ArrowRight, Eye, CheckCircle, ExternalLink, FileText } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Title } from '@/components/ui/dialog';
import { ChevronDown } from 'lucide-react';
import SharedNavbar from '@/components/shared-navbar';

interface Job {
  id: number;
  title: string;
  description: string;
  requirements: string;
  location: string;
  salary_range: string;
  employment_type: string;
  industry: string;
  company_name: string;
  employer_id: number;
  created_at: string;
  updated_at: string;
}

interface User {
  id: number;
  email: string;
  name: string;
  role: string;
}

interface Match {
  id: number;
  worker_id: number;
  job_id: number;
  status: string;
}

export default function BrowseJobs() {
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [employmentTypeFilter, setEmploymentTypeFilter] = useState('all');
  const [salaryFilter, setSalaryFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [appliedJobs, setAppliedJobs] = useState<Set<number>>(new Set());
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [showJobDetails, setShowJobDetails] = useState(false);

  const queryClient = useQueryClient();

  // Get current user
  const { data: user } = useQuery<User>({
    queryKey: ['/api/user'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/user');
      if (!res.ok) throw new Error('Failed to fetch user');
      return res.json();
    },
  });

  // Get all jobs
  const { data: jobs = [], isLoading: jobsLoading } = useQuery<Job[]>({
    queryKey: ['/api/jobs'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/jobs');
      if (!res.ok) throw new Error('Failed to fetch jobs');
      return res.json();
    },
  });

  // Get user's applications to track applied jobs
  const { data: matches = [] } = useQuery<Match[]>({
    queryKey: ['/api/matches'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/matches');
      if (!res.ok) throw new Error('Failed to fetch matches');
      return res.json();
    },
    enabled: !!user && user.role === 'worker',
  });

  // Update applied jobs set when matches change
  useEffect(() => {
    if (matches) {
      const appliedJobIds = new Set(matches.map(match => match.job_id));
      setAppliedJobs(appliedJobIds);
    }
  }, [matches]);

  // Apply for job mutation
  const applyMutation = useMutation({
    mutationFn: async (jobId: number) => {
      const res = await apiRequest('POST', '/api/matches', {
        job_id: jobId,
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to apply for job');
      }
      return res.json();
    },
    onSuccess: (data, jobId) => {
      queryClient.invalidateQueries({ queryKey: ['/api/matches'] });
      setAppliedJobs(prev => new Set([...prev, jobId]));
      toast({
        title: "Application Submitted",
        description: "Your application has been submitted successfully!",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Application Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Filter jobs based on search criteria
  const filteredJobs = jobs.filter(job => {
    // Search filter - check multiple fields
    const matchesSearch = !searchTerm ||
      job.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.company_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.requirements?.toLowerCase().includes(searchTerm.toLowerCase());

    // Location filter - exact match or contains
    const matchesLocation = !locationFilter || locationFilter === 'all' ||
      job.location?.toLowerCase().includes(locationFilter.toLowerCase());

    // Industry filter - exact match or contains
    const matchesIndustry = !industryFilter || industryFilter === 'all' ||
      job.industry?.toLowerCase().includes(industryFilter.toLowerCase());

    // Employment type filter - exact match
    const matchesEmploymentType = !employmentTypeFilter || employmentTypeFilter === 'all' ||
      job.employment_type === employmentTypeFilter;

    // Salary filter - contains search
    const matchesSalary = !salaryFilter ||
      job.salary_range?.toLowerCase().includes(salaryFilter.toLowerCase());

    return matchesSearch && matchesLocation && matchesIndustry &&
           matchesEmploymentType && matchesSalary;
  });

  // Get unique values for filter options
  const uniqueIndustries = [...new Set(jobs.map(job => job.industry))].filter(Boolean);
  const uniqueLocations = [...new Set(jobs.map(job => job.location))].filter(Boolean);
  const uniqueEmploymentTypes = [...new Set(jobs.map(job => job.employment_type))].filter(Boolean);

  const clearFilters = () => {
    setSearchTerm('');
    setLocationFilter('all');
    setIndustryFilter('all');
    setEmploymentTypeFilter('all');
    setSalaryFilter('');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleApply = (jobId: number) => {
    if (user?.role !== 'worker') {
      toast({
        title: "Access Denied",
        description: "Only workers can apply for jobs.",
        variant: "destructive",
      });
      return;
    }
    applyMutation.mutate(jobId);
  };

  const handleViewDetails = (job: Job) => {
    setSelectedJob(job);
    setShowJobDetails(true);
  };

  const closeJobDetails = () => {
    setSelectedJob(null);
    setShowJobDetails(false);
  };

  if (jobsLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#E5DEFF]/30 via-white to-[#E5DEFF]/20">
        <SharedNavbar />
        <div className="max-w-7xl mx-auto px-6 py-12">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-16 w-16 border-b-4 border-[#1C2A42] mx-auto"></div>
            <p className="mt-6 text-lg text-gray-600">Loading job opportunities...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-[#E5DEFF]/30 via-white to-[#E5DEFF]/20">
        <SharedNavbar />

        {/* Hero Section */}
        <div className="bg-gradient-to-r from-[#1C2A42] to-[#2A3F5F] text-white">
        <div className="max-w-7xl mx-auto px-6 py-16">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="bg-white/10 p-4 rounded-full">
                <Briefcase className="h-12 w-12 text-white" />
              </div>
            </div>
            <h1 className="text-5xl font-bold mb-4">
              Discover Your Next Opportunity
            </h1>
            <p className="text-xl text-white/90 mb-2">
              Explore {jobs.length} available positions from top employers across all industries
            </p>
            <p className="text-lg text-white/70">
              Find the perfect role that matches your skills and career goals
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 h-6 w-6" />
              <Input
                type="text"
                placeholder="Search jobs by title, company, description, or requirements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-16 pr-6 py-6 text-lg bg-white/95 backdrop-blur-sm border-0 focus:ring-2 focus:ring-white/50 rounded-2xl shadow-lg placeholder:text-gray-500"
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <div className="flex justify-center mt-8">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 bg-white/10 border-white/20 text-white hover:bg-white/20 hover:text-white px-6 py-3"
            >
              <Filter className="h-5 w-5" />
              {showFilters ? 'Hide Advanced Filters' : 'Show Advanced Filters'}
              <ChevronDown className={`h-5 w-5 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
            </Button>
          </div>
        </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="bg-white border-b shadow-sm">
            <div className="max-w-7xl mx-auto px-6 py-8">
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-[#E5DEFF]/20 to-[#E5DEFF]/10">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl text-[#1C2A42] flex items-center gap-2">
                    <Filter className="h-5 w-5" />
                    Advanced Filters
                  </CardTitle>
                  <Button variant="ghost" size="sm" onClick={clearFilters} className="text-[#1C2A42] hover:bg-[#E5DEFF]/20">
                    <X className="h-4 w-4 mr-2" />
                    Clear All
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {/* Location Filter */}
                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Select value={locationFilter} onValueChange={setLocationFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Any location" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Any location</SelectItem>
                          {uniqueLocations.map(location => (
                            <SelectItem key={location} value={location}>
                              {location}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Industry Filter */}
                    <div>
                      <Label htmlFor="industry">Industry</Label>
                      <Select value={industryFilter} onValueChange={setIndustryFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Any industry" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Any industry</SelectItem>
                          {uniqueIndustries.map(industry => (
                            <SelectItem key={industry} value={industry}>
                              {industry}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Employment Type Filter */}
                    <div>
                      <Label htmlFor="employment-type">Employment Type</Label>
                      <Select value={employmentTypeFilter} onValueChange={setEmploymentTypeFilter}>
                        <SelectTrigger>
                          <SelectValue placeholder="Any type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Any type</SelectItem>
                          {uniqueEmploymentTypes.map(type => (
                            <SelectItem key={type} value={type}>
                              {type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Salary Filter */}
                    <div>
                      <Label htmlFor="salary">Salary Range</Label>
                      <Input
                        type="text"
                        placeholder="e.g. 50k, 60000, $70k"
                        value={salaryFilter}
                        onChange={(e) => setSalaryFilter(e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Results Section */}
        <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between mb-8 gap-4">
          <div>
            <h2 className="text-3xl font-bold text-[#1C2A42] mb-2">
              {filteredJobs.length} Job{filteredJobs.length !== 1 ? 's' : ''} Found
            </h2>
            <p className="text-gray-600">
              Discover opportunities that match your career goals
            </p>
          </div>
          {(searchTerm || (locationFilter && locationFilter !== 'all') || (industryFilter && industryFilter !== 'all') || (employmentTypeFilter && employmentTypeFilter !== 'all') || salaryFilter) && (
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-sm text-gray-600 font-medium">Active filters:</span>
              {searchTerm && <Badge variant="secondary" className="bg-[#E5DEFF]/50 text-[#1C2A42]">Search: {searchTerm}</Badge>}
              {locationFilter && locationFilter !== 'all' && <Badge variant="secondary" className="bg-[#E5DEFF]/50 text-[#1C2A42]">Location: {locationFilter}</Badge>}
              {industryFilter && industryFilter !== 'all' && <Badge variant="secondary" className="bg-[#E5DEFF]/50 text-[#1C2A42]">Industry: {industryFilter}</Badge>}
              {employmentTypeFilter && employmentTypeFilter !== 'all' && <Badge variant="secondary" className="bg-[#E5DEFF]/50 text-[#1C2A42]">Type: {employmentTypeFilter}</Badge>}
              {salaryFilter && <Badge variant="secondary" className="bg-[#E5DEFF]/50 text-[#1C2A42]">Salary: {salaryFilter}</Badge>}
            </div>
          )}
        </div>

        {/* Job Listings */}
        <div className="grid gap-8">
          {filteredJobs.length === 0 ? (
            <Card className="border-0 shadow-lg">
              <CardContent className="text-center py-16">
                <div className="bg-[#E5DEFF]/20 p-6 rounded-full w-24 h-24 mx-auto mb-6 flex items-center justify-center">
                  <Search className="h-12 w-12 text-[#1C2A42]" />
                </div>
                <h3 className="text-2xl font-semibold text-[#1C2A42] mb-3">No jobs found</h3>
                <p className="text-gray-600 mb-6 max-w-md mx-auto">
                  We couldn't find any positions matching your criteria. Try adjusting your search or clearing some filters.
                </p>
                <Button onClick={clearFilters} className="bg-[#1C2A42] hover:bg-[#2A3F5F] text-white px-8 py-3">
                  Clear All Filters
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredJobs.map((job) => (
              <Card key={job.id} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className="bg-[#E5DEFF]/20 p-2 rounded-lg">
                          <Briefcase className="h-5 w-5 text-[#1C2A42]" />
                        </div>
                        <div>
                          <CardTitle
                            className="text-2xl text-[#1C2A42] mb-1 hover:text-[#2A3F5F] transition-colors cursor-pointer"
                            onClick={() => handleViewDetails(job)}
                          >
                            {job.title}
                          </CardTitle>
                          <CardDescription className="text-lg font-semibold text-gray-700">
                            {job.company_name}
                          </CardDescription>
                        </div>
                      </div>

                      {/* Job Meta Info */}
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {job.location}
                        </div>
                        <div className="flex items-center gap-1">
                          <Building className="h-4 w-4" />
                          {job.industry}
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4" />
                          {job.employment_type}
                        </div>
                        {job.salary_range && (
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            {job.salary_range}
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          Posted {formatDate(job.created_at)}
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="ml-4 flex flex-col gap-3">
                      <Button
                        onClick={() => handleViewDetails(job)}
                        variant="outline"
                        className="px-6 py-2 border-[#1C2A42] text-[#1C2A42] hover:bg-[#1C2A42] hover:text-white transition-all duration-200"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>

                      {user?.role === 'worker' ? (
                        <Button
                          onClick={() => handleApply(job.id)}
                          disabled={appliedJobs.has(job.id) || applyMutation.isPending}
                          className={`px-6 py-3 font-semibold transition-all duration-200 ${
                            appliedJobs.has(job.id)
                              ? 'bg-green-600 hover:bg-green-700 text-white'
                              : 'bg-[#1C2A42] hover:bg-[#2A3F5F] text-white hover:scale-105'
                          }`}
                        >
                          {appliedJobs.has(job.id) ? (
                            <>
                              <CheckCircle className="h-4 w-4 mr-2" />
                              Applied
                            </>
                          ) : applyMutation.isPending ? (
                            <>
                              <Clock className="h-4 w-4 mr-2 animate-spin" />
                              Applying...
                            </>
                          ) : (
                            <>
                              <ArrowRight className="h-4 w-4 mr-2" />
                              Apply Now
                            </>
                          )}
                        </Button>
                      ) : (
                        <Badge variant="outline" className="px-4 py-2 text-sm">
                          Login as Worker to Apply
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Job Description */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-[#1C2A42] mb-3 flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Job Description
                    </h4>
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <p className="text-gray-700 leading-relaxed">
                        {job.description}
                      </p>
                    </div>
                  </div>

                  <Separator className="my-6" />

                  {/* Requirements */}
                  <div>
                    <h4 className="font-semibold text-[#1C2A42] mb-3 flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      Requirements
                    </h4>
                    <div className="bg-[#E5DEFF]/10 p-4 rounded-lg">
                      <p className="text-gray-700 leading-relaxed">
                        {job.requirements}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Footer CTA */}
        {filteredJobs.length > 0 && (
          <div className="mt-16 text-center">
            <Card className="border-0 shadow-lg bg-gradient-to-r from-[#1C2A42] to-[#2A3F5F] text-white">
              <CardContent className="py-12">
                <h3 className="text-2xl font-bold mb-4">
                  Ready to Take the Next Step?
                </h3>
                <p className="text-lg text-white/90 mb-6 max-w-2xl mx-auto">
                  Join thousands of professionals who have found their dream jobs through PathLink.
                  Your perfect opportunity is just one application away.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button
                    variant="secondary"
                    size="lg"
                    className="bg-white text-[#1C2A42] hover:bg-gray-100 px-8 py-3"
                    onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
                  >
                    Back to Top
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white text-white hover:bg-white hover:text-[#1C2A42] px-8 py-3"
                    onClick={() => {
                      window.location.href = '/dashboard';
                    }}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
        </div>
      </div>

      {/* Job Details Modal */}
      {showJobDetails && selectedJob && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-[#1C2A42] to-[#2A3F5F] text-white p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-4">
                  <div className="bg-white/10 p-3 rounded-lg">
                    <Briefcase className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h2 className="text-3xl font-bold mb-2">{selectedJob.title}</h2>
                    <p className="text-xl text-white/90">{selectedJob.company_name}</p>
                  </div>
                </div>
                <Button
                  onClick={closeJobDetails}
                  variant="ghost"
                  size="sm"
                  className="text-white hover:bg-white/10 p-2"
                >
                  <X className="h-6 w-6" />
                </Button>
              </div>

              {/* Job Meta Info */}
              <div className="flex flex-wrap items-center gap-6 mt-6 text-white/90">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  {selectedJob.location}
                </div>
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {selectedJob.industry}
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {selectedJob.employment_type}
                </div>
                {selectedJob.salary_range && (
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    {selectedJob.salary_range}
                  </div>
                )}
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Posted {formatDate(selectedJob.created_at)}
                </div>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[60vh]">
              {/* Job Description */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-[#1C2A42] mb-4 flex items-center gap-2">
                  <FileText className="h-6 w-6" />
                  Job Description
                </h3>
                <div className="bg-gray-50 p-6 rounded-xl">
                  <p className="text-gray-700 leading-relaxed text-lg whitespace-pre-wrap">
                    {selectedJob.description}
                  </p>
                </div>
              </div>

              <Separator className="my-8" />

              {/* Requirements */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-[#1C2A42] mb-4 flex items-center gap-2">
                  <Star className="h-6 w-6" />
                  Requirements & Qualifications
                </h3>
                <div className="bg-[#E5DEFF]/10 p-6 rounded-xl">
                  <p className="text-gray-700 leading-relaxed text-lg whitespace-pre-wrap">
                    {selectedJob.requirements}
                  </p>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-50 p-6 flex flex-col sm:flex-row gap-4 justify-between items-center">
              <div className="text-sm text-gray-600">
                Job ID: {selectedJob.id} • Posted {formatDate(selectedJob.created_at)}
              </div>
              <div className="flex gap-3">
                <Button
                  onClick={closeJobDetails}
                  variant="outline"
                  className="px-6 py-2"
                >
                  Close
                </Button>
                {user?.role === 'worker' && (
                  <Button
                    onClick={() => {
                      handleApply(selectedJob.id);
                      closeJobDetails();
                    }}
                    disabled={appliedJobs.has(selectedJob.id) || applyMutation.isPending}
                    className={`px-8 py-2 font-semibold ${
                      appliedJobs.has(selectedJob.id)
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-[#1C2A42] hover:bg-[#2A3F5F] text-white'
                    }`}
                  >
                    {appliedJobs.has(selectedJob.id) ? (
                      <>
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Applied
                      </>
                    ) : applyMutation.isPending ? (
                      <>
                        <Clock className="h-4 w-4 mr-2 animate-spin" />
                        Applying...
                      </>
                    ) : (
                      <>
                        <ArrowRight className="h-4 w-4 mr-2" />
                        Apply Now
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
