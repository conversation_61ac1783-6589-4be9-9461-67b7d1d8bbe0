const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function testLogin() {
  try {
    console.log('🔌 Connecting to Neon database...');
    const client = await pool.connect();
    console.log('✅ Connected to Neon database');
    
    // Test <NAME_EMAIL>
    const email = '<EMAIL>';
    const password = 'password123';
    
    console.log(`🔍 Looking up user: ${email}`);
    const userResult = await client.query('SELECT * FROM users WHERE email = $1', [email]);
    
    if (userResult.rows.length === 0) {
      console.log('❌ User not found');
      client.release();
      await pool.end();
      return;
    }
    
    const user = userResult.rows[0];
    console.log('👤 User found:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      created_at: user.created_at
    });
    
    // Test password comparison
    console.log('🔐 Testing password...');
    const isPasswordValid = await bcrypt.compare(password, user.password);
    
    if (isPasswordValid) {
      console.log('✅ Password is correct! Login should work.');
    } else {
      console.log('❌ Password is incorrect.');
      console.log('🔧 Let me update the password to "password123"...');
      
      // Hash the new password
      const hashedPassword = await bcrypt.hash(password, 10);
      
      // Update the user's password
      await client.query('UPDATE users SET password = $1 WHERE email = $2', [hashedPassword, email]);
      console.log('✅ Password updated successfully!');
      
      // Test again
      const updatedUserResult = await client.query('SELECT * FROM users WHERE email = $1', [email]);
      const updatedUser = updatedUserResult.rows[0];
      const isNewPasswordValid = await bcrypt.compare(password, updatedUser.password);
      
      if (isNewPasswordValid) {
        console.log('✅ New password verified! Login should now work.');
      } else {
        console.log('❌ Something went wrong with password update.');
      }
    }
    
    client.release();
    await pool.end();
    console.log('✅ Test complete');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testLogin();
