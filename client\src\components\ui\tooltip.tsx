import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

// COMPLETELY DISABLED TOOLTIP IMPLEMENTATION
// This fixes the persistent "Dashboard" overlay issue by making tooltips render nothing

// Empty provider that does nothing - this prevents tooltip portals from being created
const TooltipProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>
}

// No-op tooltip that renders nothing but its children
const Tooltip: React.FC<TooltipPrimitive.TooltipProps> = ({ children }) => {
  return <>{children}</>
}

// Just renders its children directly with no wrapping
const TooltipTrigger: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <>{children}</>
}

// Completely empty tooltip content - renders nothing
const TooltipContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  // Return null to render nothing
  return null
})
TooltipContent.displayName = "TooltipContent"

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }
