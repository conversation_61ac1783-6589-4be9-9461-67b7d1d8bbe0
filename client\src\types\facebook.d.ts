interface Window {
  FB?: {
    init(options: {
      appId: string;
      cookie?: boolean;
      xfbml?: boolean;
      version: string;
    }): void;
    login(
      callback: (response: {
        authResponse?: {
          accessToken: string;
          expiresIn: string;
          signedRequest: string;
          userID: string;
        };
        status?: string;
      }) => void,
      options?: {
        scope?: string;
        return_scopes?: boolean;
        enable_profile_selector?: boolean;
        profile_selector_ids?: string;
      }
    ): void;
    logout(callback: (response: any) => void): void;
    getLoginStatus(
      callback: (response: {
        status: 'connected' | 'not_authorized' | 'unknown';
        authResponse?: {
          accessToken: string;
          expiresIn: string;
          signedRequest: string;
          userID: string;
        };
      }) => void
    ): void;
    api(
      path: string,
      method: 'get' | 'post' | 'delete',
      params: any,
      callback: (response: any) => void
    ): void;
  };
  fbAsyncInit?: () => void;
}
