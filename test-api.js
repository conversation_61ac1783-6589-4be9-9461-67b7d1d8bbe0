// Simple test script to call the comprehensive matches API
import fetch from 'node-fetch';

async function testAPI() {
  try {
    console.log('Testing comprehensive matches API...');

    // First login to get session
    const loginResponse = await fetch('http://localhost:5000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password'
      })
    });

    const loginData = await loginResponse.json();
    console.log('Login response:', loginData);

    // Get cookies from login response
    const cookies = loginResponse.headers.get('set-cookie');
    console.log('Cookies:', cookies);

    // Call comprehensive matches API
    const matchesResponse = await fetch('http://localhost:5000/api/matches/comprehensive', {
      headers: {
        'Cookie': cookies
      }
    });

    const matchesData = await matchesResponse.json();
    console.log('Matches response:', JSON.stringify(matchesData, null, 2));

  } catch (error) {
    console.error('Error:', error);
  }
}

testAPI();
