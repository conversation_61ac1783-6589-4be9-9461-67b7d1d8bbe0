import fetch from 'node-fetch';

async function testReapplyAPI() {
  try {
    console.log('=== TESTING REAPPLY API FUNCTIONALITY ===');
    
    // Test the reapply API directly
    console.log('1. Testing reapply for job 11 (should work since match 28 is withdrawn)...');
    
    const reapplyResponse = await fetch('http://localhost:5000/api/jobs/11/apply', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Simulate being logged in as user 27
        'Cookie': 'connect.sid=s%3A...' // This won't work without a real session, but let's see the response
      }
    });
    
    console.log('Response status:', reapplyResponse.status);
    const responseText = await reapplyResponse.text();
    console.log('Response:', responseText);
    
    if (reapplyResponse.status === 401) {
      console.log('❌ Authentication required - this is expected without a valid session');
      console.log('✅ The API endpoint is working and properly checking authentication');
    } else if (reapplyResponse.status === 200) {
      console.log('🎉 REAPPLY SUCCESS!');
    } else {
      console.log('⚠️  Unexpected response status');
    }
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

testReapplyAPI();
