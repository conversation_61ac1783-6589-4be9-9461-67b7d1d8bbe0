#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting PathLink Netlify Deployment...\n');

// Check if netlify.toml exists
const netlifyConfigPath = path.join(process.cwd(), 'netlify.toml');
if (!fs.existsSync(netlifyConfigPath)) {
  console.error('❌ netlify.toml not found. Please ensure it exists in the root directory.');
  process.exit(1);
}

// Check if required environment variables are set
const requiredEnvVars = [
  'DATABASE_URL',
  'SESSION_SECRET',
  'JWT_SECRET'
];

console.log('🔍 Checking environment variables...');
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.warn('⚠️  Missing environment variables (set these in Netlify dashboard):');
  missingEnvVars.forEach(envVar => console.warn(`   - ${envVar}`));
  console.log('');
}

try {
  // Clean previous builds
  console.log('🧹 Cleaning previous builds...');
  if (fs.existsSync('dist')) {
    execSync('rm -rf dist', { stdio: 'inherit' });
  }

  // Install dependencies
  console.log('📦 Installing dependencies...');
  execSync('npm ci', { stdio: 'inherit' });

  // Run type checking
  console.log('🔍 Running type checks...');
  execSync('npm run check', { stdio: 'inherit' });

  // Build the application
  console.log('🏗️  Building application...');
  execSync('npm run build', { stdio: 'inherit' });

  // Verify build output
  console.log('✅ Verifying build output...');
  const distPath = path.join(process.cwd(), 'dist');
  const functionsPath = path.join(distPath, 'functions');
  
  if (!fs.existsSync(distPath)) {
    throw new Error('Build output directory (dist) not found');
  }
  
  if (!fs.existsSync(functionsPath)) {
    throw new Error('Functions directory (dist/functions) not found');
  }

  const indexHtmlPath = path.join(distPath, 'index.html');
  if (!fs.existsSync(indexHtmlPath)) {
    throw new Error('index.html not found in build output');
  }

  console.log('✅ Build verification complete!');
  console.log('\n📋 Build Summary:');
  console.log(`   - Client build: ${distPath}`);
  console.log(`   - Functions: ${functionsPath}`);
  console.log(`   - Entry point: ${indexHtmlPath}`);

  // Check if Netlify CLI is available
  try {
    execSync('netlify --version', { stdio: 'pipe' });
    console.log('\n🌐 Netlify CLI detected. You can now deploy with:');
    console.log('   npm run netlify:deploy');
  } catch (error) {
    console.log('\n📝 To deploy to Netlify:');
    console.log('   1. Install Netlify CLI: npm install -g netlify-cli');
    console.log('   2. Login: netlify login');
    console.log('   3. Deploy: netlify deploy --prod');
  }

  console.log('\n🎉 Build completed successfully!');
  console.log('\n📚 Next steps:');
  console.log('   1. Set environment variables in Netlify dashboard');
  console.log('   2. Connect your GitHub repository');
  console.log('   3. Configure build settings:');
  console.log('      - Build command: npm run build');
  console.log('      - Publish directory: dist');
  console.log('      - Functions directory: dist/functions');
  console.log('   4. Deploy your site');

} catch (error) {
  console.error('\n❌ Build failed:', error.message);
  process.exit(1);
}
