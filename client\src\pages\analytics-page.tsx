import { useState } from 'react';
import Layout from "@/components/layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
// Remove Tooltip import to prevent persistent tooltip issues
import { Bar<PERSON><PERSON>2, TrendingUp, Pie<PERSON>hart as PieChartIcon, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Loader } from '@/components/ui/loader';

// Sample data for demonstration
const talentSupplyData = [
  { name: 'Jan', Software: 400, Healthcare: 280, Finance: 240, Manufacturing: 180 },
  { name: 'Feb', Software: 420, Healthcare: 290, Finance: 220, Manufacturing: 190 },
  { name: 'Mar', Software: 460, Healthcare: 300, Finance: 230, Manufacturing: 200 },
  { name: 'Apr', Software: 490, Healthcare: 320, Finance: 240, Manufacturing: 210 },
  { name: 'May', Software: 520, Healthcare: 340, Finance: 260, Manufacturing: 220 },
  { name: 'Jun', Software: 550, Healthcare: 360, Finance: 280, Manufacturing: 230 },
];

const talentDemandData = [
  { name: 'Jan', Software: 500, Healthcare: 260, Finance: 230, Manufacturing: 170 },
  { name: 'Feb', Software: 530, Healthcare: 270, Finance: 210, Manufacturing: 180 },
  { name: 'Mar', Software: 570, Healthcare: 290, Finance: 220, Manufacturing: 190 },
  { name: 'Apr', Software: 600, Healthcare: 310, Finance: 230, Manufacturing: 200 },
  { name: 'May', Software: 630, Healthcare: 330, Finance: 250, Manufacturing: 210 },
  { name: 'Jun', Software: 650, Healthcare: 350, Finance: 270, Manufacturing: 220 },
];

const gapAnalysisData = [
  { name: 'Software', value: 18 },
  { name: 'Healthcare', value: -3 },
  { name: 'Finance', value: -4 },
  { name: 'Manufacturing', value: -5 },
  { name: 'Retail', value: 8 },
  { name: 'Education', value: -2 },
];

const topSkillsData = [
  { name: 'JavaScript', value: 85 },
  { name: 'Data Science', value: 78 },
  { name: 'Cloud Computing', value: 76 },
  { name: 'DevOps', value: 72 },
  { name: 'Machine Learning', value: 68 },
  { name: 'Cybersecurity', value: 65 },
  { name: 'UX/UI Design', value: 62 },
  { name: 'Project Management', value: 58 },
];

const hiringTrendsData = [
  { name: 'Remote', value: 42 },
  { name: 'Hybrid', value: 38 },
  { name: 'On-site', value: 20 },
];

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#A64AC9', '#FF5A5F', '#3D4A5D', '#82ca9d'];

// Define the market analysis response type
interface MarketAnalysis {
  overview: string;
  trends: string[];
  skills: string[];
  outlook: string;
  salary: string;
  recommendations: string[];
}

export default function AnalyticsPage() {
  const [period, setPeriod] = useState('6m');
  const [marketField, setMarketField] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [marketAnalysis, setMarketAnalysis] = useState<MarketAnalysis | null>(null);
  const { toast } = useToast();

  const handleMarketAnalysis = async () => {
    if (!marketField.trim()) return;

    setIsAnalyzing(true);
    setMarketAnalysis(null);

    try {
      const response = await fetch('/api/market-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ field: marketField }),
      });

      if (!response.ok) {
        throw new Error('Failed to analyze market');
      }

      const data = await response.json();
      setMarketAnalysis(data);
    } catch (error) {
      console.error('Error analyzing market:', error);
      toast({
        title: 'Error',
        description: 'Failed to analyze market. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-[#1C2A42] dark:text-white">Labor Market Analytics</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Real-time insights into talent supply, demand, and skills needed across industries.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Talent Gap Index</CardTitle>
              <CardDescription className="text-2xl font-bold">+4.3%</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                Overall talent demand exceeds supply by 4.3%
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Top Skill in Demand</CardTitle>
              <CardDescription className="text-2xl font-bold">JavaScript</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                85% of software positions require this skill
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Fastest Growing Sector</CardTitle>
              <CardDescription className="text-2xl font-bold">Cloud Computing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                Demand up 24% in the last quarter
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="supply-demand" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-xl">
            <TabsTrigger value="supply-demand" className="flex items-center">
              <TrendingUp className="mr-2 h-4 w-4" />
              Supply vs. Demand
            </TabsTrigger>
            <TabsTrigger value="skills" className="flex items-center">
              <BarChart2 className="mr-2 h-4 w-4" />
              Skills Analysis
            </TabsTrigger>
            <TabsTrigger value="trends" className="flex items-center">
              <PieChartIcon className="mr-2 h-4 w-4" />
              Hiring Trends
            </TabsTrigger>
            <TabsTrigger value="market-analysis" className="flex items-center">
              <Search className="mr-2 h-4 w-4" />
              Market Analysis
            </TabsTrigger>
          </TabsList>

          {/* Supply vs. Demand Tab */}
          <TabsContent value="supply-demand">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Talent Supply by Industry</CardTitle>
                  <CardDescription>Available workforce over time</CardDescription>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={talentSupplyData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />

                        <Legend />
                        <Line type="monotone" dataKey="Software" stroke="#0088FE" />
                        <Line type="monotone" dataKey="Healthcare" stroke="#00C49F" />
                        <Line type="monotone" dataKey="Finance" stroke="#FFBB28" />
                        <Line type="monotone" dataKey="Manufacturing" stroke="#FF8042" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Talent Demand by Industry</CardTitle>
                  <CardDescription>Open positions over time</CardDescription>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={talentDemandData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />

                        <Legend />
                        <Line type="monotone" dataKey="Software" stroke="#0088FE" />
                        <Line type="monotone" dataKey="Healthcare" stroke="#00C49F" />
                        <Line type="monotone" dataKey="Finance" stroke="#FFBB28" />
                        <Line type="monotone" dataKey="Manufacturing" stroke="#FF8042" />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>Industry Talent Gap Analysis</CardTitle>
                  <CardDescription>Negative values indicate surplus, positive values indicate shortage</CardDescription>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={gapAnalysisData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />

                        <Legend />
                        <Bar dataKey="value" fill="#8884d8">
                          {gapAnalysisData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={entry.value > 0 ? '#ef4444' : '#22c55e'}
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Skills Analysis Tab */}
          <TabsContent value="skills">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Top Skills in Demand</CardTitle>
                  <CardDescription>Skills with highest employer demand</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {topSkillsData.map((skill) => (
                      <div key={skill.name} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{skill.name}</span>
                          <span className="text-sm text-muted-foreground">{skill.value}%</span>
                        </div>
                        <Progress value={skill.value} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Skills by Abundance</CardTitle>
                  <CardDescription>Distribution of skills in workforce</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={topSkillsData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {topSkillsData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>

                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Hiring Trends Tab */}
          <TabsContent value="trends">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Working Model Trends</CardTitle>
                  <CardDescription>Distribution of remote vs. on-site positions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={hiringTrendsData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {hiringTrendsData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>

                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Industry Growth Rates</CardTitle>
                  <CardDescription>Projected hiring growth by industry</CardDescription>
                </CardHeader>
                <CardContent className="pt-2">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={[
                          { name: 'Software', growth: 28 },
                          { name: 'Healthcare', growth: 14 },
                          { name: 'Finance', growth: 8 },
                          { name: 'Manufacturing', growth: 5 },
                          { name: 'Retail', growth: -3 },
                          { name: 'Energy', growth: 12 }
                        ]}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />

                        <Legend />
                        <Bar dataKey="growth" fill="#1C2A42">
                          {gapAnalysisData.map((entry, index) => (
                            <Cell
                              key={`cell-${index}`}
                              fill={index % 2 === 0 ? '#1C2A42' : '#3B82F6'}
                            />
                          ))}
                        </Bar>
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Market Analysis Tab */}
          <TabsContent value="market-analysis">
            <div className="grid grid-cols-1 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>AI-Powered Market Analysis</CardTitle>
                  <CardDescription>
                    Enter a specific field or job title to get detailed market insights powered by AI
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="flex flex-col md:flex-row gap-4">
                      <Input
                        placeholder="Enter a field (e.g., Data Science, Healthcare, Cybersecurity)"
                        value={marketField}
                        onChange={(e) => setMarketField(e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        onClick={handleMarketAnalysis}
                        disabled={isAnalyzing || !marketField.trim()}
                        className="bg-[#6100ee] hover:bg-opacity-90"
                      >
                        {isAnalyzing ? (
                          <>
                            <div className="w-4 h-2 mr-2 overflow-hidden">
                              <div className="loader h-2 w-16"></div>
                            </div>
                            Analyzing...
                          </>
                        ) : (
                          <>
                            <Search className="mr-2 h-4 w-4" />
                            Analyze Market
                          </>
                        )}
                      </Button>
                    </div>

                    {marketAnalysis ? (
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold mb-2">Market Overview</h3>
                          <p className="text-gray-700 dark:text-gray-300">{marketAnalysis.overview}</p>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Key Trends</h3>
                          <ul className="list-disc pl-5 space-y-2">
                            {marketAnalysis.trends.map((trend, index) => (
                              <li key={index} className="text-gray-700 dark:text-gray-300">{trend}</li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold mb-2">In-Demand Skills</h3>
                          <ul className="list-disc pl-5 space-y-2">
                            {marketAnalysis.skills.map((skill, index) => (
                              <li key={index} className="text-gray-700 dark:text-gray-300">{skill}</li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Hiring Outlook</h3>
                          <p className="text-gray-700 dark:text-gray-300">{marketAnalysis.outlook}</p>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Salary Insights</h3>
                          <p className="text-gray-700 dark:text-gray-300">{marketAnalysis.salary}</p>
                        </div>

                        <div>
                          <h3 className="text-lg font-semibold mb-2">Recommendations</h3>
                          <ul className="list-disc pl-5 space-y-2">
                            {marketAnalysis.recommendations.map((rec, index) => (
                              <li key={index} className="text-gray-700 dark:text-gray-300">{rec}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                        {isAnalyzing ? (
                          <div className="flex flex-col items-center">
                            <Loader
                              size="large"
                              text={`Analyzing market data for ${marketField}... This may take a moment as we gather comprehensive insights.`}
                            />
                          </div>
                        ) : (
                          <div>
                            <Search className="h-8 w-8 mx-auto mb-4 opacity-50" />
                            <p>Enter a field and click "Analyze Market" to get AI-powered insights</p>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-8 text-center text-gray-500 dark:text-gray-400 text-sm italic">
          <p>Note: This data is for demonstration purposes. In production, this would be powered by real-time labor market data.</p>
        </div>
      </div>
    </Layout>
  );
}