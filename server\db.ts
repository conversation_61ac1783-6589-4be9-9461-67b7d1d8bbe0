import pg from 'pg'
import { drizzle } from 'drizzle-orm/node-postgres'
import * as schema from '@shared/schema'

const { Pool } = pg

// Connection config
const connectionOptions = {
  connectionTimeoutMillis: 60000,     // Wait max 60s to connect
  query_timeout: 30000,               // Wait max 30s for any query
  max: 10,                            // Max DB connections
  idleTimeoutMillis: 30000            // Close idle clients after 30s
}

// Check if DATABASE_URL is set
const useInMemoryStorage = !process.env.DATABASE_URL

if (useInMemoryStorage) {
  console.warn("⚠️ WARNING: Using in-memory storage. Set DATABASE_URL to connect to Neon Database.")
} else {
  console.log("Using Neon Database for storage")
}

let pool: any = {}
let db: any = {}

if (!useInMemoryStorage) {
  try {
    // Use the DATABASE_URL directly
    console.log("Initializing PostgreSQL connection to Neon Database")

    // Create a connection pool with proper SSL configuration for Neon
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: {
        rejectUnauthorized: false  // Allow self-signed certificates for easier development
      },
      ...connectionOptions
    })

    console.log("PostgreSQL connection configured with SSL")

  // Test the connection early
  ;(async () => {
    try {
      console.log("🔌 Testing database connection to Neon...")
      const client = await pool.connect()
      console.log("✅ Neon Database connected successfully.")

      // Run a simple query to verify the connection works
      const result = await client.query('SELECT NOW()')
      console.log(`Neon Database time: ${result.rows[0].now}`)

      client.release()
    } catch (err) {
      console.error("❌ Neon Database connection failed:", err)
      console.log("Falling back to in-memory storage due to connection test failure")
      pool = {}
      db = {}
    }
  })()

  // Initialize Drizzle with the pool
  db = drizzle(pool, { schema })

} catch (error) {
  console.error("Failed to initialize Neon DB:", error)
  console.log("Falling back to in-memory storage")
  pool = {}
  db = {}
}
} else {
  console.warn("⚠️ WARNING: Using in-memory storage. Set DATABASE_URL to connect to Neon Database.")
  pool = {}
  db = {}
}

// Export the database connection
export { pool, db }
