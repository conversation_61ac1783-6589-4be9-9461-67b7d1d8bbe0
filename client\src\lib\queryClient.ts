import { QueryClient, QueryFunction } from "@tanstack/react-query";

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    try {
      const contentType = res.headers.get("content-type");
      if (contentType && contentType.includes("application/json")) {
        try {
          const errorData = await res.json();
          throw new Error(errorData.message || `${res.status}: ${res.statusText}`);
        } catch (jsonError) {
          // If JSON parsing fails, fall back to text
          console.error("Failed to parse error response as JSON:", jsonError);
          const text = await res.text();
          throw new Error(`${res.status}: ${text.substring(0, 100)}...`);
        }
      } else {
        const text = (await res.text()) || res.statusText;
        throw new Error(`${res.status}: ${text.substring(0, 100)}...`);
      }
    } catch (error: any) {
      if (error.name === "SyntaxError") {
        throw new Error(`${res.status}: ${res.statusText}`);
      }
      throw error;
    }
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
  options?: { headers?: Record<string, string> }
): Promise<Response> {
  try {
    // Determine if we're dealing with FormData
    const isFormData = data instanceof FormData;

    // Set up headers based on data type
    let headers: Record<string, string> = {};

    // Add JWT token from localStorage if available
    const token = localStorage.getItem('authToken');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // If custom headers are provided, use them
    if (options?.headers) {
      headers = { ...headers, ...options.headers };
    }
    // Otherwise, set Content-Type for JSON data (but not for FormData)
    else if (data && !isFormData) {
      headers["Content-Type"] = "application/json";
    }

    // Prepare the request body based on data type
    let body: any = undefined;
    if (data) {
      body = isFormData ? data : JSON.stringify(data);
    }

    const res = await fetch(url, {
      method,
      headers,
      body,
      credentials: "include",
    });

    // We don't await throwIfResNotOk here to return the response
    // and let the calling function handle the error if needed
    return res;
  } catch (error) {
    console.error(`API request error (${method} ${url}):`, error);
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    try {
      console.log(`Fetching: ${queryKey[0]}`);

      // Prepare headers with JWT token if available
      const headers: Record<string, string> = {};
      const token = localStorage.getItem('authToken');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const res = await fetch(queryKey[0] as string, {
        credentials: "include",
        headers,
      });

      if (unauthorizedBehavior === "returnNull" && res.status === 401) {
        console.log(`Unauthorized, returning null for: ${queryKey[0]}`);
        return null;
      }

      // Check if response is OK before trying to parse JSON
      if (!res.ok) {
        const contentType = res.headers.get("content-type");
        let errorMessage = `Request failed with status ${res.status}`;

        try {
          if (contentType && contentType.includes("application/json")) {
            const errorData = await res.json();
            errorMessage = errorData.message || errorMessage;
          } else {
            const text = await res.text();
            errorMessage = `${errorMessage}: ${text.substring(0, 100)}`;
          }
        } catch (parseError) {
          console.error("Error parsing error response:", parseError);
        }

        throw new Error(errorMessage);
      }

      try {
        const data = await res.json();
        console.log(`Data received for ${queryKey[0]}:`, data);
        return data;
      } catch (jsonError) {
        console.error(`Error parsing JSON for ${queryKey[0]}:`, jsonError);
        throw new Error(`Failed to parse response as JSON. The server might be returning HTML instead of JSON.`);
      }
    } catch (error) {
      console.error(`Query error (${queryKey[0]}):`, error);
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 60000, // 1 minute instead of Infinity for better reactivity
      retry: 1,
    },
    mutations: {
      retry: 1,
    },
  },
});
