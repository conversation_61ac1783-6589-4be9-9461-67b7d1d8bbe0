# OpenAI API Setup for PathLink AI Coach

## Overview
The PathLink AI Coach now supports real OpenAI API integration to provide personalized career guidance based on user profiles and resume data.

## Setup Instructions

### 1. Get Your OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in or create an account
3. Click "Create new secret key"
4. Copy the API key (starts with `sk-`)

### 2. Update Environment Variables
1. Open the `.env` file in your project root
2. Replace the placeholder with your actual API key:
   ```
   OPENAI_API_KEY=sk-your-actual-api-key-here
   ```
3. Save the file

### 3. Restart the Server
```bash
npm run dev
```

## Features Enabled with OpenAI API

### Personalized Responses
- **User Profile Integration**: AI knows user's name, role, position, skills, and experience
- **Resume Analysis**: For workers, AI can reference their uploaded resume content
- **Job Context**: For employers, AI can reference their current job postings
- **Conversation Memory**: AI maintains context throughout the chat session

### Enhanced Capabilities
- **Tailored Advice**: Responses are customized based on user's specific situation
- **Industry-Specific Guidance**: AI adapts advice to user's industry and role
- **Experience-Level Appropriate**: Advice matches user's experience level
- **Goal-Oriented**: AI can reference user's career goals and preferences

## Example Interactions

### For Workers
- "How can I improve my resume?" → AI references their actual resume content
- "What skills should I develop?" → AI suggests based on their current skills and goals
- "How do I prepare for interviews?" → AI provides role-specific interview tips

### For Employers
- "How do I write better job descriptions?" → AI references their current job postings
- "What interview questions should I ask?" → AI suggests questions for their specific roles
- "How do I attract better candidates?" → AI analyzes their hiring patterns

## Fallback Behavior
If no OpenAI API key is provided, the system automatically falls back to comprehensive demo responses that still provide valuable career guidance.

## Cost Considerations
- OpenAI charges per token used
- Typical chat responses cost $0.01-0.05 per interaction
- Monitor usage at [OpenAI Usage Dashboard](https://platform.openai.com/usage)

## Security
- API key is stored securely in environment variables
- Never commit API keys to version control
- User data is only sent to OpenAI for processing, not stored

## Troubleshooting

### Common Issues
1. **"Demo mode" messages**: Check that your API key is correctly set in `.env`
2. **API errors**: Verify your OpenAI account has sufficient credits
3. **Slow responses**: OpenAI API can take 2-10 seconds to respond

### Debug Logs
Check the server console for:
- `🤖 Using OpenAI API for chat response` (success)
- `🤖 Using demo chat response` (fallback mode)
- API error messages with specific error codes

## Support
For OpenAI API issues, visit [OpenAI Help Center](https://help.openai.com/)
