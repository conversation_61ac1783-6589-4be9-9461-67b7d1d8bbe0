/**
 * This script adds the mock resumes to the database
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { drizzle } from 'drizzle-orm/node-postgres';
import { eq } from 'drizzle-orm';
import pg from 'pg';
import * as schema from '../shared/schema.ts';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const { Pool } = pg;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: {
    rejectUnauthorized: false
  }
});

const db = drizzle(pool, { schema });

// Sample resume data with worker information
const sampleResumes = [
  {
    name: "<PERSON>",
    email: "<EMAIL>",
    skills: ["JavaScript", "React", "Node.js", "TypeScript", "MongoDB"],
    experience: "5 years of frontend development experience at tech startups",
    education: "Bachelor's in Computer Science, Stanford University",
    industry: "Technology"
  },
  {
    name: "Emily Johnson",
    email: "<EMAIL>",
    skills: ["Python", "Django", "PostgreSQL", "AWS", "Docker"],
    experience: "7 years as a backend developer with focus on scalable systems",
    education: "Master's in Software Engineering, MIT",
    industry: "Technology"
  },
  {
    name: "Michael Brown",
    email: "<EMAIL>",
    skills: ["Java", "Spring Boot", "Microservices", "Kubernetes", "CI/CD"],
    experience: "10 years in enterprise software development",
    education: "Bachelor's in Computer Engineering, UC Berkeley",
    industry: "Technology"
  },
  {
    name: "Sarah Davis",
    email: "<EMAIL>",
    skills: ["Data Analysis", "SQL", "Python", "Tableau", "Excel"],
    experience: "6 years as a data analyst in the finance sector",
    education: "Master's in Business Analytics, NYU",
    industry: "Finance"
  },
  {
    name: "David Wilson",
    email: "<EMAIL>",
    skills: ["UX/UI Design", "Figma", "Adobe XD", "HTML/CSS", "User Research"],
    experience: "8 years designing user interfaces for web and mobile applications",
    education: "Bachelor's in Graphic Design, RISD",
    industry: "Design"
  }
];

// Function to generate resume content
function generateResumeContent(resume) {
  return `
# ${resume.name}
Email: ${resume.email}

## Skills
${resume.skills.join(', ')}

## Experience
${resume.experience}

## Education
${resume.education}

## Industry
${resume.industry}

## Additional Information
Passionate professional with a strong background in ${resume.industry.toLowerCase()}.
Experienced in ${resume.skills.slice(0, 3).join(', ')}, and other technologies.
Looking for opportunities to leverage my expertise in ${resume.skills[0]} and ${resume.skills[1]}.

References available upon request.
`;
}

async function addResumesToDatabase() {
  try {
    console.log('Connecting to database...');

    for (const resumeData of sampleResumes) {
      // First, create or find the worker user
      let worker;
      try {
        // Try to find existing user
        const existingUsers = await db.select().from(schema.users).where(eq(schema.users.email, resumeData.email));

        if (existingUsers.length > 0) {
          worker = existingUsers[0];
          console.log(`Found existing worker: ${worker.name}`);
        } else {
          // Create new worker user
          const [newWorker] = await db.insert(schema.users).values({
            email: resumeData.email,
            password: '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', // hashed "password123"
            name: resumeData.name,
            role: 'worker'
          }).returning();

          worker = newWorker;
          console.log(`Created new worker: ${worker.name}`);
        }
      } catch (error) {
        console.error(`Error creating/finding worker ${resumeData.name}:`, error);
        continue;
      }

      // Generate resume content
      const resumeContent = generateResumeContent(resumeData);
      const filename = `${resumeData.name.replace(/\s+/g, '_')}_Resume.txt`;
      const filePath = path.join('uploads', 'resumes', filename);

      // Ensure the file exists
      const fullFilePath = path.join(__dirname, '..', filePath);
      if (!fs.existsSync(fullFilePath)) {
        fs.writeFileSync(fullFilePath, resumeContent);
        console.log(`Created resume file: ${fullFilePath}`);
      }

      // Check if resume already exists for this worker
      const existingResumes = await db.select().from(schema.resumes).where(eq(schema.resumes.worker_id, worker.id));

      if (existingResumes.length === 0) {
        // Add resume to database
        const [resume] = await db.insert(schema.resumes).values({
          worker_id: worker.id,
          filename: filename,
          file_path: filePath,
          file_size: Buffer.byteLength(resumeContent, 'utf8'),
          file_type: 'txt',
          extracted_text: resumeContent
        }).returning();

        console.log(`Added resume to database: ${resume.filename} for worker ${worker.name}`);
      } else {
        console.log(`Resume already exists for worker ${worker.name}`);
      }
    }

    console.log('Successfully added all resumes to database!');

  } catch (error) {
    console.error('Error adding resumes to database:', error);
  } finally {
    await pool.end();
  }
}

addResumesToDatabase();
