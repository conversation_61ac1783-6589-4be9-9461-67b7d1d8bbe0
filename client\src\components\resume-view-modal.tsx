import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, FileText, Download, AlertCircle, Eye, ArrowLeft } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

// Resume interface to match the server schema
interface Resume {
  id: number;
  worker_id: number;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: 'pdf' | 'docx' | 'doc' | 'txt';
  upload_date: string;
  extracted_text?: string;
}

interface ResumeViewModalProps {
  open: boolean;
  onClose: () => void;
  workerId?: number;
  workerName: string;
  resumeData?: Resume;
}

export default function ResumeViewModal({
  open,
  onClose,
  workerId,
  workerName,
  resumeData,
}: ResumeViewModalProps) {
  const { toast } = useToast();
  const [viewMode, setViewMode] = useState<'info' | 'view'>('info');

  // Use passed resume data or fetch if not provided
  const {
    data: fetchedResume,
    isLoading,
    error,
  } = useQuery<Resume>({
    queryKey: ['/api/resumes/worker', workerId],
    queryFn: async () => {
      if (!workerId) {
        return null; // No workerId provided
      }
      const response = await apiRequest('GET', `/api/resumes/worker/${workerId}`);
      if (!response.ok) {
        if (response.status === 404) {
          return null; // No resume found is not an error
        }
        throw new Error('Failed to fetch resume');
      }
      return response.json();
    },
    enabled: open && !!workerId && !resumeData, // Only fetch when modal is open, workerId is provided, and no resume data passed
  });

  // Use passed resume data or fetched data
  const resume = resumeData || fetchedResume;

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  // Handle download button click
  const handleDownload = () => {
    if (resume) {
      window.open(`/api/resumes/download/${resume.id}`, '_blank');
    }
  };

  // Handle view button click
  const handleViewResume = () => {
    setViewMode('view');
  };

  // Handle view in new tab
  const handleViewInNewTab = () => {
    if (resume) {
      window.open(`/api/resumes/view/${resume.id}`, '_blank');
    }
  };

  // Handle back button click
  const handleBackToInfo = () => {
    setViewMode('info');
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={viewMode === 'view' ? "sm:max-w-5xl h-[90vh] w-[90vw]" : "sm:max-w-md"}>
        <DialogHeader>
          <DialogTitle>{workerName}'s Resume</DialogTitle>
          <DialogDescription>
            {viewMode === 'info' ?
              "View or download the candidate's resume to evaluate their skills and experience." :
              "Viewing resume content. You can download for better quality."
            }
          </DialogDescription>
        </DialogHeader>

        <div className={`py-4 ${viewMode === 'view' ? 'h-full flex flex-col' : ''}`}>
          {isLoading && !resumeData ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-destructive">
              <AlertCircle className="h-10 w-10 mx-auto mb-2" />
              <p>Error loading resume. Please try again later.</p>
            </div>
          ) : !resume ? (
            <div className="text-center py-8">
              <FileText className="h-10 w-10 mx-auto mb-2 text-muted-foreground" />
              <p className="text-muted-foreground">This candidate has not uploaded a resume yet.</p>
            </div>
          ) : viewMode === 'view' ? (
            <div className="flex flex-col h-full">
              <div className="flex-grow overflow-hidden bg-white rounded-md shadow-sm border">
                <div className="relative w-full h-full p-6">
                  {resume.extracted_text ? (
                    <div className="h-[calc(90vh-200px)] overflow-y-auto">
                      <div className="prose max-w-none">
                        <pre className="whitespace-pre-wrap font-sans text-sm leading-relaxed text-gray-800">
                          {resume.extracted_text}
                        </pre>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center h-[calc(90vh-200px)] text-gray-500">
                      <FileText className="h-16 w-16 mb-4 text-gray-300" />
                      <p className="text-lg font-medium mb-2">Resume content not available</p>
                      <p className="text-sm text-center mb-4">
                        The text content of this resume could not be extracted for viewing.
                        <br />
                        Please download the file to view the original document.
                      </p>
                      <Button
                        onClick={handleViewInNewTab}
                        variant="outline"
                        className="mb-2"
                      >
                        <Eye className="mr-2 h-4 w-4" />
                        Try Opening in New Tab
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex justify-between mt-4">
                <Button
                  variant="outline"
                  onClick={handleBackToInfo}
                  className="flex items-center"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Details
                </Button>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    onClick={handleViewInNewTab}
                    className="flex items-center"
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    Open in New Tab
                  </Button>
                  <Button
                    onClick={handleDownload}
                    className="bg-[#1C2A42] hover:bg-[#1C2A42]/90 text-white"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Download Resume
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-start p-4 border rounded-md">
                <FileText className="h-10 w-10 mr-3 text-primary" />
                <div className="flex-1">
                  <h4 className="font-medium">{resume.filename}</h4>
                  <p className="text-sm text-muted-foreground">
                    {formatFileSize(resume.file_size)} • {resume.file_type.toUpperCase()} •
                    Uploaded {formatDistanceToNow(new Date(resume.upload_date), { addSuffix: true })}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  onClick={onClose}
                >
                  Close
                </Button>
                <Button
                  variant="outline"
                  onClick={handleViewResume}
                  disabled={!resume}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Resume
                </Button>
                <Button
                  onClick={handleDownload}
                  disabled={!resume}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}