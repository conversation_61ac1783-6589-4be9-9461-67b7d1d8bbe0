import { migrate } from 'drizzle-orm/node-postgres/migrator';
import { db, pool } from './db';

/**
 * Run database migrations to ensure the schema is up to date
 */
export async function runMigrations() {
  if (!process.env.DATABASE_URL) {
    console.warn('DATABASE_URL not set, skipping migrations');
    return;
  }

  try {
    console.log('Running database migrations...');

    // Check if db is properly initialized
    if (!db || !db.select || typeof db.select !== 'function') {
      throw new Error('Database connection not properly initialized');
    }

    // Run migrations
    await migrate(db, { migrationsFolder: 'drizzle' });

    console.log('Database migrations completed successfully');

    // Seed the database with initial data if needed
    // await seedDatabase();

    return true;
  } catch (error) {
    console.error('Error running database migrations:', error instanceof Error ? error.message : String(error));
    throw error; // Rethrow to allow the caller to handle it
  }
}

/**
 * Seed the database with initial data
 * This is useful for adding admin users, default settings, etc.
 */
async function seedDatabase() {
  // Check if we need to seed the database
  // For example, check if there are any users
  // If not, add some default users

  // This is just an example and should be customized based on your needs
  console.log('Checking if database needs seeding...');

  // Add your seeding logic here

  console.log('Database seeding completed');
}
