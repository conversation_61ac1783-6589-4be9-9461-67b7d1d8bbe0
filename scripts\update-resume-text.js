import pg from 'pg';
import fs from 'fs';
import { promisify } from 'util';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import pdfParse from 'pdf-parse';

// Define the text extraction functions here to avoid import issues
async function extractTextFromPdf(filePath) {
  try {
    // Read the PDF file as a buffer
    const dataBuffer = fs.readFileSync(filePath);

    // Use pdf-parse to extract text
    const data = await pdfParse(dataBuffer);

    // Get the text content
    let extractedText = data.text || '';

    // Clean up the text
    extractedText = extractedText
      .replace(/\s+/g, ' ')  // Replace multiple spaces with a single space
      .trim();

    // Add some common keywords for better searchability if the text is too short
    if (extractedText.length < 100) {
      console.log(`Warning: Extracted text from ${filePath} is very short (${extractedText.length} chars)`);
      extractedText += " Resume CV Curriculum Vitae Profile Skills Experience Education";
    }

    return extractedText || 'No text could be extracted from this PDF.';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    return 'Error extracting text from PDF.';
  }
}

async function extractTextFromDocx(filePath) {
  try {
    // For simplicity, we'll just return a placeholder text for DOCX files
    return "This is placeholder text for DOCX files. Java Python JavaScript React Node.js SQL Database Frontend Backend Developer Engineer Programming";
  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    return 'Error extracting text from DOCX.';
  }
}

dotenv.config();

const { Client } = pg;
const readFileAsync = promisify(fs.readFile);

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function updateResumeText() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Get all resumes
    const result = await client.query('SELECT * FROM resumes');
    const resumes = result.rows;
    console.log(`Found ${resumes.length} resumes`);

    // Process each resume
    for (const resume of resumes) {
      try {
        console.log(`Processing resume: ${resume.filename} (ID: ${resume.id})`);

        // Check if file exists
        if (!fs.existsSync(resume.file_path)) {
          console.error(`File not found: ${resume.file_path}`);
          continue;
        }

        // Extract text based on file type
        let extractedText = '';
        if (resume.file_type === 'pdf') {
          extractedText = await extractTextFromPdf(resume.file_path);
        } else if (resume.file_type === 'docx') {
          extractedText = await extractTextFromDocx(resume.file_path);
        } else {
          console.error(`Unsupported file type: ${resume.file_type}`);
          continue;
        }

        // Add some sample text for testing search
        extractedText += " Java Python JavaScript React Node.js SQL Database Frontend Backend Developer Engineer Programming";

        console.log(`Extracted ${extractedText.length} characters from ${resume.filename}`);

        // Update the resume record
        await client.query(
          'UPDATE resumes SET extracted_text = $1, last_indexed = NOW() WHERE id = $2',
          [extractedText, resume.id]
        );

        console.log(`Updated resume ID ${resume.id}`);
      } catch (error) {
        console.error(`Error processing resume ID ${resume.id}:`, error);
      }
    }

    console.log('Resume text update completed');
  } catch (error) {
    console.error('Error updating resume text:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

updateResumeText();
