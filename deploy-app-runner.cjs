#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 PathLink AWS App Runner Deployment');
console.log('=====================================\n');

// AWS CLI path
const AWS_CLI = '"C:\\Program Files\\Amazon\\AWSCLIV2\\aws.exe"';

function createDockerfile() {
  console.log('🐳 Creating Dockerfile for App Runner...');
  
  const dockerfileContent = `FROM node:18-alpine

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy built application
COPY dist ./dist
COPY uploads ./uploads

# Create uploads directory if it doesn't exist
RUN mkdir -p uploads/resumes uploads/profile

# Expose port
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production
ENV PORT=8080

# Start the application
CMD ["node", "dist/index.js"]
`;

  fs.writeFileSync('Dockerfile', dockerfileContent);
  console.log('✅ Dockerfile created');
}

function createAppRunnerConfig() {
  console.log('📋 Creating App Runner configuration...');
  
  const appRunnerConfig = {
    "Version": "1.0",
    "Runtime": "nodejs18",
    "Build": {
      "Commands": {
        "Build": [
          "npm ci",
          "npm run build:aws"
        ]
      }
    },
    "Run": {
      "Runtime-version": "18",
      "Command": "npm start",
      "Network": {
        "Port": "8080",
        "Env": "PORT"
      },
      "Env": [
        {
          "Name": "NODE_ENV",
          "Value": "production"
        },
        {
          "Name": "PORT",
          "Value": "8080"
        },
        {
          "Name": "DATABASE_URL",
          "Value": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
        },
        {
          "Name": "SESSION_SECRET",
          "Value": "pathlink-production-secret-2024"
        },
        {
          "Name": "JWT_SECRET",
          "Value": "pathlink-jwt-secret-2024"
        },
        {
          "Name": "OPENAI_API_KEY",
          "Value": "********************************************************************************************************************************************************************"
        },
        {
          "Name": "SENDGRID_API_KEY",
          "Value": "SG.1234567890abcdefghijklmnopqrstuvwxyz"
        }
      ]
    }
  };

  fs.writeFileSync('apprunner.yaml', JSON.stringify(appRunnerConfig, null, 2));
  console.log('✅ App Runner configuration created');
}

function buildApplication() {
  console.log('\n🔨 Building application...');
  try {
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed:', error.message);
    return false;
  }
}

function createECRRepository() {
  console.log('\n📦 Creating ECR repository...');
  
  const repoName = 'pathlink-app';
  
  try {
    // Check if repository exists
    try {
      execSync(`${AWS_CLI} ecr describe-repositories --repository-names ${repoName}`, { stdio: 'pipe' });
      console.log('✅ ECR repository already exists');
      return repoName;
    } catch (e) {
      // Repository doesn't exist, create it
    }
    
    execSync(`${AWS_CLI} ecr create-repository --repository-name ${repoName} --region us-east-1`, { stdio: 'inherit' });
    console.log('✅ ECR repository created');
    return repoName;
  } catch (error) {
    console.log('❌ Failed to create ECR repository:', error.message);
    return null;
  }
}

function buildAndPushDockerImage(repoName) {
  console.log('\n🐳 Building and pushing Docker image...');
  
  try {
    // Get ECR login token
    const loginCmd = execSync(`${AWS_CLI} ecr get-login-password --region us-east-1`, { encoding: 'utf8' }).trim();
    
    // Get account ID
    const accountResult = execSync(`${AWS_CLI} sts get-caller-identity --query Account --output text`, { encoding: 'utf8' }).trim();
    const accountId = accountResult;
    
    const imageUri = `${accountId}.dkr.ecr.us-east-1.amazonaws.com/${repoName}:latest`;
    
    // Docker login
    execSync(`echo ${loginCmd} | docker login --username AWS --password-stdin ${accountId}.dkr.ecr.us-east-1.amazonaws.com`, { stdio: 'inherit' });
    
    // Build image
    execSync(`docker build -t ${repoName} .`, { stdio: 'inherit' });
    
    // Tag image
    execSync(`docker tag ${repoName}:latest ${imageUri}`, { stdio: 'inherit' });
    
    // Push image
    execSync(`docker push ${imageUri}`, { stdio: 'inherit' });
    
    console.log('✅ Docker image built and pushed');
    return imageUri;
  } catch (error) {
    console.log('❌ Failed to build/push Docker image:', error.message);
    return null;
  }
}

function createAppRunnerService(imageUri) {
  console.log('\n🏃 Creating App Runner service...');
  
  const serviceName = 'pathlink-service';
  
  try {
    // Check if service exists
    try {
      const result = execSync(`${AWS_CLI} apprunner describe-service --service-arn arn:aws:apprunner:us-east-1:*:service/${serviceName}/*`, { stdio: 'pipe' });
      console.log('✅ App Runner service already exists');
      return serviceName;
    } catch (e) {
      // Service doesn't exist, create it
    }
    
    const serviceConfig = {
      "ServiceName": serviceName,
      "SourceConfiguration": {
        "ImageRepository": {
          "ImageIdentifier": imageUri,
          "ImageConfiguration": {
            "Port": "8080",
            "RuntimeEnvironmentVariables": {
              "NODE_ENV": "production",
              "PORT": "8080",
              "DATABASE_URL": "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require",
              "SESSION_SECRET": "pathlink-production-secret-2024",
              "JWT_SECRET": "pathlink-jwt-secret-2024",
              "OPENAI_API_KEY": "********************************************************************************************************************************************************************",
              "SENDGRID_API_KEY": "SG.1234567890abcdefghijklmnopqrstuvwxyz"
            }
          },
          "ImageRepositoryType": "ECR"
        },
        "AutoDeploymentsEnabled": false
      },
      "InstanceConfiguration": {
        "Cpu": "0.25 vCPU",
        "Memory": "0.5 GB"
      }
    };
    
    // Write config to file
    fs.writeFileSync('service-config.json', JSON.stringify(serviceConfig, null, 2));
    
    execSync(`${AWS_CLI} apprunner create-service --cli-input-json file://service-config.json`, { stdio: 'inherit' });
    
    console.log('✅ App Runner service creation initiated');
    console.log('⏳ Waiting for service to be ready...');
    
    // Wait for service to be ready
    let attempts = 0;
    const maxAttempts = 20; // 10 minutes max
    
    while (attempts < maxAttempts) {
      try {
        const result = execSync(`${AWS_CLI} apprunner list-services --query "ServiceSummaryList[?ServiceName=='${serviceName}'].Status" --output text`, { encoding: 'utf8' }).trim();
        
        console.log(`Service status: ${result}`);
        
        if (result === 'RUNNING') {
          console.log('✅ Service is running!');
          return serviceName;
        }
      } catch (e) {
        console.log('Checking service status...');
      }
      
      attempts++;
      // Wait 30 seconds before next check
      execSync('timeout /t 30 /nobreak', { stdio: 'pipe' });
    }
    
    console.log('⚠️ Service creation is taking longer than expected');
    return serviceName;
    
  } catch (error) {
    console.log('❌ Failed to create App Runner service:', error.message);
    return null;
  }
}

function getServiceURL(serviceName) {
  console.log('\n🔍 Getting service URL...');
  
  try {
    const result = execSync(`${AWS_CLI} apprunner list-services --query "ServiceSummaryList[?ServiceName=='${serviceName}'].ServiceUrl" --output text`, { encoding: 'utf8' }).trim();
    
    if (result && result !== 'None') {
      console.log(`✅ Service URL: ${result}`);
      return result.replace('https://', '');
    }
    
    console.log('⚠️ Could not get service URL');
    return null;
  } catch (error) {
    console.log('❌ Failed to get service URL:', error.message);
    return null;
  }
}

function updateNetlifyConfig(appUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');
    
    // Replace any existing AWS URLs
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/[a-zA-Z0-9-]+\.us-east-1\.elasticbeanstalk\.com/g,
      `https://${appUrl}`
    );
    
    // Also replace any App Runner URLs
    netlifyConfig = netlifyConfig.replace(
      /https:\/\/[a-zA-Z0-9-]+\.us-east-1\.awsapprunner\.com/g,
      `https://${appUrl}`
    );
    
    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated');
    console.log(`🔗 API calls will redirect to: https://${appUrl}`);
    
  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log(`Please manually update netlify.toml with: https://${appUrl}`);
  }
}

async function main() {
  console.log('Starting PathLink AWS App Runner deployment...\n');
  
  // Step 1: Create deployment files
  createDockerfile();
  createAppRunnerConfig();
  
  // Step 2: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed.');
    process.exit(1);
  }
  
  // Step 3: Create ECR repository
  const repoName = createECRRepository();
  if (!repoName) {
    console.log('\n❌ Failed to create ECR repository.');
    process.exit(1);
  }
  
  // Step 4: Build and push Docker image
  const imageUri = buildAndPushDockerImage(repoName);
  if (!imageUri) {
    console.log('\n❌ Failed to build/push Docker image.');
    process.exit(1);
  }
  
  // Step 5: Create App Runner service
  const serviceName = createAppRunnerService(imageUri);
  if (!serviceName) {
    console.log('\n❌ Failed to create App Runner service.');
    process.exit(1);
  }
  
  // Step 6: Get service URL
  const appUrl = getServiceURL(serviceName);
  if (appUrl) {
    updateNetlifyConfig(appUrl);
    
    console.log('\n🎉 DEPLOYMENT COMPLETE!');
    console.log('======================');
    console.log('✅ Backend deployed to AWS App Runner');
    console.log('✅ Netlify configuration updated');
    console.log('✅ API redirects configured');
    console.log(`\n🌍 Your application: https://${appUrl}`);
    console.log(`🔍 Health check: https://${appUrl}/health`);
    console.log(`🔌 API test: https://${appUrl}/api/health`);
  } else {
    console.log('\n⚠️ Deployment completed but could not get URL');
    console.log('Check AWS console for service status');
  }
  
  // Cleanup
  try {
    fs.unlinkSync('service-config.json');
    console.log('✅ Cleaned up temporary files');
  } catch (e) {
    // Ignore cleanup errors
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
