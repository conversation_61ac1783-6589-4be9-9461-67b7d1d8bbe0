const { neon } = require('@neondatabase/serverless');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
// First try to load from the root directory
dotenv.config();
// Then try to load from the netlify directory
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Get the database connection string from environment variables
const connectionString = process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require';

// Initialize Neon client
const sql = neon(connectionString);

// Test the database connection
const testConnection = async () => {
  try {
    const result = await sql`SELECT 1 as test`;
    console.log('Successfully connected to Neon database');
    return true;
  } catch (error) {
    console.error('Error connecting to Neon database:', error);
    return false;
  }
};

// Get user by email
const getUserByEmail = async (email) => {
  try {
    const result = await sql`
      SELECT u.id, u.email, u.name, u.role, u.password,
             up.profile_picture as "profilePicture",
             up.position, up.location, up.bio, up.company,
             up.years_of_experience as "yearsOfExperience", up.skills
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.email = ${email}
      LIMIT 1
    `;
    return result[0] || null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
};

// Get user by ID
const getUserById = async (id) => {
  try {
    const result = await sql`
      SELECT u.id, u.email, u.name, u.role, u.password,
             up.profile_picture as "profilePicture",
             up.position, up.location, up.bio, up.company,
             up.years_of_experience as "yearsOfExperience", up.skills
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.id = ${id}
      LIMIT 1
    `;
    return result[0] || null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
};

// Get user profile
const getUserProfile = async (userId) => {
  try {
    const result = await sql`
      SELECT * FROM user_profiles WHERE user_id = ${userId}
    `;
    return result[0] || null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

// Update user profile
const updateUserProfile = async (userId, profileData) => {
  try {
    // Check if profile exists
    const checkResult = await sql`
      SELECT * FROM user_profiles WHERE user_id = ${userId}
    `;

    if (checkResult.length === 0) {
      // Create new profile
      const result = await sql`
        INSERT INTO user_profiles (
          user_id, position, location, bio, company,
          years_of_experience, skills, profile_picture, created_at, updated_at
        ) VALUES (
          ${userId}, ${profileData.position || null}, ${profileData.location || null},
          ${profileData.bio || null}, ${profileData.company || null},
          ${profileData.yearsOfExperience || null}, ${profileData.skills || null},
          ${profileData.profilePicture || null}, NOW(), NOW()
        )
        RETURNING *
      `;
      return result[0];
    } else {
      // Update existing profile
      const result = await sql`
        UPDATE user_profiles SET
          position = COALESCE(${profileData.position}, position),
          location = COALESCE(${profileData.location}, location),
          bio = COALESCE(${profileData.bio}, bio),
          company = COALESCE(${profileData.company}, company),
          years_of_experience = COALESCE(${profileData.yearsOfExperience}, years_of_experience),
          skills = COALESCE(${profileData.skills}, skills),
          profile_picture = COALESCE(${profileData.profilePicture}, profile_picture),
          updated_at = NOW()
        WHERE user_id = ${userId}
        RETURNING *
      `;
      return result[0];
    }
  } catch (error) {
    console.error('Error updating user profile:', error);
    return null;
  }
};

// Get all jobs
const getJobs = async () => {
  try {
    const result = await sql`
      SELECT id, title, company, location, industry,
             salary_range, description, required_skills, created_at, employer_id
      FROM jobs
      ORDER BY created_at DESC
    `;
    return result;
  } catch (error) {
    console.error('Error getting jobs:', error);
    return [];
  }
};

// Get all resumes with worker information
const getResumesWithWorkers = async () => {
  try {
    const result = await sql`
      SELECT r.*, u.id as worker_id, u.name as worker_name, u.email as worker_email
      FROM resumes r
      JOIN users u ON r.worker_id = u.id
      WHERE u.role = 'worker'
      ORDER BY r.upload_date DESC
    `;

    return result.map(row => ({
      resume: {
        id: row.id,
        filename: row.filename,
        file_path: row.file_path,
        file_size: row.file_size,
        file_type: row.file_type,
        extracted_text: row.extracted_text,
        upload_date: row.upload_date,
        user_id: row.worker_id
      },
      worker: {
        id: row.worker_id,
        name: row.worker_name,
        email: row.worker_email,
        role: 'worker'
      }
    }));
  } catch (error) {
    console.error('Error getting resumes with workers:', error);
    return [];
  }
};

// Get resumes by user
const getResumesByUser = async (userId) => {
  try {
    const result = await sql`
      SELECT id, filename, file_path, file_size, file_type, extracted_text, upload_date, worker_id as user_id
      FROM resumes
      WHERE worker_id = ${userId}
      ORDER BY upload_date DESC
    `;
    return result;
  } catch (error) {
    console.error('Error getting resumes by user:', error);
    return [];
  }
};

// Create application (using matches table since that's what exists)
const createApplication = async (applicationData) => {
  try {
    const { user_id, job_id } = applicationData;
    const result = await sql`
      INSERT INTO matches (worker_id, job_id, status, match_score, match_date)
      VALUES (${user_id}, ${job_id}, 'pending', 85, NOW())
      RETURNING id, worker_id as user_id, job_id, status, match_date as created_at
    `;
    return result[0];
  } catch (error) {
    console.error('Error creating application:', error);
    throw error;
  }
};

// Get applications by user (using matches table)
const getApplicationsByUser = async (userId) => {
  try {
    const result = await sql`
      SELECT m.id, m.worker_id as user_id, m.job_id, m.status, m.match_date as created_at,
             j.title, j.company, j.location, j.salary_range as salary
      FROM matches m
      JOIN jobs j ON m.job_id = j.id
      WHERE m.worker_id = ${userId}
      ORDER BY m.match_date DESC
    `;
    return result;
  } catch (error) {
    console.error('Error getting applications by user:', error);
    return [];
  }
};

// Get applications by employer (using matches table)
const getApplicationsByEmployer = async (employerId) => {
  try {
    const result = await sql`
      SELECT m.id, m.worker_id as user_id, m.job_id, m.status, m.match_date as created_at,
             j.title, j.company, u.name as worker_name, u.email as worker_email
      FROM matches m
      JOIN jobs j ON m.job_id = j.id
      JOIN users u ON m.worker_id = u.id
      WHERE j.employer_id = ${employerId}
      ORDER BY m.match_date DESC
    `;
    return result;
  } catch (error) {
    console.error('Error getting applications by employer:', error);
    return [];
  }
};

module.exports = {
  sql,
  testConnection,
  getUserByEmail,
  getUserById,
  getUserProfile,
  updateUserProfile,
  getJobs,
  getResumesWithWorkers,
  getResumesByUser,
  createApplication,
  getApplicationsByUser,
  getApplicationsByEmployer
};
