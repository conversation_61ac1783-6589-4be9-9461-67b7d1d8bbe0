const { Pool } = require('pg');
const dotenv = require('dotenv');
const path = require('path');

// Load environment variables
// First try to load from the root directory
dotenv.config();
// Then try to load from the netlify directory
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Get the database connection string from environment variables
const connectionString = process.env.DATABASE_URL;

// Create a new pool using the connection string
const pool = new Pool({
  connectionString,
  ssl: {
    rejectUnauthorized: false // Required for Neon database connection
  }
});

// Test the database connection
const testConnection = async () => {
  try {
    const client = await pool.connect();
    console.log('Successfully connected to Neon database');
    client.release();
    return true;
  } catch (error) {
    console.error('Error connecting to Neon database:', error);
    return false;
  }
};

// Get user by email
const getUserByEmail = async (email) => {
  try {
    const query = 'SELECT * FROM users WHERE email = $1';
    const result = await pool.query(query, [email]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    return null;
  }
};

// Get user by ID
const getUserById = async (id) => {
  try {
    const query = 'SELECT * FROM users WHERE id = $1';
    const result = await pool.query(query, [id]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    return null;
  }
};

// Get user profile
const getUserProfile = async (userId) => {
  try {
    const query = 'SELECT * FROM profiles WHERE user_id = $1';
    const result = await pool.query(query, [userId]);
    return result.rows[0] || null;
  } catch (error) {
    console.error('Error getting user profile:', error);
    return null;
  }
};

// Update user profile
const updateUserProfile = async (userId, profileData) => {
  try {
    // Check if profile exists
    const checkQuery = 'SELECT * FROM profiles WHERE user_id = $1';
    const checkResult = await pool.query(checkQuery, [userId]);

    if (checkResult.rows.length === 0) {
      // Create new profile
      const insertQuery = `
        INSERT INTO profiles (
          user_id, position, location, bio, company,
          years_of_experience, skills, profile_picture
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;
      const insertResult = await pool.query(insertQuery, [
        userId,
        profileData.position || null,
        profileData.location || null,
        profileData.bio || null,
        profileData.company || null,
        profileData.yearsOfExperience || null,
        profileData.skills || null,
        profileData.profilePicture || null
      ]);
      return insertResult.rows[0];
    } else {
      // Update existing profile
      const updateQuery = `
        UPDATE profiles SET
          position = COALESCE($2, position),
          location = COALESCE($3, location),
          bio = COALESCE($4, bio),
          company = COALESCE($5, company),
          years_of_experience = COALESCE($6, years_of_experience),
          skills = COALESCE($7, skills),
          profile_picture = COALESCE($8, profile_picture),
          updated_at = NOW()
        WHERE user_id = $1
        RETURNING *
      `;
      const updateResult = await pool.query(updateQuery, [
        userId,
        profileData.position || null,
        profileData.location || null,
        profileData.bio || null,
        profileData.company || null,
        profileData.yearsOfExperience || null,
        profileData.skills || null,
        profileData.profilePicture || null
      ]);
      return updateResult.rows[0];
    }
  } catch (error) {
    console.error('Error updating user profile:', error);
    return null;
  }
};

// Get all jobs
const getJobs = async () => {
  try {
    const query = 'SELECT * FROM jobs ORDER BY created_at DESC';
    const result = await pool.query(query);
    return result.rows;
  } catch (error) {
    console.error('Error getting jobs:', error);
    return [];
  }
};

// Get all resumes with worker information
const getResumesWithWorkers = async () => {
  try {
    const query = `
      SELECT r.*, u.id as worker_id, u.name as worker_name, u.email as worker_email
      FROM resumes r
      JOIN users u ON r.user_id = u.id
      WHERE u.role = 'worker'
      ORDER BY r.upload_date DESC
    `;
    const result = await pool.query(query);

    return result.rows.map(row => ({
      resume: {
        id: row.id,
        filename: row.filename,
        file_size: row.file_size,
        file_type: row.file_type,
        upload_date: row.upload_date,
        user_id: row.user_id
      },
      worker: {
        id: row.worker_id,
        name: row.worker_name,
        email: row.worker_email,
        role: 'worker'
      }
    }));
  } catch (error) {
    console.error('Error getting resumes with workers:', error);
    return [];
  }
};

module.exports = {
  pool,
  testConnection,
  getUserByEmail,
  getUserById,
  getUserProfile,
  updateUserProfile,
  getJobs,
  getResumesWithWorkers
};
