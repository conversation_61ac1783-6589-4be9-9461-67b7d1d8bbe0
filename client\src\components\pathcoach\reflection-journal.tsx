import { useState } from "react";
import { useReflectionJournals } from "@/hooks/use-reflection-journals";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Save, BookOpenText, Calendar, MessageSquare } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ReflectionJournal } from "@shared/schema";

// This metadata will be stored in the prompt field of the journal entry
interface JournalMetadata {
  mood: string;
  career_stage: string;
  title: string;
}

const careerStages = [
  "Early Career",
  "Mid Career",
  "Senior Level",
  "Career Transition",
  "Management",
  "Leadership",
  "Post-Retirement"
];

const moodOptions = [
  "Excited",
  "Motivated",
  "Curious",
  "Neutral",
  "Confused",
  "Anxious",
  "Frustrated"
];

export function GPTReflectionJournal() {
  const { journals, isLoading, createJournalMutation } = useReflectionJournals();
  
  const [entryTitle, setEntryTitle] = useState<string>("");
  const [entryText, setEntryText] = useState<string>("");
  const [mood, setMood] = useState<string>("");
  const [careerStage, setCareerStage] = useState<string>("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Store metadata in the prompt field as JSON
    const metadata: JournalMetadata = {
      title: entryTitle,
      mood,
      career_stage: careerStage
    };
    
    createJournalMutation.mutate({
      prompt: JSON.stringify(metadata),
      entry: entryText
    });
  };

  const handleReset = () => {
    setEntryTitle("");
    setEntryText("");
    setMood("");
    setCareerStage("");
  };

  // Parse journal metadata from the prompt field
  const parseJournalMetadata = (journal: ReflectionJournal): JournalMetadata => {
    try {
      return JSON.parse(journal.prompt) as JournalMetadata;
    } catch (error) {
      return { title: "Untitled Entry", mood: "Unknown", career_stage: "Not specified" };
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpenText className="h-5 w-5" />
            GPT Reflection Journal
          </CardTitle>
          <CardDescription>Loading your reflection journals...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BookOpenText className="h-5 w-5" />
          GPT Reflection Journal
        </CardTitle>
        <CardDescription>
          Document your career journey and receive AI-powered insights
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="create" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="create">Create Entry</TabsTrigger>
            <TabsTrigger value="history">
              Journal History 
              {journals && journals.length > 0 && (
                <span className="ml-1.5 inline-flex h-5 w-5 items-center justify-center rounded-full bg-primary/10 text-xs font-medium">
                  {journals.length}
                </span>
              )}
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="create" className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="entry-title">Entry Title</Label>
                <Input
                  id="entry-title"
                  placeholder="Today's career reflection..."
                  value={entryTitle}
                  onChange={(e) => setEntryTitle(e.target.value)}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="mood">Current Mood</Label>
                  <Select value={mood} onValueChange={setMood} required>
                    <SelectTrigger id="mood">
                      <SelectValue placeholder="Select your mood" />
                    </SelectTrigger>
                    <SelectContent>
                      {moodOptions.map((option) => (
                        <SelectItem key={option} value={option}>{option}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="career-stage">Career Stage</Label>
                  <Select value={careerStage} onValueChange={setCareerStage} required>
                    <SelectTrigger id="career-stage">
                      <SelectValue placeholder="Select your career stage" />
                    </SelectTrigger>
                    <SelectContent>
                      {careerStages.map((stage) => (
                        <SelectItem key={stage} value={stage}>{stage}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="entry-text">Journal Entry</Label>
                <Textarea
                  id="entry-text"
                  placeholder="Share your thoughts, challenges, or victories in your career journey..."
                  value={entryText}
                  onChange={(e) => setEntryText(e.target.value)}
                  className="min-h-[150px] resize-none"
                  required
                />
              </div>

              <div className="flex gap-3 justify-end">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={handleReset}
                >
                  Clear
                </Button>
                <Button 
                  type="submit" 
                  disabled={createJournalMutation.isPending}
                >
                  {createJournalMutation.isPending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Journal Entry
                    </>
                  )}
                </Button>
              </div>
            </form>
          </TabsContent>
          
          <TabsContent value="history">
            {journals && journals.length > 0 ? (
              <div className="space-y-6">
                {journals.map((journal: ReflectionJournal, index: number) => {
                  const metadata = parseJournalMetadata(journal);
                  return (
                    <Card key={index} className="border shadow-sm">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg">{metadata.title}</CardTitle>
                            <div className="flex items-center text-sm text-muted-foreground mt-1">
                              <Calendar className="h-3.5 w-3.5 mr-1.5" />
                              {journal.created_at ? new Date(journal.created_at).toLocaleDateString() : "Recent"}
                              <span className="mx-2">•</span>
                              <span className="capitalize">{metadata.mood}</span>
                              <span className="mx-2">•</span>
                              <span>{metadata.career_stage}</span>
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-2">
                        <p className="text-sm mb-4">{journal.entry}</p>
                        
                        {journal.ai_reflection && (
                          <div className="bg-primary/5 p-4 rounded-lg mt-4">
                            <div className="flex items-center gap-2 text-sm font-medium mb-2">
                              <MessageSquare className="h-4 w-4 text-primary" />
                              AI Reflection
                            </div>
                            <p className="text-sm">{journal.ai_reflection}</p>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <BookOpenText className="h-12 w-12 mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-medium mb-2">No journal entries yet</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Start documenting your career journey to receive AI insights
                </p>
                <Button onClick={() => {
                  const createTab = document.querySelector('button[value="create"]');
                  if (createTab) (createTab as HTMLButtonElement).click();
                }}>
                  Create Your First Entry
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}