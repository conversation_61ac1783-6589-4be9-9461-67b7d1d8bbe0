import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Loader2, Search, FileText, User, Filter, Calendar, Clock, Briefcase, Code, Building, SlidersHorizontal, X } from 'lucide-react';
import { formatDistanceToNow, subDays, subMonths } from 'date-fns';
import ResumeViewModal from './resume-view-modal';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Pop<PERSON>,
  <PERSON>overContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";

// Resume interface to match the server schema
interface Resume {
  id: number;
  worker_id: number;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: 'pdf' | 'docx';
  upload_date: string;
  extracted_text?: string;
  last_indexed?: string;
}

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface ResumeWithUser {
  resume: Resume;
  user: User;
}

// Define filter interfaces
interface FilterOptions {
  experienceRange: [number, number];
  fileTypes: string[];
  uploadDateRange: string;
  skills: string[];
  industries: string[];
}

// Define sort options
type SortOption = 'relevance' | 'recent' | 'experience';

export function ResumeSearch() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [showResumeViewModal, setShowResumeViewModal] = useState(false);
  const [selectedWorker, setSelectedWorker] = useState<{id: number, name: string} | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [activeFiltersCount, setActiveFiltersCount] = useState(0);
  const [sortOption, setSortOption] = useState<SortOption>('relevance');

  // Filter states
  const [filters, setFilters] = useState<FilterOptions>({
    experienceRange: [0, 20],
    fileTypes: ['pdf', 'docx'],
    uploadDateRange: 'any',
    skills: [],
    industries: [],
  });

  // Common skills and industries for filtering
  const commonSkills = [
    'JavaScript', 'Python', 'Java', 'React', 'Angular', 'Vue',
    'Node.js', 'SQL', 'NoSQL', 'AWS', 'Azure', 'Docker',
    'Kubernetes', 'Project Management', 'Agile', 'Scrum',
    'Marketing', 'Sales', 'Customer Service', 'Leadership'
  ];

  const commonIndustries = [
    'Technology', 'Finance', 'Healthcare', 'Education',
    'Manufacturing', 'Retail', 'Media', 'Consulting',
    'Government', 'Non-profit'
  ];

  // Filtered results
  const [filteredResults, setFilteredResults] = useState<ResumeWithUser[]>([]);

  // Query for resume search
  const {
    data: searchResults = [],
    refetch,
    isLoading,
    error
  } = useQuery<ResumeWithUser[]>({
    queryKey: ['/api/resumes/search', searchQuery],
    queryFn: async () => {
      if (!searchQuery.trim()) return [];

      console.log('Searching for:', searchQuery);
      const response = await apiRequest('GET', `/api/resumes/search?query=${encodeURIComponent(searchQuery)}`);

      if (!response.ok) {
        console.error('Search API error:', response.status, response.statusText);
        throw new Error('Failed to search resumes');
      }

      const data = await response.json();
      console.log('Search results:', data);
      return data;
    },
    enabled: false, // Don't run automatically
  });

  // Function to apply filters to search results
  const applyFilters = () => {
    // Skip if there are no search results
    if (!searchResults || !searchResults.length) {
      setFilteredResults([]);
      return;
    }

    let results = [...searchResults];

    // Apply file type filter
    if (filters.fileTypes.length && filters.fileTypes.length < 2) {
      results = results.filter(result =>
        filters.fileTypes.includes(result.resume.file_type.toLowerCase())
      );
    }

    // Apply upload date filter
    if (filters.uploadDateRange !== 'any') {
      const now = new Date();
      let cutoffDate: Date;

      switch (filters.uploadDateRange) {
        case 'last7days':
          cutoffDate = subDays(now, 7);
          break;
        case 'last30days':
          cutoffDate = subDays(now, 30);
          break;
        case 'last3months':
          cutoffDate = subMonths(now, 3);
          break;
        case 'last6months':
          cutoffDate = subMonths(now, 6);
          break;
        case 'lastyear':
          cutoffDate = subMonths(now, 12);
          break;
        default:
          cutoffDate = new Date(0); // Beginning of time
      }

      results = results.filter(result =>
        new Date(result.resume.upload_date) >= cutoffDate
      );
    }

    // Apply skills filter
    if (filters.skills.length) {
      results = results.filter(result => {
        const resumeText = (result.resume.extracted_text || '').toLowerCase();
        return filters.skills.some(skill =>
          resumeText.includes(skill.toLowerCase())
        );
      });
    }

    // Apply industry filter
    if (filters.industries.length) {
      results = results.filter(result => {
        const resumeText = (result.resume.extracted_text || '').toLowerCase();
        return filters.industries.some(industry =>
          resumeText.includes(industry.toLowerCase())
        );
      });
    }

    // Apply experience filter - this is approximate based on text analysis
    if (filters.experienceRange[0] > 0 || filters.experienceRange[1] < 20) {
      results = results.filter(result => {
        const resumeText = (result.resume.extracted_text || '').toLowerCase();

        // Try to extract years of experience from resume text
        const experienceMatches = resumeText.match(/(\d+)[\s-]*years?[\s-]*(of)?[\s-]*experience/gi);
        if (!experienceMatches) return true; // If we can't determine, include it

        // Extract the numbers from the matches
        const years = experienceMatches.map(match => {
          const num = parseInt(match.match(/\d+/)?.[0] || '0');
          return num;
        });

        // Use the highest number of years found
        const maxYears = Math.max(...years);

        return maxYears >= filters.experienceRange[0] &&
               maxYears <= filters.experienceRange[1];
      });
    }

    // Apply sorting
    switch (sortOption) {
      case 'recent':
        results.sort((a, b) =>
          new Date(b.resume.upload_date).getTime() - new Date(a.resume.upload_date).getTime()
        );
        break;
      case 'experience':
        results.sort((a, b) => {
          const getExperience = (text: string = '') => {
            const match = text.match(/(\d+)[\s-]*years?[\s-]*(of)?[\s-]*experience/i);
            return match ? parseInt(match[1]) : 0;
          };

          const aExp = getExperience(a.resume.extracted_text);
          const bExp = getExperience(b.resume.extracted_text);

          return bExp - aExp;
        });
        break;
      // 'relevance' is the default sort from the API
    }

    // Count active filters
    let count = 0;
    if (filters.fileTypes.length < 2) count++;
    if (filters.uploadDateRange !== 'any') count++;
    if (filters.skills.length) count++;
    if (filters.industries.length) count++;
    if (filters.experienceRange[0] > 0 || filters.experienceRange[1] < 20) count++;

    setActiveFiltersCount(count);
    setFilteredResults(results);
  };

  // Consolidated useEffect to call applyFilters when search results or filters change
  useEffect(() => {
    if (searchResults && searchResults.length > 0) {
      applyFilters();
    }
  }, [
    searchResults,
    filters.fileTypes,
    filters.uploadDateRange,
    filters.skills,
    filters.industries,
    filters.experienceRange,
    sortOption
  ]);

  // Handle search button click
  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast({
        title: 'Search query required',
        description: 'Please enter keywords to search for resumes',
        variant: 'destructive',
      });
      return;
    }

    // Reset search results before starting a new search
    setFilteredResults([]);
    setIsSearching(true);

    try {
      const results = await refetch();
      // The applyFilters function will be called automatically by the useEffect
      // that depends on searchResults
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: 'Search failed',
        description: 'There was an error searching for resumes. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSearching(false);
    }
  };

  // Reset all filters
  const resetFilters = () => {
    setFilters({
      experienceRange: [0, 20],
      fileTypes: ['pdf', 'docx'],
      uploadDateRange: 'any',
      skills: [],
      industries: [],
    });
    setSortOption('relevance');

    // The applyFilters function will be called automatically by the useEffect
    // that depends on filters
  };

  // Toggle a skill in the filter
  const toggleSkill = (skill: string) => {
    setFilters(prev => {
      if (prev.skills.includes(skill)) {
        return { ...prev, skills: prev.skills.filter(s => s !== skill) };
      } else {
        return { ...prev, skills: [...prev.skills, skill] };
      }
    });
  };

  // Toggle an industry in the filter
  const toggleIndustry = (industry: string) => {
    setFilters(prev => {
      if (prev.industries.includes(industry)) {
        return { ...prev, industries: prev.industries.filter(i => i !== industry) };
      } else {
        return { ...prev, industries: [...prev.industries, industry] };
      }
    });
  };

  // Toggle a file type in the filter
  const toggleFileType = (fileType: string) => {
    setFilters(prev => {
      if (prev.fileTypes.includes(fileType)) {
        // Don't allow removing all file types
        if (prev.fileTypes.length === 1) return prev;
        return { ...prev, fileTypes: prev.fileTypes.filter(t => t !== fileType) };
      } else {
        return { ...prev, fileTypes: [...prev.fileTypes, fileType] };
      }
    });
  };

  // Handle view resume button click
  const handleViewResume = (workerId: number, workerName: string) => {
    setSelectedWorker({ id: workerId, name: workerName });
    setShowResumeViewModal(true);
  };

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Resume Search</CardTitle>
        <CardDescription>
          Search for resumes by keywords, skills, or company names to find the best candidates.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Search Bar with Filters Toggle */}
        <div className="flex flex-col gap-4 mb-6">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                placeholder="Enter keywords, skills, or company names..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  // Clear results if search input is cleared
                  if (e.target.value === '') {
                    setFilteredResults([]);
                  }
                }}
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                className="pr-8 w-full"
              />
              {searchQuery && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-0"
                  onClick={() => {
                    setSearchQuery('');
                    setFilteredResults([]);
                  }}
                >
                  <X className="h-4 w-4 text-muted-foreground" />
                  <span className="sr-only">Clear search</span>
                </Button>
              )}
            </div>
            <Button
              onClick={handleSearch}
              disabled={isSearching}
              className="bg-[#1C2A42] hover:bg-opacity-90"
            >
              {isSearching ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Search className="h-4 w-4 mr-2" />
              )}
              Search
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              <Filter className="h-4 w-4" />
              {activeFiltersCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full w-4 h-4 flex items-center justify-center">
                  {activeFiltersCount}
                </span>
              )}
            </Button>
          </div>

          {/* Advanced Filters Panel */}
          {showFilters && (
            <div className="border rounded-md p-4 bg-muted/30">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-sm font-medium">Advanced Filters</h3>
                <div className="flex gap-2">
                  <Button variant="ghost" size="sm" onClick={resetFilters} className="h-8 px-2 text-xs">
                    <X className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Experience Range */}
                <div className="space-y-2">
                  <Label className="text-xs flex items-center gap-1">
                    <Briefcase className="h-3 w-3" />
                    Years of Experience: {filters.experienceRange[0]} - {filters.experienceRange[1]}
                  </Label>
                  <Slider
                    value={filters.experienceRange}
                    min={0}
                    max={20}
                    step={1}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, experienceRange: value as [number, number] }))}
                    className="py-2"
                  />
                </div>

                {/* Upload Date */}
                <div className="space-y-2">
                  <Label className="text-xs flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    Upload Date
                  </Label>
                  <Select
                    value={filters.uploadDateRange}
                    onValueChange={(value) => setFilters(prev => ({ ...prev, uploadDateRange: value }))}
                  >
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Select date range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="any">Any time</SelectItem>
                      <SelectItem value="last7days">Last 7 days</SelectItem>
                      <SelectItem value="last30days">Last 30 days</SelectItem>
                      <SelectItem value="last3months">Last 3 months</SelectItem>
                      <SelectItem value="last6months">Last 6 months</SelectItem>
                      <SelectItem value="lastyear">Last year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* File Type */}
                <div className="space-y-2">
                  <Label className="text-xs flex items-center gap-1">
                    <FileText className="h-3 w-3" />
                    File Type
                  </Label>
                  <div className="flex gap-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="pdf"
                        checked={filters.fileTypes.includes('pdf')}
                        onCheckedChange={() => toggleFileType('pdf')}
                      />
                      <label htmlFor="pdf" className="text-xs font-medium">PDF</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="docx"
                        checked={filters.fileTypes.includes('docx')}
                        onCheckedChange={() => toggleFileType('docx')}
                      />
                      <label htmlFor="docx" className="text-xs font-medium">DOCX</label>
                    </div>
                  </div>
                </div>

                {/* Sort Options */}
                <div className="space-y-2">
                  <Label className="text-xs flex items-center gap-1">
                    <SlidersHorizontal className="h-3 w-3" />
                    Sort By
                  </Label>
                  <Select
                    value={sortOption}
                    onValueChange={(value) => setSortOption(value as SortOption)}
                  >
                    <SelectTrigger className="h-8 text-xs">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="relevance">Relevance</SelectItem>
                      <SelectItem value="recent">Most Recent</SelectItem>
                      <SelectItem value="experience">Experience (High to Low)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator className="my-4" />

              {/* Skills Filter */}
              <div className="space-y-2 mb-4">
                <Label className="text-xs flex items-center gap-1">
                  <Code className="h-3 w-3" />
                  Filter by Skills
                </Label>
                <div className="flex flex-wrap gap-2">
                  {commonSkills.map(skill => (
                    <Badge
                      key={skill}
                      variant={filters.skills.includes(skill) ? "default" : "outline"}
                      className="cursor-pointer hover:bg-muted"
                      onClick={() => toggleSkill(skill)}
                    >
                      {skill}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Industry Filter */}
              <div className="space-y-2">
                <Label className="text-xs flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  Filter by Industry
                </Label>
                <div className="flex flex-wrap gap-2">
                  {commonIndustries.map(industry => (
                    <Badge
                      key={industry}
                      variant={filters.industries.includes(industry) ? "default" : "outline"}
                      className="cursor-pointer hover:bg-muted"
                      onClick={() => toggleIndustry(industry)}
                    >
                      {industry}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">
            Error searching resumes. Please try again.
          </div>
        ) : filteredResults.length > 0 ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-500">
                Found {filteredResults.length} matching {filteredResults.length === 1 ? 'resume' : 'resumes'}
                {activeFiltersCount > 0 && ` (filtered from ${searchResults.length})`}
              </h3>
            </div>

            {filteredResults.map((result) => (
              <div
                key={result.resume.id}
                className="border rounded-md p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-start">
                  <User className="h-10 w-10 mr-3 text-primary" />
                  <div className="flex-1">
                    <h4 className="font-medium">{result.user.name}</h4>
                    <p className="text-sm text-muted-foreground">{result.user.email}</p>

                    {/* Experience Badge - Extract from resume text */}
                    {result.resume.extracted_text && (
                      <div className="mt-1">
                        {(() => {
                          const expMatch = result.resume.extracted_text.match(/(\d+)[\s-]*years?[\s-]*(of)?[\s-]*experience/i);
                          if (expMatch) {
                            const years = parseInt(expMatch[1]);
                            return (
                              <Badge variant="secondary" className="text-xs">
                                <Briefcase className="h-3 w-3 mr-1 inline" />
                                {years} {years === 1 ? 'year' : 'years'} experience
                              </Badge>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    )}
                  </div>
                </div>

                <div className="mt-4 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium">{result.resume.filename}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(result.resume.file_size)} • {result.resume.file_type.toUpperCase()} •
                      Uploaded {formatDistanceToNow(new Date(result.resume.upload_date), { addSuffix: true })}
                    </p>
                  </div>
                </div>

                {/* Show a snippet of the extracted text if available */}
                {result.resume.extracted_text && (
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md text-sm">
                    <p className="text-xs font-medium text-gray-500 mb-1">Content Preview:</p>
                    <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-3">
                      {result.resume.extracted_text.substring(0, 300)}...
                    </p>
                  </div>
                )}

                {/* Extract and display skills */}
                {result.resume.extracted_text && (
                  <div className="mt-3">
                    <p className="text-xs font-medium text-gray-500 mb-1">Detected Skills:</p>
                    <div className="flex flex-wrap gap-1">
                      {commonSkills.filter(skill =>
                        result.resume.extracted_text?.toLowerCase().includes(skill.toLowerCase())
                      ).slice(0, 5).map(skill => (
                        <Badge key={skill} variant="outline" className="text-xs">
                          {skill}
                        </Badge>
                      ))}
                      {commonSkills.filter(skill =>
                        result.resume.extracted_text?.toLowerCase().includes(skill.toLowerCase())
                      ).length > 5 && (
                        <Badge variant="outline" className="text-xs">
                          +{commonSkills.filter(skill =>
                            result.resume.extracted_text?.toLowerCase().includes(skill.toLowerCase())
                          ).length - 5} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}

                <div className="mt-4 flex justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewResume(result.user.id, result.user.name)}
                  >
                    <FileText className="h-3 w-3 mr-1" />
                    View Resume
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : searchQuery && !isSearching ? (
          <div className="text-center py-8 text-gray-500">
            No resumes found matching your search criteria.
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            Enter search terms above to find candidate resumes.
          </div>
        )}
      </CardContent>

      {/* Resume View Modal */}
      {selectedWorker && (
        <ResumeViewModal
          open={showResumeViewModal}
          onClose={() => setShowResumeViewModal(false)}
          workerId={selectedWorker.id}
          workerName={selectedWorker.name}
        />
      )}
    </Card>
  );
}
