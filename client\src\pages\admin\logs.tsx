import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  RefreshCw,
  AlertTriangle,
  Info,
  AlertCircle,
  Download,
  Plus,
  Loader2,
  Calendar,
  Clock,
  User,
  Tag,
  FileText,
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useLocation } from 'wouter';

interface SystemLog {
  id: number;
  level: 'info' | 'warning' | 'error';
  message: string;
  source: string;
  user_id?: number;
  metadata?: any;
  created_at: string;
}

const SystemLogs: React.FC = () => {
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [levelFilter, setLevelFilter] = useState<string>('all');
  const [sourceFilter, setSourceFilter] = useState<string>('all');
  const [showAddLogDialog, setShowAddLogDialog] = useState(false);
  const [newLogLevel, setNewLogLevel] = useState<'info' | 'warning' | 'error'>('info');
  const [newLogMessage, setNewLogMessage] = useState('');
  const [newLogSource, setNewLogSource] = useState('');
  const [newLogMetadata, setNewLogMetadata] = useState('');

  // Fetch logs
  const { data: logs, isLoading, refetch } = useQuery({
    queryKey: ['/api/admin/logs', levelFilter, sourceFilter],
    queryFn: async () => {
      const queryParams = new URLSearchParams();
      if (levelFilter !== 'all') queryParams.append('level', levelFilter);
      if (sourceFilter !== 'all') queryParams.append('source', sourceFilter);
      
      const url = `/api/admin/logs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const res = await apiRequest('GET', url);
      if (!res.ok) throw new Error('Failed to fetch logs');
      return res.json() as Promise<SystemLog[]>;
    },
  });

  // Add log mutation
  const addLog = async () => {
    try {
      if (!newLogMessage || !newLogSource) {
        toast({
          title: 'Missing Information',
          description: 'Please enter a message and source.',
          variant: 'destructive',
        });
        return;
      }

      let metadata = null;
      if (newLogMetadata) {
        try {
          metadata = JSON.parse(newLogMetadata);
        } catch (error) {
          toast({
            title: 'Invalid Metadata',
            description: 'Please enter valid JSON for metadata.',
            variant: 'destructive',
          });
          return;
        }
      }

      const res = await apiRequest('POST', '/api/admin/logs', {
        level: newLogLevel,
        message: newLogMessage,
        source: newLogSource,
        metadata,
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to add log');
      }

      toast({
        title: 'Log Added',
        description: 'The system log has been successfully added.',
      });
      setShowAddLogDialog(false);
      setNewLogLevel('info');
      setNewLogMessage('');
      setNewLogSource('');
      setNewLogMetadata('');
      refetch();
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to add log',
        variant: 'destructive',
      });
    }
  };

  // Filter logs based on search query
  const filteredLogs = logs
    ? logs.filter((log) => {
        const matchesSearch =
          log.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
          log.source.toLowerCase().includes(searchQuery.toLowerCase());
        
        return matchesSearch;
      })
    : [];

  // Get unique sources for filter
  const uniqueSources = logs
    ? Array.from(new Set(logs.map((log) => log.source)))
    : [];

  // Get level badge color
  const getLevelBadgeColor = (level: string) => {
    switch (level) {
      case 'info':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'error':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  // Get level icon
  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  // Reset filters
  const resetFilters = () => {
    setSearchQuery('');
    setLevelFilter('all');
    setSourceFilter('all');
  };

  // Export logs as JSON
  const exportLogs = () => {
    if (!filteredLogs.length) return;

    const dataStr = JSON.stringify(filteredLogs, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `pathlink-logs-${new Date().toISOString().slice(0, 10)}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">System Logs</h1>
          <div className="space-x-2">
            <Button variant="outline" onClick={exportLogs} disabled={!filteredLogs.length}>
              <Download className="h-4 w-4 mr-2" />
              Export Logs
            </Button>
            <Button onClick={() => setShowAddLogDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Log Entry
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md mb-6">
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search logs by message or source..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={levelFilter} onValueChange={setLevelFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Levels</SelectItem>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sourceFilter} onValueChange={setSourceFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sources</SelectItem>
                  {uniqueSources.map((source) => (
                    <SelectItem key={source} value={source}>
                      {source}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button variant="outline" onClick={resetFilters} className="flex items-center">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>

              <Button variant="outline" onClick={() => refetch()} className="flex items-center">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        {/* Logs Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading logs...</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">Level</TableHead>
                    <TableHead className="w-[400px]">Message</TableHead>
                    <TableHead className="w-[150px]">Source</TableHead>
                    <TableHead className="w-[200px]">Timestamp</TableHead>
                    <TableHead className="w-[150px]">User ID</TableHead>
                    <TableHead className="w-[200px]">Metadata</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.length > 0 ? (
                    filteredLogs.map((log) => (
                      <TableRow key={log.id}>
                        <TableCell>
                          <Badge className={getLevelBadgeColor(log.level)}>
                            <div className="flex items-center space-x-1">
                              {getLevelIcon(log.level)}
                              <span className="ml-1 capitalize">{log.level}</span>
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium">{log.message}</TableCell>
                        <TableCell>
                          <Badge variant="outline">{log.source}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col text-xs">
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1 text-gray-500" />
                              {new Date(log.created_at).toLocaleDateString()}
                            </div>
                            <div className="flex items-center mt-1">
                              <Clock className="h-3 w-3 mr-1 text-gray-500" />
                              {new Date(log.created_at).toLocaleTimeString()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {log.user_id ? (
                            <div className="flex items-center">
                              <User className="h-4 w-4 mr-1 text-gray-500" />
                              {log.user_id}
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {log.metadata ? (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-blue-600 hover:text-blue-800"
                              onClick={() => {
                                toast({
                                  title: 'Log Metadata',
                                  description: (
                                    <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4 overflow-x-auto">
                                      <code className="text-white">
                                        {JSON.stringify(log.metadata, null, 2)}
                                      </code>
                                    </pre>
                                  ),
                                });
                              }}
                            >
                              <FileText className="h-4 w-4 mr-1" />
                              View
                            </Button>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                          <AlertTriangle className="h-12 w-12 mb-2 opacity-20" />
                          <p>No logs found</p>
                          {(searchQuery || levelFilter !== 'all' || sourceFilter !== 'all') && (
                            <p className="text-sm">Try adjusting your search or filters</p>
                          )}
                          <Button variant="outline" className="mt-4" onClick={resetFilters}>
                            Reset Filters
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </div>

      {/* Add Log Dialog */}
      <Dialog open={showAddLogDialog} onOpenChange={setShowAddLogDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add System Log Entry</DialogTitle>
            <DialogDescription>
              Create a new system log entry. This is useful for tracking important events or issues.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="log-level">Log Level</Label>
              <Select value={newLogLevel} onValueChange={(value: any) => setNewLogLevel(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select log level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="info">Info</SelectItem>
                  <SelectItem value="warning">Warning</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="log-message">Message</Label>
              <Input
                id="log-message"
                placeholder="Log message"
                value={newLogMessage}
                onChange={(e) => setNewLogMessage(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="log-source">Source</Label>
              <Input
                id="log-source"
                placeholder="e.g., auth, database, api"
                value={newLogSource}
                onChange={(e) => setNewLogSource(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="log-metadata">Metadata (Optional JSON)</Label>
              <Textarea
                id="log-metadata"
                placeholder='e.g., {"user_agent": "Mozilla/5.0", "ip": "***********"}'
                value={newLogMetadata}
                onChange={(e) => setNewLogMetadata(e.target.value)}
                className="font-mono text-sm"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddLogDialog(false)}>
              Cancel
            </Button>
            <Button onClick={addLog}>
              Add Log Entry
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default SystemLogs;
