-- Add indexes for performance optimization

-- Users table indexes
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_verified ON users(is_verified);

-- Jobs table indexes
CREATE INDEX IF NOT EXISTS idx_jobs_employer_id ON jobs(employer_id);
CREATE INDEX IF NOT EXISTS idx_jobs_industry ON jobs(industry);
CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs(location);

-- Matches table indexes
CREATE INDEX IF NOT EXISTS idx_matches_worker_id ON matches(worker_id);
CREATE INDEX IF NOT EXISTS idx_matches_job_id ON matches(job_id);
CREATE INDEX IF NOT EXISTS idx_matches_status ON matches(status);

-- Resumes table indexes
CREATE INDEX IF NOT EXISTS idx_resumes_worker_id ON resumes(worker_id);
CREATE INDEX IF NOT EXISTS idx_resumes_file_type ON resumes(file_type);

-- Approved domains table indexes
CREATE INDEX IF NOT EXISTS idx_approved_domains_is_active ON approved_domains(is_active);
