// Script to add approved domains to the database
import { db, pool } from '../server/db.js';
import { approvedDomains } from '../shared/schema.js';

async function addApprovedDomains() {
  try {
    console.log('Adding approved domains...');

    // List of domains to add
    const domains = [
      { domain: 'google.com', description: 'Google Inc.', is_active: true },
      { domain: 'microsoft.com', description: 'Microsoft Corporation', is_active: true },
      { domain: 'apple.com', description: 'Apple Inc.', is_active: true },
      { domain: 'amazon.com', description: 'Amazon', is_active: true },
      { domain: 'meta.com', description: 'Meta (Facebook)', is_active: true },
      { domain: 'ibm.com', description: 'IBM', is_active: true },
      { domain: 'intel.com', description: 'Intel Corporation', is_active: true },
      { domain: 'cisco.com', description: 'Cisco Systems', is_active: true },
      { domain: 'oracle.com', description: 'Oracle Corporation', is_active: true },
      { domain: 'salesforce.com', description: 'Salesforce', is_active: true },
      { domain: 'pathlink.com', description: 'PathLink', is_active: true }
    ];

    // Add each domain to the in-memory storage
    for (const domain of domains) {
      // Check if domain already exists in memory
      const existingDomains = Array.from(global.storage.approvedDomains.values());
      const exists = existingDomains.some(d => d.domain.toLowerCase() === domain.domain.toLowerCase());
      
      if (!exists) {
        await global.storage.createApprovedDomain({
          domain: domain.domain,
          description: domain.description,
          is_active: domain.is_active
        });
        console.log(`Added domain: ${domain.domain}`);
      } else {
        console.log(`Domain already exists: ${domain.domain}`);
      }
    }

    console.log('Approved domains added successfully');
    
    // List all domains to verify
    const allDomains = await global.storage.getApprovedDomains();
    console.log('Current approved domains:');
    allDomains.forEach(domain => {
      console.log(`- ${domain.domain} (${domain.description})`);
    });
    
  } catch (error) {
    console.error('Error adding approved domains:', error);
  }
}

// Execute the function
addApprovedDomains();
