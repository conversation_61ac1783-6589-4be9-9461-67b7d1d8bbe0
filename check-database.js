const { Pool } = require('pg');
const bcrypt = require('bcrypt');

const pool = new Pool({
  connectionString: 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require',
  ssl: { rejectUnauthorized: false }
});

async function checkAndSetupDatabase() {
  try {
    console.log('🔌 Connecting to Neon database...');
    const client = await pool.connect();
    console.log('✅ Connected to Neon database');
    
    // Check if users table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
      );
    `);
    
    console.log('Users table exists:', tableCheck.rows[0].exists);
    
    if (!tableCheck.rows[0].exists) {
      console.log('📋 Creating users table...');
      await client.query(`
        CREATE TABLE users (
          id SERIAL PRIMARY KEY,
          email TEXT NOT NULL UNIQUE,
          password TEXT NOT NULL,
          name TEXT NOT NULL,
          role TEXT NOT NULL CHECK (role IN ('worker', 'employer', 'admin')),
          original_role TEXT CHECK (original_role IN ('worker', 'employer', 'admin')),
          is_verified BOOLEAN DEFAULT FALSE,
          verification_token TEXT,
          verification_expires TIMESTAMP,
          created_at TIMESTAMP DEFAULT NOW()
        )
      `);
      console.log('✅ Users table created');
    }
    
    // Check existing users
    const users = await client.query('SELECT id, email, name, role, created_at FROM users ORDER BY id');
    console.log('📊 Current users in database:', users.rows);
    
    // Create test users if none exist
    if (users.rows.length === 0) {
      console.log('👤 Creating test users...');
      
      const hashedPassword = await bcrypt.hash('password123', 10);
      
      const testUsers = [
        {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'Peter Dimian',
          role: 'worker'
        },
        {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'PathLink Employer',
          role: 'employer'
        },
        {
          email: '<EMAIL>',
          password: hashedPassword,
          name: 'PathLink Admin',
          role: 'admin'
        }
      ];
      
      for (const user of testUsers) {
        await client.query(
          'INSERT INTO users (email, password, name, role) VALUES ($1, $2, $3, $4)',
          [user.email, user.password, user.name, user.role]
        );
        console.log(`✅ Created user: ${user.email}`);
      }
    }
    
    // Show final user list
    const finalUsers = await client.query('SELECT id, email, name, role, created_at FROM users ORDER BY id');
    console.log('📋 Final users in database:', finalUsers.rows);
    
    client.release();
    await pool.end();
    console.log('✅ Database check complete');
    
  } catch (error) {
    console.error('❌ Database error:', error);
  }
}

checkAndSetupDatabase();
