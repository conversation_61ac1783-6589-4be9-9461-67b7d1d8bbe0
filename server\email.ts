import { MailService } from '@sendgrid/mail';

// Check if SendGrid API key is provided
if (!process.env.SENDGRID_API_KEY) {
  console.warn("WARNING: SENDGRID_API_KEY environment variable is not set. Email functionality will be disabled.");
}

// Initialize SendGrid client only if API key is available
const mailService = new MailService();
if (process.env.SENDGRID_API_KEY) {
  mailService.setApiKey(process.env.SENDGRID_API_KEY);
}

// Template for password reset emails
const RESET_PASSWORD_TEMPLATE = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
  <div style="text-align: center; margin-bottom: 20px;">
    <h1 style="color: #1C2A42; margin-bottom: 5px;">PathLink</h1>
    <p style="color: #666; font-size: 14px;">AI-Powered Job Matching Platform</p>
  </div>
  <div style="padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
    <h2 style="color: #1C2A42; margin-top: 0;">Password Reset Request</h2>
    <p>Hello,</p>
    <p>We received a request to reset your password for your PathLink account. If you didn't make this request, you can safely ignore this email.</p>
    <p>If you did request a password reset, your temporary password is:</p>
    <div style="background-color: #EAE6E1; padding: 10px; border-radius: 5px; text-align: center; margin: 15px 0; font-family: monospace; font-size: 18px;">
      {{temporaryPassword}}
    </div>
    <p>Please use this temporary password to log in, then immediately change your password to something secure.</p>
    <p style="margin-top: 30px;">Thank you,<br>The PathLink Team</p>
  </div>
  <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
    <p>&copy; 2025 PathLink – All rights reserved.</p>
  </div>
</div>
`;

// Template for welcome emails
const WELCOME_EMAIL_TEMPLATE = `
<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
  <div style="text-align: center; margin-bottom: 20px;">
    <h1 style="color: #1C2A42; margin-bottom: 5px;">PathLink</h1>
    <p style="color: #666; font-size: 14px;">AI-Powered Job Matching Platform</p>
  </div>
  <div style="padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
    <h2 style="color: #1C2A42; margin-top: 0;">Welcome to PathLink!</h2>
    <p>Hello {{name}},</p>
    <p>Thank you for joining PathLink! We're excited to have you on board as a {{role}}.</p>
    <p>With PathLink, you can:</p>
    <ul style="padding-left: 20px;">
      <li>Connect with opportunities that match your skills</li>
      <li>Discover new career paths with our AI-powered recommendations</li>
      <li>Build your professional network in a supportive community</li>
    </ul>
    <p>To get started, simply log in to your account and complete your profile.</p>
    <div style="text-align: center; margin: 25px 0;">
      <a href="http://localhost:5000" style="background-color: #1C2A42; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">Go to Dashboard</a>
    </div>
    <p style="margin-top: 30px;">Welcome aboard,<br>The PathLink Team</p>
  </div>
  <div style="text-align: center; margin-top: 20px; color: #666; font-size: 12px;">
    <p>&copy; 2025 PathLink – All rights reserved.</p>
  </div>
</div>
`;

interface EmailParams {
  to: string;
  from: string;
  subject: string;
  text: string;  // Making this required to avoid undefined
  html: string;  // Making this required to avoid undefined
}

/**
 * Send a generic email using SendGrid or mock service for development
 */
export async function sendEmail(params: EmailParams): Promise<boolean> {
  // If we're in development mode and no SendGrid key, use mock email service
  const isDev = process.env.NODE_ENV !== 'production';

  if (!process.env.SENDGRID_API_KEY) {
    if (isDev) {
      console.log('=== MOCK EMAIL SERVICE (SENDGRID_API_KEY not set) ===');
      console.log(`To: ${params.to}`);
      console.log(`From: ${params.from}`);
      console.log(`Subject: ${params.subject}`);
      console.log(`Text: ${params.text.substring(0, 100)}...`);
      console.log('=== END MOCK EMAIL ===');
      return true; // Pretend it worked in development
    } else {
      console.warn("Email not sent: SENDGRID_API_KEY is not set");
      return false;
    }
  }

  try {
    await mailService.send({
      to: params.to,
      from: params.from,
      subject: params.subject,
      text: params.text,
      html: params.html,
    });
    console.log(`Email sent successfully to ${params.to}`);
    return true;
  } catch (error: any) {
    console.error('SendGrid email error:', error);

    // Log more detailed error information
    if (error.response && error.response.body && error.response.body.errors) {
      console.error('SendGrid error details:', JSON.stringify(error.response.body.errors));
    }

    // In development, still return success so the app can continue
    if (isDev) {
      console.log('=== MOCK EMAIL FALLBACK (SendGrid failed) ===');
      console.log(`To: ${params.to}`);
      console.log(`From: ${params.from}`);
      console.log(`Subject: ${params.subject}`);
      console.log(`Text: ${params.text.substring(0, 100)}...`);
      console.log('=== END MOCK EMAIL ===');
      return true; // Pretend it worked in development
    }

    return false;
  }
}

/**
 * Generate a random temporary password
 */
function generateTemporaryPassword(length = 10): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*()';
  let result = '';
  const charactersLength = chars.length;

  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
}

/**
 * Send password reset email to user
 */
export async function sendPasswordResetEmail(email: string): Promise<{success: boolean, temporaryPassword?: string}> {
  const temporaryPassword = generateTemporaryPassword();
  const emailContent = RESET_PASSWORD_TEMPLATE.replace('{{temporaryPassword}}', temporaryPassword);

  const success = await sendEmail({
    to: email,
    from: '<EMAIL>', // This is a verified sender in your SendGrid account
    subject: 'PathLink Password Reset',
    html: emailContent,
    text: `Your temporary password is: ${temporaryPassword}. Please log in and change it immediately.`,
  });

  return {
    success,
    temporaryPassword: success ? temporaryPassword : undefined
  };
}

/**
 * Send welcome email to new user
 */
export async function sendWelcomeEmail(email: string, name: string, role: string): Promise<boolean> {
  // Replace placeholders in the template
  let emailContent = WELCOME_EMAIL_TEMPLATE.replace('{{name}}', name);
  emailContent = emailContent.replace('{{role}}', role === 'employer' ? 'an employer' : 'a worker');

  // Send the email
  const success = await sendEmail({
    to: email,
    from: '<EMAIL>', // This is a verified sender in your SendGrid account
    subject: 'Welcome to PathLink!',
    html: emailContent,
    text: `Hello ${name}, Welcome to PathLink! We're excited to have you on board as ${role === 'employer' ? 'an employer' : 'a worker'}. Log in to your account to get started.`,
  });

  return success;
}