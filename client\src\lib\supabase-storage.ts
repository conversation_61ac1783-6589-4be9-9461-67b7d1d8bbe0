/**
 * Supabase Storage utilities for file uploads
 */
import { supabase } from './supabase';

// Define bucket names
export const BUCKETS = {
  RESUMES: 'resumes',
  PROFILE_IMAGES: 'profile-images',
  DOCUMENTS: 'documents'
};

/**
 * Initialize storage buckets if they don't exist
 */
export async function initializeStorage() {
  try {
    console.log('Initializing Supabase storage buckets...');
    
    // Check and create buckets if they don't exist
    for (const bucketName of Object.values(BUCKETS)) {
      const { data: existingBucket, error: getBucketError } = await supabase
        .storage
        .getBucket(bucketName);
      
      if (getBucketError && !existingBucket) {
        console.log(`Creating bucket: ${bucketName}`);
        const { data, error } = await supabase
          .storage
          .createBucket(bucketName, {
            public: false,
            fileSizeLimit: 10485760, // 10MB
          });
          
        if (error) {
          console.error(`Error creating bucket ${bucketName}:`, error);
        } else {
          console.log(`Bucket ${bucketName} created successfully`);
        }
      } else {
        console.log(`Bucket ${bucketName} already exists`);
      }
    }
    
    console.log('Storage initialization complete');
  } catch (error) {
    console.error('Failed to initialize storage:', error);
  }
}

/**
 * Upload a file to Supabase Storage
 * @param bucket The bucket name to upload to
 * @param path The path within the bucket
 * @param file The file to upload
 * @returns The URL of the uploaded file
 */
export async function uploadFile(bucket: string, path: string, file: File): Promise<string> {
  try {
    const fileName = `${Date.now()}_${file.name.replace(/\s+/g, '_')}`;
    const filePath = path ? `${path}/${fileName}` : fileName;
    
    const { data, error } = await supabase
      .storage
      .from(bucket)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false
      });
      
    if (error) {
      throw error;
    }
    
    // Get the public URL
    const { data: urlData } = supabase
      .storage
      .from(bucket)
      .getPublicUrl(data.path);
      
    return urlData.publicUrl;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw error;
  }
}

/**
 * Delete a file from Supabase Storage
 * @param bucket The bucket name
 * @param path The file path to delete
 */
export async function deleteFile(bucket: string, path: string): Promise<void> {
  try {
    const { error } = await supabase
      .storage
      .from(bucket)
      .remove([path]);
      
    if (error) {
      throw error;
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    throw error;
  }
}

/**
 * Get a signed URL for a file (for private files)
 * @param bucket The bucket name
 * @param path The file path
 * @param expiresIn Expiration time in seconds (default: 60 minutes)
 */
export async function getSignedUrl(bucket: string, path: string, expiresIn = 3600): Promise<string> {
  try {
    const { data, error } = await supabase
      .storage
      .from(bucket)
      .createSignedUrl(path, expiresIn);
      
    if (error) {
      throw error;
    }
    
    return data.signedUrl;
  } catch (error) {
    console.error('Error getting signed URL:', error);
    throw error;
  }
}

/**
 * List all files in a bucket/folder
 * @param bucket The bucket name
 * @param path Optional path within the bucket
 */
export async function listFiles(bucket: string, path?: string) {
  try {
    const { data, error } = await supabase
      .storage
      .from(bucket)
      .list(path || '');
      
    if (error) {
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error('Error listing files:', error);
    throw error;
  }
}
