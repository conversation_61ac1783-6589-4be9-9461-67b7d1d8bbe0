option_settings:
  aws:elasticbeanstalk:application:environment:
    NODE_ENV: production
    PORT: 8080
    DATABASE_URL: postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require
    SESSION_SECRET: pathlink-production-secret-2024
    OPENAI_API_KEY: your-openai-api-key-here
    SENDGRID_API_KEY: your-sendgrid-api-key-here
  aws:autoscaling:asg:
    MinSize: 1
    MaxSize: 4
  aws:autoscaling:launchconfiguration:
    InstanceType: t3.micro
  aws:elasticbeanstalk:environment:
    LoadBalancerType: application
