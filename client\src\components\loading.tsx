import React from 'react';
import { Loader } from '@/components/ui/loader';

interface LoadingProps {
  message?: string;
  fullScreen?: boolean;
}

export function Loading({ message = 'Loading...', fullScreen = false }: LoadingProps) {
  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-900/80 z-50">
        <Loader size="large" text={message} />
      </div>
    );
  }

  return (
    <div className="w-full py-8 flex items-center justify-center">
      <Loader size="medium" text={message} />
    </div>
  );
}
