import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import AdminLayout from '@/components/admin-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  FileText,
  File,
  FileCode,
  FileCog,
  Loader2,
  RefreshCw,
  Save,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLocation } from 'wouter';

interface ContentItem {
  id: number;
  title: string;
  type: string;
  slug: string;
  content: string;
  created_at: string;
  updated_at: string;
  published: boolean;
}

const ContentManagement: React.FC = () => {
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [showAddContentDialog, setShowAddContentDialog] = useState(false);
  const [showEditContentDialog, setShowEditContentDialog] = useState(false);
  const [showDeleteContentDialog, setShowDeleteContentDialog] = useState(false);
  const [contentToEdit, setContentToEdit] = useState<ContentItem | null>(null);
  const [contentToDelete, setContentToDelete] = useState<ContentItem | null>(null);
  const [newContentTitle, setNewContentTitle] = useState('');
  const [newContentType, setNewContentType] = useState('page');
  const [newContentSlug, setNewContentSlug] = useState('');
  const [newContentBody, setNewContentBody] = useState('');
  const [newContentPublished, setNewContentPublished] = useState(true);
  const [editorTab, setEditorTab] = useState('visual');

  // Fetch content
  const { data: contentItems, isLoading, refetch } = useQuery({
    queryKey: ['/api/admin/content'],
    queryFn: async () => {
      const res = await apiRequest('GET', '/api/admin/content');
      if (!res.ok) throw new Error('Failed to fetch content');
      return res.json() as Promise<ContentItem[]>;
    },
  });

  // Add content mutation (this would be implemented in a real app)
  const addContentMutation = useMutation({
    mutationFn: async (contentData: Omit<ContentItem, 'id' | 'created_at' | 'updated_at'>) => {
      // In a real implementation, this would be a POST request to add content
      // For now, we'll just simulate a successful addition
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return {
        ...contentData,
        id: Math.floor(Math.random() * 1000),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: 'Content Added',
        description: 'The content has been successfully added.',
      });
      setShowAddContentDialog(false);
      resetContentForm();
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to add content',
        variant: 'destructive',
      });
    },
  });

  // Update content mutation (this would be implemented in a real app)
  const updateContentMutation = useMutation({
    mutationFn: async (contentData: ContentItem) => {
      // In a real implementation, this would be a PUT request to update content
      // For now, we'll just simulate a successful update
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return contentData;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: 'Content Updated',
        description: 'The content has been successfully updated.',
      });
      setShowEditContentDialog(false);
      setContentToEdit(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update content',
        variant: 'destructive',
      });
    },
  });

  // Delete content mutation (this would be implemented in a real app)
  const deleteContentMutation = useMutation({
    mutationFn: async (contentId: number) => {
      // In a real implementation, this would be a DELETE request to delete content
      // For now, we'll just simulate a successful deletion
      await new Promise((resolve) => setTimeout(resolve, 1000));
      return contentId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/admin/content'] });
      toast({
        title: 'Content Deleted',
        description: 'The content has been successfully deleted.',
      });
      setShowDeleteContentDialog(false);
      setContentToDelete(null);
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete content',
        variant: 'destructive',
      });
    },
  });

  // Filter content based on search query and type filter
  const filteredContent = contentItems
    ? contentItems.filter((item) => {
        const matchesSearch =
          item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.slug.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesType = typeFilter === 'all' || item.type === typeFilter;

        return matchesSearch && matchesType;
      })
    : [];

  // Reset content form
  const resetContentForm = () => {
    setNewContentTitle('');
    setNewContentType('page');
    setNewContentSlug('');
    setNewContentBody('');
    setNewContentPublished(true);
    setEditorTab('visual');
  };

  // Handle add content
  const handleAddContent = () => {
    if (!newContentTitle || !newContentSlug) {
      toast({
        title: 'Missing Information',
        description: 'Please enter a title and slug.',
        variant: 'destructive',
      });
      return;
    }

    addContentMutation.mutate({
      title: newContentTitle,
      type: newContentType,
      slug: newContentSlug,
      content: newContentBody,
      published: newContentPublished,
    });
  };

  // Handle edit content
  const handleEditContent = (content: ContentItem) => {
    setContentToEdit(content);
    setNewContentTitle(content.title);
    setNewContentType(content.type);
    setNewContentSlug(content.slug);
    setNewContentBody(content.content);
    setNewContentPublished(content.published);
    setShowEditContentDialog(true);
  };

  // Handle update content
  const handleUpdateContent = () => {
    if (!contentToEdit) return;
    if (!newContentTitle || !newContentSlug) {
      toast({
        title: 'Missing Information',
        description: 'Please enter a title and slug.',
        variant: 'destructive',
      });
      return;
    }

    updateContentMutation.mutate({
      ...contentToEdit,
      title: newContentTitle,
      type: newContentType,
      slug: newContentSlug,
      content: newContentBody,
      published: newContentPublished,
      updated_at: new Date().toISOString(),
    });
  };

  // Handle delete content
  const handleDeleteContent = (content: ContentItem) => {
    setContentToDelete(content);
    setShowDeleteContentDialog(true);
  };

  // Confirm delete content
  const confirmDeleteContent = () => {
    if (contentToDelete) {
      deleteContentMutation.mutate(contentToDelete.id);
    }
  };

  // Generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  // Handle title change with auto slug generation
  const handleTitleChange = (title: string) => {
    setNewContentTitle(title);
    if (!contentToEdit) {
      // Only auto-generate slug for new content
      setNewContentSlug(generateSlug(title));
    }
  };

  // Get content type icon
  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'page':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'legal':
        return <FileCog className="h-4 w-4 text-purple-500" />;
      case 'blog':
        return <File className="h-4 w-4 text-green-500" />;
      case 'template':
        return <FileCode className="h-4 w-4 text-amber-500" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  return (
    <AdminLayout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-[#1C2A42] dark:text-white">Content Management</h1>
          <div className="space-x-2">
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button onClick={() => setShowAddContentDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Content
            </Button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md mb-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search content by title or slug..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="page">Pages</SelectItem>
                <SelectItem value="blog">Blog Posts</SelectItem>
                <SelectItem value="legal">Legal Documents</SelectItem>
                <SelectItem value="template">Templates</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Content Table */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading content...</span>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-100 dark:bg-gray-800">
                    <TableHead className="w-[300px] font-bold">Title</TableHead>
                    <TableHead className="w-[120px] font-bold">Type</TableHead>
                    <TableHead className="w-[200px] font-bold">Slug</TableHead>
                    <TableHead className="w-[120px] font-bold">Status</TableHead>
                    <TableHead className="w-[150px] font-bold">Last Updated</TableHead>
                    <TableHead className="w-[250px] text-right font-bold">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContent.length > 0 ? (
                    filteredContent.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.title}</TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {getContentTypeIcon(item.type)}
                            <span className="ml-2 capitalize">{item.type}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-sm">
                            {item.slug}
                          </code>
                        </TableCell>
                        <TableCell>
                          {item.published ? (
                            <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Published
                            </Badge>
                          ) : (
                            <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                              <XCircle className="h-3 w-3 mr-1" />
                              Draft
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          {new Date(item.updated_at).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric'
                          })}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => window.open(`/${item.slug}`, '_blank')}
                              title="View"
                              className="h-8 px-2 py-0"
                            >
                              <Eye className="h-4 w-4 mr-1" />
                              View
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditContent(item)}
                              title="Edit"
                              className="h-8 px-2 py-0"
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteContent(item)}
                              title="Delete"
                              className="h-8 px-2 py-0 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                          <FileText className="h-12 w-12 mb-2 opacity-20" />
                          <p>No content found</p>
                          {(searchQuery || typeFilter !== 'all') && (
                            <p className="text-sm">Try adjusting your search or filters</p>
                          )}
                          <Button
                            variant="outline"
                            className="mt-4"
                            onClick={() => setShowAddContentDialog(true)}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Content
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </div>

      {/* Add Content Dialog */}
      <Dialog open={showAddContentDialog} onOpenChange={setShowAddContentDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Add New Content</DialogTitle>
            <DialogDescription>
              Create a new page, blog post, or other content for your site.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="content-title">Title</Label>
                <Input
                  id="content-title"
                  placeholder="Content Title"
                  value={newContentTitle}
                  onChange={(e) => handleTitleChange(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="content-type">Content Type</Label>
                <Select value={newContentType} onValueChange={setNewContentType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select content type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="page">Page</SelectItem>
                    <SelectItem value="blog">Blog Post</SelectItem>
                    <SelectItem value="legal">Legal Document</SelectItem>
                    <SelectItem value="template">Template</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="content-slug">Slug</Label>
              <div className="flex items-center">
                <span className="text-gray-500 dark:text-gray-400 mr-2">/</span>
                <Input
                  id="content-slug"
                  placeholder="content-slug"
                  value={newContentSlug}
                  onChange={(e) => setNewContentSlug(e.target.value)}
                />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                The URL-friendly identifier for this content
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="content-body">Content</Label>
                <Tabs value={editorTab} onValueChange={setEditorTab} className="w-auto">
                  <TabsList className="h-8">
                    <TabsTrigger value="visual" className="text-xs px-2 py-1">Visual</TabsTrigger>
                    <TabsTrigger value="html" className="text-xs px-2 py-1">HTML</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              <TabsContent value="visual" className="mt-0">
                <Textarea
                  id="content-body"
                  placeholder="Enter content here..."
                  value={newContentBody}
                  onChange={(e) => setNewContentBody(e.target.value)}
                  className="min-h-[300px] font-sans"
                />
              </TabsContent>
              <TabsContent value="html" className="mt-0">
                <Textarea
                  id="content-body-html"
                  placeholder="<h1>Enter HTML content here...</h1>"
                  value={newContentBody}
                  onChange={(e) => setNewContentBody(e.target.value)}
                  className="min-h-[300px] font-mono text-sm"
                />
              </TabsContent>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="content-published"
                checked={newContentPublished}
                onCheckedChange={setNewContentPublished}
              />
              <Label htmlFor="content-published">Publish immediately</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddContentDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddContent} disabled={addContentMutation.isPending}>
              {addContentMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Add Content
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Content Dialog */}
      <Dialog open={showEditContentDialog} onOpenChange={setShowEditContentDialog}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Content</DialogTitle>
            <DialogDescription>
              Update the content details and body.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-content-title">Title</Label>
                <Input
                  id="edit-content-title"
                  placeholder="Content Title"
                  value={newContentTitle}
                  onChange={(e) => setNewContentTitle(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-content-type">Content Type</Label>
                <Select value={newContentType} onValueChange={setNewContentType}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select content type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="page">Page</SelectItem>
                    <SelectItem value="blog">Blog Post</SelectItem>
                    <SelectItem value="legal">Legal Document</SelectItem>
                    <SelectItem value="template">Template</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-content-slug">Slug</Label>
              <div className="flex items-center">
                <span className="text-gray-500 dark:text-gray-400 mr-2">/</span>
                <Input
                  id="edit-content-slug"
                  placeholder="content-slug"
                  value={newContentSlug}
                  onChange={(e) => setNewContentSlug(e.target.value)}
                />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="edit-content-body">Content</Label>
                <Tabs value={editorTab} onValueChange={setEditorTab} className="w-auto">
                  <TabsList className="h-8">
                    <TabsTrigger value="visual" className="text-xs px-2 py-1">Visual</TabsTrigger>
                    <TabsTrigger value="html" className="text-xs px-2 py-1">HTML</TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              <TabsContent value="visual" className="mt-0">
                <Textarea
                  id="edit-content-body"
                  placeholder="Enter content here..."
                  value={newContentBody}
                  onChange={(e) => setNewContentBody(e.target.value)}
                  className="min-h-[300px] font-sans"
                />
              </TabsContent>
              <TabsContent value="html" className="mt-0">
                <Textarea
                  id="edit-content-body-html"
                  placeholder="<h1>Enter HTML content here...</h1>"
                  value={newContentBody}
                  onChange={(e) => setNewContentBody(e.target.value)}
                  className="min-h-[300px] font-mono text-sm"
                />
              </TabsContent>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="edit-content-published"
                checked={newContentPublished}
                onCheckedChange={setNewContentPublished}
              />
              <Label htmlFor="edit-content-published">Published</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditContentDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateContent} disabled={updateContentMutation.isPending}>
              {updateContentMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Content Dialog */}
      <Dialog open={showDeleteContentDialog} onOpenChange={setShowDeleteContentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Content</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{contentToDelete?.title}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteContentDialog(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDeleteContent}
              disabled={deleteContentMutation.isPending}
            >
              {deleteContentMutation.isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </AdminLayout>
  );
};

export default ContentManagement;
