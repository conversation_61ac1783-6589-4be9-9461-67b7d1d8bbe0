const serverless = require('serverless-http');
const express = require('express');
const cors = require('cors');
const { neon } = require('@neondatabase/serverless');

// Initialize Neon client
const sql = neon(process.env.DATABASE_URL);

const app = express();

// Configure CORS
app.use(cors({
  origin: ['https://pathlink.netlify.app', 'http://localhost:5000'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Test database connection
app.get('/api/health', async (req, res) => {
  try {
    const result = await sql`SELECT 1 as test`;
    res.json({ 
      status: 'healthy', 
      database: 'connected',
      timestamp: new Date().toISOString() 
    });
  } catch (error) {
    res.status(500).json({ 
      status: 'error', 
      database: 'disconnected',
      error: error.message 
    });
  }
});

// Get all jobs
app.get('/api/jobs', async (req, res) => {
  try {
    const jobs = await sql`
      SELECT id, title, company, location, industry,
             salary_range, description, required_skills, created_at, employer_id
      FROM jobs
      ORDER BY created_at DESC
    `;
    res.json(jobs);
  } catch (error) {
    console.error('Error getting jobs:', error);
    res.status(500).json({ error: 'Failed to get jobs' });
  }
});

// User authentication
app.post('/api/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    
    const users = await sql`
      SELECT u.id, u.email, u.name, u.role, u.password,
             up.profile_picture as "profilePicture",
             up.position, up.location, up.bio, up.company,
             up.years_of_experience as "yearsOfExperience", up.skills
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.email = ${email}
      LIMIT 1
    `;
    
    if (users.length === 0) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    const user = users[0];
    
    // Simple password check (in production, use proper hashing)
    if (user.password !== password) {
      return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Remove password from response
    delete user.password;
    
    res.json({ user, message: 'Login successful' });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed' });
  }
});

// Get user profile
app.get('/api/profile/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const users = await sql`
      SELECT u.id, u.email, u.name, u.role,
             up.profile_picture as "profilePicture",
             up.position, up.location, up.bio, up.company,
             up.years_of_experience as "yearsOfExperience", up.skills
      FROM users u
      LEFT JOIN user_profiles up ON u.id = up.user_id
      WHERE u.id = ${userId}
      LIMIT 1
    `;
    
    if (users.length === 0) {
      return res.status(404).json({ error: 'User not found' });
    }
    
    res.json(users[0]);
  } catch (error) {
    console.error('Error getting profile:', error);
    res.status(500).json({ error: 'Failed to get profile' });
  }
});

// Get resumes
app.get('/api/resumes', async (req, res) => {
  try {
    const resumes = await sql`
      SELECT r.*, u.id as worker_id, u.name as worker_name, u.email as worker_email
      FROM resumes r
      JOIN users u ON r.worker_id = u.id
      WHERE u.role = 'worker'
      ORDER BY r.upload_date DESC
    `;

    const formattedResumes = resumes.map(row => ({
      resume: {
        id: row.id,
        filename: row.filename,
        file_path: row.file_path,
        file_size: row.file_size,
        file_type: row.file_type,
        extracted_text: row.extracted_text,
        upload_date: row.upload_date,
        user_id: row.worker_id
      },
      worker: {
        id: row.worker_id,
        name: row.worker_name,
        email: row.worker_email,
        role: 'worker'
      }
    }));

    res.json(formattedResumes);
  } catch (error) {
    console.error('Error getting resumes:', error);
    res.status(500).json({ error: 'Failed to get resumes' });
  }
});

// Get applications
app.get('/api/applications/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    
    const applications = await sql`
      SELECT m.id, m.worker_id as user_id, m.job_id, m.status, m.match_date as created_at,
             j.title, j.company, j.location, j.salary_range as salary
      FROM matches m
      JOIN jobs j ON m.job_id = j.id
      WHERE m.worker_id = ${userId}
      ORDER BY m.match_date DESC
    `;
    
    res.json(applications);
  } catch (error) {
    console.error('Error getting applications:', error);
    res.status(500).json({ error: 'Failed to get applications' });
  }
});

// Apply for job
app.post('/api/apply', async (req, res) => {
  try {
    const { user_id, job_id } = req.body;
    
    const result = await sql`
      INSERT INTO matches (worker_id, job_id, status, match_score, match_date)
      VALUES (${user_id}, ${job_id}, 'pending', 85, NOW())
      RETURNING id, worker_id as user_id, job_id, status, match_date as created_at
    `;
    
    res.json(result[0]);
  } catch (error) {
    console.error('Error applying for job:', error);
    res.status(500).json({ error: 'Failed to apply for job' });
  }
});

// Catch all other routes
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Export the serverless handler
module.exports.handler = serverless(app);
