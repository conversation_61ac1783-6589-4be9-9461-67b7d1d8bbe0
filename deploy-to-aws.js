#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🚀 PathLink AWS Deployment Script');
console.log('==================================\n');

// Check if AWS CLI is installed
function checkAWSCLI() {
  try {
    execSync('aws --version', { stdio: 'pipe' });
    console.log('✅ AWS CLI is installed');
    return true;
  } catch (error) {
    console.log('❌ AWS CLI is not installed');
    console.log('Please install AWS CLI: https://aws.amazon.com/cli/');
    return false;
  }
}

// Check if Elastic Beanstalk CLI is installed
function checkEBCLI() {
  try {
    execSync('eb --version', { stdio: 'pipe' });
    console.log('✅ Elastic Beanstalk CLI is installed');
    return true;
  } catch (error) {
    console.log('❌ Elastic Beanstalk CLI is not installed');
    console.log('Install with: pip install awsebcli');
    return false;
  }
}

// Create necessary directories and files
function setupAWSFiles() {
  console.log('\n📁 Setting up AWS configuration files...');
  
  // Create .ebextensions directory
  if (!fs.existsSync('.ebextensions')) {
    fs.mkdirSync('.ebextensions');
    console.log('✅ Created .ebextensions directory');
  }

  // Create Procfile for Elastic Beanstalk
  const procfileContent = 'web: npm start';
  fs.writeFileSync('Procfile', procfileContent);
  console.log('✅ Created Procfile');

  // Create .ebignore file
  const ebignoreContent = `
node_modules/
.git/
.env
*.log
.DS_Store
screenshots/
test-*.json
*-test-report.json
.vscode/
.idea/
*.md
README.md
`;
  fs.writeFileSync('.ebignore', ebignoreContent.trim());
  console.log('✅ Created .ebignore file');

  // Update package.json engines
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (!packageJson.engines) {
    packageJson.engines = {};
  }
  packageJson.engines.node = '18.x';
  packageJson.engines.npm = '9.x';
  
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  console.log('✅ Updated package.json with Node.js version');
}

// Build the application
function buildApplication() {
  console.log('\n🔨 Building application for AWS...');
  try {
    execSync('npm run build:aws', { stdio: 'inherit' });
    console.log('✅ Application built successfully');
    return true;
  } catch (error) {
    console.log('❌ Build failed');
    console.log('Error:', error.message);
    return false;
  }
}

// Initialize Elastic Beanstalk
function initializeEB() {
  console.log('\n🌐 Initializing Elastic Beanstalk...');
  try {
    // Check if already initialized
    if (fs.existsSync('.elasticbeanstalk')) {
      console.log('✅ Elastic Beanstalk already initialized');
      return true;
    }

    execSync('eb init pathlink --region us-east-1 --platform "Node.js 18 running on 64bit Amazon Linux 2"', { 
      stdio: 'inherit' 
    });
    console.log('✅ Elastic Beanstalk initialized');
    return true;
  } catch (error) {
    console.log('❌ EB initialization failed');
    console.log('Error:', error.message);
    return false;
  }
}

// Create Elastic Beanstalk environment
function createEBEnvironment() {
  console.log('\n🏗️ Creating Elastic Beanstalk environment...');
  try {
    // Check if environment already exists
    try {
      const status = execSync('eb status', { stdio: 'pipe' }).toString();
      if (status.includes('Environment details')) {
        console.log('✅ Environment already exists');
        return true;
      }
    } catch (e) {
      // Environment doesn't exist, create it
    }

    execSync('eb create pathlink-production --instance-type t3.micro --region us-east-1', { 
      stdio: 'inherit' 
    });
    console.log('✅ Environment created successfully');
    return true;
  } catch (error) {
    console.log('❌ Environment creation failed');
    console.log('Error:', error.message);
    return false;
  }
}

// Deploy to Elastic Beanstalk
function deployToEB() {
  console.log('\n🚀 Deploying to Elastic Beanstalk...');
  try {
    execSync('eb deploy', { stdio: 'inherit' });
    console.log('✅ Deployment successful');
    
    // Get the application URL
    const status = execSync('eb status', { stdio: 'pipe' }).toString();
    const urlMatch = status.match(/CNAME:\s*(.+)/);
    if (urlMatch) {
      const appUrl = urlMatch[1].trim();
      console.log(`\n🌍 Your application is live at: https://${appUrl}`);
      
      // Update netlify.toml with the correct URL
      updateNetlifyConfig(appUrl);
    }
    
    return true;
  } catch (error) {
    console.log('❌ Deployment failed');
    console.log('Error:', error.message);
    return false;
  }
}

// Update Netlify configuration with AWS URL
function updateNetlifyConfig(awsUrl) {
  console.log('\n🔄 Updating Netlify configuration...');
  try {
    let netlifyConfig = fs.readFileSync('netlify.toml', 'utf8');

    // Replace any existing AWS URLs with the new one
    const urlPatterns = [
      /https:\/\/pathlink-production\.us-east-1\.elasticbeanstalk\.com/g,
      /https:\/\/[a-zA-Z0-9-]+\.us-east-1\.elasticbeanstalk\.com/g
    ];

    for (const pattern of urlPatterns) {
      netlifyConfig = netlifyConfig.replace(pattern, `https://${awsUrl}`);
    }

    // Ensure all API redirects point to the new URL
    const redirectSections = [
      '# Redirect API calls to AWS Elastic Beanstalk',
      '# Redirect upload calls to AWS',
      '# Health check redirect to AWS'
    ];

    fs.writeFileSync('netlify.toml', netlifyConfig);
    console.log('✅ Netlify configuration updated with AWS URL');
    console.log(`🔗 API calls will be redirected to: https://${awsUrl}`);

    // Show the updated redirects
    console.log('\n📋 Updated redirects:');
    console.log(`   /api/* → https://${awsUrl}/api/*`);
    console.log(`   /upload/* → https://${awsUrl}/upload/*`);
    console.log(`   /health → https://${awsUrl}/health`);

  } catch (error) {
    console.log('⚠️ Could not update Netlify configuration');
    console.log('Please manually update netlify.toml with your AWS URL');
    console.log(`New URL: https://${awsUrl}`);
  }
}

// Test the deployment
function testDeployment() {
  console.log('\n🧪 Testing deployment...');
  try {
    const status = execSync('eb status', { stdio: 'pipe' }).toString();
    const urlMatch = status.match(/CNAME:\s*(.+)/);
    
    if (urlMatch) {
      const appUrl = urlMatch[1].trim();
      console.log(`✅ Application URL: https://${appUrl}`);
      console.log(`🔍 Test your application at: https://${appUrl}/health`);
      console.log(`🌐 Frontend will be served via Netlify with API calls redirected to AWS`);
    }
  } catch (error) {
    console.log('⚠️ Could not get application status');
  }
}

// Main deployment function
async function deployToAWS() {
  console.log('Starting AWS deployment process...\n');

  // Step 1: Check prerequisites
  if (!checkAWSCLI() || !checkEBCLI()) {
    console.log('\n❌ Prerequisites not met. Please install required tools.');
    process.exit(1);
  }

  // Step 2: Setup AWS files
  setupAWSFiles();

  // Step 3: Build application
  if (!buildApplication()) {
    console.log('\n❌ Build failed. Cannot proceed with deployment.');
    process.exit(1);
  }

  // Step 4: Initialize EB
  if (!initializeEB()) {
    console.log('\n❌ EB initialization failed. Cannot proceed.');
    process.exit(1);
  }

  // Step 5: Create environment
  if (!createEBEnvironment()) {
    console.log('\n❌ Environment creation failed. Cannot proceed.');
    process.exit(1);
  }

  // Step 6: Deploy
  if (!deployToEB()) {
    console.log('\n❌ Deployment failed.');
    process.exit(1);
  }

  // Step 7: Test deployment
  testDeployment();

  console.log('\n🎉 AWS Deployment Complete!');
  console.log('==========================');
  console.log('✅ Backend deployed to AWS Elastic Beanstalk');
  console.log('✅ Netlify configuration updated');
  console.log('✅ API calls will be redirected from Netlify to AWS');
  console.log('\nNext steps:');
  console.log('1. Deploy your frontend to Netlify');
  console.log('2. Test the full application');
  console.log('3. Configure your custom domain (optional)');
  console.log('\nUseful commands:');
  console.log('- eb status: Check application status');
  console.log('- eb logs: View application logs');
  console.log('- eb deploy: Deploy updates');
  console.log('- eb terminate: Terminate environment');
}

// Run the deployment
deployToAWS().catch(console.error);

export { deployToAWS };
