<!DOCTYPE html>
<html>
<head>
  <title>View Matches | PathLink</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 30px;
      background-color: #f5f5f5;
      min-height: 100vh;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border: 2px solid #e5e7eb;
      min-height: calc(100vh - 60px);
    }
    h1 {
      color: #1C2A42;
      margin-bottom: 20px;
    }
    .filters {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      margin-bottom: 30px;
      padding: 25px;
      background-color: #f8fafc;
      border: 1px solid #e5e7eb;
      border-radius: 10px;
    }
    .search-container {
      position: relative;
      flex: 1;
    }
    .search-icon {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #666;
    }
    .search-input {
      width: 100%;
      padding: 8px 8px 8px 30px;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .select {
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: white;
    }
    .tabs {
      display: flex;
      border-bottom: 2px solid #e5e7eb;
      margin-bottom: 30px;
      background-color: #f8fafc;
      border-radius: 10px 10px 0 0;
      padding: 0 20px;
    }
    .tab {
      padding: 15px 25px;
      cursor: pointer;
      border-bottom: 3px solid transparent;
      font-weight: 500;
      transition: all 0.2s ease;
    }
    .tab:hover {
      background-color: #e5e7eb;
      border-radius: 8px 8px 0 0;
    }
    .tab.active {
      border-bottom-color: #1C2A42;
      color: #1C2A42;
      font-weight: bold;
      background-color: white;
      border-radius: 8px 8px 0 0;
    }
    .card {
      border: 2px solid #e5e7eb;
      border-radius: 12px;
      padding: 30px;
      margin-bottom: 30px;
      background-color: white;
      box-shadow: 0 2px 8px rgba(0,0,0,0.08);
      transition: box-shadow 0.2s ease, border-color 0.2s ease;
    }
    .card:hover {
      box-shadow: 0 4px 16px rgba(0,0,0,0.12);
      border-color: #d1d5db;
    }
    .card-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }
    .card-title {
      font-size: 18px;
      font-weight: bold;
      color: #1C2A42;
    }
    .card-description {
      color: #666;
      font-size: 14px;
    }
    .button {
      background-color: #1C2A42;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    .button-outline {
      background-color: white;
      color: #1C2A42;
      border: 1px solid #1C2A42;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }
    .progress-container {
      margin-bottom: 10px;
    }
    .progress-label {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 14px;
    }
    .progress-bar {
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
    }
    .progress-fill {
      height: 100%;
    }
    .progress-fill.high {
      background-color: #4CAF50;
    }
    .progress-fill.medium {
      background-color: #FFC107;
    }
    .progress-fill.low {
      background-color: #F44336;
    }
    .badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      margin-right: 5px;
      margin-bottom: 5px;
    }
    .badge-success {
      background-color: #e8f5e9;
      color: #2e7d32;
    }
    .badge-danger {
      background-color: #ffebee;
      color: #c62828;
    }
    .separator {
      height: 1px;
      background-color: #e0e0e0;
      margin: 15px 0;
    }
    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
    }
    @media (max-width: 768px) {
      body {
        padding: 15px;
      }
      .container {
        padding: 20px;
        border-radius: 8px;
      }
      .grid {
        grid-template-columns: 1fr;
      }
      .filters {
        flex-direction: column;
        padding: 20px;
      }
      .card {
        padding: 20px;
        margin-bottom: 20px;
      }
      .card-header {
        flex-direction: column;
        gap: 15px;
      }
      .tabs {
        padding: 0 10px;
      }
      .tab {
        padding: 12px 15px;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Match Analysis</h1>

    <div class="filters">
      <div class="search-container">
        <div class="search-icon">🔍</div>
        <input type="text" class="search-input" placeholder="Search jobs or candidates...">
      </div>
      <select class="select">
        <option value="score">Sort by: Match Score</option>
        <option value="name">Sort by: Candidate Name</option>
        <option value="date">Sort by: Resume Date</option>
      </select>
      <select class="select">
        <option value="">Min Score: All Scores</option>
        <option value="90">Min Score: 90% and above</option>
        <option value="80">Min Score: 80% and above</option>
        <option value="70">Min Score: 70% and above</option>
        <option value="60">Min Score: 60% and above</option>
      </select>
      <select class="select">
        <option value="">Industry: All Industries</option>
        <option value="technology">Industry: Technology</option>
        <option value="healthcare">Industry: Healthcare</option>
        <option value="finance">Industry: Finance</option>
      </select>
    </div>

    <div class="tabs">
      <div class="tab active">All Matches</div>
      <div class="tab">By Job</div>
      <div class="tab">By Candidate</div>
    </div>

    <div class="card">
      <div class="card-header">
        <div>
          <div class="card-title">John Smith - Software Engineer</div>
          <div class="card-description">Match Score: 85% | Industry: Technology</div>
        </div>
        <div>
          <button class="button-outline">View Resume</button>
          <button class="button-outline">Job Details</button>
        </div>
      </div>
      <div class="grid">
        <div>
          <h3>Match Score Breakdown</h3>
          <div class="progress-container">
            <div class="progress-label">
              <span>Overall Match</span>
              <span>85%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 85%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Skills Match</span>
              <span>90%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 90%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Experience Match</span>
              <span>80%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 80%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Education Match</span>
              <span>75%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill medium" style="width: 75%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Industry Match</span>
              <span>95%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 95%;"></div>
            </div>
          </div>
        </div>
        <div>
          <h3>Match Details</h3>
          <div>
            <h4>Skills</h4>
            <div>
              <span class="badge badge-success">JavaScript</span>
              <span class="badge badge-success">React</span>
              <span class="badge badge-success">Node.js</span>
              <span class="badge badge-danger">Python</span>
              <span class="badge badge-danger">AWS</span>
            </div>
          </div>
          <div class="separator"></div>
          <div>
            <h4>Experience</h4>
            <p>Exceeds required experience by 2 years. Required: 3 years, Candidate: 5 years.</p>
          </div>
          <div class="separator"></div>
          <div>
            <h4>Strengths & Development Areas</h4>
            <div>
              <h5 style="color: #2e7d32;">Strengths:</h5>
              <ul>
                <li>Strong technical background in software development</li>
                <li>Excellent communication and presentation skills</li>
              </ul>
            </div>
            <div>
              <h5 style="color: #c62828;">Development Areas:</h5>
              <ul>
                <li>Develop proficiency in Python</li>
                <li>Gain experience with AWS cloud services</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div>Resume uploaded: 05/01/2025</div>
        <button class="button">Contact Candidate</button>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <div>
          <div class="card-title">Jane Doe - UX Designer</div>
          <div class="card-description">Match Score: 92% | Industry: Technology</div>
        </div>
        <div>
          <button class="button-outline">View Resume</button>
          <button class="button-outline">Job Details</button>
        </div>
      </div>
      <div class="grid">
        <div>
          <h3>Match Score Breakdown</h3>
          <div class="progress-container">
            <div class="progress-label">
              <span>Overall Match</span>
              <span>92%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 92%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Skills Match</span>
              <span>95%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 95%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Experience Match</span>
              <span>90%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 90%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Education Match</span>
              <span>85%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 85%;"></div>
            </div>
          </div>
          <div class="progress-container">
            <div class="progress-label">
              <span>Industry Match</span>
              <span>100%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill high" style="width: 100%;"></div>
            </div>
          </div>
        </div>
        <div>
          <h3>Match Details</h3>
          <div>
            <h4>Skills</h4>
            <div>
              <span class="badge badge-success">UI/UX Design</span>
              <span class="badge badge-success">Figma</span>
              <span class="badge badge-success">Adobe XD</span>
              <span class="badge badge-success">Prototyping</span>
              <span class="badge badge-danger">Motion Design</span>
            </div>
          </div>
          <div class="separator"></div>
          <div>
            <h4>Experience</h4>
            <p>Exceeds required experience by 3 years. Required: 4 years, Candidate: 7 years.</p>
          </div>
          <div class="separator"></div>
          <div>
            <h4>Strengths & Development Areas</h4>
            <div>
              <h5 style="color: #2e7d32;">Strengths:</h5>
              <ul>
                <li>Strong portfolio of user-centered design work</li>
                <li>Experience leading design teams</li>
              </ul>
            </div>
            <div>
              <h5 style="color: #c62828;">Development Areas:</h5>
              <ul>
                <li>Develop skills in motion design</li>
                <li>Enhance knowledge of accessibility standards</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <div>Resume uploaded: 04/15/2025</div>
        <button class="button">Contact Candidate</button>
      </div>
    </div>
  </div>
</body>
</html>
