import fs from 'fs';
import { promisify } from 'util';
import path from 'path';
import { exec } from 'child_process';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { DOMParser } from '@xmldom/xmldom';
import pdfParse from 'pdf-parse';
import mammoth from 'mammoth';

const execAsync = promisify(exec);

/**
 * Extract text from a PDF file using pdf-parse library with enhanced extraction
 */
export async function extractTextFromPdf(filePath: string): Promise<string> {
  try {
    console.log(`Starting PDF text extraction for: ${filePath}`);

    // Read the PDF file as a buffer
    const dataBuffer = await promisify(fs.readFile)(filePath);
    console.log(`PDF file size: ${dataBuffer.length} bytes`);

    // Use pdf-parse to extract text with enhanced options
    const data = await pdfParse(dataBuffer, {
      // Normalize whitespace and preserve structure
      normalizeWhitespace: false,
      // Extract all text including headers, footers, and metadata
      max: 0, // No limit on pages
    });

    console.log(`PDF parsing completed. Pages: ${data.numpages}, Info:`, data.info);

    // Get the text content
    let extractedText = data.text || '';

    // Log initial extraction results
    console.log(`Initial extracted text length: ${extractedText.length} characters`);
    console.log(`First 200 characters: ${extractedText.substring(0, 200)}`);

    // Enhanced text cleaning while preserving important information
    extractedText = extractedText
      // Normalize line breaks
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Remove excessive whitespace but preserve single spaces and line breaks
      .replace(/[ \t]+/g, ' ')  // Multiple spaces/tabs to single space
      .replace(/\n\s*\n/g, '\n') // Multiple newlines to single newline
      // Remove common PDF artifacts
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Control characters
      .replace(/\uFEFF/g, '') // BOM
      // Trim whitespace
      .trim();

    // Extract additional metadata if available
    if (data.info) {
      const metadata = [];
      if (data.info.Title) metadata.push(`Title: ${data.info.Title}`);
      if (data.info.Author) metadata.push(`Author: ${data.info.Author}`);
      if (data.info.Subject) metadata.push(`Subject: ${data.info.Subject}`);
      if (data.info.Keywords) metadata.push(`Keywords: ${data.info.Keywords}`);

      if (metadata.length > 0) {
        extractedText = metadata.join(' ') + '\n' + extractedText;
        console.log(`Added PDF metadata: ${metadata.join(', ')}`);
      }
    }

    // Log final results
    console.log(`Final extracted text length: ${extractedText.length} characters`);

    // Validate extraction quality
    if (extractedText.length < 50) {
      console.warn(`Warning: Extracted text from ${filePath} is very short (${extractedText.length} chars)`);
      console.warn(`This might indicate a scanned PDF or extraction issues`);

      // Add fallback keywords for searchability
      extractedText += " Resume CV Curriculum Vitae Profile Skills Experience Education Work History";
    }

    // Count words for logging
    const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;
    console.log(`Successfully extracted ${wordCount} words from PDF`);

    return extractedText || 'No text could be extracted from this PDF.';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);

    // Return a more helpful error message
    if (error instanceof Error) {
      console.error(`PDF extraction error details: ${error.message}`);
      return `Error extracting text from PDF: ${error.message}`;
    }

    return 'Error extracting text from PDF.';
  }
}

/**
 * Extract text from a DOCX file using mammoth.js for comprehensive extraction
 */
export async function extractTextFromDocx(filePath: string): Promise<string> {
  try {
    console.log(`Starting DOCX text extraction for: ${filePath}`);

    // Read the file as a buffer
    const buffer = await promisify(fs.readFile)(filePath);
    console.log(`DOCX file size: ${buffer.length} bytes`);

    // Primary method: Use mammoth.js for high-quality text extraction
    try {
      console.log('Using mammoth.js for DOCX extraction...');

      // Extract raw text (preserves structure better than HTML conversion)
      const result = await mammoth.extractRawText({ buffer });

      let extractedText = result.value || '';
      console.log(`Mammoth extraction completed. Text length: ${extractedText.length} characters`);

      // Log any conversion messages/warnings
      if (result.messages && result.messages.length > 0) {
        console.log('Mammoth conversion messages:', result.messages);
      }

      // Clean up the text while preserving structure
      extractedText = extractedText
        // Normalize line breaks
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        // Remove excessive whitespace but preserve structure
        .replace(/[ \t]+/g, ' ')  // Multiple spaces/tabs to single space
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Multiple newlines to double newline
        // Remove control characters but keep printable ones
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
        .trim();

      if (extractedText && extractedText.length > 50) {
        const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;
        console.log(`Successfully extracted ${wordCount} words from DOCX using mammoth.js`);
        return extractedText;
      }
    } catch (mammothError) {
      console.error('Mammoth.js extraction failed:', mammothError);
      console.log('Falling back to manual XML extraction...');
    }

    // Fallback method: Manual XML extraction using JSZip
    try {
      const zip = await JSZip.loadAsync(buffer);
      console.log('DOCX ZIP structure loaded, extracting document.xml...');

      // DOCX files store their content in word/document.xml
      const documentXml = await zip.file('word/document.xml')?.async('string');

      if (documentXml) {
        console.log('Document.xml found, parsing XML...');

        // Parse the XML
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(documentXml, 'text/xml');

        // Extract text from all text nodes (w:t elements)
        let extractedText = '';
        const textNodes = xmlDoc.getElementsByTagName('w:t');
        console.log(`Found ${textNodes.length} text nodes in document.xml`);

        for (let i = 0; i < textNodes.length; i++) {
          const node = textNodes[i];
          if (node.textContent) {
            extractedText += node.textContent + ' ';
          }
        }

        // Also extract text from table cells (w:tc elements)
        const tableCells = xmlDoc.getElementsByTagName('w:tc');
        console.log(`Found ${tableCells.length} table cells`);

        for (let i = 0; i < tableCells.length; i++) {
          const cell = tableCells[i];
          const cellTextNodes = cell.getElementsByTagName('w:t');
          for (let j = 0; j < cellTextNodes.length; j++) {
            if (cellTextNodes[j].textContent) {
              extractedText += cellTextNodes[j].textContent + ' ';
            }
          }
        }

        // Clean up the text
        extractedText = extractedText
          .replace(/\s+/g, ' ')
          .trim();

        if (extractedText && extractedText.length > 20) {
          const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;
          console.log(`Successfully extracted ${wordCount} words from DOCX using XML parsing`);
          return extractedText;
        }
      }
    } catch (zipError) {
      console.error('JSZip extraction failed:', zipError);
      console.log('Falling back to regex-based extraction...');
    }

    // Final fallback: Regex-based extraction from raw buffer
    const content = buffer.toString('utf8');
    let extractedText = '';

    // Look for text between <w:t> and </w:t> tags (Word XML format)
    const matches = content.match(/<w:t[^>]*>(.*?)<\/w:t>/g);
    if (matches) {
      console.log(`Found ${matches.length} text matches using regex`);

      extractedText = matches
        .map(match => {
          // Extract the text between the tags
          const textMatch = match.match(/<w:t[^>]*>(.*?)<\/w:t>/);
          return textMatch ? textMatch[1] : '';
        })
        .join(' ')
        .replace(/[^\x20-\x7E\n\u00A0-\uFFFF]/g, ' ') // Keep printable chars including Unicode
        .replace(/\s+/g, ' ')
        .trim();
    }

    // If we still don't have good text, try a more aggressive approach
    if (!extractedText || extractedText.length < 50) {
      console.log('Attempting aggressive text extraction...');

      // Look for any text that looks like words (2+ consecutive letters)
      const wordMatches = content.match(/[a-zA-Z]{2,}[a-zA-Z\s.,;:!?'"\-@#$%&*()+={}[\]|\\/<>]{2,}/g);
      if (wordMatches) {
        const wordText = wordMatches.join(' ')
          .replace(/[^\x20-\x7E\n\u00A0-\uFFFF]/g, ' ')
          .replace(/\s+/g, ' ')
          .trim();

        if (wordText.length > extractedText.length) {
          extractedText = wordText;
          console.log(`Aggressive extraction found ${wordText.length} characters`);
        }
      }
    }

    // Final validation
    if (extractedText && extractedText.length > 10) {
      const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;
      console.log(`Final DOCX extraction: ${wordCount} words, ${extractedText.length} characters`);
      return extractedText;
    }

    console.warn(`DOCX extraction failed or produced minimal text for: ${filePath}`);
    return extractedText || 'No text could be extracted from this DOCX file.';

  } catch (error) {
    console.error('Error extracting text from DOCX:', error);
    return 'Error extracting text from DOCX.';
  }
}

/**
 * Extract text from older .doc files (Microsoft Word 97-2003)
 */
export async function extractTextFromDoc(filePath: string): Promise<string> {
  try {
    console.log(`Starting DOC text extraction for: ${filePath}`);

    // Read the file as a buffer
    const buffer = await promisify(fs.readFile)(filePath);
    console.log(`DOC file size: ${buffer.length} bytes`);

    // Try mammoth.js first (it can handle some .doc files)
    try {
      console.log('Attempting mammoth.js extraction for DOC file...');
      const result = await mammoth.extractRawText({ buffer });

      if (result.value && result.value.length > 50) {
        const wordCount = result.value.split(/\s+/).filter(word => word.length > 0).length;
        console.log(`Successfully extracted ${wordCount} words from DOC using mammoth.js`);
        return result.value.trim();
      }
    } catch (mammothError) {
      console.log('Mammoth.js failed for DOC file, trying alternative methods...');
    }

    // Fallback: Try to extract text using basic string parsing
    // .doc files are binary but may contain readable text
    const content = buffer.toString('utf8');

    // Look for readable text patterns
    const textMatches = content.match(/[a-zA-Z][a-zA-Z\s.,;:!?'"\-@#$%&*()+={}[\]|\\/<>0-9]{10,}/g);

    if (textMatches && textMatches.length > 0) {
      let extractedText = textMatches
        .join(' ')
        .replace(/[^\x20-\x7E\n\u00A0-\uFFFF]/g, ' ') // Keep printable chars
        .replace(/\s+/g, ' ')
        .trim();

      // Filter out common binary artifacts
      extractedText = extractedText
        .replace(/Microsoft Office Word/gi, '')
        .replace(/Times New Roman/gi, '')
        .replace(/Arial/gi, '')
        .replace(/Calibri/gi, '')
        .replace(/\b[A-Z]{10,}\b/g, '') // Remove long uppercase strings (likely binary)
        .replace(/\s+/g, ' ')
        .trim();

      if (extractedText.length > 100) {
        const wordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;
        console.log(`Extracted ${wordCount} words from DOC using text parsing`);
        return extractedText;
      }
    }

    console.warn(`DOC extraction produced minimal text for: ${filePath}`);
    return 'Limited text extraction from DOC file. Consider converting to DOCX for better results.';

  } catch (error) {
    console.error('Error extracting text from DOC:', error);
    return 'Error extracting text from DOC file.';
  }
}

/**
 * Extract text from a file based on its type with comprehensive support
 */
export async function extractTextFromFile(filePath: string, fileType: string): Promise<string> {
  console.log(`Starting text extraction for file: ${filePath}, type: ${fileType}`);

  try {
    let extractedText = '';

    switch (fileType.toLowerCase()) {
      case 'pdf':
        extractedText = await extractTextFromPdf(filePath);
        break;

      case 'docx':
        extractedText = await extractTextFromDocx(filePath);
        break;

      case 'doc':
        extractedText = await extractTextFromDoc(filePath);
        break;

      case 'txt':
        // Handle plain text files
        const textBuffer = await promisify(fs.readFile)(filePath, 'utf8');
        extractedText = textBuffer.trim();
        console.log(`Extracted ${extractedText.length} characters from TXT file`);
        break;

      default:
        console.warn(`Unsupported file type: ${fileType}`);
        return `Unsupported file type: ${fileType}. Supported formats: PDF, DOCX, DOC, TXT`;
    }

    // Post-processing: Ensure we have meaningful content
    if (extractedText && extractedText.length > 10) {
      // Add file type information for better searchability
      const fileInfo = `File Type: ${fileType.toUpperCase()} `;

      // Count final words
      const finalWordCount = extractedText.split(/\s+/).filter(word => word.length > 0).length;
      console.log(`Text extraction completed successfully: ${finalWordCount} words, ${extractedText.length} characters`);

      return fileInfo + extractedText;
    } else {
      console.warn(`Text extraction failed or produced minimal content for: ${filePath}`);
      return `Text extraction failed for ${fileType.toUpperCase()} file. File may be corrupted, password-protected, or contain only images.`;
    }

  } catch (error) {
    console.error(`Error in extractTextFromFile for ${filePath}:`, error);
    return `Error extracting text from ${fileType.toUpperCase()} file: ${error instanceof Error ? error.message : 'Unknown error'}`;
  }
}
