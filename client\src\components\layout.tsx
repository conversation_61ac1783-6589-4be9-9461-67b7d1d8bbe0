import { ReactNode, useState } from "react";
import { useLocation } from "wouter";
import ResetPasswordModal from "@/components/reset-password-modal";
import SharedNavbar from "@/components/shared-navbar";

interface LayoutProps {
  children: ReactNode;
}

export default function Layout({ children }: LayoutProps) {
  const [location] = useLocation();
  const [showResetPasswordModal, setShowResetPasswordModal] = useState(false);

  // This key prop forces a full remount of the component on location change,
  // which can help clear any stuck tooltips or overlays
  return (
    <div key={location} className="min-h-screen flex flex-col bg-[#EAE6E1] dark:bg-gray-900 text-[#1C2A42] dark:text-white transition-colors">
      {/* Navbar */}
      <SharedNavbar />

      {/* Main content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-[#1C2A42] dark:bg-gray-800 text-white p-4 mt-auto">
        <div className="container mx-auto text-center">
          <p>&copy; 2025 PathLink – AI-Powered Labor Redistribution. All rights reserved.</p>
        </div>
      </footer>

      {/* Reset Password Modal - Commented out until we update the shared navbar to handle this
      <ResetPasswordModal
        open={showResetPasswordModal}
        onClose={() => setShowResetPasswordModal(false)}
      /> */}
    </div>
  );
}
