const { chromium } = require('playwright');
const fs = require('fs');

class BrowserUITestSuite {
  constructor() {
    this.results = [];
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5000';
  }

  async log(test, status, details = '', screenshot = null) {
    const result = {
      test,
      status,
      details,
      screenshot,
      timestamp: new Date().toISOString()
    };
    this.results.push(result);
    
    const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${emoji} [Browser] ${test}: ${status} ${details}`);
  }

  async setup() {
    console.log('🚀 Setting up browser for UI testing...');
    this.browser = await chromium.launch({ 
      headless: false, // Set to true for headless testing
      slowMo: 500 // Slow down actions for better visibility
    });
    this.page = await this.browser.newPage();
    
    // Set viewport size
    await this.page.setViewportSize({ width: 1280, height: 720 });
    
    console.log('✅ Browser setup complete');
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('🧹 Browser cleanup complete');
    }
  }

  async takeScreenshot(name) {
    const filename = `screenshots/${name}-${Date.now()}.png`;
    
    // Create screenshots directory if it doesn't exist
    if (!fs.existsSync('screenshots')) {
      fs.mkdirSync('screenshots');
    }
    
    await this.page.screenshot({ path: filename, fullPage: true });
    return filename;
  }

  async testPageLoad(pageName, url) {
    try {
      console.log(`\n🌐 Testing ${pageName} page...`);
      
      await this.page.goto(url, { waitUntil: 'networkidle' });
      
      // Check if page loaded successfully
      const title = await this.page.title();
      
      if (title && !title.includes('Error')) {
        const screenshot = await this.takeScreenshot(`${pageName.toLowerCase()}-page`);
        await this.log(`${pageName} Page Load`, 'PASS', `Title: ${title}`, screenshot);
        return true;
      } else {
        await this.log(`${pageName} Page Load`, 'FAIL', `Title: ${title}`);
        return false;
      }
    } catch (error) {
      await this.log(`${pageName} Page Load`, 'FAIL', `Error: ${error.message}`);
      return false;
    }
  }

  async testNavigation() {
    console.log('\n🧭 TESTING NAVIGATION...');

    await this.page.goto(this.baseUrl);
    
    // Test navigation links
    const navLinks = [
      'Home',
      'Dashboard', 
      'Browse Jobs',
      'Path Coach'
    ];

    for (const linkText of navLinks) {
      try {
        // Look for navigation link
        const navElement = await this.page.locator(`text=${linkText}`).first();
        
        if (await navElement.isVisible()) {
          await this.log(`Navigation Link: ${linkText}`, 'PASS', 'Link is visible');
        } else {
          await this.log(`Navigation Link: ${linkText}`, 'FAIL', 'Link not found');
        }
      } catch (error) {
        await this.log(`Navigation Link: ${linkText}`, 'FAIL', `Error: ${error.message}`);
      }
    }
  }

  async testAuthentication() {
    console.log('\n🔐 TESTING AUTHENTICATION UI...');

    try {
      // Navigate to auth page
      await this.page.goto(`${this.baseUrl}/auth`);
      await this.page.waitForTimeout(2000);
      
      // Check if login form exists
      const emailInput = this.page.locator('input[type="email"]');
      const passwordInput = this.page.locator('input[type="password"]');
      const loginButton = this.page.locator('button:has-text("Sign In")');

      if (await emailInput.isVisible() && await passwordInput.isVisible()) {
        await this.log('Login Form Elements', 'PASS', 'Email and password fields found');
        
        // Test login process
        await emailInput.fill('<EMAIL>');
        await passwordInput.fill('password123');
        
        const screenshot = await this.takeScreenshot('login-form-filled');
        await this.log('Login Form Fill', 'PASS', 'Form filled successfully', screenshot);
        
        // Click login button
        await loginButton.click();
        
        // Wait for navigation or response
        await this.page.waitForTimeout(3000);
        
        // Check if redirected to dashboard
        const currentUrl = this.page.url();
        if (currentUrl.includes('/dashboard')) {
          const dashboardScreenshot = await this.takeScreenshot('dashboard-after-login');
          await this.log('Login Process', 'PASS', 'Successfully logged in and redirected', dashboardScreenshot);
          return true;
        } else {
          await this.log('Login Process', 'FAIL', `Still on: ${currentUrl}`);
          return false;
        }
      } else {
        await this.log('Login Form Elements', 'FAIL', 'Login form elements not found');
        return false;
      }
    } catch (error) {
      await this.log('Authentication Test', 'FAIL', `Error: ${error.message}`);
      return false;
    }
  }

  async testJobBrowsing() {
    console.log('\n💼 TESTING JOB BROWSING UI...');

    try {
      await this.page.goto(`${this.baseUrl}/browse-jobs`);
      await this.page.waitForTimeout(3000);
      
      // Check for job content
      const pageContent = await this.page.textContent('body');
      
      if (pageContent.includes('job') || pageContent.includes('position') || pageContent.includes('Frontend Developer')) {
        const screenshot = await this.takeScreenshot('job-listings');
        await this.log('Job Listings Display', 'PASS', 'Job content found on page', screenshot);
      } else {
        await this.log('Job Listings Display', 'WARN', 'No job content clearly visible');
      }

      // Test search functionality if available
      const searchInput = this.page.locator('input[placeholder*="search"], input[placeholder*="Search"]');
      if (await searchInput.isVisible()) {
        await searchInput.fill('developer');
        await this.log('Job Search', 'PASS', 'Search functionality available');
      }

    } catch (error) {
      await this.log('Job Browsing Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testProfileManagement() {
    console.log('\n👤 TESTING PROFILE MANAGEMENT UI...');

    try {
      await this.page.goto(`${this.baseUrl}/profile`);
      await this.page.waitForTimeout(3000);
      
      // Check for profile form elements
      const profileInputs = await this.page.locator('input, textarea, select').count();
      
      if (profileInputs > 0) {
        const screenshot = await this.takeScreenshot('profile-page');
        await this.log('Profile Form', 'PASS', `Found ${profileInputs} form elements`, screenshot);
        
        // Test profile update if possible
        const nameInput = this.page.locator('input[name="name"], input[placeholder*="name"]').first();
        if (await nameInput.isVisible()) {
          await nameInput.fill('Test User Name');
          await this.log('Profile Update', 'PASS', 'Profile form is editable');
        }
      } else {
        await this.log('Profile Form', 'FAIL', 'No profile form elements found');
      }

    } catch (error) {
      await this.log('Profile Management Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testAIChat() {
    console.log('\n🤖 TESTING AI CHAT UI...');

    try {
      await this.page.goto(`${this.baseUrl}/path-coach`);
      await this.page.waitForTimeout(4000);
      
      // Look for chat input
      const chatInput = this.page.locator('input[placeholder*="Ask"], textarea[placeholder*="Ask"], input[placeholder*="message"]');
      
      if (await chatInput.isVisible()) {
        await chatInput.fill('Hello, can you help me with my career?');
        
        // Look for send button
        const sendButton = this.page.locator('button:has-text("Send"), button[type="submit"]').first();
        
        if (await sendButton.isVisible()) {
          const screenshot = await this.takeScreenshot('ai-chat-interface');
          await this.log('AI Chat Interface', 'PASS', 'Chat interface is functional', screenshot);
          
          // Test sending message
          await sendButton.click();
          await this.page.waitForTimeout(3000);
          
          await this.log('AI Chat Message', 'PASS', 'Message sent successfully');
        } else {
          await this.log('AI Chat Interface', 'WARN', 'Chat input found but no send button');
        }
      } else {
        await this.log('AI Chat Interface', 'FAIL', 'Chat interface not found');
      }

    } catch (error) {
      await this.log('AI Chat Test', 'FAIL', `Error: ${error.message}`);
    }
  }

  async testResponsiveDesign() {
    console.log('\n📱 TESTING RESPONSIVE DESIGN...');

    const viewports = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1280, height: 720 }
    ];

    for (const viewport of viewports) {
      try {
        await this.page.setViewportSize({ width: viewport.width, height: viewport.height });
        await this.page.goto(this.baseUrl);
        await this.page.waitForTimeout(2000);
        
        // Check if navigation is accessible
        const navVisible = await this.page.locator('nav, .navbar, [role="navigation"]').isVisible();
        
        const screenshot = await this.takeScreenshot(`responsive-${viewport.name.toLowerCase()}`);
        
        if (navVisible) {
          await this.log(`${viewport.name} Layout`, 'PASS', `${viewport.width}x${viewport.height}`, screenshot);
        } else {
          await this.log(`${viewport.name} Layout`, 'WARN', 'Navigation not clearly visible', screenshot);
        }

      } catch (error) {
        await this.log(`${viewport.name} Layout`, 'FAIL', `Error: ${error.message}`);
      }
    }

    // Reset to desktop viewport
    await this.page.setViewportSize({ width: 1280, height: 720 });
  }

  async runBrowserTests() {
    console.log('🚀 STARTING COMPREHENSIVE BROWSER UI TESTING...\n');
    console.log('This will open a browser and test the actual user interface.\n');

    try {
      await this.setup();

      // Test basic page loads
      await this.testPageLoad('Home', this.baseUrl);
      await this.testPageLoad('Auth', `${this.baseUrl}/auth`);
      
      // Test navigation
      await this.testNavigation();
      
      // Test authentication flow
      const loginSuccess = await this.testAuthentication();
      
      // Test authenticated features if login worked
      if (loginSuccess) {
        await this.testJobBrowsing();
        await this.testProfileManagement();
        await this.testAIChat();
      }
      
      // Test responsive design
      await this.testResponsiveDesign();

    } catch (error) {
      console.error('Browser testing failed:', error);
    } finally {
      await this.cleanup();
    }

    // Generate report
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.status === 'PASS').length;
    const failedTests = this.results.filter(r => r.status === 'FAIL').length;
    const warningTests = this.results.filter(r => r.status === 'WARN').length;

    console.log('\n📊 BROWSER UI TEST SUMMARY:');
    console.log('='.repeat(60));
    console.log(`✅ PASSED: ${passedTests}`);
    console.log(`❌ FAILED: ${failedTests}`);
    console.log(`⚠️  WARNINGS: ${warningTests}`);
    console.log(`📈 UI SUCCESS RATE: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));

    // Save detailed report
    const report = {
      summary: {
        totalTests,
        passedTests,
        failedTests,
        warningTests,
        successRate: (passedTests / totalTests) * 100
      },
      results: this.results,
      timestamp: new Date().toISOString()
    };

    fs.writeFileSync('browser-ui-test-report.json', JSON.stringify(report, null, 2));
    console.log('\n📄 Detailed browser UI test report saved to browser-ui-test-report.json');
    console.log('📸 Screenshots saved in the screenshots/ directory');

    return report;
  }
}

// Run tests
const tester = new BrowserUITestSuite();
tester.runBrowserTests().catch(console.error);
