/**
 * Supabase storage implementation
 * This provides an alternative storage implementation using the Supabase client
 * instead of direct PostgreSQL connections
 */

import { createClient } from '@supabase/supabase-js';
import { User, Job, Resume, Application, Message } from '@shared/types';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || '';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key is not set in environment variables');
}

const supabase = createClient(supabaseUrl, supabaseKey);

/**
 * Supabase storage class
 * This class provides methods for interacting with the Supabase database
 */
export class SupabaseStorage {
  /**
   * Get a user by ID
   */
  async getUser(id: number): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error('Error getting user:', error);
      return null;
    }
    
    return data as User;
  }

  /**
   * Get a user by email
   */
  async getUserByEmail(email: string): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .single();
    
    if (error) {
      console.error('Error getting user by email:', error);
      return null;
    }
    
    return data as User;
  }

  /**
   * Create a new user
   */
  async createUser(user: Omit<User, 'id'>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .insert([user])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating user:', error);
      return null;
    }
    
    return data as User;
  }

  /**
   * Update a user
   */
  async updateUser(id: number, user: Partial<User>): Promise<User | null> {
    const { data, error } = await supabase
      .from('users')
      .update(user)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating user:', error);
      return null;
    }
    
    return data as User;
  }

  /**
   * Get all jobs
   */
  async getJobs(): Promise<Job[]> {
    const { data, error } = await supabase
      .from('jobs')
      .select('*');
    
    if (error) {
      console.error('Error getting jobs:', error);
      return [];
    }
    
    return data as Job[];
  }

  /**
   * Get a job by ID
   */
  async getJob(id: number): Promise<Job | null> {
    const { data, error } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error('Error getting job:', error);
      return null;
    }
    
    return data as Job;
  }

  /**
   * Create a new job
   */
  async createJob(job: Omit<Job, 'id'>): Promise<Job | null> {
    const { data, error } = await supabase
      .from('jobs')
      .insert([job])
      .select()
      .single();
    
    if (error) {
      console.error('Error creating job:', error);
      return null;
    }
    
    return data as Job;
  }

  /**
   * Update a job
   */
  async updateJob(id: number, job: Partial<Job>): Promise<Job | null> {
    const { data, error } = await supabase
      .from('jobs')
      .update(job)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error('Error updating job:', error);
      return null;
    }
    
    return data as Job;
  }

  /**
   * Delete a job
   */
  async deleteJob(id: number): Promise<boolean> {
    const { error } = await supabase
      .from('jobs')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error('Error deleting job:', error);
      return false;
    }
    
    return true;
  }
}

// Export a singleton instance
export const supabaseStorage = new SupabaseStorage();
