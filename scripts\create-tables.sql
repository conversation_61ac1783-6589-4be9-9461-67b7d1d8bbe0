-- Create tables for SocialPulse application

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    role TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Jobs table
CREATE TABLE IF NOT EXISTS jobs (
    id SERIAL PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    industry TEXT NOT NULL,
    location TEXT NOT NULL,
    salary TEXT,
    employer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Matches table
CREATE TABLE IF NOT EXISTS matches (
    id SERIAL PRIMARY KEY,
    job_id INTEGER NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    worker_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status TEXT NOT NULL,
    match_score INTEGER NOT NULL,
    match_date TIMESTAMP NOT NULL
);

-- Resumes table
CREATE TABLE IF NOT EXISTS resumes (
    id SERIAL PRIMARY KEY,
    worker_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    file_type TEXT NOT NULL,
    upload_date TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Surplus Employees table
CREATE TABLE IF NOT EXISTS surplus_employees (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    previous_role TEXT NOT NULL,
    skills TEXT NOT NULL,
    industry TEXT NOT NULL,
    years_experience INTEGER NOT NULL,
    status TEXT NOT NULL,
    employer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Worker Values table
CREATE TABLE IF NOT EXISTS worker_values (
    id SERIAL PRIMARY KEY,
    worker_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    work_life_balance INTEGER,
    compensation INTEGER,
    job_security INTEGER,
    remote_work INTEGER,
    professional_growth INTEGER
);

-- Mentor Preferences table
CREATE TABLE IF NOT EXISTS mentor_preferences (
    id SERIAL PRIMARY KEY,
    worker_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    industry_preference TEXT,
    skill_level TEXT,
    communication_preference TEXT,
    availability TEXT,
    goals TEXT
);

-- Create sample users
INSERT INTO users (email, password, name, role)
VALUES 
    ('<EMAIL>', '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 'TechCorp Inc', 'employer'),
    ('<EMAIL>', '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 'Finance Solutions', 'employer'),
    ('<EMAIL>', '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 'John Doe', 'worker'),
    ('<EMAIL>', '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 'Jane Smith', 'worker')
ON CONFLICT (email) DO NOTHING;

-- Create sample jobs
INSERT INTO jobs (title, description, industry, location, employer_id)
VALUES 
    ('Frontend Developer', 'Building beautiful user interfaces with React', 'Technology', 'Remote', 1),
    ('Data Scientist', 'Analyzing large datasets to drive business decisions', 'Finance', 'New York', 2)
ON CONFLICT DO NOTHING;

-- Create sample surplus employees
INSERT INTO surplus_employees (name, previous_role, skills, industry, years_experience, status, employer_id)
VALUES 
    ('Alex Johnson', 'Senior Developer', 'JavaScript, React, Node.js', 'Technology', 8, 'available', 1),
    ('Maria Garcia', 'Data Analyst', 'Python, SQL, Tableau', 'Finance', 5, 'available', 2)
ON CONFLICT DO NOTHING;
