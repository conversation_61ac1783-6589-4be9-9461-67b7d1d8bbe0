import { useState } from 'react';
import Layout from "@/components/layout";
import { useQuery } from '@tanstack/react-query';
import { User, Job, SurplusEmployee, Match } from '@shared/schema';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Shield, Users, Briefcase, UserPlus, Handshake, AlertTriangle, Trash2 } from 'lucide-react';
import { queryClient } from '@/lib/queryClient';

export default function AdminPage() {
  const { toast } = useToast();
  const [deleteConfirm, setDeleteConfirm] = useState<{type: string, id: number} | null>(null);
  
  // Fetch all users
  const { data: users = [] } = useQuery<User[]>({
    queryKey: ['/api/admin/users'],
    onError: () => {
      toast({
        title: "Access Denied",
        description: "You don't have permission to view this page.",
        variant: "destructive",
      });
    }
  });
  
  // Fetch all jobs
  const { data: jobs = [] } = useQuery<Job[]>({
    queryKey: ['/api/admin/jobs'],
  });
  
  // Fetch all surplus employees
  const { data: employees = [] } = useQuery<SurplusEmployee[]>({
    queryKey: ['/api/admin/surplus-employees'],
  });
  
  // Fetch all matches
  const { data: matches = [] } = useQuery<Match[]>({
    queryKey: ['/api/admin/matches'],
  });
  
  // Delete handlers
  const handleDeleteUser = async (id: number) => {
    if (deleteConfirm?.type === 'user' && deleteConfirm.id === id) {
      try {
        const response = await fetch(`/api/admin/users/${id}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          queryClient.invalidateQueries({ queryKey: ['/api/admin/users'] });
          toast({
            title: "User Deleted",
            description: "The user has been deleted successfully.",
          });
        } else {
          throw new Error('Failed to delete user');
        }
      } catch (error) {
        toast({
          title: "Delete Failed",
          description: "There was an error deleting the user.",
          variant: "destructive",
        });
      }
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm({ type: 'user', id });
    }
  };
  
  const handleDeleteJob = async (id: number) => {
    if (deleteConfirm?.type === 'job' && deleteConfirm.id === id) {
      try {
        const response = await fetch(`/api/admin/jobs/${id}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          queryClient.invalidateQueries({ queryKey: ['/api/admin/jobs'] });
          toast({
            title: "Job Deleted",
            description: "The job has been deleted successfully.",
          });
        } else {
          throw new Error('Failed to delete job');
        }
      } catch (error) {
        toast({
          title: "Delete Failed",
          description: "There was an error deleting the job.",
          variant: "destructive",
        });
      }
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm({ type: 'job', id });
    }
  };
  
  const handleDeleteEmployee = async (id: number) => {
    if (deleteConfirm?.type === 'employee' && deleteConfirm.id === id) {
      try {
        const response = await fetch(`/api/admin/surplus-employees/${id}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          queryClient.invalidateQueries({ queryKey: ['/api/admin/surplus-employees'] });
          toast({
            title: "Employee Deleted",
            description: "The surplus employee has been deleted successfully.",
          });
        } else {
          throw new Error('Failed to delete employee');
        }
      } catch (error) {
        toast({
          title: "Delete Failed",
          description: "There was an error deleting the employee.",
          variant: "destructive",
        });
      }
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm({ type: 'employee', id });
    }
  };
  
  const handleDeleteMatch = async (id: number) => {
    if (deleteConfirm?.type === 'match' && deleteConfirm.id === id) {
      try {
        const response = await fetch(`/api/admin/matches/${id}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          queryClient.invalidateQueries({ queryKey: ['/api/admin/matches'] });
          toast({
            title: "Match Deleted",
            description: "The match has been deleted successfully.",
          });
        } else {
          throw new Error('Failed to delete match');
        }
      } catch (error) {
        toast({
          title: "Delete Failed",
          description: "There was an error deleting the match.",
          variant: "destructive",
        });
      }
      setDeleteConfirm(null);
    } else {
      setDeleteConfirm({ type: 'match', id });
    }
  };
  
  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center mb-8">
          <Shield className="h-8 w-8 text-[#1C2A42] dark:text-white mr-3" />
          <h1 className="text-2xl font-bold text-[#1C2A42] dark:text-white">Admin Dashboard</h1>
        </div>
        
        <Tabs defaultValue="users" className="space-y-4">
          <TabsList className="grid grid-cols-4 w-full max-w-2xl">
            <TabsTrigger value="users" className="flex items-center">
              <Users className="mr-2 h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="jobs" className="flex items-center">
              <Briefcase className="mr-2 h-4 w-4" />
              Jobs
            </TabsTrigger>
            <TabsTrigger value="employees" className="flex items-center">
              <UserPlus className="mr-2 h-4 w-4" />
              Employees
            </TabsTrigger>
            <TabsTrigger value="matches" className="flex items-center">
              <Handshake className="mr-2 h-4 w-4" />
              Matches
            </TabsTrigger>
          </TabsList>
          
          {/* Users Tab */}
          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>All Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {users.length > 0 ? (
                    users.map(user => (
                      <div key={user.id} className="p-4 border rounded-md flex justify-between items-center bg-white dark:bg-gray-800">
                        <div>
                          <div className="font-medium">{user.name}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{user.email}</div>
                          <Badge variant="outline" className={
                            user.role === 'admin' 
                              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' 
                              : user.role === 'employer'
                                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }>
                            {user.role}
                          </Badge>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDeleteUser(user.id)}
                          className={deleteConfirm?.type === 'user' && deleteConfirm.id === user.id 
                            ? "bg-red-100 text-red-800 hover:bg-red-200 hover:text-red-900 flex items-center" 
                            : "text-gray-500 hover:text-red-700 flex items-center"}
                        >
                          {deleteConfirm?.type === 'user' && deleteConfirm.id === user.id ? (
                            <>
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              Confirm
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </>
                          )}
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No users found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Jobs Tab */}
          <TabsContent value="jobs">
            <Card>
              <CardHeader>
                <CardTitle>All Jobs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {jobs.length > 0 ? (
                    jobs.map(job => (
                      <div key={job.id} className="p-4 border rounded-md flex justify-between items-start bg-white dark:bg-gray-800">
                        <div>
                          <div className="font-medium">{job.title}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">{job.description}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                            <span className="mr-3">Industry: {job.industry}</span>
                            <span>Location: {job.location}</span>
                          </div>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDeleteJob(job.id)}
                          className={deleteConfirm?.type === 'job' && deleteConfirm.id === job.id 
                            ? "bg-red-100 text-red-800 hover:bg-red-200 hover:text-red-900 flex items-center" 
                            : "text-gray-500 hover:text-red-700 flex items-center"}
                        >
                          {deleteConfirm?.type === 'job' && deleteConfirm.id === job.id ? (
                            <>
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              Confirm
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </>
                          )}
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No jobs found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Employees Tab */}
          <TabsContent value="employees">
            <Card>
              <CardHeader>
                <CardTitle>All Surplus Employees</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {employees.length > 0 ? (
                    employees.map(employee => (
                      <div key={employee.id} className="p-4 border rounded-md flex justify-between items-start bg-white dark:bg-gray-800">
                        <div>
                          <div className="font-medium">{employee.name}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">Previous Role: {employee.previous_role}</div>
                          <div className="flex justify-between mt-2 items-center">
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Industry: {employee.industry}
                            </div>
                            <Badge variant="outline" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                              {employee.status}
                            </Badge>
                          </div>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => handleDeleteEmployee(employee.id)}
                          className={deleteConfirm?.type === 'employee' && deleteConfirm.id === employee.id 
                            ? "bg-red-100 text-red-800 hover:bg-red-200 hover:text-red-900 flex items-center" 
                            : "text-gray-500 hover:text-red-700 flex items-center"}
                        >
                          {deleteConfirm?.type === 'employee' && deleteConfirm.id === employee.id ? (
                            <>
                              <AlertTriangle className="h-4 w-4 mr-1" />
                              Confirm
                            </>
                          ) : (
                            <>
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </>
                          )}
                        </Button>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No surplus employees found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Matches Tab */}
          <TabsContent value="matches">
            <Card>
              <CardHeader>
                <CardTitle>All Matches</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {matches.length > 0 ? (
                    matches.map(match => {
                      const job = jobs.find(j => j.id === match.job_id);
                      const worker = users.find(u => u.id === match.worker_id);
                      
                      return (
                        <div key={match.id} className="p-4 border rounded-md flex justify-between items-start bg-white dark:bg-gray-800">
                          <div>
                            <div className="font-medium">
                              {job ? job.title : "Unknown Job"} - {worker ? worker.name : "Unknown Worker"}
                            </div>
                            <div className="flex justify-between mt-2 items-center">
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                Match Score: {match.match_score || "N/A"}
                              </div>
                              <Badge variant="outline" className={
                                match.status === 'matched' 
                                  ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                  : match.status === 'pending'
                                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                              }>
                                {match.status}
                              </Badge>
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleDeleteMatch(match.id)}
                            className={deleteConfirm?.type === 'match' && deleteConfirm.id === match.id 
                              ? "bg-red-100 text-red-800 hover:bg-red-200 hover:text-red-900 flex items-center" 
                              : "text-gray-500 hover:text-red-700 flex items-center"}
                          >
                            {deleteConfirm?.type === 'match' && deleteConfirm.id === match.id ? (
                              <>
                                <AlertTriangle className="h-4 w-4 mr-1" />
                                Confirm
                              </>
                            ) : (
                              <>
                                <Trash2 className="h-4 w-4 mr-1" />
                                Delete
                              </>
                            )}
                          </Button>
                        </div>
                      );
                    })
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">No matches found.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}