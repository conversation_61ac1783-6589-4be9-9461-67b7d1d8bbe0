CREATE TABLE IF NOT EXISTS "jobs" (
    "id" serial PRIMARY KEY NOT NULL,
    "title" text NOT NULL,
    "description" text NOT NULL,
    "industry" text NOT NULL,
    "location" text NOT NULL,
    "salary" text,
    "employer_id" integer NOT NULL,
    "created_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "matches" (
    "id" serial PRIMARY KEY NOT NULL,
    "job_id" integer NOT NULL,
    "worker_id" integer NOT NULL,
    "status" text NOT NULL,
    "match_score" integer NOT NULL,
    "match_date" timestamp NOT NULL
);

CREATE TABLE IF NOT EXISTS "mentor_preferences" (
    "id" serial PRIMARY KEY NOT NULL,
    "worker_id" integer NOT NULL,
    "industry_preference" text,
    "skill_level" text,
    "communication_preference" text,
    "availability" text,
    "goals" text
);

CREATE TABLE IF NOT EXISTS "resumes" (
    "id" serial PRIMARY KEY NOT NULL,
    "worker_id" integer NOT NULL,
    "filename" text NOT NULL,
    "file_path" text NOT NULL,
    "file_size" integer NOT NULL,
    "file_type" text NOT NULL,
    "upload_date" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "surplus_employees" (
    "id" serial PRIMARY KEY NOT NULL,
    "name" text NOT NULL,
    "previous_role" text NOT NULL,
    "skills" text NOT NULL,
    "industry" text NOT NULL,
    "years_experience" integer NOT NULL,
    "status" text NOT NULL,
    "employer_id" integer NOT NULL
);

CREATE TABLE IF NOT EXISTS "users" (
    "id" serial PRIMARY KEY NOT NULL,
    "email" text NOT NULL,
    "password" text NOT NULL,
    "name" text NOT NULL,
    "role" text NOT NULL,
    "created_at" timestamp DEFAULT now() NOT NULL
);

CREATE TABLE IF NOT EXISTS "worker_values" (
    "id" serial PRIMARY KEY NOT NULL,
    "worker_id" integer NOT NULL,
    "work_life_balance" integer,
    "compensation" integer,
    "job_security" integer,
    "remote_work" integer,
    "professional_growth" integer
);

CREATE UNIQUE INDEX IF NOT EXISTS "email_idx" ON "users" ("email");

ALTER TABLE "jobs" ADD CONSTRAINT "jobs_employer_id_users_id_fk" FOREIGN KEY ("employer_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "matches" ADD CONSTRAINT "matches_job_id_jobs_id_fk" FOREIGN KEY ("job_id") REFERENCES "jobs" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "matches" ADD CONSTRAINT "matches_worker_id_users_id_fk" FOREIGN KEY ("worker_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "mentor_preferences" ADD CONSTRAINT "mentor_preferences_worker_id_users_id_fk" FOREIGN KEY ("worker_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "resumes" ADD CONSTRAINT "resumes_worker_id_users_id_fk" FOREIGN KEY ("worker_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "surplus_employees" ADD CONSTRAINT "surplus_employees_employer_id_users_id_fk" FOREIGN KEY ("employer_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;

ALTER TABLE "worker_values" ADD CONSTRAINT "worker_values_worker_id_users_id_fk" FOREIGN KEY ("worker_id") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE NO ACTION;
