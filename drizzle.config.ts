import { defineConfig } from "drizzle-kit";
import * as dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

// Check for DATABASE_URL but don't throw an error
// This allows the app to run without a database for development
if (!process.env.DATABASE_URL) {
  console.warn("WARNING: DATABASE_URL is not set. Database migrations will not work.");
}

export default defineConfig({
  out: "./drizzle",
  schema: "./shared/schema.ts",
  dialect: "postgresql",
  dbCredentials: {
    url: process.env.DATABASE_URL || '',
  },
  // Enable verbose output for debugging
  verbose: true,
});
