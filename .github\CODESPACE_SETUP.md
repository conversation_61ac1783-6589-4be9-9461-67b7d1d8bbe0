# PathLink Codespace Setup Guide

## 🚀 Getting Started with PathLink Codespace

This repository is configured with a restricted GitHub Codespace environment for secure development.

### 🔒 Enhanced Security & Restrictions

**What you CAN do:**
- ✅ Edit source code files and documentation
- ✅ Run development servers (`npm run dev`)
- ✅ Test features locally on forwarded ports
- ✅ Create branches and commits
- ✅ Submit pull requests for code review
- ✅ Use approved git commands (add, commit, push, pull, status)
- ✅ Install npm packages for development
- ✅ View security status with `pathlink-security status`

**What you CANNOT do:**
- ❌ Download or copy source code files
- ❌ Use file transfer tools (wget, curl, scp, rsync, ftp, sftp)
- ❌ Create archives (tar, zip, gzip) of project files
- ❌ Clone additional repositories (`git clone` blocked)
- ❌ Access sensitive configuration files (.env, .key, .pem)
- ❌ Use network tools (nc, netcat, telnet)
- ❌ Bypass security restrictions (all attempts logged)

**Security Monitoring:**
- 🔍 All terminal commands are monitored
- 📝 Security violations are logged with timestamps
- 🚨 Repeated violations may result in access revocation
- 📋 Use `pathlink-security log` to view security events

### 🛠️ Development Workflow

1. **Start the Codespace**
   - Click "Code" → "Codespaces" → "Create codespace on main"
   - Wait for environment setup to complete

2. **Set up Environment Variables**
   ```bash
   # Copy the example environment file
   cp .env.example .env
   
   # Edit with development values (not production!)
   nano .env
   ```

3. **Install Dependencies**
   ```bash
   npm install
   ```

4. **Start Development Server**
   ```bash
   npm run dev
   ```

5. **Access the Application**
   - The app will be available on forwarded port 5000
   - Click the "Ports" tab to access the running application

### 📝 Making Changes

1. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Your Changes**
   - Edit files using the VS Code interface
   - Test your changes locally

3. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "Description of your changes"
   ```

4. **Push and Create PR**
   ```bash
   git push origin feature/your-feature-name
   ```
   - Go to GitHub and create a pull request

### 🔧 Available Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run type-check   # Check TypeScript types

# Database
npm run db:migrate   # Run database migrations
npm run db:studio    # Open database studio (if configured)

# Testing
npm test            # Run tests
npm run test:watch  # Run tests in watch mode

# Security Commands
pathlink-security status    # Show security status and restrictions
pathlink-security log       # View recent security events
pathlink-security help      # Show security command help
```

### 🚫 Restricted Operations

If you try to perform restricted operations, you'll see:
```
Download and copy operations are restricted in this environment.
You can only edit code and submit pull requests.
```

### 📞 Support

If you need help or have questions:
- Create an issue in the repository
- Contact the project maintainer
- Check the main README.md for documentation

### 🔐 Environment Variables

For development, you'll need these environment variables in your `.env` file:

```env
# Database (use development/staging, never production)
DATABASE_URL=your_development_database_url

# Session Secret (development only)
SESSION_SECRET=development_session_secret

# OpenAI API (optional for testing)
OPENAI_API_KEY=your_development_openai_key

# Email (optional for testing)
SENDGRID_API_KEY=your_development_sendgrid_key
```

**⚠️ Important**: Never use production credentials in Codespace!

---

Happy coding! 🎉
