const express = require('express');
const serverless = require('serverless-http');
const path = require('path');
const dotenv = require('dotenv');
const session = require('express-session');
const passport = require('passport');
const MemoryStore = require('memorystore');
const multer = require('multer');
const fs = require('fs');
const crypto = require('crypto');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const db = require('../db-connection');

// Load environment variables
dotenv.config();

// Test database connection
db.testConnection()
  .then(connected => {
    if (connected) {
      console.log('Connected to Neon database successfully');
    } else {
      console.error('Failed to connect to Neon database');
    }
  })
  .catch(error => {
    console.error('Error testing database connection:', error);
  });

// Create Express app
const app = express();

// Configure session store
const MemoryStoreSession = MemoryStore(session);
const sessionStore = new MemoryStoreSession({
  checkPeriod: 86400000 // prune expired entries every 24h
});

// Configure CORS
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl requests)
    if (!origin) return callback(null, true);

    // List of allowed origins
    const allowedOrigins = [
      'http://localhost:5000',
      'http://localhost:8888',
      'https://pathlink.netlify.app',
      'https://pathlink.com'
    ];

    // Allow any netlify.app subdomain
    if (origin && origin.includes('.netlify.app')) {
      return callback(null, true);
    }

    if (allowedOrigins.indexOf(origin) !== -1 || !origin) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(null, true); // Allow all origins in development
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Cookie'],
  exposedHeaders: ['X-PathLink-Auth', 'Set-Cookie']
};

app.use(cors(corsOptions));

// Configure middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Configure session
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  store: sessionStore,
  name: 'pathlink.sid', // Custom name for the session cookie
  cookie: {
    secure: process.env.NODE_ENV === 'production', // Use secure cookies in production
    httpOnly: true,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Required for cross-site cookies in production
    path: '/', // Ensure cookie is available on all paths
    domain: process.env.NODE_ENV === 'production' ? '.netlify.app' : undefined // Allow subdomain access
  }
}));

// Initialize Passport
app.use(passport.initialize());
app.use(passport.session());

// Simple in-memory user storage for demo purposes
const users = {
  '<EMAIL>': {
    id: '1',
    email: '<EMAIL>',
    password: 'admin123', // In production, this would be hashed
    name: 'Admin User',
    role: 'admin',
    profilePicture: 'https://ui-avatars.com/api/?name=Admin+User&background=random',
    position: 'System Administrator',
    location: 'New York, NY',
    bio: 'Experienced system administrator with a passion for technology.',
    company: 'PathLink',
    yearsOfExperience: '10',
    skills: 'System Administration, Network Security, Cloud Infrastructure'
  },
  '<EMAIL>': {
    id: '2',
    email: '<EMAIL>',
    password: 'employer123',
    name: 'Employer User',
    role: 'employer',
    profilePicture: 'https://ui-avatars.com/api/?name=Employer+User&background=random',
    position: 'HR Manager',
    location: 'San Francisco, CA',
    bio: 'HR professional with experience in talent acquisition and management.',
    company: 'Tech Solutions Inc.',
    yearsOfExperience: '8',
    skills: 'Recruitment, Employee Relations, Talent Management'
  },
  '<EMAIL>': {
    id: '3',
    email: '<EMAIL>',
    password: 'worker123',
    name: 'Worker User',
    role: 'worker',
    profilePicture: 'https://ui-avatars.com/api/?name=Worker+User&background=random',
    position: 'Software Developer',
    location: 'Austin, TX',
    bio: 'Full-stack developer with a focus on web technologies.',
    company: 'Previous: Tech Innovations',
    yearsOfExperience: '5',
    skills: 'JavaScript, React, Node.js, Python'
  },
  '<EMAIL>': {
    id: '4',
    email: '<EMAIL>',
    password: '1234567',
    name: 'Peter Dimian',
    role: 'worker',
    profilePicture: 'https://ui-avatars.com/api/?name=Peter+Dimian&background=random',
    position: 'Software Engineer',
    location: 'Chicago, IL',
    bio: 'Passionate about building innovative solutions and learning new technologies.',
    company: 'Previous: Tech Startups',
    yearsOfExperience: '7',
    skills: 'JavaScript, React, Node.js, TypeScript, AWS'
  }
};

// Configure Passport
passport.use(new (require('passport-local').Strategy)({
  usernameField: 'email',
  passwordField: 'password'
}, async (email, password, done) => {
  try {
    console.log('Login attempt for email:', email);

    // For testing purposes, use the in-memory users if available
    if (users[email]) {
      const user = users[email];
      console.log('Found user in memory:', user.name);

      if (user.password !== password) {
        console.log('Password mismatch for user:', user.name);
        return done(null, false, { message: 'Incorrect password.' });
      }

      console.log('Successful login for user:', user.name);
      return done(null, user);
    }

    // If not in memory, try the database
    console.log('User not found in memory, trying database');
    const user = await db.getUserByEmail(email);

    if (!user) {
      console.log('User not found in database either');
      return done(null, false, { message: 'Incorrect email.' });
    }

    console.log('Found user in database:', user.name);

    // In a real application, you would hash the password and compare the hash
    // For now, we'll just compare the plain text password
    if (user.password !== password) {
      console.log('Password mismatch for database user:', user.name);
      return done(null, false, { message: 'Incorrect password.' });
    }

    console.log('Successful login for database user:', user.name);
    return done(null, user);
  } catch (error) {
    console.error('Error authenticating user:', error);
    return done(error);
  }
}));

passport.serializeUser((user, done) => {
  done(null, user.id);
});

passport.deserializeUser(async (id, done) => {
  try {
    console.log('Deserializing user with ID:', id);

    // First try to get user from in-memory storage
    const inMemoryUser = Object.values(users).find(user => user.id === id);
    if (inMemoryUser) {
      console.log('Found user in memory:', inMemoryUser.name);
      return done(null, inMemoryUser);
    }

    // If not found in memory, try to get from database
    try {
      const user = await db.getUserById(id);
      if (user) {
        console.log('Found user in database:', user.name);

        // Ensure profile picture is set
        if (!user.profilePicture) {
          user.profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random`;
        }

        return done(null, user);
      }
    } catch (dbError) {
      console.error('Database error when deserializing user:', dbError);
      // Continue to check if we can find the user elsewhere
    }

    console.log('User not found with ID:', id);
    return done(null, false);
  } catch (error) {
    console.error('Error deserializing user:', error);
    done(error);
  }
});

// JWT Helper functions
const JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-key';

const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    },
    JWT_SECRET,
    { expiresIn: '7d' }
  );
};

const verifyToken = (token) => {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {
    return null;
  }
};

// Enhanced Authentication middleware that supports both sessions and JWT
const isAuthenticated = (req, res, next) => {
  console.log('Auth check - Session ID:', req.sessionID);
  console.log('Auth check - Is authenticated:', req.isAuthenticated());

  // First try session-based authentication
  if (req.isAuthenticated()) {
    console.log('Session auth successful for:', req.user.email);
    return next();
  }

  // If session auth fails, try JWT authentication
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.startsWith('Bearer ')
    ? authHeader.substring(7)
    : req.cookies?.authToken;

  if (token) {
    const decoded = verifyToken(token);
    if (decoded) {
      console.log('JWT auth successful for:', decoded.email);
      req.user = decoded;
      return next();
    }
  }

  console.log('Authentication failed for request to:', req.path);
  res.status(401).json({
    message: 'Not authenticated',
    sessionID: req.sessionID,
    path: req.path,
    debug: {
      hasSession: !!req.session,
      sessionID: req.sessionID,
      isAuthenticated: req.isAuthenticated(),
      hasAuthHeader: !!authHeader,
      hasToken: !!token,
      user: req.user ? 'User exists' : 'No user'
    }
  });
};

// API routes
app.get('/api/test', (req, res) => {
  res.json({ message: 'API is working!' });
});

// Debug endpoint
app.get('/api/debug', (req, res) => {
  res.json({
    message: 'Debug info',
    sessionID: req.sessionID,
    isAuthenticated: req.isAuthenticated(),
    user: req.user ? {
      id: req.user.id,
      email: req.user.email,
      name: req.user.name,
      role: req.user.role
    } : null,
    session: {
      exists: !!req.session,
      id: req.sessionID,
      cookie: req.session ? req.session.cookie : null
    },
    headers: {
      cookie: req.headers.cookie,
      origin: req.headers.origin,
      userAgent: req.headers['user-agent']
    },
    environment: {
      nodeEnv: process.env.NODE_ENV,
      hasSessionSecret: !!process.env.SESSION_SECRET
    }
  });
});

// User authentication routes
app.post('/api/login', (req, res, next) => {
  console.log('Login attempt with email:', req.body.email);

  passport.authenticate('local', (err, user, info) => {
    if (err) {
      console.error('Login error:', err);
      return next(err);
    }
    if (!user) {
      console.log('Authentication failed:', info.message);
      return res.status(401).json({ message: info.message || 'Authentication failed' });
    }

    // Ensure profile picture exists
    if (!user.profilePicture) {
      user.profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random`;
    }

    req.login(user, (err) => {
      if (err) {
        console.error('Session login error:', err);
        return next(err);
      }

      // Include profile picture in the response
      const userData = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        profilePicture: user.profilePicture
      };

      // Log session info
      console.log('Login successful for:', user.name);
      console.log('Session ID:', req.sessionID);
      console.log('Session cookie:', req.session.cookie);

      // Generate JWT token
      const token = generateToken(userData);

      // Set JWT token as HTTP-only cookie
      res.cookie('authToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        path: '/'
      });

      // Set a custom header to help with debugging
      res.setHeader('X-PathLink-Auth', 'true');

      return res.json({
        ...userData,
        token: token // Also return token for client-side storage if needed
      });
    });
  })(req, res, next);
});

// Alias /auth/login to /login for compatibility with client
app.post('/api/auth/login', (req, res, next) => {
  console.log('Auth login attempt with email:', req.body.email);

  passport.authenticate('local', (err, user, info) => {
    if (err) {
      console.error('Auth login error:', err);
      return next(err);
    }
    if (!user) {
      console.log('Auth authentication failed:', info.message);
      return res.status(401).json({ message: info.message || 'Authentication failed' });
    }

    // Ensure profile picture exists
    if (!user.profilePicture) {
      user.profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&background=random`;
    }

    req.login(user, (err) => {
      if (err) {
        console.error('Auth session login error:', err);
        return next(err);
      }

      // Include profile picture in the response
      const userData = {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        profilePicture: user.profilePicture
      };

      // Log session info
      console.log('Auth login successful for:', user.name);
      console.log('Auth session ID:', req.sessionID);

      // Generate JWT token
      const token = generateToken(userData);

      // Set JWT token as HTTP-only cookie
      res.cookie('authToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        path: '/'
      });

      // Set a custom header to help with debugging
      res.setHeader('X-PathLink-Auth', 'true');

      return res.json({
        ...userData,
        token: token // Also return token for client-side storage if needed
      });
    });
  })(req, res, next);
});

app.post('/api/signup', (req, res) => {
  const { email, password, name, role } = req.body;

  if (!email || !password || !name || !role) {
    return res.status(400).json({ message: 'All fields are required' });
  }

  if (users[email]) {
    return res.status(400).json({ message: 'User already exists' });
  }

  const id = crypto.randomUUID();
  const profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`;

  users[email] = {
    id,
    email,
    password,
    name,
    role,
    profilePicture
  };

  req.login(users[email], (err) => {
    if (err) {
      return res.status(500).json({ message: 'Error logging in after signup' });
    }

    console.log('Signup successful for:', name);
    return res.json({
      user: {
        id,
        email,
        name,
        role,
        profilePicture
      }
    });
  });
});

// Alias /register to /signup for compatibility with client
app.post('/api/register', async (req, res) => {
  try {
    const { email, password, name, role } = req.body;

    if (!email || !password || !name || !role) {
      return res.status(400).json({ message: 'All fields are required' });
    }

    console.log('Registration attempt for:', email, name, role);

    // Check if user already exists in database
    try {
      const existingUserQuery = 'SELECT id FROM users WHERE email = $1';
      const existingUserResult = await db.pool.query(existingUserQuery, [email]);

      if (existingUserResult.rows && existingUserResult.rows.length > 0) {
        return res.status(400).json({ message: 'User already exists' });
      }
    } catch (dbError) {
      console.error('Database check error:', dbError);
      // Continue with registration even if check fails
    }

    // For employer role, validate domain
    if (role === 'employer') {
      const domain = email.split('@')[1];
      const approvedDomains = ['pathlink.com', 'pathlinl.com'];
      if (!domain || !approvedDomains.includes(domain)) {
        return res.status(400).json({
          message: `Employer email must use an approved company domain (e.g., @pathlink.com). Approved domains: ${approvedDomains.join(', ')}`
        });
      }
    }

    const id = crypto.randomUUID();
    const profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`;

    // Save user to database
    try {
      const insertQuery = `
        INSERT INTO users (id, email, password, name, role, profile_picture, is_verified, created_at, updated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING id, email, name, role, profile_picture, is_verified
      `;

      const isVerified = role === 'worker'; // Workers are auto-verified, employers need domain verification
      const insertResult = await db.pool.query(insertQuery, [
        id, email, password, name, role, profilePicture, isVerified
      ]);

      if (!insertResult.rows || insertResult.rows.length === 0) {
        throw new Error('Failed to create user in database');
      }

      const newUser = insertResult.rows[0];
      console.log('User saved to database:', newUser.email);

      // Create user object for session
      const userForSession = {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
        role: newUser.role,
        profilePicture: newUser.profile_picture,
        is_verified: newUser.is_verified
      };

      req.login(userForSession, (err) => {
        if (err) {
          console.error('Session login error after registration:', err);
          return res.status(500).json({ message: 'Error logging in after signup' });
        }

        // Generate JWT token
        const token = generateToken(userForSession);

        // Set JWT token as HTTP-only cookie
        res.cookie('authToken', token, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
          maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
          path: '/'
        });

        console.log('Registration and login successful for:', name);
        return res.status(201).json({
          ...userForSession,
          token: token
        });
      });

    } catch (dbError) {
      console.error('Database insert error:', dbError);

      // Fallback to in-memory storage if database fails
      console.log('Falling back to in-memory storage for user:', email);

      users[email] = {
        id,
        email,
        password,
        name,
        role,
        profilePicture
      };

      req.login(users[email], (err) => {
        if (err) {
          return res.status(500).json({ message: 'Error logging in after signup' });
        }

        console.log('Register successful (in-memory) for:', name);
        return res.status(201).json({
          id,
          email,
          name,
          role,
          profilePicture
        });
      });
    }

  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({ message: 'Registration failed' });
  }
});

// Alias /auth/register to /register for compatibility with client
app.post('/api/auth/register', (req, res) => {
  const { email, password, name, role } = req.body;

  if (!email || !password || !name || !role) {
    return res.status(400).json({ message: 'All fields are required' });
  }

  if (users[email]) {
    return res.status(400).json({ message: 'User already exists' });
  }

  const id = crypto.randomUUID();
  const profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`;

  users[email] = {
    id,
    email,
    password,
    name,
    role,
    profilePicture
  };

  req.login(users[email], (err) => {
    if (err) {
      return res.status(500).json({ message: 'Error logging in after signup' });
    }

    console.log('Auth register successful for:', name);
    return res.json({
      user: {
        id,
        email,
        name,
        role,
        profilePicture
      }
    });
  });
});

app.post('/api/logout', (req, res) => {
  console.log('Logout request received');

  if (req.isAuthenticated()) {
    console.log('Logging out user:', req.user.name);
    console.log('Session ID before logout:', req.sessionID);
  } else {
    console.log('No authenticated user to logout');
  }

  req.logout(function(err) {
    if (err) {
      console.error('Error during logout:', err);
      return res.status(500).json({ message: 'Error logging out' });
    }

    // Destroy the session completely
    req.session.destroy(function(err) {
      if (err) {
        console.error('Error destroying session:', err);
        return res.status(500).json({ message: 'Error destroying session' });
      }

      // Clear the cookie on the client
      res.clearCookie('pathlink.sid');
      console.log('Session destroyed and cookie cleared');

      res.json({
        message: 'Logged out successfully',
        success: true
      });
    });
  });
});

// Alias /auth/logout to /logout for compatibility with client
app.post('/api/auth/logout', (req, res) => {
  console.log('Auth logout request received');

  if (req.isAuthenticated()) {
    console.log('Auth logging out user:', req.user.name);
    console.log('Auth session ID before logout:', req.sessionID);
  } else {
    console.log('Auth no authenticated user to logout');
  }

  req.logout(function(err) {
    if (err) {
      console.error('Auth error during logout:', err);
      return res.status(500).json({ message: 'Error logging out' });
    }

    // Destroy the session completely
    req.session.destroy(function(err) {
      if (err) {
        console.error('Auth error destroying session:', err);
        return res.status(500).json({ message: 'Error destroying session' });
      }

      // Clear both session and JWT cookies
      res.clearCookie('pathlink.sid');
      res.clearCookie('authToken', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
        path: '/'
      });
      console.log('Auth session destroyed and cookies cleared');

      res.json({
        message: 'Logged out successfully',
        success: true
      });
    });
  });
});

app.get('/api/user', (req, res) => {
  console.log('User data request received');
  console.log('Session ID:', req.sessionID);
  console.log('Is authenticated:', req.isAuthenticated());

  let user = null;
  let authMethod = 'none';

  // Try session authentication first
  if (req.isAuthenticated()) {
    user = req.user;
    authMethod = 'session';
  } else {
    // Try JWT authentication
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.startsWith('Bearer ')
      ? authHeader.substring(7)
      : req.cookies?.authToken;

    if (token) {
      const decoded = verifyToken(token);
      if (decoded) {
        user = decoded;
        authMethod = 'jwt';
      }
    }
  }

  if (user) {
    console.log('User data request for:', user.name, user.email, user.role, 'via', authMethod);
    const { id, email, name, role } = user;

    // Ensure profile picture exists
    if (!user.profilePicture) {
      user.profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`;
    }

    // Include profile picture and other profile data
    const userData = {
      id,
      email,
      name,
      role,
      profilePicture: user.profilePicture
    };

    // Set a custom header to help with debugging
    res.setHeader('X-PathLink-Auth', 'true');
    res.setHeader('X-Auth-Method', authMethod);

    return res.json(userData);
  }

  console.log('User data request: Not authenticated');
  res.status(401).json({
    message: 'Not authenticated',
    authenticated: false,
    sessionID: req.sessionID || 'no-session'
  });
});

// Profile routes
app.get('/api/profile', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  try {
    const { id, email, name, role } = req.user;
    console.log('Profile request for user:', name, email);

    // Get profile data from database
    let profile;
    try {
      profile = await db.getUserProfile(id);
      console.log('Database profile data:', profile);
    } catch (dbError) {
      console.error('Error fetching profile from database:', dbError);
      // Continue with in-memory data
    }

    // Combine user data with profile data
    const userData = {
      id,
      email,
      name,
      role,
      position: req.user.position || "",
      location: req.user.location || "",
      bio: req.user.bio || "",
      company: req.user.company || "",
      yearsOfExperience: req.user.yearsOfExperience || "",
      skills: req.user.skills || "",
      profilePicture: req.user.profilePicture || `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`
    };

    // If profile exists in database, override with database data
    if (profile) {
      userData.position = profile.position || userData.position;
      userData.location = profile.location || userData.location;
      userData.bio = profile.bio || userData.bio;
      userData.company = profile.company || userData.company;
      userData.yearsOfExperience = profile.years_of_experience || userData.yearsOfExperience;
      userData.skills = profile.skills || userData.skills;
      userData.profilePicture = profile.profile_picture || userData.profilePicture;
    }

    console.log('Returning profile data:', userData);
    return res.json(userData);
  } catch (error) {
    console.error('Error getting profile:', error);
    return res.status(500).json({ message: 'Error getting profile' });
  }
});

app.put('/api/profile', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  try {
    const { id, email, name, role } = req.user;
    const { position, location, bio, company, yearsOfExperience, skills, profilePicture } = req.body;

    // Update user name if provided
    if (name && name !== req.user.name) {
      // In a real implementation, you would update the user's name in the database
      // For now, we'll just update it in the session
      req.user.name = name;
    }

    // Update profile data in database
    const profileData = {
      position,
      location,
      bio,
      company,
      yearsOfExperience,
      skills,
      profilePicture
    };

    const updatedProfile = await db.updateUserProfile(id, profileData);

    // Combine user data with updated profile data
    const userData = {
      id,
      email,
      name: req.user.name,
      role,
      position: updatedProfile?.position || "",
      location: updatedProfile?.location || "",
      bio: updatedProfile?.bio || "",
      company: updatedProfile?.company || "",
      yearsOfExperience: updatedProfile?.years_of_experience || "",
      skills: updatedProfile?.skills || "",
      profilePicture: updatedProfile?.profile_picture || ""
    };

    return res.json(userData);
  } catch (error) {
    console.error('Error updating profile:', error);
    return res.status(500).json({ message: 'Error updating profile' });
  }
});

// Configure multer for file uploads
const multerConfig = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Check if file is an image
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'), false);
    }
  }
});

// Profile picture upload
app.post('/api/profile/picture', isAuthenticated, multerConfig.single('profilePicture'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    // In a production environment, you would upload this to a cloud storage service
    // For now, we'll convert to base64 and store it (not recommended for production)
    const base64Image = `data:${req.file.mimetype};base64,${req.file.buffer.toString('base64')}`;

    // For demo purposes, we'll just return a placeholder URL
    // In production, upload to AWS S3, Cloudinary, or similar service
    const profilePicture = `https://ui-avatars.com/api/?name=${encodeURIComponent(req.user.name)}&background=random&size=200`;

    console.log('Profile picture upload for user:', req.user.email);
    console.log('File size:', req.file.size, 'bytes');
    console.log('File type:', req.file.mimetype);

    // Update the profile picture in the database
    try {
      await db.updateUserProfile(req.user.id, { profilePicture });
    } catch (dbError) {
      console.error('Database update error:', dbError);
      // Continue even if database update fails
    }

    return res.json({
      profilePicture,
      message: 'Profile picture uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading profile picture:', error);
    return res.status(500).json({
      message: 'Error uploading profile picture',
      error: error.message
    });
  }
});

// Profile picture delete
app.delete('/api/profile/picture', isAuthenticated, async (req, res) => {
  try {
    console.log('Profile picture delete for user:', req.user.email);

    // Remove the profile picture from the database
    try {
      await db.updateUserProfile(req.user.id, { profilePicture: null });
    } catch (dbError) {
      console.error('Database update error:', dbError);
      // Continue even if database update fails
    }

    return res.json({
      message: 'Profile picture removed successfully'
    });
  } catch (error) {
    console.error('Error removing profile picture:', error);
    return res.status(500).json({
      message: 'Error removing profile picture',
      error: error.message
    });
  }
});

// Jobs API
app.get('/api/jobs', async (req, res) => {
  try {
    // Get jobs from database
    const jobs = await db.getJobs();

    // If no jobs found, return empty array
    if (!jobs || jobs.length === 0) {
      return res.json([]);
    }

    // Format jobs for client
    const formattedJobs = jobs.map(job => ({
      id: job.id.toString(),
      title: job.title,
      company: job.company,
      location: job.location,
      description: job.description,
      requirements: job.requirements ? job.requirements.split(',').map(req => req.trim()) : [],
      salary: job.salary,
      postedDate: job.posted_date ? new Date(job.posted_date).toISOString().split('T')[0] : null,
      industry: job.industry
    }));

    res.json(formattedJobs);
  } catch (error) {
    console.error('Error getting jobs:', error);
    res.status(500).json({ message: 'Error getting jobs' });
  }
});

// Resumes API
app.get('/api/resumes', isAuthenticated, async (req, res) => {
  try {
    // Get resumes with worker information from database
    const resumesWithWorkers = await db.getResumesWithWorkers();

    // If no resumes found, return empty array
    if (!resumesWithWorkers || resumesWithWorkers.length === 0) {
      return res.json([]);
    }

    res.json(resumesWithWorkers);
  } catch (error) {
    console.error('Error getting resumes:', error);
    res.status(500).json({ message: 'Error getting resumes' });
  }
});

// Get resume for a specific worker
app.get('/api/resumes/worker/:workerId', isAuthenticated, async (req, res) => {
  try {
    const workerId = req.params.workerId;
    console.log(`Resume request for worker ID: ${workerId}`);

    // Check if this is the current user or if the user is an employer/admin
    if (req.user.id !== workerId && req.user.role !== 'employer' && req.user.role !== 'admin') {
      console.log(`User ${req.user.id} not authorized to view resume for worker ${workerId}`);
      return res.status(403).json({ message: 'Not authorized to view this resume' });
    }

    // Try to get resume from database
    let resume = null;
    try {
      const query = 'SELECT * FROM resumes WHERE user_id = $1 ORDER BY upload_date DESC LIMIT 1';
      const result = await db.pool.query(query, [workerId]);

      if (result.rows && result.rows.length > 0) {
        resume = result.rows[0];
        console.log(`Found resume in database for worker ${workerId}:`, resume.filename);
      }
    } catch (dbError) {
      console.error(`Database error fetching resume for worker ${workerId}:`, dbError);
      // Continue with fallback data
    }

    // If no resume found in database, create a sample one
    if (!resume) {
      console.log(`No resume found in database for worker ${workerId}, using sample data`);

      // Get user name for the filename
      let userName = 'user';
      if (req.user.id === workerId) {
        userName = req.user.name.replace(/\s+/g, '_').toLowerCase();
      } else {
        try {
          const userQuery = 'SELECT name FROM users WHERE id = $1';
          const userResult = await db.pool.query(userQuery, [workerId]);
          if (userResult.rows && userResult.rows.length > 0) {
            userName = userResult.rows[0].name.replace(/\s+/g, '_').toLowerCase();
          }
        } catch (error) {
          console.error(`Error getting user name for worker ${workerId}:`, error);
        }
      }

      // Create a sample resume
      const formattedResume = {
        id: '1',
        filename: `${userName}_resume.pdf`,
        file_size: 1024 * 1024, // 1MB
        file_type: 'pdf',
        upload_date: new Date().toISOString(),
        worker_id: workerId
      };

      console.log(`Returning sample resume for worker ${workerId}:`, formattedResume);
      return res.json(formattedResume);
    }

    // Format resume from database for client
    const formattedResume = {
      id: resume.id.toString(),
      filename: resume.filename,
      file_size: resume.file_size,
      file_type: resume.file_type,
      upload_date: resume.upload_date.toISOString(),
      worker_id: resume.user_id.toString()
    };

    console.log(`Returning database resume for worker ${workerId}:`, formattedResume);
    res.json(formattedResume);
  } catch (error) {
    console.error('Error getting resume:', error);
    res.status(500).json({ message: 'Error getting resume' });
  }
});

// View resume
app.get('/api/resumes/view/:resumeId', isAuthenticated, async (req, res) => {
  try {
    const resumeId = req.params.resumeId;

    // Get resume from database
    const query = 'SELECT * FROM resumes WHERE id = $1';
    const result = await db.pool.query(query, [resumeId]);

    // If no resume found, return 404
    if (!result.rows || result.rows.length === 0) {
      return res.status(404).json({ message: 'Resume not found' });
    }

    const resume = result.rows[0];

    // Check if user is authorized to view this resume
    if (req.user.id !== resume.user_id && req.user.role !== 'employer' && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to view this resume' });
    }

    // In a real implementation, this would fetch the resume file from storage
    // For now, we'll just return a sample PDF content
    res.setHeader('Content-Type', 'application/pdf');
    res.send('Sample PDF content');
  } catch (error) {
    console.error('Error viewing resume:', error);
    res.status(500).json({ message: 'Error viewing resume' });
  }
});

// Download resume
app.get('/api/resumes/download/:resumeId', isAuthenticated, async (req, res) => {
  try {
    const resumeId = req.params.resumeId;

    // Get resume from database
    const query = 'SELECT * FROM resumes WHERE id = $1';
    const result = await db.pool.query(query, [resumeId]);

    // If no resume found, return 404
    if (!result.rows || result.rows.length === 0) {
      return res.status(404).json({ message: 'Resume not found' });
    }

    const resume = result.rows[0];

    // Check if user is authorized to download this resume
    if (req.user.id !== resume.user_id && req.user.role !== 'employer' && req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Not authorized to download this resume' });
    }

    // In a real implementation, this would download the resume file from storage
    // For now, we'll just return a sample PDF content
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${resume.filename}"`);
    res.send('Sample PDF content');
  } catch (error) {
    console.error('Error downloading resume:', error);
    res.status(500).json({ message: 'Error downloading resume' });
  }
});

// Worker Job Recommendations API
app.get('/api/worker/job-recommendations', isAuthenticated, async (req, res) => {
  try {
    console.log('Fetching job recommendations for worker:', req.user.email);

    // Get real jobs from database
    const jobs = await db.getJobs();

    if (!jobs || jobs.length === 0) {
      return res.json([]);
    }

    // Get worker's applications to filter out already applied jobs
    const appliedJobsQuery = 'SELECT job_id FROM matches WHERE worker_id = $1';
    const appliedJobsResult = await db.pool.query(appliedJobsQuery, [req.user.id]);
    const appliedJobIds = appliedJobsResult.rows.map(row => row.job_id);

    // Filter out jobs the worker has already applied to
    const availableJobs = jobs.filter(job => !appliedJobIds.includes(job.id));

    // Format jobs for recommendations with match scores
    const jobRecommendations = availableJobs.map(job => ({
      id: job.id,
      title: job.title,
      company: job.company || 'Company Name',
      location: job.location,
      salary: job.salary ? `$${job.salary.toLocaleString()}` : null,
      matchScore: Math.floor(Math.random() * 20) + 80, // Random score between 80-100
      description: job.description,
      requirements: job.required_skills ? job.required_skills.split(',').map(skill => skill.trim()) : [],
      benefits: job.benefits ? job.benefits.split(',').map(benefit => benefit.trim()) : [],
      postedDate: job.created_at ? new Date(job.created_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      industry: job.industry,
      employment_type: job.employment_type,
      experience_level: job.experience_level,
      matchReasons: [
        "Your profile matches this position",
        "Skills align with job requirements",
        "Experience level is appropriate"
      ]
    }));

    // Sort by match score (highest first)
    const sortedRecommendations = jobRecommendations.sort((a, b) => b.matchScore - a.matchScore);

    console.log(`Returning ${sortedRecommendations.length} job recommendations`);
    res.json(sortedRecommendations);
  } catch (error) {
    console.error('Error getting job recommendations:', error);
    res.status(500).json({
      message: 'Failed to fetch job recommendations',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Create a match
app.post('/api/matches', async (req, res) => {
  if (!req.isAuthenticated()) {
    return res.status(401).json({ message: 'Not authenticated' });
  }

  try {
    const { job_id, resume_id } = req.body;

    if (!job_id) {
      return res.status(400).json({ message: 'Job ID is required' });
    }

    if (!resume_id) {
      // Try to find a resume for the current user
      const resumeQuery = 'SELECT id FROM resumes WHERE user_id = $1 ORDER BY upload_date DESC LIMIT 1';
      const resumeResult = await db.pool.query(resumeQuery, [req.user.id]);

      if (!resumeResult.rows || resumeResult.rows.length === 0) {
        return res.status(400).json({ message: 'Resume ID is required and no resume was found for the current user' });
      }

      req.body.resume_id = resumeResult.rows[0].id;
    }

    // Calculate match score (in a real application, this would be more sophisticated)
    const score = Math.floor(Math.random() * 30) + 70; // Random score between 70 and 100

    // Insert match into database
    const insertQuery = `
      INSERT INTO matches (job_id, resume_id, score, created_at)
      VALUES ($1, $2, $3, NOW())
      RETURNING id, created_at
    `;
    const insertResult = await db.pool.query(insertQuery, [job_id, req.body.resume_id, score]);

    if (!insertResult.rows || insertResult.rows.length === 0) {
      return res.status(500).json({ message: 'Failed to create match' });
    }

    const match = insertResult.rows[0];

    // Get job and worker information
    const jobQuery = 'SELECT title, company FROM jobs WHERE id = $1';
    const jobResult = await db.pool.query(jobQuery, [job_id]);

    const resumeQuery = `
      SELECT u.id as user_id, u.name, u.email
      FROM resumes r
      JOIN users u ON r.user_id = u.id
      WHERE r.id = $1
    `;
    const resumeResult = await db.pool.query(resumeQuery, [req.body.resume_id]);

    // Format match for client
    const formattedMatch = {
      id: match.id,
      job_id: parseInt(job_id),
      resume_id: parseInt(req.body.resume_id),
      score,
      created_at: match.created_at.toISOString(),
      job: {
        id: parseInt(job_id),
        title: jobResult.rows[0]?.title || 'Unknown Job',
        company: jobResult.rows[0]?.company || 'Unknown Company'
      },
      worker: {
        id: resumeResult.rows[0]?.user_id || req.user.id,
        name: resumeResult.rows[0]?.name || req.user.name,
        email: resumeResult.rows[0]?.email || req.user.email
      }
    };

    res.status(201).json(formattedMatch);
  } catch (error) {
    console.error('Error creating match:', error);
    res.status(500).json({ message: 'Error creating match' });
  }
});

// Matches API
app.get('/api/matches', isAuthenticated, async (req, res) => {
  try {
    console.log('Fetching matches for user:', req.user.email);

    // Fetch real matches from database
    let matches = [];

    if (req.user.role === 'worker') {
      // For workers, get their applications
      const matchQuery = 'SELECT * FROM matches WHERE worker_id = $1 ORDER BY match_date DESC';
      const matchResult = await db.pool.query(matchQuery, [req.user.id]);
      matches = matchResult.rows || [];
    } else if (req.user.role === 'employer') {
      // For employers, get applications to their jobs
      const jobQuery = 'SELECT id FROM jobs WHERE employer_id = $1';
      const jobResult = await db.pool.query(jobQuery, [req.user.id]);
      const jobIds = jobResult.rows.map(row => row.id);

      if (jobIds.length > 0) {
        const matchQuery = 'SELECT * FROM matches WHERE job_id = ANY($1) ORDER BY match_date DESC';
        const matchResult = await db.pool.query(matchQuery, [jobIds]);
        matches = matchResult.rows || [];
      }
    }

    console.log(`Returning ${matches.length} matches`);
    res.json(matches);
  } catch (error) {
    console.error('Error getting matches:', error);
    res.status(500).json({
      message: 'Failed to fetch matches',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Surplus employees API
app.get('/api/surplus-employees', isAuthenticated, async (req, res) => {
  try {
    console.log('Fetching surplus employees for user:', req.user.email);

    // In a real implementation, this would fetch surplus employees from the database
    // For now, we'll just return sample data
    const employees = [
      {
        id: '1',
        name: 'John Smith',
        role: 'Software Developer',
        industry: 'Technology',
        status: 'Available'
      },
      {
        id: '2',
        name: 'Jane Doe',
        role: 'Data Scientist',
        industry: 'Data Science',
        status: 'Placed'
      }
    ];

    console.log(`Returning ${employees.length} surplus employees`);
    res.json(employees);
  } catch (error) {
    console.error('Error getting surplus employees:', error);
    res.status(500).json({
      message: 'Failed to fetch surplus employees',
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Employer-specific surplus employees
app.get('/api/surplus-employees/employer', isAuthenticated, async (req, res) => {
  try {
    if (req.user.role !== 'employer') {
      return res.status(403).json({ message: 'Only employers can access this endpoint' });
    }

    // In a real implementation, this would fetch surplus employees for the specific employer
    // For now, we'll just return sample data
    const employees = [
      {
        id: '1',
        name: 'John Smith',
        role: 'Software Developer',
        industry: 'Technology',
        status: 'Available',
        employer_id: req.user.id
      }
    ];

    res.json(employees);
  } catch (error) {
    console.error('Error getting employer surplus employees:', error);
    res.status(500).json({ message: 'Failed to fetch employer surplus employees' });
  }
});

// Enhanced matching algorithm functions
function extractSkillsFromJob(job) {
  const skills = [];
  const text = `${job.title || ''} ${job.description || ''} ${job.required_skills || ''}`.toLowerCase();

  // Comprehensive skill patterns
  const skillPatterns = {
    programming: [
      'javascript', 'js', 'typescript', 'ts', 'python', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash'
    ],
    frontend: [
      'react', 'vue', 'angular', 'html', 'css', 'sass', 'scss', 'less', 'bootstrap', 'tailwind', 'jquery', 'webpack', 'vite', 'parcel', 'gulp', 'grunt'
    ],
    backend: [
      'node.js', 'nodejs', 'express', 'django', 'flask', 'spring', 'laravel', 'rails', 'asp.net', 'fastapi', 'nestjs', 'koa', 'hapi'
    ],
    databases: [
      'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'oracle', 'sql server', 'cassandra', 'dynamodb', 'elasticsearch', 'neo4j'
    ],
    cloud: [
      'aws', 'azure', 'gcp', 'google cloud', 'docker', 'kubernetes', 'terraform', 'ansible', 'jenkins', 'gitlab', 'github actions', 'circleci'
    ],
    tools: [
      'git', 'jira', 'confluence', 'slack', 'figma', 'sketch', 'photoshop', 'illustrator', 'postman', 'insomnia', 'vs code', 'intellij'
    ]
  };

  // Extract skills from all categories
  for (const category of Object.values(skillPatterns)) {
    for (const skill of category) {
      if (text.includes(skill)) {
        skills.push(skill);
      }
    }
  }

  // Also extract from required_skills field if it exists
  if (job.required_skills) {
    const requiredSkills = job.required_skills.split(',').map(s => s.trim().toLowerCase());
    skills.push(...requiredSkills);
  }

  return [...new Set(skills)]; // Remove duplicates
}

function extractSkillsFromResume(resumeText) {
  if (!resumeText) return [];

  const skills = [];
  const text = resumeText.toLowerCase();

  // Same skill patterns as job extraction
  const skillPatterns = {
    programming: [
      'javascript', 'js', 'typescript', 'ts', 'python', 'java', 'c++', 'c#', 'php', 'ruby', 'go', 'rust', 'swift', 'kotlin', 'scala', 'r', 'matlab', 'perl', 'shell', 'bash'
    ],
    frontend: [
      'react', 'vue', 'angular', 'html', 'css', 'sass', 'scss', 'less', 'bootstrap', 'tailwind', 'jquery', 'webpack', 'vite', 'parcel', 'gulp', 'grunt'
    ],
    backend: [
      'node.js', 'nodejs', 'express', 'django', 'flask', 'spring', 'laravel', 'rails', 'asp.net', 'fastapi', 'nestjs', 'koa', 'hapi'
    ],
    databases: [
      'mysql', 'postgresql', 'mongodb', 'redis', 'sqlite', 'oracle', 'sql server', 'cassandra', 'dynamodb', 'elasticsearch', 'neo4j'
    ],
    cloud: [
      'aws', 'azure', 'gcp', 'google cloud', 'docker', 'kubernetes', 'terraform', 'ansible', 'jenkins', 'gitlab', 'github actions', 'circleci'
    ],
    tools: [
      'git', 'jira', 'confluence', 'slack', 'figma', 'sketch', 'photoshop', 'illustrator', 'postman', 'insomnia', 'vs code', 'intellij'
    ]
  };

  // Extract skills from all categories
  for (const category of Object.values(skillPatterns)) {
    for (const skill of category) {
      if (text.includes(skill)) {
        skills.push(skill);
      }
    }
  }

  return [...new Set(skills)]; // Remove duplicates
}

function calculateSkillSimilarity(skill1, skill2) {
  const s1 = skill1.toLowerCase().trim();
  const s2 = skill2.toLowerCase().trim();

  // Exact match
  if (s1 === s2) return 1.0;

  // One contains the other
  if (s1.includes(s2) || s2.includes(s1)) {
    const longer = s1.length > s2.length ? s1 : s2;
    const shorter = s1.length <= s2.length ? s1 : s2;
    return shorter.length / longer.length;
  }

  // Check synonyms
  const synonyms = {
    'js': ['javascript'],
    'javascript': ['js'],
    'ts': ['typescript'],
    'typescript': ['ts'],
    'nodejs': ['node.js', 'node'],
    'node.js': ['nodejs', 'node'],
    'react.js': ['react', 'reactjs'],
    'vue.js': ['vue', 'vuejs'],
    'angular.js': ['angular', 'angularjs']
  };

  if (synonyms[s1] && synonyms[s1].includes(s2)) return 0.9;
  if (synonyms[s2] && synonyms[s2].includes(s1)) return 0.9;

  // Levenshtein distance for typos
  const distance = levenshteinDistance(s1, s2);
  const maxLength = Math.max(s1.length, s2.length);
  const similarity = 1 - (distance / maxLength);

  return similarity > 0.7 ? similarity : 0;
}

function levenshteinDistance(str1, str2) {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }

  return matrix[str2.length][str1.length];
}

function calculateEnhancedSkillsMatch(jobSkills, resumeSkills) {
  const matched = [];
  const missing = [];
  const partial = [];

  for (const jobSkill of jobSkills) {
    let bestMatch = null;
    let bestSimilarity = 0;

    for (const resumeSkill of resumeSkills) {
      const similarity = calculateSkillSimilarity(jobSkill, resumeSkill);
      if (similarity > bestSimilarity) {
        bestMatch = resumeSkill;
        bestSimilarity = similarity;
      }
    }

    if (bestSimilarity >= 0.8) {
      matched.push(jobSkill);
    } else if (bestSimilarity >= 0.5) {
      partial.push({ job: jobSkill, resume: bestMatch, confidence: bestSimilarity });
    } else {
      missing.push(jobSkill);
    }
  }

  // Calculate score with partial matches considered
  const exactScore = matched.length;
  const partialScore = partial.reduce((sum, p) => sum + p.confidence, 0);
  const totalPossible = jobSkills.length;

  const score = totalPossible > 0
    ? Math.round(((exactScore + partialScore) / totalPossible) * 100)
    : 75; // Default score if no skills specified

  return { score, matched, missing, partial };
}

function extractExperienceFromJob(job) {
  const text = `${job.description || ''} ${job.required_skills || ''}`.toLowerCase();
  const patterns = [
    /(\d+)\+?\s*years?\s*(?:of\s*)?experience/,
    /(\d+)\+?\s*years?\s*(?:of\s*)?(?:relevant\s*)?(?:work\s*)?experience/,
    /minimum\s*(?:of\s*)?(\d+)\+?\s*years?/,
    /at\s*least\s*(\d+)\+?\s*years?/
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      return parseInt(match[1]);
    }
  }

  return 0; // No specific experience requirement found
}

function extractExperienceFromResume(resumeText) {
  if (!resumeText) return 0;

  const text = resumeText.toLowerCase();

  // Look for experience statements
  const patterns = [
    /(\d+)\+?\s*years?\s*(?:of\s*)?experience/,
    /(\d+)\+?\s*years?\s*(?:of\s*)?(?:professional\s*)?(?:work\s*)?experience/,
    /over\s*(\d+)\+?\s*years?/,
    /more\s*than\s*(\d+)\+?\s*years?/
  ];

  let maxYears = 0;

  for (const pattern of patterns) {
    const matches = text.match(new RegExp(pattern.source, 'g'));
    if (matches) {
      for (const match of matches) {
        const yearMatch = match.match(/(\d+)/);
        if (yearMatch) {
          maxYears = Math.max(maxYears, parseInt(yearMatch[1]));
        }
      }
    }
  }

  // If no explicit experience statement, try to count job positions
  if (maxYears === 0) {
    const jobSections = text.split(/(?:experience|employment|work\s*history)/i);
    if (jobSections.length > 1) {
      // Estimate based on number of positions (rough estimate)
      const positions = (text.match(/\b(?:software\s*engineer|developer|programmer|analyst|manager|specialist|coordinator|assistant)\b/gi) || []).length;
      maxYears = Math.min(positions * 2, 15); // Estimate 2 years per position, max 15
    }
  }

  return maxYears;
}

// Extract contact information from resume
function extractContactInfo(resumeText) {
  if (!resumeText) return {};

  const contact = {};

  // Email extraction
  const emailMatch = resumeText.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/);
  if (emailMatch) contact.email = emailMatch[0];

  // Phone extraction
  const phoneMatch = resumeText.match(/(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/);
  if (phoneMatch) contact.phone = phoneMatch[0];

  // LinkedIn extraction
  const linkedinMatch = resumeText.match(/linkedin\.com\/in\/[a-zA-Z0-9-]+/);
  if (linkedinMatch) contact.linkedin = `https://${linkedinMatch[0]}`;

  // GitHub extraction
  const githubMatch = resumeText.match(/github\.com\/[a-zA-Z0-9-]+/);
  if (githubMatch) contact.github = `https://${githubMatch[0]}`;

  return contact;
}

// Comprehensive matches API
app.get('/api/matches/comprehensive', async (req, res) => {
  try {
    // Get jobs
    const jobsQuery = 'SELECT * FROM jobs';
    const jobsResult = await db.pool.query(jobsQuery);

    // Get resumes with worker information
    const resumesQuery = `
      SELECT r.*, u.id as worker_id, u.name as worker_name, u.email as worker_email
      FROM resumes r
      JOIN users u ON r.user_id = u.id
      WHERE u.role = 'worker'
    `;
    const resumesResult = await db.pool.query(resumesQuery);

    // If no jobs or resumes found, return empty array
    if (!jobsResult.rows || jobsResult.rows.length === 0 || !resumesResult.rows || resumesResult.rows.length === 0) {
      return res.json([]);
    }

    // Generate comprehensive matches
    const comprehensiveMatches = [];

    for (const job of jobsResult.rows) {
      for (const resume of resumesResult.rows) {
        // Extract skills from job and resume using enhanced algorithm
        const jobSkills = extractSkillsFromJob(job);
        const resumeSkills = extractSkillsFromResume(resume.extracted_text || '');

        // Calculate enhanced skills match
        const skillsMatch = calculateEnhancedSkillsMatch(jobSkills, resumeSkills);

        // Calculate experience match
        const requiredYears = extractExperienceFromJob(job);
        const candidateYears = extractExperienceFromResume(resume.extracted_text || '');

        let experienceScore = 0;
        let experienceDescription = "";

        if (requiredYears === 0) {
          experienceScore = 80;
          experienceDescription = `No specific experience requirement. Candidate has ${candidateYears} years of experience.`;
        } else if (candidateYears >= requiredYears) {
          const exceededBy = candidateYears - requiredYears;
          experienceScore = Math.min(100, 80 + exceededBy * 5);
          experienceDescription = `Exceeds experience requirements by ${exceededBy} years. Required: ${requiredYears}, Candidate: ${candidateYears}.`;
        } else {
          const shortBy = requiredYears - candidateYears;
          experienceScore = Math.max(30, 70 - shortBy * 10);
          experienceDescription = `Below experience requirements by ${shortBy} years. Required: ${requiredYears}, Candidate: ${candidateYears}.`;
        }

        // Calculate education match
        const resumeText = resume.extracted_text || '';
        let educationScore = 80;
        let educationDescription = "Education requirements assessment";

        if (resumeText.toLowerCase().includes('phd') || resumeText.toLowerCase().includes('doctorate')) {
          educationScore = 100;
          educationDescription = "PhD/Doctorate level education";
        } else if (resumeText.toLowerCase().includes('master') || resumeText.toLowerCase().includes('ms') || resumeText.toLowerCase().includes('ma')) {
          educationScore = 95;
          educationDescription = "Master's degree level education";
        } else if (resumeText.toLowerCase().includes('bachelor') || resumeText.toLowerCase().includes('bs') || resumeText.toLowerCase().includes('ba')) {
          educationScore = 85;
          educationDescription = "Bachelor's degree level education";
        }

        // Calculate industry match
        let industryScore = 70;
        let industryDescription = "Industry experience assessment";

        if (job.industry && resumeText.toLowerCase().includes(job.industry.toLowerCase())) {
          industryScore = 95;
          industryDescription = `Strong industry match. Candidate has experience in ${job.industry}.`;
        } else if (job.industry) {
          // Check for related industry terms
          const industryRelations = {
            'technology': ['software', 'tech', 'it', 'computer', 'digital', 'startup'],
            'finance': ['banking', 'financial', 'investment', 'fintech', 'accounting'],
            'healthcare': ['medical', 'health', 'hospital', 'clinical', 'pharmaceutical']
          };

          const relatedTerms = industryRelations[job.industry.toLowerCase()] || [];
          const hasRelatedExperience = relatedTerms.some(term => resumeText.toLowerCase().includes(term));

          if (hasRelatedExperience) {
            industryScore = 75;
            industryDescription = `Related industry experience. Candidate has transferable skills for ${job.industry}.`;
          } else {
            industryScore = 50;
            industryDescription = `Limited industry match. Candidate may need to adapt to ${job.industry} industry.`;
          }
        }

        // Calculate overall score with weighted average
        const overallScore = Math.round(
          (skillsMatch.score * 0.4) +
          (experienceScore * 0.3) +
          (educationScore * 0.15) +
          (industryScore * 0.15)
        );

        // Identify strengths and development areas
        const strengths = [];
        if (skillsMatch.matched.length > 0) {
          strengths.push(`Strong technical skills in ${skillsMatch.matched.slice(0, 3).join(', ')}`);
        }
        if (candidateYears > requiredYears) {
          strengths.push('Exceeds experience requirements');
        }
        if (educationScore >= 90) {
          strengths.push('Advanced education credentials');
        }

        const developmentAreas = [];
        if (skillsMatch.missing.length > 0) {
          developmentAreas.push(`Develop skills in: ${skillsMatch.missing.slice(0, 3).join(', ')}`);
        }
        if (experienceScore < 70) {
          developmentAreas.push('Gain more relevant experience');
        }

        comprehensiveMatches.push({
          job: {
            id: job.id,
            title: job.title,
            company: job.company,
            location: job.location,
            description: job.description,
            industry: job.industry,
            required_skills: job.required_skills,
            salary_range: job.salary_range
          },
          worker: {
            id: resume.worker_id,
            name: resume.worker_name,
            email: resume.worker_email
          },
          resume: {
            id: resume.id,
            file_name: resume.file_name,
            upload_date: resume.upload_date,
            extracted_text: resume.extracted_text
          },
          score: {
            overall: overallScore,
            skills: skillsMatch.score,
            experience: experienceScore,
            education: educationScore,
            industry: industryScore,
            details: {
              matchedSkills: skillsMatch.matched,
              missingSkills: skillsMatch.missing,
              partialMatches: skillsMatch.partial || [],
              experienceMatch: experienceDescription,
              educationMatch: educationDescription,
              industryMatch: industryDescription,
              additionalStrengths: strengths.length > 0 ? strengths : ["Strong technical foundation"],
              developmentAreas: developmentAreas.length > 0 ? developmentAreas : ["Continue professional development"]
            }
          },
          contactInfo: extractContactInfo(resume.extracted_text || '')
        });
      }
    }

    // Sort by overall score (highest first)
    comprehensiveMatches.sort((a, b) => b.score.overall - a.score.overall);

    console.log(`Generated ${comprehensiveMatches.length} comprehensive matches`);
    res.json(comprehensiveMatches);
  } catch (error) {
    console.error('Error generating comprehensive matches:', error);
    res.status(500).json({ message: 'Error generating comprehensive matches' });
  }
});

// Admin API endpoints
app.get('/api/admin/stats/platform', isAuthenticated, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    // Return sample platform statistics
    const stats = {
      total_users: 25,
      employers: 8,
      workers: 15,
      admin_users: 2,
      total_jobs: 12,
      total_applications: 45,
      total_resumes: 18,
      active_users_last_30_days: 20,
      new_users_last_30_days: 5,
      verified_domains: 3
    };

    console.log('Returning platform stats for admin:', req.user.email);
    res.json(stats);
  } catch (error) {
    console.error('Error getting platform stats:', error);
    res.status(500).json({
      message: 'Failed to fetch platform statistics',
      error: error.message
    });
  }
});

app.get('/api/admin/stats/users', isAuthenticated, async (req, res) => {
  try {
    if (req.user.role !== 'admin') {
      return res.status(403).json({ message: 'Admin access required' });
    }

    // Return sample user statistics
    const stats = {
      total: 25,
      verified: 20,
      unverified: 5,
      active_last_30_days: 18,
      inactive: 7,
      employers: 8,
      workers: 15,
      admins: 2
    };

    console.log('Returning user stats for admin:', req.user.email);
    res.json(stats);
  } catch (error) {
    console.error('Error getting user stats:', error);
    res.status(500).json({
      message: 'Failed to fetch user statistics',
      error: error.message
    });
  }
});

// Create serverless handler
const handler = serverless(app);

// Export the serverless handler
module.exports = {
  handler: async (event, context) => {
    // Log request details
    console.log('Request path:', event.path);
    console.log('Request method:', event.httpMethod);

    // Log cookies for debugging
    if (event.headers && event.headers.cookie) {
      console.log('Request cookies:', event.headers.cookie);
    } else {
      console.log('No cookies in request');
    }

    // Log origin for CORS debugging
    if (event.headers && event.headers.origin) {
      console.log('Request origin:', event.headers.origin);
    }

    // Add custom headers for all responses
    const response = await handler(event, context);

    // Ensure CORS headers are set
    if (!response.headers) {
      response.headers = {};
    }

    // Add debug header
    response.headers['X-PathLink-Debug'] = 'true';

    return response;
  }
};
