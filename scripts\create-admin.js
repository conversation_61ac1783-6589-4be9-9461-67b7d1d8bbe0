import pg from 'pg';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

async function createAdminUser() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Hash the password
    const saltRounds = 10;
    const password = 'password';
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create the admin user
    const insertQuery = `
      INSERT INTO users (email, password, name, role, is_verified, original_role)
      VALUES ($1, $2, $3, $4, $5, $6)
      ON CONFLICT (email) DO UPDATE SET
        password = $2,
        name = $3,
        role = $4,
        is_verified = $5,
        original_role = $6
      RETURNING id, email, name, role;
    `;

    const values = [
      '<EMAIL>',
      hashedPassword,
      'System Administrator',
      'admin',
      true,
      'admin'
    ];

    const result = await client.query(insertQuery, values);
    console.log('Admin user created/updated successfully:', result.rows[0]);

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

createAdminUser();
