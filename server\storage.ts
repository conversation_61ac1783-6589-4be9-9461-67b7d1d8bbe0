import {
  users, type User, type InsertUser,
  jobs, type Job, type InsertJob,
  surplusEmployees, type SurplusEmployee, type InsertSurplusEmployee,
  matches, type Match, type InsertMatch,
  resumes, type Resume, type InsertResume,
  skillingSuggestions, type SkillingSuggestions, type InsertSkillingSuggestions,
  careerTransitionPlans, type CareerTransitionPlan, type InsertCareerTransitionPlan,
  workerProfiles, type WorkerProfile, type InsertWorkerProfile,
  reflectionJournals, type ReflectionJournal, type InsertReflectionJournal,
  workerValues, type WorkerValues, type InsertWorkerValues,
  mentorPreferences, type MentorPreferences, type InsertMentorPreferences,
  approvedDomains, type ApprovedDomain, type InsertApprovedDomain,
  systemLogs, type SystemLog, type InsertSystemLog,
  userProfiles, type UserProfile, type InsertUserProfile
} from "@shared/schema";
import { db } from "./db";
import { eq, and, desc } from "drizzle-orm";
import session from "express-session";
import { pool } from "./db";
import connectPg from "connect-pg-simple";
import memorystore from "memorystore";

const MemoryStore = memorystore(session);

// Use PostgreSQL for session storage
const PostgresSessionStore = connectPg(session);

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUsers(): Promise<User[]>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUser(id: number, userData: Partial<User>): Promise<User | undefined>;
  deleteUser(id: number): Promise<boolean>;

  // Approved Domains methods
  getApprovedDomains(): Promise<ApprovedDomain[]>;
  getApprovedDomain(id: number): Promise<ApprovedDomain | undefined>;
  getApprovedDomainByName(domain: string): Promise<ApprovedDomain | undefined>;
  createApprovedDomain(domain: InsertApprovedDomain): Promise<ApprovedDomain>;
  updateApprovedDomain(id: number, domain: Partial<ApprovedDomain>): Promise<ApprovedDomain | undefined>;
  deleteApprovedDomain(id: number): Promise<boolean>;

  // Admin functionality
  getSystemLogs(): Promise<SystemLog[]>;
  addSystemLog(log: InsertSystemLog): Promise<SystemLog>;

  // Job methods
  getJob(id: number): Promise<Job | undefined>;
  getJobs(): Promise<Job[]>;
  getJobsByEmployer(employerId: number): Promise<Job[]>;
  createJob(job: InsertJob): Promise<Job>;

  // Surplus Employee methods
  getSurplusEmployee(id: number): Promise<SurplusEmployee | undefined>;
  getSurplusEmployees(): Promise<SurplusEmployee[]>;
  getSurplusEmployeesByEmployer(employerId: number): Promise<SurplusEmployee[]>;
  createSurplusEmployee(employee: InsertSurplusEmployee): Promise<SurplusEmployee>;

  // Match methods
  getMatch(id: number): Promise<Match | undefined>;
  getMatches(): Promise<Match[]>;
  getMatchesByWorkerId(workerId: number): Promise<Match[]>;
  getMatchesByJobId(jobId: number): Promise<Match[]>;
  getMatchByJobAndWorker(jobId: number, workerId: number): Promise<Match | undefined>;
  createMatch(match: InsertMatch): Promise<Match>;
  updateMatch(id: number, updates: Partial<Match>): Promise<Match | undefined>;
  deleteMatch(id: number): Promise<boolean>;
  deleteJob(id: number): Promise<boolean>;

  // Resume methods
  getResume(id: number): Promise<Resume | undefined>;
  getResumeByWorkerId(workerId: number): Promise<Resume | undefined>;
  getResumes(): Promise<Resume[]>; // Added for resume search
  createResume(resume: InsertResume): Promise<Resume>;
  updateResume(id: number, resume: Partial<InsertResume>): Promise<Resume | undefined>;
  deleteResume(id: number): Promise<boolean>;

  // 1. Growth Memory Engine - Skilling Suggestions History
  getSkillingHistory(workerId: number): Promise<SkillingSuggestions[]>;
  saveSkillingHistory(skillingSuggestion: InsertSkillingSuggestions): Promise<SkillingSuggestions>;

  // 2. Career Quantum Leap Generator
  getCareerTransitionPlans(workerId: number): Promise<CareerTransitionPlan[]>;
  saveCareerTransitionPlan(plan: InsertCareerTransitionPlan): Promise<CareerTransitionPlan>;

  // 3. Skill Context Analyzer
  getWorkerProfile(workerId: number): Promise<WorkerProfile | undefined>;
  createOrUpdateWorkerProfile(profile: InsertWorkerProfile): Promise<WorkerProfile>;

  // 4. GPT Reflection Journal
  getReflectionJournals(workerId: number): Promise<ReflectionJournal[]>;
  saveReflectionJournal(journal: InsertReflectionJournal): Promise<ReflectionJournal>;

  // 5. Workplace Soul Match
  getWorkerValues(workerId: number): Promise<WorkerValues | undefined>;
  saveWorkerValues(values: InsertWorkerValues): Promise<WorkerValues>;

  // 6. AI Mentor Constellations
  getMentorPreference(workerId: number): Promise<MentorPreferences | undefined>;
  saveMentorPreference(preference: InsertMentorPreferences): Promise<MentorPreferences>;

  // User Profile methods
  getUserProfile(userId: number): Promise<UserProfile | undefined>;
  updateUserProfile(userId: number, profileData: Partial<UserProfile>): Promise<UserProfile>;

  // Admin functionality
  getSystemLogs(): Promise<SystemLog[]>;
  addSystemLog(log: InsertSystemLog): Promise<SystemLog>;

  // Session store
  sessionStore: session.Store;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private jobs: Map<number, Job>;
  private surplusEmployees: Map<number, SurplusEmployee>;
  private matches: Map<number, Match>;
  private resumes: Map<number, Resume>;
  private skillingHistory: Map<number, SkillingSuggestions>;
  private careerPlans: Map<number, CareerTransitionPlan>;
  private workerProfiles: Map<number, WorkerProfile>;
  private reflectionJournals: Map<number, ReflectionJournal>;
  private workerValuesList: Map<number, WorkerValues>;
  private mentorPreferences: Map<number, MentorPreferences>;
  private approvedDomains: Map<number, ApprovedDomain>;
  private systemLogs: Map<number, SystemLog>;
  private userProfiles: Map<number, UserProfile>;

  sessionStore: session.Store;
  currentUserId: number;
  currentJobId: number;
  currentSurplusEmployeeId: number;
  currentMatchId: number;
  currentResumeId: number;
  currentSkillingHistoryId: number;
  currentCareerPlanId: number;
  currentWorkerProfileId: number;
  currentReflectionJournalId: number;
  currentWorkerValuesId: number;
  currentMentorPreferenceId: number;

  currentApprovedDomainId: number;
  currentSystemLogId: number;
  currentUserProfileId: number;

  constructor() {
    this.users = new Map();
    this.jobs = new Map();
    this.surplusEmployees = new Map();
    this.matches = new Map();
    this.resumes = new Map();
    this.skillingHistory = new Map();
    this.careerPlans = new Map();
    this.workerProfiles = new Map();
    this.reflectionJournals = new Map();
    this.workerValuesList = new Map();
    this.mentorPreferences = new Map();
    this.approvedDomains = new Map();
    this.systemLogs = new Map();
    this.userProfiles = new Map();

    this.sessionStore = new MemoryStore({
      checkPeriod: 86400000,
    });

    this.currentUserId = 1;
    this.currentJobId = 1;
    this.currentSurplusEmployeeId = 1;
    this.currentMatchId = 1;
    this.currentResumeId = 1;
    this.currentSkillingHistoryId = 1;
    this.currentCareerPlanId = 1;
    this.currentWorkerProfileId = 1;
    this.currentReflectionJournalId = 1;
    this.currentWorkerValuesId = 1;
    this.currentMentorPreferenceId = 1;
    this.currentApprovedDomainId = 1;
    this.currentSystemLogId = 1;
    this.currentUserProfileId = 1;

    // Initialize sample data
    this.initSampleData();
  }

  private initSampleData() {
    // Add sample employers
    const employer1 = this.createUser({
      name: "TechCorp Inc",
      email: "<EMAIL>",
      password: "$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu", // "password" hashed
      role: "employer"
    });

    const employer2 = this.createUser({
      name: "Finance Solutions",
      email: "<EMAIL>",
      password: "$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu", // "password" hashed
      role: "employer"
    });

    // Add sample workers
    const worker1 = this.createUser({
      name: "John Doe",
      email: "<EMAIL>",
      password: "$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu", // "password" hashed
      role: "worker"
    });

    const worker2 = this.createUser({
      name: "Jane Smith",
      email: "<EMAIL>",
      password: "$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu", // "password" hashed
      role: "worker"
    });

    // Add sample jobs
    const job1 = this.createJob({
      title: "Frontend Developer",
      description: "Building beautiful user interfaces with React",
      industry: "Technology",
      location: "Remote",
      employer_id: 1
    });

    const job2 = this.createJob({
      title: "Data Scientist",
      description: "Analyzing large datasets to drive business decisions",
      industry: "Finance",
      location: "New York",
      employer_id: 2
    });

    // Add sample surplus employees
    this.createSurplusEmployee({
      name: "Alex Johnson",
      previous_role: "Project Manager",
      industry: "Technology",
      employer_id: 1,
      status: "available"
    });

    this.createSurplusEmployee({
      name: "Sarah Williams",
      previous_role: "Marketing Specialist",
      industry: "Retail",
      employer_id: 2,
      status: "available"
    });

    // Add sample matches
    this.createMatch({
      worker_id: 3,
      job_id: 2,
      status: "pending"
    });

    this.createMatch({
      worker_id: 4,
      job_id: 1,
      status: "matched"
    });

    // Add approved domains
    this.createApprovedDomain({
      domain: "google.com",
      description: "Google Inc.",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "microsoft.com",
      description: "Microsoft Corporation",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "apple.com",
      description: "Apple Inc.",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "amazon.com",
      description: "Amazon",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "meta.com",
      description: "Meta (Facebook)",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "ibm.com",
      description: "IBM",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "intel.com",
      description: "Intel Corporation",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "cisco.com",
      description: "Cisco Systems",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "oracle.com",
      description: "Oracle Corporation",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "salesforce.com",
      description: "Salesforce",
      is_active: true
    });

    this.createApprovedDomain({
      domain: "pathlink.com",
      description: "PathLink",
      is_active: true
    });
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUsers(): Promise<User[]> {
    return Array.from(this.users.values());
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(user => user.email === email);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.getUserByEmail(username); // Using email as username
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const now = new Date();
    const user: User = {
      ...insertUser,
      id,
      original_role: insertUser.role || null,
      is_verified: false,
      verification_token: null,
      verification_expires: null,
      created_at: now
    };
    this.users.set(id, user);
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    const existingUser = this.users.get(id);
    if (!existingUser) {
      return undefined;
    }

    const updatedUser: User = { ...existingUser, ...userData };
    this.users.set(id, updatedUser);
    return updatedUser;
  }

  async deleteUser(id: number): Promise<boolean> {
    return this.users.delete(id);
  }

  // Job methods
  async getJob(id: number): Promise<Job | undefined> {
    return this.jobs.get(id);
  }

  async getJobs(): Promise<Job[]> {
    return Array.from(this.jobs.values());
  }

  async getJobsByEmployer(employerId: number): Promise<Job[]> {
    return Array.from(this.jobs.values()).filter(job => job.employer_id === employerId);
  }

  async createJob(insertJob: InsertJob): Promise<Job> {
    const id = this.currentJobId++;
    const job: Job = { ...insertJob, id };
    this.jobs.set(id, job);
    return job;
  }

  // Surplus Employee methods
  async getSurplusEmployee(id: number): Promise<SurplusEmployee | undefined> {
    return this.surplusEmployees.get(id);
  }

  async getSurplusEmployees(): Promise<SurplusEmployee[]> {
    return Array.from(this.surplusEmployees.values());
  }

  async getSurplusEmployeesByEmployer(employerId: number): Promise<SurplusEmployee[]> {
    return Array.from(this.surplusEmployees.values()).filter(
      employee => employee.employer_id === employerId
    );
  }

  async createSurplusEmployee(insertEmployee: InsertSurplusEmployee): Promise<SurplusEmployee> {
    const id = this.currentSurplusEmployeeId++;

    // Ensure all fields have default values
    const employee: SurplusEmployee = {
      ...insertEmployee,
      id,
      status: insertEmployee.status || 'available',
      notes: insertEmployee.notes || null,
      skills: insertEmployee.skills || null,
      years_experience: insertEmployee.years_experience || null,
      transfer_reason: insertEmployee.transfer_reason || null,
      potential_positions: insertEmployee.potential_positions || null,
      user_id: insertEmployee.user_id || null
    };

    console.log("Creating surplus employee in storage:", employee);
    this.surplusEmployees.set(id, employee);
    return employee;
  }

  async updateSurplusEmployee(id: number, updates: Partial<Omit<SurplusEmployee, "id" | "employer_id">>): Promise<SurplusEmployee | null> {
    const employee = await this.getSurplusEmployee(id);
    if (!employee) return null;

    const updatedEmployee = { ...employee, ...updates };
    this.surplusEmployees.set(id, updatedEmployee);
    return updatedEmployee;
  }

  async deleteSurplusEmployee(id: number): Promise<boolean> {
    const exists = this.surplusEmployees.has(id);
    if (!exists) return false;

    this.surplusEmployees.delete(id);
    return true;
  }

  // Match methods
  async getMatch(id: number): Promise<Match | undefined> {
    return this.matches.get(id);
  }

  async getMatches(): Promise<Match[]> {
    return Array.from(this.matches.values());
  }

  async getMatchesByWorkerId(workerId: number): Promise<Match[]> {
    return Array.from(this.matches.values()).filter(match => match.worker_id === workerId);
  }

  async getMatchesByJobId(jobId: number): Promise<Match[]> {
    return Array.from(this.matches.values()).filter(match => match.job_id === jobId);
  }

  async getMatchByJobAndWorker(jobId: number, workerId: number): Promise<Match | undefined> {
    return Array.from(this.matches.values()).find(
      match => match.job_id === jobId && match.worker_id === workerId
    );
  }

  async deleteJob(id: number): Promise<boolean> {
    return this.jobs.delete(id);
  }

  async createMatch(insertMatch: InsertMatch): Promise<Match> {
    const id = this.currentMatchId++;
    // Ensure required fields have defaults if not provided
    const match: Match = {
      ...insertMatch,
      id,
      status: insertMatch.status || 'pending',
      match_score: insertMatch.match_score ?? null,
      match_date: insertMatch.match_date ?? new Date(),
      interview_date: insertMatch.interview_date || null
    };
    this.matches.set(id, match);
    return match;
  }

  async updateMatch(id: number, updates: Partial<Match>): Promise<Match | undefined> {
    const existingMatch = this.matches.get(id);
    if (!existingMatch) {
      return undefined;
    }

    const updatedMatch: Match = { ...existingMatch, ...updates };
    this.matches.set(id, updatedMatch);
    return updatedMatch;
  }

  async deleteMatch(id: number): Promise<boolean> {
    const exists = this.matches.has(id);
    if (!exists) return false;

    this.matches.delete(id);
    return true;
  }

  // Resume methods
  async getResume(id: number): Promise<Resume | undefined> {
    return this.resumes.get(id);
  }

  async getResumeByWorkerId(workerId: number): Promise<Resume | undefined> {
    return Array.from(this.resumes.values()).find(resume => resume.worker_id === workerId);
  }

  async getResumes(): Promise<Resume[]> {
    return Array.from(this.resumes.values());
  }

  async createResume(insertResume: InsertResume): Promise<Resume> {
    const id = this.currentResumeId++;
    const now = new Date();
    const resume: Resume = {
      ...insertResume,
      id,
      upload_date: now,
      file_url: insertResume.file_url || null,
      extracted_text: insertResume.extracted_text || null,
      last_indexed: insertResume.last_indexed || null
    };
    console.log("Creating resume in memory storage:", resume);
    this.resumes.set(id, resume);
    return resume;
  }

  async updateResume(id: number, resumeData: Partial<InsertResume>): Promise<Resume | undefined> {
    const existingResume = this.resumes.get(id);
    if (!existingResume) {
      return undefined;
    }

    // Add file_url if not present
    if (resumeData.file_path && !resumeData.file_url) {
      resumeData.file_url = `/uploads/resumes/${resumeData.file_path.split('/').pop()}`;
    }

    const updatedResume: Resume = { ...existingResume, ...resumeData };
    this.resumes.set(id, updatedResume);
    return updatedResume;
  }

  async deleteResume(id: number): Promise<boolean> {
    return this.resumes.delete(id);
  }

  // 1. Growth Memory Engine - Skilling Suggestions History
  async getSkillingHistory(workerId: number): Promise<SkillingSuggestions[]> {
    return Array.from(this.skillingHistory.values())
      .filter(suggestion => suggestion.worker_id === workerId)
      .sort((a, b) => {
        const timeA = a.created_at ? a.created_at.getTime() : 0;
        const timeB = b.created_at ? b.created_at.getTime() : 0;
        return timeB - timeA;
      });
  }

  async saveSkillingHistory(skillingSuggestion: InsertSkillingSuggestions): Promise<SkillingSuggestions> {
    const id = this.currentSkillingHistoryId++;
    const suggestion: SkillingSuggestions = {
      ...skillingSuggestion,
      id,
      created_at: new Date()
    };
    this.skillingHistory.set(id, suggestion);
    return suggestion;
  }

  // 2. Career Quantum Leap Generator
  async getCareerTransitionPlans(workerId: number): Promise<CareerTransitionPlan[]> {
    return Array.from(this.careerPlans.values())
      .filter(plan => plan.worker_id === workerId)
      .sort((a, b) => {
        const timeA = a.created_at ? a.created_at.getTime() : 0;
        const timeB = b.created_at ? b.created_at.getTime() : 0;
        return timeB - timeA;
      });
  }

  async saveCareerTransitionPlan(plan: InsertCareerTransitionPlan): Promise<CareerTransitionPlan> {
    const id = this.currentCareerPlanId++;
    const careerPlan: CareerTransitionPlan = {
      ...plan,
      id,
      current_role: plan.current_role || null,
      created_at: new Date()
    };
    this.careerPlans.set(id, careerPlan);
    return careerPlan;
  }

  // 3. Skill Context Analyzer
  async getWorkerProfile(workerId: number): Promise<WorkerProfile | undefined> {
    return Array.from(this.workerProfiles.values())
      .find(profile => profile.worker_id === workerId);
  }

  async createOrUpdateWorkerProfile(profile: InsertWorkerProfile): Promise<WorkerProfile> {
    const existingProfile = await this.getWorkerProfile(profile.worker_id);
    if (existingProfile) {
      const updatedProfile: WorkerProfile = {
        ...existingProfile,
        ...profile,
        updated_at: new Date()
      };
      this.workerProfiles.set(existingProfile.id, updatedProfile);
      return updatedProfile;
    } else {
      const id = this.currentWorkerProfileId++;
      const now = new Date();
      const newProfile: WorkerProfile = {
        ...profile,
        id,
        skills_context: profile.skills_context || null,
        created_at: now,
        updated_at: now
      };
      this.workerProfiles.set(id, newProfile);
      return newProfile;
    }
  }

  // 4. GPT Reflection Journal
  async getReflectionJournals(workerId: number): Promise<ReflectionJournal[]> {
    return Array.from(this.reflectionJournals.values())
      .filter(journal => journal.worker_id === workerId)
      .sort((a, b) => {
        const timeA = a.created_at ? a.created_at.getTime() : 0;
        const timeB = b.created_at ? b.created_at.getTime() : 0;
        return timeB - timeA;
      });
  }

  async saveReflectionJournal(journal: InsertReflectionJournal): Promise<ReflectionJournal> {
    const id = this.currentReflectionJournalId++;
    const newJournal: ReflectionJournal = {
      ...journal,
      id,
      ai_reflection: null,
      created_at: new Date()
    };
    this.reflectionJournals.set(id, newJournal);
    return newJournal;
  }

  // 5. Workplace Soul Match
  async getWorkerValues(workerId: number): Promise<WorkerValues | undefined> {
    return Array.from(this.workerValuesList.values())
      .find(values => values.worker_id === workerId);
  }

  async saveWorkerValues(values: InsertWorkerValues): Promise<WorkerValues> {
    const existingValues = await this.getWorkerValues(values.worker_id);
    if (existingValues) {
      const updatedValues: WorkerValues = {
        ...existingValues,
        ...values,
        updated_at: new Date()
      };
      this.workerValuesList.set(existingValues.id, updatedValues);
      return updatedValues;
    } else {
      const id = this.currentWorkerValuesId++;
      const newValues: WorkerValues = {
        ...values,
        id,
        values_match_enabled: values.values_match_enabled || false,
        core_values: values.core_values || null,
        mission_statement: values.mission_statement || null,
        updated_at: new Date()
      };
      this.workerValuesList.set(id, newValues);
      return newValues;
    }
  }

  // 6. AI Mentor Constellations
  async getMentorPreference(workerId: number): Promise<MentorPreferences | undefined> {
    return Array.from(this.mentorPreferences.values())
      .find(preference => preference.worker_id === workerId);
  }

  async saveMentorPreference(preference: InsertMentorPreferences): Promise<MentorPreferences> {
    const existingPreference = await this.getMentorPreference(preference.worker_id);
    if (existingPreference) {
      const updatedPreference: MentorPreferences = {
        ...existingPreference,
        ...preference,
        updated_at: new Date()
      };
      this.mentorPreferences.set(existingPreference.id, updatedPreference);
      return updatedPreference;
    } else {
      const id = this.currentMentorPreferenceId++;
      const newPreference: MentorPreferences = {
        ...preference,
        id,
        mentor_tone: preference.mentor_tone || null,
        updated_at: new Date()
      };
      this.mentorPreferences.set(id, newPreference);
      return newPreference;
    }
  }

  // User Profile methods
  async getUserProfile(userId: number): Promise<UserProfile | undefined> {
    return Array.from(this.userProfiles.values())
      .find(profile => profile.user_id === userId);
  }

  async updateUserProfile(userId: number, profileData: Partial<UserProfile>): Promise<UserProfile> {
    const existingProfile = await this.getUserProfile(userId);
    if (existingProfile) {
      const updatedProfile: UserProfile = {
        ...existingProfile,
        ...profileData,
        updated_at: new Date()
      };
      this.userProfiles.set(existingProfile.id, updatedProfile);
      return updatedProfile;
    } else {
      const id = this.currentUserProfileId++;
      const now = new Date();
      const newProfile: UserProfile = {
        id,
        user_id: userId,
        position: profileData.position || null,
        location: profileData.location || null,
        bio: profileData.bio || null,
        company: profileData.company || null,
        years_of_experience: profileData.years_of_experience || null,
        skills: profileData.skills || null,
        profile_picture: profileData.profile_picture || null,
        created_at: now,
        updated_at: now,
        pending_email: profileData.pending_email || null
      };
      this.userProfiles.set(id, newProfile);
      return newProfile;
    }
  }

  // Approved Domains methods
  async getApprovedDomains(): Promise<ApprovedDomain[]> {
    return Array.from(this.approvedDomains.values());
  }

  async getApprovedDomain(id: number): Promise<ApprovedDomain | undefined> {
    return this.approvedDomains.get(id);
  }

  async getApprovedDomainByName(domain: string): Promise<ApprovedDomain | undefined> {
    return Array.from(this.approvedDomains.values()).find(
      d => d.domain.toLowerCase() === domain.toLowerCase()
    );
  }

  // Admin functionality
  async getSystemLogs(): Promise<SystemLog[]> {
    return Array.from(this.systemLogs.values())
      .sort((a, b) => {
        const timeA = a.created_at ? a.created_at.getTime() : 0;
        const timeB = b.created_at ? b.created_at.getTime() : 0;
        return timeB - timeA;
      });
  }

  async addSystemLog(log: InsertSystemLog): Promise<SystemLog> {
    const id = this.currentSystemLogId++;
    const systemLog: SystemLog = {
      ...log,
      id,
      created_at: new Date(),
      user_id: log.user_id || null,
      metadata: log.metadata || null
    };
    this.systemLogs.set(id, systemLog);
    return systemLog;
  }

  async createApprovedDomain(domain: InsertApprovedDomain): Promise<ApprovedDomain> {
    const id = this.currentApprovedDomainId++;
    const now = new Date();
    const newDomain: ApprovedDomain = {
      id,
      domain: domain.domain,
      description: domain.description || null,
      is_active: domain.is_active ?? true,
      added_by: domain.added_by || null,
      created_at: now
    };
    this.approvedDomains.set(id, newDomain);
    return newDomain;
  }

  async updateApprovedDomain(id: number, domainData: Partial<ApprovedDomain>): Promise<ApprovedDomain | undefined> {
    const existingDomain = this.approvedDomains.get(id);
    if (!existingDomain) {
      return undefined;
    }

    const updatedDomain: ApprovedDomain = { ...existingDomain, ...domainData };
    this.approvedDomains.set(id, updatedDomain);
    return updatedDomain;
  }

  async deleteApprovedDomain(id: number): Promise<boolean> {
    return this.approvedDomains.delete(id);
  }

  // This section was removed to fix duplicate method declarations
}

// PostgreSQL storage implementation
export class DatabaseStorage implements IStorage {
  sessionStore: session.Store;

  constructor() {
    this.sessionStore = new PostgresSessionStore({
      pool,
      tableName: 'sessions',
      createTableIfMissing: true
    });
  }

  // User methods
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUsers(): Promise<User[]> {
    try {
      return await db.select().from(users);
    } catch (error) {
      console.error('Error getting users:', error);
      return [];
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.email, email));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return this.getUserByEmail(username); // Using email as username
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async updateUser(id: number, userData: Partial<User>): Promise<User | undefined> {
    try {
      const [updatedUser] = await db
        .update(users)
        .set(userData)
        .where(eq(users.id, id))
        .returning();
      return updatedUser;
    } catch (error) {
      console.error('Error updating user:', error);
      return undefined;
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    try {
      const result = await db.delete(users).where(eq(users.id, id)).returning();
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting user:', error);
      return false;
    }
  }

  // Job methods
  async getJob(id: number): Promise<Job | undefined> {
    const [job] = await db.select().from(jobs).where(eq(jobs.id, id));
    return job;
  }

  async getJobs(): Promise<Job[]> {
    return db.select().from(jobs);
  }

  async getJobsByEmployer(employerId: number): Promise<Job[]> {
    return db.select().from(jobs).where(eq(jobs.employer_id, employerId));
  }

  async createJob(insertJob: InsertJob): Promise<Job> {
    const [job] = await db.insert(jobs).values(insertJob).returning();
    return job;
  }

  // Surplus Employee methods
  async getSurplusEmployee(id: number): Promise<SurplusEmployee | undefined> {
    const [employee] = await db.select().from(surplusEmployees).where(eq(surplusEmployees.id, id));
    return employee;
  }

  async getSurplusEmployees(): Promise<SurplusEmployee[]> {
    return db.select().from(surplusEmployees);
  }

  async getSurplusEmployeesByEmployer(employerId: number): Promise<SurplusEmployee[]> {
    return db.select().from(surplusEmployees).where(eq(surplusEmployees.employer_id, employerId));
  }

  async createSurplusEmployee(insertEmployee: InsertSurplusEmployee): Promise<SurplusEmployee> {
    try {
      // Ensure all fields have default values
      const employeeData = {
        ...insertEmployee,
        status: insertEmployee.status || 'available',
        notes: insertEmployee.notes || '',
        skills: insertEmployee.skills || '',
        years_experience: insertEmployee.years_experience || 0,
        transfer_reason: insertEmployee.transfer_reason || '',
        potential_positions: insertEmployee.potential_positions || '',
      };

      console.log("Creating surplus employee in database:", employeeData);
      const [employee] = await db.insert(surplusEmployees).values(employeeData).returning();
      console.log("Created employee in database:", employee);
      return employee;
    } catch (error) {
      console.error("Error creating surplus employee in database:", error);
      throw error;
    }
  }

  async updateSurplusEmployee(id: number, updates: Partial<Omit<SurplusEmployee, "id" | "employer_id">>): Promise<SurplusEmployee | null> {
    try {
      const [updatedEmployee] = await db
        .update(surplusEmployees)
        .set(updates)
        .where(eq(surplusEmployees.id, id))
        .returning();

      return updatedEmployee || null;
    } catch (error) {
      console.error('Error updating surplus employee:', error);
      return null;
    }
  }

  async deleteSurplusEmployee(id: number): Promise<boolean> {
    try {
      const result = await db
        .delete(surplusEmployees)
        .where(eq(surplusEmployees.id, id))
        .returning();

      return result.length > 0;
    } catch (error) {
      console.error('Error deleting surplus employee:', error);
      return false;
    }
  }

  // Match methods
  async getMatch(id: number): Promise<Match | undefined> {
    const [match] = await db.select().from(matches).where(eq(matches.id, id));
    return match;
  }

  async getMatches(): Promise<Match[]> {
    return db.select().from(matches);
  }

  async getMatchesByWorkerId(workerId: number): Promise<Match[]> {
    return db.select().from(matches).where(eq(matches.worker_id, workerId));
  }

  async getMatchesByJobId(jobId: number): Promise<Match[]> {
    return db.select().from(matches).where(eq(matches.job_id, jobId));
  }

  async getMatchByJobAndWorker(jobId: number, workerId: number): Promise<Match | undefined> {
    const [match] = await db.select().from(matches).where(
      and(
        eq(matches.job_id, jobId),
        eq(matches.worker_id, workerId)
      )
    );
    return match;
  }

  async deleteJob(id: number): Promise<boolean> {
    const result = await db.delete(jobs).where(eq(jobs.id, id)).returning();
    return result.length > 0;
  }

  async createMatch(insertMatch: InsertMatch): Promise<Match> {
    try {
      console.log('Creating match with data:', insertMatch);
      const [match] = await db.insert(matches).values(insertMatch).returning();
      console.log('Match created successfully:', match);
      return match;
    } catch (error) {
      console.error('Error in createMatch:', error);
      throw error;
    }
  }

  async updateMatch(id: number, updates: Partial<Match>): Promise<Match | undefined> {
    try {
      console.log('Updating match with data:', { id, updates });
      const [updatedMatch] = await db
        .update(matches)
        .set(updates)
        .where(eq(matches.id, id))
        .returning();
      console.log('Match updated successfully:', updatedMatch);
      return updatedMatch;
    } catch (error) {
      console.error('Error in updateMatch:', error);
      throw error;
    }
  }

  async deleteMatch(id: number): Promise<boolean> {
    try {
      console.log(`Deleting match ${id} from Neon Database`);
      const result = await db
        .delete(matches)
        .where(eq(matches.id, id))
        .returning({ id: matches.id });

      console.log(`Match ${id} deleted successfully from Neon Database:`, result.length > 0);
      return result.length > 0;
    } catch (error) {
      console.error(`Error deleting match ${id} from Neon Database:`, error);
      return false;
    }
  }

  // Resume methods
  async getResume(id: number): Promise<Resume | undefined> {
    try {
      // Use a more specific select to avoid missing column errors
      const [resume] = await db
        .select({
          id: resumes.id,
          worker_id: resumes.worker_id,
          filename: resumes.filename,
          file_path: resumes.file_path,
          file_size: resumes.file_size,
          file_type: resumes.file_type,
          upload_date: resumes.upload_date,
          extracted_text: resumes.extracted_text,
          last_indexed: resumes.last_indexed
        })
        .from(resumes)
        .where(eq(resumes.id, id));

      // Add file_url property if it doesn't exist
      if (resume && !('file_url' in resume)) {
        (resume as any).file_url = `/uploads/resumes/${resume.file_path.split('/').pop()}`;
      }

      return resume;
    } catch (error) {
      console.error('Error in getResume:', error);
      return undefined;
    }
  }

  async getResumeByWorkerId(workerId: number): Promise<Resume | undefined> {
    try {
      // Use a more specific select to avoid missing column errors
      const [resume] = await db
        .select({
          id: resumes.id,
          worker_id: resumes.worker_id,
          filename: resumes.filename,
          file_path: resumes.file_path,
          file_size: resumes.file_size,
          file_type: resumes.file_type,
          upload_date: resumes.upload_date,
          extracted_text: resumes.extracted_text,
          last_indexed: resumes.last_indexed
        })
        .from(resumes)
        .where(eq(resumes.worker_id, workerId));

      // Add file_url property if it doesn't exist
      if (resume && !('file_url' in resume)) {
        (resume as any).file_url = `/uploads/resumes/${resume.file_path.split('/').pop()}`;
      }

      return resume;
    } catch (error) {
      console.error('Error in getResumeByWorkerId:', error);
      return undefined;
    }
  }

  async getResumes(): Promise<Resume[]> {
    try {
      // Use a more specific select to avoid missing column errors
      const results = await db
        .select({
          id: resumes.id,
          worker_id: resumes.worker_id,
          filename: resumes.filename,
          file_path: resumes.file_path,
          file_size: resumes.file_size,
          file_type: resumes.file_type,
          upload_date: resumes.upload_date,
          extracted_text: resumes.extracted_text,
          last_indexed: resumes.last_indexed
        })
        .from(resumes);

      // Add file_url property to each resume if it doesn't exist
      return results.map(resume => {
        if (!('file_url' in resume)) {
          (resume as any).file_url = `/uploads/resumes/${resume.file_path.split('/').pop()}`;
        }
        return resume;
      });
    } catch (error) {
      console.error('Error in getResumes:', error);
      return [];
    }
  }

  async createResume(insertResume: InsertResume): Promise<Resume> {
    try {
      console.log("Creating resume in Neon Database:", insertResume);

      // Create a clean insert data object with only the fields that exist in the database
      const insertData = {
        worker_id: insertResume.worker_id,
        filename: insertResume.filename,
        file_path: insertResume.file_path,
        file_size: insertResume.file_size,
        file_type: insertResume.file_type,
        extracted_text: insertResume.extracted_text || null,
        last_indexed: insertResume.last_indexed || null
      };

      // Store the file_url for later
      const fileUrl = insertResume.file_url || `/uploads/resumes/${insertResume.file_path.split('\\').pop()}`;

      // Insert the resume with only valid fields
      const [resume] = await db.insert(resumes).values(insertData).returning({
        id: resumes.id,
        worker_id: resumes.worker_id,
        filename: resumes.filename,
        file_path: resumes.file_path,
        file_size: resumes.file_size,
        file_type: resumes.file_type,
        upload_date: resumes.upload_date,
        extracted_text: resumes.extracted_text,
        last_indexed: resumes.last_indexed
      });

      // Add file_url property to the result
      (resume as any).file_url = fileUrl;

      console.log("Resume created successfully in Neon Database:", resume);
      return resume;
    } catch (error) {
      console.error("Error creating resume in Neon Database:", error);
      throw error;
    }
  }

  async updateResume(id: number, resumeData: Partial<InsertResume>): Promise<Resume | undefined> {
    try {
      console.log(`Updating resume ${id} in Neon Database:`, resumeData);

      // Create a copy of the update data without file_url if it exists
      const updateData = { ...resumeData };
      if ('file_url' in updateData) {
        delete (updateData as any).file_url;
      }

      // Update the resume without file_url
      const [updatedResume] = await db
        .update(resumes)
        .set(updateData)
        .where(eq(resumes.id, id))
        .returning({
          id: resumes.id,
          worker_id: resumes.worker_id,
          filename: resumes.filename,
          file_path: resumes.file_path,
          file_size: resumes.file_size,
          file_type: resumes.file_type,
          upload_date: resumes.upload_date,
          extracted_text: resumes.extracted_text,
          last_indexed: resumes.last_indexed
        });

      // Add file_url property if it doesn't exist
      if (updatedResume && !('file_url' in updatedResume)) {
        (updatedResume as any).file_url = `/uploads/resumes/${updatedResume.file_path.split('/').pop()}`;
      }

      console.log("Resume updated successfully in Neon Database:", updatedResume);
      return updatedResume;
    } catch (error) {
      console.error(`Error updating resume ${id} in Neon Database:`, error);
      throw error;
    }
  }

  async deleteResume(id: number): Promise<boolean> {
    try {
      console.log(`Deleting resume ${id} from Neon Database`);

      // Use a more specific returning to avoid missing column errors
      const result = await db
        .delete(resumes)
        .where(eq(resumes.id, id))
        .returning({
          id: resumes.id
        });

      console.log(`Resume ${id} deleted successfully from Neon Database:`, result.length > 0);
      return result.length > 0;
    } catch (error) {
      console.error(`Error deleting resume ${id} from Neon Database:`, error);
      return false;
    }
  }

  // PathCoach methods

  // 1. Growth Memory Engine - Store reskilling suggestions history
  async getSkillingHistory(workerId: number): Promise<SkillingSuggestions[]> {
    return db
      .select()
      .from(skillingSuggestions)
      .where(eq(skillingSuggestions.worker_id, workerId))
      .orderBy(desc(skillingSuggestions.created_at));
  }

  async saveSkillingHistory(suggestion: InsertSkillingSuggestions): Promise<SkillingSuggestions> {
    const [history] = await db
      .insert(skillingSuggestions)
      .values(suggestion)
      .returning();
    return history;
  }

  // 2. Career Quantum Leap Generator
  async getCareerTransitionPlans(workerId: number): Promise<CareerTransitionPlan[]> {
    return db
      .select()
      .from(careerTransitionPlans)
      .where(eq(careerTransitionPlans.worker_id, workerId))
      .orderBy(desc(careerTransitionPlans.created_at));
  }

  async saveCareerTransitionPlan(plan: InsertCareerTransitionPlan): Promise<CareerTransitionPlan> {
    const [savedPlan] = await db
      .insert(careerTransitionPlans)
      .values(plan)
      .returning();
    return savedPlan;
  }

  // 3. Skill Context Analyzer
  async getWorkerProfile(workerId: number): Promise<WorkerProfile | undefined> {
    const [profile] = await db
      .select()
      .from(workerProfiles)
      .where(eq(workerProfiles.worker_id, workerId));
    return profile;
  }

  async createOrUpdateWorkerProfile(profile: InsertWorkerProfile): Promise<WorkerProfile> {
    const existing = await this.getWorkerProfile(profile.worker_id);

    if (existing) {
      const [updated] = await db
        .update(workerProfiles)
        .set({
          ...profile,
          updated_at: new Date()
        })
        .where(eq(workerProfiles.worker_id, profile.worker_id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(workerProfiles)
        .values(profile)
        .returning();
      return created;
    }
  }

  // 4. GPT Reflection Journal
  async getReflectionJournals(workerId: number): Promise<ReflectionJournal[]> {
    return db
      .select()
      .from(reflectionJournals)
      .where(eq(reflectionJournals.worker_id, workerId))
      .orderBy(desc(reflectionJournals.created_at));
  }

  async saveReflectionJournal(journal: InsertReflectionJournal): Promise<ReflectionJournal> {
    const [saved] = await db
      .insert(reflectionJournals)
      .values(journal)
      .returning();
    return saved;
  }

  // 5. Workplace Soul Match
  async getWorkerValues(workerId: number): Promise<WorkerValues | undefined> {
    const [values] = await db
      .select()
      .from(workerValues)
      .where(eq(workerValues.worker_id, workerId));
    return values;
  }

  async saveWorkerValues(values: InsertWorkerValues): Promise<WorkerValues> {
    const existing = await this.getWorkerValues(values.worker_id);

    if (existing) {
      const [updated] = await db
        .update(workerValues)
        .set({
          ...values,
          updated_at: new Date()
        })
        .where(eq(workerValues.worker_id, values.worker_id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(workerValues)
        .values(values)
        .returning();
      return created;
    }
  }

  // 6. AI Mentor Constellations
  async getMentorPreference(workerId: number): Promise<MentorPreferences | undefined> {
    const [preference] = await db
      .select()
      .from(mentorPreferences)
      .where(eq(mentorPreferences.worker_id, workerId));
    return preference;
  }

  async saveMentorPreference(preference: InsertMentorPreferences): Promise<MentorPreferences> {
    const existing = await this.getMentorPreference(preference.worker_id);

    if (existing) {
      const [updated] = await db
        .update(mentorPreferences)
        .set({
          ...preference,
          updated_at: new Date()
        })
        .where(eq(mentorPreferences.worker_id, preference.worker_id))
        .returning();
      return updated;
    } else {
      const [created] = await db
        .insert(mentorPreferences)
        .values(preference)
        .returning();
      return created;
    }
  }

  // User Profile methods
  async getUserProfile(userId: number): Promise<UserProfile | undefined> {
    try {
      const [profile] = await db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.user_id, userId));
      return profile;
    } catch (error) {
      // Check if the error is due to the table not existing
      if (error instanceof Error && error.message.includes('relation "user_profiles" does not exist')) {
        console.warn('user_profiles table does not exist yet. Returning default profile.');
        // Return a default profile with empty values
        return {
          id: 0,
          user_id: userId,
          position: "",
          location: "",
          bio: "",
          company: "",
          years_of_experience: null,
          skills: "",
          profile_picture: null,
          pending_email: null,
          created_at: new Date(),
          updated_at: new Date()
        };
      }
      console.error('Error getting user profile:', error);
      return undefined;
    }
  }

  async updateUserProfile(userId: number, profileData: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const existing = await this.getUserProfile(userId);

      // If the table doesn't exist or there's no existing profile, fall back to in-memory storage
      if (existing && existing.id !== 0) {
        try {
          const [updated] = await db
            .update(userProfiles)
            .set({
              ...profileData,
              updated_at: new Date()
            })
            .where(eq(userProfiles.user_id, userId))
            .returning();
          return updated;
        } catch (error) {
          if (error instanceof Error && error.message.includes('relation "user_profiles" does not exist')) {
            console.warn('user_profiles table does not exist. Using fallback profile.');
            // Return a fallback profile with the updated data
            return {
              id: 0,
              user_id: userId,
              position: profileData.position || "",
              location: profileData.location || "",
              bio: profileData.bio || "",
              company: profileData.company || "",
              years_of_experience: profileData.years_of_experience || null,
              skills: profileData.skills || "",
              profile_picture: profileData.profile_picture || null,
              pending_email: null,
              created_at: new Date(),
              updated_at: new Date()
            };
          }
          throw error;
        }
      } else {
        try {
          const [created] = await db
            .insert(userProfiles)
            .values({
              user_id: userId,
              position: profileData.position || "",
              location: profileData.location || "",
              bio: profileData.bio || "",
              company: profileData.company || "",
              years_of_experience: profileData.years_of_experience || null,
              skills: profileData.skills || "",
              profile_picture: profileData.profile_picture || null,
              created_at: new Date(),
              updated_at: new Date()
            })
            .returning();
          return created;
        } catch (error) {
          if (error instanceof Error && error.message.includes('relation "user_profiles" does not exist')) {
            console.warn('user_profiles table does not exist. Using fallback profile.');
            // Return a fallback profile with the updated data
            return {
              id: 0,
              user_id: userId,
              position: profileData.position || "",
              location: profileData.location || "",
              bio: profileData.bio || "",
              company: profileData.company || "",
              years_of_experience: profileData.years_of_experience || null,
              skills: profileData.skills || "",
              profile_picture: profileData.profile_picture || null,
              pending_email: null,
              created_at: new Date(),
              updated_at: new Date()
            };
          }
          throw error;
        }
      }
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw error;
    }
  }

  // Approved Domains methods
  async getApprovedDomains(): Promise<ApprovedDomain[]> {
    try {
      return await db.select().from(approvedDomains);
    } catch (error) {
      console.error('Error getting approved domains:', error);
      return [];
    }
  }

  async getApprovedDomain(id: number): Promise<ApprovedDomain | undefined> {
    try {
      const [domain] = await db
        .select()
        .from(approvedDomains)
        .where(eq(approvedDomains.id, id));
      return domain;
    } catch (error) {
      console.error('Error getting approved domain:', error);
      return undefined;
    }
  }

  async getApprovedDomainByName(domain: string): Promise<ApprovedDomain | undefined> {
    try {
      const [result] = await db
        .select()
        .from(approvedDomains)
        .where(eq(approvedDomains.domain, domain));
      return result;
    } catch (error) {
      console.error('Error getting approved domain by name:', error);
      return undefined;
    }
  }

  async createApprovedDomain(domain: InsertApprovedDomain): Promise<ApprovedDomain> {
    try {
      const [result] = await db
        .insert(approvedDomains)
        .values(domain)
        .returning();
      return result;
    } catch (error) {
      console.error('Error creating approved domain:', error);
      throw error;
    }
  }

  async updateApprovedDomain(id: number, domainData: Partial<ApprovedDomain>): Promise<ApprovedDomain | undefined> {
    try {
      const [result] = await db
        .update(approvedDomains)
        .set(domainData)
        .where(eq(approvedDomains.id, id))
        .returning();
      return result;
    } catch (error) {
      console.error('Error updating approved domain:', error);
      return undefined;
    }
  }

  async deleteApprovedDomain(id: number): Promise<boolean> {
    try {
      const result = await db
        .delete(approvedDomains)
        .where(eq(approvedDomains.id, id))
        .returning();
      return result.length > 0;
    } catch (error) {
      console.error('Error deleting approved domain:', error);
      return false;
    }
  }

  // Admin functionality
  async getSystemLogs(): Promise<SystemLog[]> {
    try {
      return await db
        .select()
        .from(systemLogs)
        .orderBy(desc(systemLogs.created_at));
    } catch (error) {
      console.error('Error getting system logs:', error);
      return [];
    }
  }

  async addSystemLog(log: InsertSystemLog): Promise<SystemLog> {
    try {
      const [result] = await db
        .insert(systemLogs)
        .values(log)
        .returning();
      return result;
    } catch (error) {
      console.error('Error adding system log:', error);
      throw error;
    }
  }
}

// Use the appropriate storage implementation based on environment
let storage: IStorage;

// Check if DATABASE_URL is set for Neon PostgreSQL
const useNeonDb = process.env.DATABASE_URL && process.env.DATABASE_URL.includes('neon');

if (useNeonDb) {
  console.log("Using Neon Database storage");
  try {
    // Create the database storage
    storage = new DatabaseStorage();

    // Test the database connection
    (async () => {
      try {
        // Try to get a user to test the connection
        await storage.getUser(1);
        console.log("✅ Neon Database connection verified successfully");
      } catch (error) {
        console.error("❌ Neon Database connection test failed:", error);
        console.log("Falling back to in-memory storage due to connection test failure");

        // Initialize in-memory storage with sample data
        storage = new MemStorage();
        console.log("Initialized in-memory storage with sample data");
      }
    })();
  } catch (error) {
    console.error("Failed to initialize Neon Database storage:", error);
    console.log("Falling back to in-memory storage");

    // Initialize in-memory storage with sample data
    storage = new MemStorage();
    console.log("Initialized in-memory storage with sample data");
  }
} else {
  console.log("Using in-memory storage for development");
  storage = new MemStorage();
}

export { storage };
