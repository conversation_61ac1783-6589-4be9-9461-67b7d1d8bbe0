{"name": "rest-express", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "tsx server/index.ts", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "cross-env NODE_ENV=production node dist/index.js", "check": "tsc", "db:push": "drizzle-kit push", "db:generate": "node scripts/generate-migrations.js", "db:migrate": "tsx server/migrations.ts", "db:studio": "drizzle-kit studio", "db:indexes": "node scripts/apply-indexes.js", "db:job-fields": "node scripts/run-job-fields-migration.js", "create-sample-resume": "node scripts/create-sample-resume.js", "create-mock-resumes": "node scripts/create-mock-resumes.js", "import-resumes": "node scripts/import-existing-resumes.js", "test-db": "node scripts/test-supabase-connection.js", "test-supabase": "node scripts/test-supabase-client.js", "build:aws": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist --target=node18", "start:aws": "NODE_ENV=production PORT=8080 node dist/index.js", "aws:init": "eb init pathlink --region us-east-1 --platform node.js", "aws:create": "eb create pathlink-production --region us-east-1 --instance-type t3.micro", "aws:deploy": "npm run build:aws && eb deploy", "aws:status": "eb status", "aws:logs": "eb logs", "deploy:full": "npm run build:aws && eb deploy && echo 'Deployment complete! Check AWS and Netlify.'", "deploy:lambda": "node aws-lambda-deploy.js", "aws:install": "npm install -g @aws-cdk/cli serverless", "aws:configure": "echo 'Run: aws configure' to set up credentials"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^3.9.1", "@hubspot/api-client": "^13.0.0", "@jridgewell/trace-mapping": "^0.3.25", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-navigation-menu": "^1.2.1", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "@replit/vite-plugin-shadcn-theme-json": "^0.0.4", "@sendgrid/mail": "^8.1.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.60.5", "@types/multer": "^1.4.12", "@xmldom/xmldom": "^0.9.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.39.1", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.3.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "input-otp": "^1.2.4", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "lucide-react": "^0.453.0", "mammoth": "^1.9.0", "memorystore": "^1.6.7", "multer": "^1.4.5-lts.2", "openai": "^4.92.1", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "postgres": "^3.4.5", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.53.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.4", "react-router-dom": "^7.5.0", "react-toastify": "^11.0.5", "recharts": "^2.15.2", "serverless-http": "^3.2.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.0", "wouter": "^3.3.5", "ws": "^8.18.0", "zod": "^3.23.8", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.0.11", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "playwright": "^1.52.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "tsx": "^4.19.1", "typescript": "5.6.3", "vite": "^5.4.14"}, "optionalDependencies": {"bufferutil": "^4.0.8"}, "engines": {"node": "18.x", "npm": "9.x"}}