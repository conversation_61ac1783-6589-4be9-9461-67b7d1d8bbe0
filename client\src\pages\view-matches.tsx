import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { Helmet } from "react-helmet-async";
import { useAuth } from "@/hooks/use-auth";
import Layout from "@/components/layout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Loader2, Search, FileText, CheckCircle, XCircle, AlertCircle, Filter, ArrowUpDown, Mail, Phone, ExternalLink, Globe, MapPin, Briefcase, Star, TrendingUp, Users } from "lucide-react";
import ResumeViewModal from "@/components/resume-view-modal";
import JobDetailsModal from "@/components/job-details-modal";
import { getJobs, getResumes, getComprehensiveMatches } from "@/lib/api-client";

// Worker Matches View Component
function WorkerMatchesView() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [location, setLocation] = useLocation();
  const [selectedJob, setSelectedJob] = useState<any>(null);
  const [showJobModal, setShowJobModal] = useState(false);

  // Check if worker has uploaded a resume
  const resumeQuery = useQuery({
    queryKey: ["/api/resumes"],
    queryFn: async () => {
      const response = await fetch("/api/resumes", {
        credentials: "include",
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      if (!response.ok) {
        throw new Error("Failed to fetch resumes");
      }
      return response.json();
    },
  });

  // Fetch jobs from the database
  const jobsQuery = useQuery({
    queryKey: ["/api/jobs"],
    queryFn: async () => {
      const response = await fetch("/api/jobs", {
        credentials: "include",
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });
      if (!response.ok) {
        throw new Error("Failed to fetch jobs");
      }
      return response.json();
    },
  });

  // Get jobs from the database
  const jobs = jobsQuery.data || [];

  const handleViewJob = (job: any) => {
    setSelectedJob(job);
    setShowJobModal(true);
  };

  const handleApplyJob = async (jobId: number) => {
    try {
      const response = await fetch("/api/matches", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        credentials: "include",
        body: JSON.stringify({ job_id: jobId }),
      });

      if (!response.ok) {
        throw new Error("Failed to apply for job");
      }

      toast({
        title: "Application Submitted",
        description: "Your application has been submitted successfully!",
      });
    } catch (error) {
      toast({
        title: "Application Failed",
        description: "Failed to submit your application. Please try again.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <Layout>
      <Helmet>
        <title>Job Recommendations | PathLink</title>
      </Helmet>

      <div className="container mx-auto py-6 px-4 max-w-7xl">
        {/* Loading state */}
        {(resumeQuery.isLoading || jobsQuery.isLoading) && (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="w-full">
                <CardHeader>
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-4 w-1/4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* No resume uploaded state */}
        {resumeQuery.isSuccess && (!resumeQuery.data || resumeQuery.data.length === 0) && (
          <Card className="w-full">
            <CardContent className="flex flex-col items-center justify-center py-16">
              <FileText className="h-16 w-16 text-gray-400 mb-6" />
              <h3 className="text-2xl font-medium text-gray-700 mb-4">Upload Your Resume First</h3>
              <p className="text-gray-500 text-center mb-6 max-w-md">
                To get personalized job recommendations, you need to upload your resume.
                Our system will analyze your skills and experience to find the best job matches for you.
              </p>
              <div className="flex gap-3">
                <Button
                  onClick={() => setLocation("/upload-resume")}
                  className="bg-[#1C2A42] hover:bg-[#1C2A42]/90 text-white"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Upload Resume
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setLocation("/dashboard")}
                >
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Job Recommendations with exact styling */}
        {!jobsQuery.isLoading && jobs.length > 0 && resumeQuery.isSuccess && resumeQuery.data && resumeQuery.data.length > 0 && (
          <div className="rounded-lg border text-card-foreground bg-white dark:bg-gray-800 shadow-md overflow-hidden">
            <div className="flex flex-col space-y-1.5 p-6 pb-3">
              <h3 className="tracking-tight text-xl font-bold text-[#1C2A42] dark:text-white">Recommended for You</h3>
              <p className="text-sm text-gray-600 dark:text-gray-300">Jobs that match your profile and interests</p>
            </div>
            <div className="p-4">
              <div className="space-y-4">
                {jobs.slice(0, 3).map((job: any) => (
                  <div key={job.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-[#C1B7AA]/10 dark:hover:bg-gray-700/70 transition-all duration-200 hover:shadow-md">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h3 className="font-bold text-lg text-[#1C2A42] dark:text-white mb-1">{job.title}</h3>
                        <p className="text-[#1C2A42]/80 dark:text-white/80 font-medium text-sm">{job.company_name}</p>
                      </div>
                      <div className="flex flex-col items-end gap-1">
                        <div className="inline-flex items-center rounded-full border px-2.5 py-0.5 font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-green-50 text-green-700 border-green-200 text-xs">Recommended</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-3 text-sm">
                      <div className="flex items-center text-gray-600 dark:text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-map-pin h-4 w-4 mr-2 text-gray-400">
                          <path d="M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"></path>
                          <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                        <span className="truncate">{job.location}</span>
                      </div>
                      <div className="flex items-center text-gray-600 dark:text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-building h-4 w-4 mr-2 text-gray-400">
                          <rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect>
                          <path d="M9 22v-4h6v4"></path>
                          <path d="M8 6h.01"></path>
                          <path d="M16 6h.01"></path>
                          <path d="M12 6h.01"></path>
                          <path d="M12 10h.01"></path>
                          <path d="M12 14h.01"></path>
                          <path d="M16 10h.01"></path>
                          <path d="M16 14h.01"></path>
                          <path d="M8 10h.01"></path>
                          <path d="M8 14h.01"></path>
                        </svg>
                        <span className="truncate">{job.industry}</span>
                      </div>
                      <div className="flex items-center text-gray-600 dark:text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-clock h-4 w-4 mr-2 text-gray-400">
                          <circle cx="12" cy="12" r="10"></circle>
                          <polyline points="12 6 12 12 16 14"></polyline>
                        </svg>
                        <span>{job.employment_type}</span>
                      </div>
                      <div className="flex items-center text-gray-600 dark:text-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-calendar h-4 w-4 mr-2 text-gray-400">
                          <path d="M8 2v4"></path>
                          <path d="M16 2v4"></path>
                          <rect width="18" height="18" x="3" y="4" rx="2"></rect>
                          <path d="M3 10h18"></path>
                        </svg>
                        <span>Posted {formatDate(job.created_at)}</span>
                      </div>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-2 mb-3">{job.description}</p>
                    <div className="mb-3">
                      <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Key Skills:</p>
                      <div className="flex flex-wrap gap-1">
                        {job.requirements && job.requirements.split(',').slice(0, 3).map((skill: string, index: number) => (
                          <div key={index} className="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs px-2 py-1">{skill.trim()}</div>
                        ))}
                        {!job.requirements && (
                          <div className="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs px-2 py-1">No specific requirements</div>
                        )}
                        {job.requirements && job.requirements.split(',').length > 3 && (
                          <div className="inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80 text-xs px-2 py-1">+{job.requirements.split(',').length - 3} more</div>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between items-center pt-3 border-t border-gray-100 dark:border-gray-700">
                      <button
                        onClick={() => handleViewJob(job)}
                        className="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3 text-xs"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-eye h-3 w-3 mr-1">
                          <path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path>
                          <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        View Details
                      </button>
                      <button
                        onClick={() => handleApplyJob(job.id)}
                        className="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 h-9 rounded-md px-3 bg-green-600 text-white text-xs hover:bg-green-700"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-send h-3 w-3 mr-1">
                          <path d="M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z"></path>
                          <path d="m21.854 2.147-10.94 10.939"></path>
                        </svg>
                        Apply Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* No jobs found */}
        {!jobsQuery.isLoading && jobsQuery.isSuccess && jobs.length === 0 && (
          <Card className="w-full">
            <CardContent className="flex flex-col items-center justify-center py-10">
              <TrendingUp className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-700">No job recommendations found</h3>
              <p className="text-gray-500 mt-2 text-center">
                Check back later for new opportunities.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Job Details Modal */}
        {selectedJob && (
          <JobDetailsModal
            job={selectedJob}
            open={showJobModal}
            onClose={() => setShowJobModal(false)}
          />
        )}
      </div>
    </Layout>
  );
}

// Define interfaces for our data
interface Job {
  id: number;
  title: string;
  description: string;
  industry: string;
  location: string;
  employer_id: number;
  required_skills?: string;
  minimum_experience?: string;
}

interface Resume {
  id: number;
  worker_id: number;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  upload_date: string;
  extracted_text?: string;
}

interface Worker {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface MatchScore {
  overall: number;
  skills: number;
  experience: number;
  education: number;
  industry: number;
  details: {
    matchedSkills: string[];
    missingSkills: string[];
    experienceMatch: string;
    educationMatch: string;
    industryMatch: string;
    additionalStrengths: string[];
    developmentAreas: string[];
  };
}

interface ContactInfo {
  email?: string;
  phone?: string;
  linkedin?: string;
  github?: string;
  website?: string;
  address?: string;
}

interface Match {
  job: Job;
  worker: Worker;
  resume: Resume;
  contactInfo?: ContactInfo;
  score: MatchScore;
}

export default function ViewMatches() {
  const { toast } = useToast();
  const { user } = useAuth();
  const [location, setLocation] = useLocation();
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);
  const [selectedResume, setSelectedResume] = useState<any>(null);
  const [showResumeModal, setShowResumeModal] = useState(false);
  const [showJobModal, setShowJobModal] = useState(false);
  const [selectedContactInfo, setSelectedContactInfo] = useState<any>(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"score" | "name" | "date" | "experience" | "education" | "skills">("score");
  const [filterByScore, setFilterByScore] = useState<number | null>(null);
  const [filterByIndustry, setFilterByIndustry] = useState<string | null>(null);
  const [filterBySkill, setFilterBySkill] = useState<string | null>(null);
  const [filterByExperience, setFilterByExperience] = useState<number | null>(null);
  const [filterByEducation, setFilterByEducation] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"all" | "jobs" | "candidates">("all");
  const [customSkillSearch, setCustomSkillSearch] = useState("");
  const [customIndustrySearch, setCustomIndustrySearch] = useState("");
  const [advancedFiltersOpen, setAdvancedFiltersOpen] = useState(false);
  const [matchTypeFilter, setMatchTypeFilter] = useState<"all" | "exact" | "partial" | "missing">("all");
  const [locationFilter, setLocationFilter] = useState<string | null>(null);

  // Use Supabase client functions for data fetching

  // Fetch jobs
  const jobsQuery = useQuery({
    queryKey: ["jobs"],
    queryFn: async () => {
      const { data, error } = await getJobs();
      if (error) throw new Error("Failed to fetch jobs");
      return data || [];
    },
  });

  // Fetch resumes with worker information
  const resumesQuery = useQuery({
    queryKey: ["resumes"],
    queryFn: async () => {
      const { data, error } = await getResumes();
      if (error) throw new Error("Failed to fetch resumes");

      // For now, we'll use the in-memory data structure since we don't have the worker data
      // In a real implementation, you would join the resumes with worker data in Supabase
      return data?.map(resume => ({
        resume,
        worker: {
          id: resume.worker_id,
          name: "Sample Worker",
          email: "<EMAIL>",
          role: "worker"
        }
      })) || [];
    },
  });

  // Fetch comprehensive match scores
  const matchesQuery = useQuery({
    queryKey: ["comprehensive-matches"],
    queryFn: async () => {
      console.log("Fetching comprehensive matches...");
      console.log("Jobs data:", jobsQuery.data);
      console.log("Resumes data:", resumesQuery.data);

      try {
        const { data, error } = await getComprehensiveMatches();
        if (error) {
          console.error("Error fetching comprehensive matches:", error);
          throw new Error("Failed to fetch comprehensive matches");
        }

        console.log("Comprehensive matches API response:", data);

        // Add fallback data if no matches are returned
        if (!data || data.length === 0) {
          console.log("No comprehensive matches found, using fallback data");

          // Create fallback data using the jobs and resumes
          const fallbackData = [];

          if (jobsQuery.data && jobsQuery.data.length > 0 && resumesQuery.data && resumesQuery.data.length > 0) {
            for (const job of jobsQuery.data) {
              for (const resumeWithUser of resumesQuery.data) {
                fallbackData.push({
                  job,
                  worker: resumeWithUser.worker,
                  resume: resumeWithUser.resume,
                  // contactInfo will come from the server API
                  score: {
                    overall: 85,
                    skills: 80,
                    experience: 90,
                    education: 75,
                    industry: 95,
                    details: {
                      matchedSkills: ["JavaScript", "React", "Node.js"],
                      missingSkills: ["Python", "AWS"],
                      experienceMatch: "Exceeds required experience by 2 years",
                      educationMatch: "Meets education requirements",
                      industryMatch: "Has experience in the industry",
                      additionalStrengths: ["Strong technical background", "Good communication skills"],
                      developmentAreas: ["Learn Python", "Gain AWS experience"]
                    }
                  }
                });
              }
            }
          }

          console.log("Fallback data:", fallbackData);
          return fallbackData;
        }

        return data;
      } catch (err) {
        console.error("Exception in comprehensive matches query:", err);
        throw err;
      }
    },
    enabled: jobsQuery.isSuccess && resumesQuery.isSuccess,
  });

  // Handle errors
  useEffect(() => {
    if (jobsQuery.error) {
      toast({
        title: "Error",
        description: "Failed to load jobs. Please try again.",
        variant: "destructive",
      });
    }
    if (resumesQuery.error) {
      toast({
        title: "Error",
        description: "Failed to load resumes. Please try again.",
        variant: "destructive",
      });
    }
    if (matchesQuery.error) {
      toast({
        title: "Error",
        description: "Failed to load matches. Please try again.",
        variant: "destructive",
      });
    }
  }, [jobsQuery.error, resumesQuery.error, matchesQuery.error, toast]);

  // Enhanced filtering function for custom keywords
  const matchesCustomKeywords = (text: string, keywords: string): boolean => {
    if (!keywords.trim()) return true;

    const keywordList = keywords.toLowerCase().split(/[,\s]+/).filter(k => k.length > 0);
    const lowerText = text.toLowerCase();

    return keywordList.some(keyword => {
      // Exact match
      if (lowerText.includes(keyword)) return true;

      // Fuzzy match for similar words (simple implementation)
      const words = lowerText.split(/\W+/);
      return words.some(word => {
        if (word.length < 3 || keyword.length < 3) return false;

        // Check if words are similar (simple Levenshtein-like check)
        const similarity = calculateSimilarity(word, keyword);
        return similarity > 0.8;
      });
    });
  };

  // Simple similarity calculation
  const calculateSimilarity = (str1: string, str2: string): number => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length <= str2.length ? str1 : str2;

    if (longer.length === 0) return 1.0;

    const editDistance = levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  };

  // Levenshtein distance calculation
  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  };

  // Filter and sort matches
  const filteredMatches = matchesQuery.data && Array.isArray(matchesQuery.data)
    ? matchesQuery.data
        .filter((match) => {
          // Filter by search query (enhanced with custom keywords)
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            const basicMatch = (
              (match.job && match.job.title && match.job.title.toLowerCase().includes(query)) ||
              (match.worker && match.worker.name && match.worker.name.toLowerCase().includes(query)) ||
              (match.job && match.job.industry && match.job.industry.toLowerCase().includes(query)) ||
              (match.resume && match.resume.extracted_text && match.resume.extracted_text.toLowerCase().includes(query))
            );

            // Also check for custom keyword matching in resume text
            const customMatch = match.resume && match.resume.extracted_text &&
              matchesCustomKeywords(match.resume.extracted_text, searchQuery);

            return basicMatch || customMatch;
          }
          return true;
        })
        .filter((match) => {
          // Filter by score
          if (filterByScore !== null) {
            return match.score && typeof match.score.overall === 'number' && match.score.overall >= filterByScore;
          }
          return true;
        })
        .filter((match) => {
          // Filter by industry
          if (filterByIndustry) {
            return match.job && match.job.industry && match.job.industry === filterByIndustry;
          }
          return true;
        })
        .filter((match) => {
          // Filter by skill (enhanced with custom skill search)
          if (filterBySkill || customSkillSearch) {
            const skillToSearch = filterBySkill || customSkillSearch;
            if (skillToSearch) {
              // Check matched skills
              const matchedSkillsMatch = match.score.details &&
                     match.score.details.matchedSkills &&
                     Array.isArray(match.score.details.matchedSkills) &&
                     match.score.details.matchedSkills.some(skill =>
                       skill.toLowerCase().includes(skillToSearch.toLowerCase())
                     );

              // Also check resume text for custom skill keywords
              const resumeSkillMatch = match.resume && match.resume.extracted_text &&
                matchesCustomKeywords(match.resume.extracted_text, skillToSearch);

              // Check job requirements for skills
              const jobSkillMatch = match.job && (
                (match.job.required_skills && match.job.required_skills.toLowerCase().includes(skillToSearch.toLowerCase())) ||
                (match.job.description && matchesCustomKeywords(match.job.description, skillToSearch))
              );

              return matchedSkillsMatch || resumeSkillMatch || jobSkillMatch;
            }
          }
          return true;
        })
        .filter((match) => {
          // Filter by custom industry search
          if (customIndustrySearch) {
            const industryMatch = (
              (match.job && match.job.industry && matchesCustomKeywords(match.job.industry, customIndustrySearch)) ||
              (match.job && match.job.description && matchesCustomKeywords(match.job.description, customIndustrySearch)) ||
              (match.resume && match.resume.extracted_text && matchesCustomKeywords(match.resume.extracted_text, customIndustrySearch))
            );
            return industryMatch;
          }
          return true;
        })
        .filter((match) => {
          // Filter by match type
          if (matchTypeFilter !== "all") {
            if (matchTypeFilter === "exact" && match.score.details && match.score.details.matchedSkills) {
              return match.score.details.matchedSkills.length > 0;
            }
            if (matchTypeFilter === "partial" && match.score.details) {
              // Check if there are partial matches (this would need to be enhanced based on the actual data structure)
              return match.score.overall > 50 && match.score.overall < 90;
            }
            if (matchTypeFilter === "missing" && match.score.details && match.score.details.missingSkills) {
              return match.score.details.missingSkills.length > 0;
            }
          }
          return true;
        })
        .filter((match) => {
          // Filter by location
          if (locationFilter) {
            return match.job && match.job.location &&
              match.job.location.toLowerCase().includes(locationFilter.toLowerCase());
          }
          return true;
        })
        .filter((match) => {
          // Filter by experience
          if (filterByExperience !== null) {
            // Check if experienceMatch exists
            if (match.score.details &&
                match.score.details.experienceMatch &&
                typeof match.score.details.experienceMatch === 'string') {
              // Extract years from experience description
              const yearsMatch = match.score.details.experienceMatch.match(/(\d+)/);
              if (yearsMatch) {
                return parseInt(yearsMatch[1]) >= filterByExperience;
              }
            }
            // Default to false if we can't determine the experience
            return false;
          }
          return true;
        })
        .filter((match) => {
          // Filter by education
          if (filterByEducation) {
            return match.score.details &&
                   match.score.details.educationMatch &&
                   typeof match.score.details.educationMatch === 'string' &&
                   match.score.details.educationMatch.toLowerCase().includes(filterByEducation.toLowerCase());
          }
          return true;
        })
        .filter((match) => {
          // Filter by tab
          if (activeTab === "jobs") {
            return selectedJob && match.job ? match.job.id === selectedJob.id : true;
          }
          if (activeTab === "candidates") {
            return selectedWorker && match.worker ? match.worker.id === selectedWorker.id : true;
          }
          return true;
        })
        .sort((a, b) => {
          // Sort matches
          if (sortBy === "score") {
            return (b.score?.overall || 0) - (a.score?.overall || 0);
          }
          if (sortBy === "name") {
            return (a.worker?.name || "").localeCompare(b.worker?.name || "");
          }
          if (sortBy === "experience") {
            return (b.score?.experience || 0) - (a.score?.experience || 0);
          }
          if (sortBy === "education") {
            return (b.score?.education || 0) - (a.score?.education || 0);
          }
          if (sortBy === "skills") {
            return (b.score?.skills || 0) - (a.score?.skills || 0);
          }
          // Sort by date (assuming we have a date field)
          const dateA = a.resume?.upload_date ? new Date(a.resume.upload_date).getTime() : 0;
          const dateB = b.resume?.upload_date ? new Date(b.resume.upload_date).getTime() : 0;
          return dateB - dateA;
        })
    : [];

  // Get unique industries for filtering
  const industries = jobsQuery.data
    ? Array.from(new Set(jobsQuery.data.map((job) => job.industry)))
    : [];

  // Get unique locations for filtering
  const locations = jobsQuery.data
    ? Array.from(new Set(jobsQuery.data.map((job) => job.location).filter(Boolean)))
    : [];

  // Get all unique skills from matches for filtering
  const allSkills = matchesQuery.data && Array.isArray(matchesQuery.data)
    ? Array.from(new Set(
        matchesQuery.data.flatMap(match =>
          match.score.details?.matchedSkills || []
        ).concat(
          matchesQuery.data.flatMap(match =>
            match.score.details?.missingSkills || []
          )
        )
      ))
    : [];

  // View resume handler
  const handleViewResume = (workerId: number, workerName: string, resume: any) => {
    setSelectedWorker({ id: workerId, name: workerName } as Worker);
    setSelectedResume(resume);
    setShowResumeModal(true);
  };

  // View job details handler
  const handleViewJob = (job: Job) => {
    setSelectedJob(job);
    setShowJobModal(true);
  };

  // Contact candidate handler
  const handleContactCandidate = (match: any) => {
    console.log('Contact candidate clicked for match:', match);
    setSelectedContactInfo({
      worker: match.worker,
      contactInfo: match.contactInfo,
      job: match.job
    });
    setShowContactModal(true);
  };

  // Get score color based on match percentage
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Get badge variant based on match type
  const getMatchBadgeVariant = (matched: boolean) => {
    return matched ? "success" : "destructive";
  };

  // Render different views based on user role
  if (user?.role === 'worker') {
    return <WorkerMatchesView />;
  }

  // Check if employer has posted jobs
  if (user?.role === 'employer' && jobsQuery.isSuccess && (!jobsQuery.data || jobsQuery.data.length === 0)) {
    return (
      <Layout>
        <Helmet>
          <title>View Matches | PathLink</title>
        </Helmet>

        <div className="container mx-auto py-6 px-4 max-w-7xl">
          <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
            <Card className="w-full">
              <CardContent className="flex flex-col items-center justify-center py-16">
                <Briefcase className="h-16 w-16 text-gray-400 mb-6" />
                <h3 className="text-2xl font-medium text-gray-700 mb-4">Post Your First Job</h3>
                <p className="text-gray-500 text-center mb-6 max-w-md">
                  To see candidate matches, you need to post job openings first.
                  Our system will analyze candidate resumes and find the best matches for your positions.
                </p>
                <div className="flex gap-3">
                  <Button
                    onClick={() => setLocation("/post-job")}
                    className="bg-[#1C2A42] hover:bg-[#1C2A42]/90 text-white"
                  >
                    <Briefcase className="h-4 w-4 mr-2" />
                    Post a Job
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setLocation("/dashboard")}
                  >
                    Go to Dashboard
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Layout>
    );
  }

  // Default to employer view (current implementation)
  return (
    <Layout>
      <Helmet>
        <title>View Matches | PathLink</title>
      </Helmet>

      <div className="container mx-auto py-6 px-4 max-w-7xl">
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-6">
          <h1 className="text-3xl font-bold text-[#1C2A42] mb-6">Candidate Matches</h1>
          <p className="text-gray-600 mb-4">Find the best candidates for your job openings with detailed scoring</p>

        {/* Filters and Search */}
        <div className="flex flex-col gap-4 mb-6">
          {/* Search and Sort Row */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search jobs, candidates, or resume content..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
              <SelectTrigger className="w-[180px]">
                <div className="flex items-center">
                  <ArrowUpDown className="mr-2 h-4 w-4" />
                  <span>Sort By</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="score">Overall Match</SelectItem>
                <SelectItem value="skills">Skills Match</SelectItem>
                <SelectItem value="experience">Experience Match</SelectItem>
                <SelectItem value="education">Education Match</SelectItem>
                <SelectItem value="name">Candidate Name</SelectItem>
                <SelectItem value="date">Resume Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filters Row */}
          <div className="flex flex-col gap-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              <Select
                value={filterByScore?.toString() || "all"}
                onValueChange={(value) => setFilterByScore(value !== "all" ? parseInt(value) : null)}
              >
                <SelectTrigger className="w-full">
                  <div className="flex items-center">
                    <Filter className="mr-2 h-4 w-4" />
                    <span>Min Score</span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Scores</SelectItem>
                  <SelectItem value="90">90% and above</SelectItem>
                  <SelectItem value="80">80% and above</SelectItem>
                  <SelectItem value="70">70% and above</SelectItem>
                  <SelectItem value="60">60% and above</SelectItem>
                </SelectContent>
              </Select>

            <Select
              value={filterByExperience?.toString() || "all"}
              onValueChange={(value) => setFilterByExperience(value !== "all" ? parseInt(value) : null)}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <span>Min Experience</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Experience</SelectItem>
                <SelectItem value="1">1+ Years</SelectItem>
                <SelectItem value="3">3+ Years</SelectItem>
                <SelectItem value="5">5+ Years</SelectItem>
                <SelectItem value="10">10+ Years</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filterByEducation || "all"}
              onValueChange={(value) => setFilterByEducation(value !== "all" ? value : null)}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <span>Education</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Education</SelectItem>
                <SelectItem value="Bachelor">Bachelor's Degree</SelectItem>
                <SelectItem value="Master">Master's Degree</SelectItem>
                <SelectItem value="PhD">PhD/Doctorate</SelectItem>
              </SelectContent>
            </Select>

            {/* Skills filter - populated from actual matched skills */}
            <Select
              value={filterBySkill || "all"}
              onValueChange={(value) => setFilterBySkill(value !== "all" ? value : null)}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <span>Skills</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Skills</SelectItem>
                {allSkills.slice(0, 20).map((skill) => (
                  <SelectItem key={skill} value={skill}>
                    {skill}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Location filter */}
            <Select
              value={locationFilter || "all"}
              onValueChange={(value) => setLocationFilter(value !== "all" ? value : null)}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <span>Location</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Locations</SelectItem>
                {locations.map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Match Type filter */}
            <Select
              value={matchTypeFilter}
              onValueChange={(value) => setMatchTypeFilter(value as any)}
            >
              <SelectTrigger className="w-full">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <span>Match Type</span>
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Matches</SelectItem>
                <SelectItem value="exact">Exact Matches</SelectItem>
                <SelectItem value="partial">Partial Matches</SelectItem>
                <SelectItem value="missing">Missing Skills</SelectItem>
              </SelectContent>
            </Select>

            {/* Reset Filters Button */}
            <Button
              variant="outline"
              onClick={() => {
                setFilterByScore(null);
                setFilterByExperience(null);
                setFilterByEducation(null);
                setFilterBySkill(null);
                setLocationFilter(null);
                setMatchTypeFilter("all");
                setCustomSkillSearch("");
                setCustomIndustrySearch("");
                setSearchQuery("");
                setSortBy("score");
              }}
              className="w-full"
            >
              Reset Filters
            </Button>

            {/* Advanced Filters Toggle */}
            <Button
              variant="outline"
              onClick={() => setAdvancedFiltersOpen(!advancedFiltersOpen)}
              className="w-full"
            >
              {advancedFiltersOpen ? "Hide Advanced" : "Show Advanced"}
            </Button>
            </div>

            {/* Advanced Filters Section */}
            {advancedFiltersOpen && (
              <div className="flex flex-col gap-4 p-4 bg-[#E5DEFF]/30 rounded-lg border border-[#1C2A42]/10">
                <h3 className="text-lg font-semibold text-[#1C2A42]">Advanced Custom Keyword Search</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="customSkillSearch">Custom Skills Search</Label>
                    <Input
                      id="customSkillSearch"
                      placeholder="Enter skills (e.g., React, Python, Machine Learning)"
                      value={customSkillSearch}
                      onChange={(e) => setCustomSkillSearch(e.target.value)}
                      className="mt-1"
                    />
                    <p className="text-sm text-gray-600 mt-1">
                      Search for any skill or technology. Supports fuzzy matching and synonyms.
                    </p>
                  </div>
                  <div>
                    <Label htmlFor="customIndustrySearch">Custom Industry Search</Label>
                    <Input
                      id="customIndustrySearch"
                      placeholder="Enter industry keywords (e.g., fintech, healthcare, AI)"
                      value={customIndustrySearch}
                      onChange={(e) => setCustomIndustrySearch(e.target.value)}
                      className="mt-1"
                    />
                    <p className="text-sm text-gray-600 mt-1">
                      Search for any industry or domain. Works with related terms and keywords.
                    </p>
                  </div>
                </div>
                <div className="text-sm text-[#1C2A42] bg-[#EAE6E1] p-3 rounded border border-[#1C2A42]/20">
                  <strong>Pro Tip:</strong> Use comma-separated keywords for multiple terms.
                  The system will find matches using intelligent keyword matching, synonyms, and fuzzy search.
                  For example: "React, Vue, frontend" or "healthcare, medical, clinical"
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="mb-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Matches</TabsTrigger>
            <TabsTrigger value="jobs">By Job</TabsTrigger>
            <TabsTrigger value="candidates">By Candidate</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Loading state */}
        {(jobsQuery.isLoading || resumesQuery.isLoading || matchesQuery.isLoading) && (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="w-full">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-4 w-1/4" />
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between">
                    <div className="space-y-2 w-2/3">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                    <Skeleton className="h-16 w-16 rounded-full" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* No matches found */}
        {matchesQuery.isSuccess && filteredMatches.length === 0 && (
          <Card className="w-full">
            <CardContent className="flex flex-col items-center justify-center py-10">
              <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-700">No matches found</h3>
              <p className="text-gray-500 mt-2">
                Try adjusting your filters or search criteria to see more results.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Match results */}
        {matchesQuery.isSuccess && filteredMatches.length > 0 && (
          <div className="space-y-6">
            {filteredMatches.map((match) => (
              <Card key={`${match.job.id}-${match.worker.id}`} className="w-full overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex flex-col space-y-4 md:flex-row md:justify-between md:items-start md:space-y-0">
                    <div className="flex-1">
                      <CardTitle className="text-lg md:text-xl font-bold text-[#1C2A42]">
                        {match.worker.name} - {match.job.title}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        Match Score: {match.score.overall}% | Industry: {match.job.industry}
                      </CardDescription>
                    </div>
                    <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewResume(match.worker.id, match.worker.name, match.resume)}
                        className="flex items-center justify-center w-full sm:w-auto"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Resume
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewJob(match.job)}
                        className="flex items-center justify-center w-full sm:w-auto"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Job Details
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left column - Score breakdown */}
                    <div>
                      <h3 className="font-medium text-lg mb-3">Match Score Breakdown</h3>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Overall Match</span>
                            <span className="text-sm font-medium">{match.score.overall}%</span>
                          </div>
                          <Progress value={match.score.overall} className={getScoreColor(match.score.overall)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Skills Match</span>
                            <span className="text-sm">{match.score.skills}%</span>
                          </div>
                          <Progress value={match.score.skills} className={getScoreColor(match.score.skills)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Experience Match</span>
                            <span className="text-sm">{match.score.experience}%</span>
                          </div>
                          <Progress value={match.score.experience} className={getScoreColor(match.score.experience)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Education Match</span>
                            <span className="text-sm">{match.score.education}%</span>
                          </div>
                          <Progress value={match.score.education} className={getScoreColor(match.score.education)} />
                        </div>

                      </div>
                    </div>

                    {/* Right column - Match details */}
                    <div>
                      <h3 className="font-medium text-lg mb-3">Match Details</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium mb-2">Skills</h4>
                          <div className="flex flex-wrap gap-2">
                            {match.score.details.matchedSkills && match.score.details.matchedSkills.map((skill) => (
                              <Badge key={skill} variant="default" className="flex items-center bg-green-500 text-white">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                {skill}
                              </Badge>
                            ))}
                            {match.score.details.missingSkills && match.score.details.missingSkills.map((skill) => (
                              <Badge key={skill} variant="destructive" className="flex items-center">
                                <XCircle className="h-3 w-3 mr-1" />
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <Separator />
                        <div>
                          <h4 className="text-sm font-medium mb-2">Experience</h4>
                          <p className="text-sm">{match.score.details.experienceMatch}</p>
                        </div>
                        <Separator />
                        <div>
                          <h4 className="text-sm font-medium mb-2">Strengths & Development Areas</h4>
                          <div className="space-y-2">
                            <div>
                              <h5 className="text-xs font-medium text-green-600">Strengths:</h5>
                              <ul className="list-disc list-inside text-sm">
                                {match.score.details.additionalStrengths.map((strength, i) => (
                                  <li key={i}>{strength}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <h5 className="text-xs font-medium text-red-600">Development Areas:</h5>
                              <ul className="list-disc list-inside text-sm">
                                {match.score.details.developmentAreas.map((area, i) => (
                                  <li key={i}>{area}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-[#EAE6E1]/50 border-t border-[#1C2A42]/10">
                  <div className="w-full flex flex-col space-y-3 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
                    <div className="text-sm text-[#1C2A42]/70 text-center sm:text-left">
                      Resume uploaded: {new Date(match.resume.upload_date).toLocaleDateString()}
                    </div>
                    <Button
                      variant="default"
                      className="bg-[#1C2A42] hover:bg-[#1C2A42]/90 text-white w-full sm:w-auto"
                      onClick={() => handleContactCandidate(match)}
                    >
                      Contact Candidate
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
        </div>
      </div>

      {/* Resume View Modal */}
      {selectedWorker && selectedResume && (
        <ResumeViewModal
          open={showResumeModal}
          onClose={() => setShowResumeModal(false)}
          workerId={selectedWorker.id}
          workerName={selectedWorker.name || "Candidate"}
          resumeData={selectedResume}
        />
      )}

      {/* Job Details Modal */}
      {selectedJob && (
        <JobDetailsModal
          open={showJobModal}
          onClose={() => setShowJobModal(false)}
          job={selectedJob}
        />
      )}

      {/* Contact Candidate Modal */}
      <Dialog open={showContactModal} onOpenChange={setShowContactModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-xl font-bold text-[#1C2A42]">
              <Mail className="h-5 w-5" />
              Contact Candidate
            </DialogTitle>
            <DialogDescription>
              {selectedContactInfo && (
                <>Contact information for {selectedContactInfo.worker?.name} - {selectedContactInfo.job?.title}</>
              )}
            </DialogDescription>
          </DialogHeader>

          {selectedContactInfo && (
            <div className="space-y-4">
              {/* Candidate Info */}
              <div className="bg-[#E5DEFF]/30 p-4 rounded-lg border border-[#1C2A42]/10">
                <h3 className="font-semibold text-[#1C2A42] mb-2">Candidate Details</h3>
                <p className="text-[#1C2A42]"><strong>Name:</strong> {selectedContactInfo.worker?.name}</p>
                <p className="text-[#1C2A42]"><strong>Position:</strong> {selectedContactInfo.job?.title}</p>
                <p className="text-[#1C2A42]"><strong>Industry:</strong> {selectedContactInfo.job?.industry}</p>
              </div>

              {/* Contact Information */}
              <div className="space-y-3">
                <h3 className="font-semibold text-[#1C2A42]">Contact Information</h3>

                {/* Email - Use extracted email or fallback to worker email */}
                {(selectedContactInfo.contactInfo?.email || selectedContactInfo.worker?.email) && (
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="font-medium">Email</p>
                      <a
                        href={`mailto:${selectedContactInfo.contactInfo?.email || selectedContactInfo.worker?.email}`}
                        className="text-blue-600 hover:underline"
                      >
                        {selectedContactInfo.contactInfo?.email || selectedContactInfo.worker?.email}
                      </a>
                      {!selectedContactInfo.contactInfo?.email && selectedContactInfo.worker?.email && (
                        <p className="text-xs text-gray-500 mt-1">From account information</p>
                      )}
                    </div>
                  </div>
                )}

                {selectedContactInfo.contactInfo?.phone && (
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <Phone className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="font-medium">Phone</p>
                      <a
                        href={`tel:${selectedContactInfo.contactInfo.phone}`}
                        className="text-green-600 hover:underline"
                      >
                        {selectedContactInfo.contactInfo.phone}
                      </a>
                    </div>
                  </div>
                )}

                {selectedContactInfo.contactInfo?.linkedin && (
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <ExternalLink className="h-5 w-5 text-blue-700" />
                    <div>
                      <p className="font-medium">LinkedIn</p>
                      <a
                        href={selectedContactInfo.contactInfo.linkedin}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-700 hover:underline flex items-center gap-1"
                      >
                        View LinkedIn Profile
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                )}

                {selectedContactInfo.contactInfo?.github && (
                  <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <ExternalLink className="h-5 w-5 text-gray-700" />
                    <div>
                      <p className="font-medium">GitHub</p>
                      <a
                        href={selectedContactInfo.contactInfo.github}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-700 hover:underline flex items-center gap-1"
                      >
                        View GitHub Profile
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                )}

                {selectedContactInfo.contactInfo?.website && (
                  <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                    <Globe className="h-5 w-5 text-purple-600" />
                    <div>
                      <p className="font-medium">Website</p>
                      <a
                        href={selectedContactInfo.contactInfo.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-600 hover:underline flex items-center gap-1"
                      >
                        Visit Website
                        <ExternalLink className="h-3 w-3" />
                      </a>
                    </div>
                  </div>
                )}

                {selectedContactInfo.contactInfo?.address && (
                  <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <MapPin className="h-5 w-5 text-orange-600" />
                    <div>
                      <p className="font-medium">Location</p>
                      <p className="text-orange-600">{selectedContactInfo.contactInfo.address}</p>
                    </div>
                  </div>
                )}

                {/* No contact info message - Updated to account for worker email fallback */}
                {(!selectedContactInfo.contactInfo?.email &&
                  !selectedContactInfo.worker?.email &&
                  !selectedContactInfo.contactInfo?.phone &&
                  !selectedContactInfo.contactInfo?.linkedin &&
                  !selectedContactInfo.contactInfo?.github &&
                  !selectedContactInfo.contactInfo?.website &&
                  !selectedContactInfo.contactInfo?.address) && (
                  <div className="text-center py-6 text-gray-500">
                    <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                    <p>No contact information available for this candidate.</p>
                    <p className="text-sm mt-1">Contact information may not have been extracted from the resume.</p>
                  </div>
                )}

                {/* Contact Summary */}
                {(selectedContactInfo.contactInfo?.email ||
                  selectedContactInfo.worker?.email ||
                  selectedContactInfo.contactInfo?.phone ||
                  selectedContactInfo.contactInfo?.linkedin ||
                  selectedContactInfo.contactInfo?.github ||
                  selectedContactInfo.contactInfo?.website ||
                  selectedContactInfo.contactInfo?.address) && (
                  <div className="mt-4 p-3 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <p className="text-sm font-medium text-green-800">Contact Information Available</p>
                    </div>
                    <p className="text-xs text-green-600">
                      You can reach out to this candidate using the contact methods shown above.
                    </p>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-2 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => setShowContactModal(false)}
                >
                  Close
                </Button>
                {(selectedContactInfo.contactInfo?.email || selectedContactInfo.worker?.email) && (
                  <Button
                    className="bg-[#1C2A42] hover:bg-[#1C2A42]/90 text-white"
                    onClick={() => {
                      const emailAddress = selectedContactInfo.contactInfo?.email || selectedContactInfo.worker?.email;
                      const subject = `Job Opportunity: ${selectedContactInfo.job?.title}`;
                      const body = `Dear ${selectedContactInfo.worker?.name},\n\nI hope this email finds you well. I am reaching out regarding an exciting job opportunity for the position of ${selectedContactInfo.job?.title}.\n\nBased on your background and experience, I believe you would be an excellent fit for this role. I would love to discuss this opportunity with you further.\n\nPlease let me know if you would be interested in learning more.\n\nBest regards`;
                      window.location.href = `mailto:${emailAddress}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
                    }}
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Layout>
  );
}
