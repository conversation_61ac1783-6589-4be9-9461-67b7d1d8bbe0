-- Create admin user <NAME_EMAIL>
-- The password is 'password' hashed with bcrypt
INSERT INTO users (email, password, name, role, is_verified, original_role, created_at)
VALUES (
    '<EMAIL>', 
    '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu', 
    'System Administrator', 
    'admin',
    TRUE,
    'admin',
    NOW()
)
ON CONFLICT (email) DO UPDATE SET
    password = '$2b$10$Y7mFin7AQUaLEGIhBTPk2.AqjX11IrqsW9jPW8q.OGF9J5HYa/uEu',
    name = 'System Administrator',
    role = 'admin',
    is_verified = TRUE,
    original_role = 'admin';
