# PathLink Code Owners Configuration
# This file defines who must review changes to specific files and directories
# See: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# Global Owners - Default reviewers for all files
* @peterdimian

# Security-Critical Files - Require security team review
.github/workflows/security-*.yml @peterdimian
.github/workflows/codespace-security.yml @peterdimian
.github/rulesets/ @peterdimian
.devcontainer/security-*.sh @peterdimian
.devcontainer/Dockerfile @peterdimian
.devcontainer/devcontainer.json @peterdimian
.github/CODESPACE_SECURITY_POLICY.md @peterdimian
.github/GITHUB_RULESET_SETUP.md @peterdimian
CODESPACE_SECURITY_*.md @peterdimian

# Configuration Files - Require admin review
.github/ @peterdimian
.gitignore @peterdimian
package.json @peterdimian
package-lock.json @peterdimian
netlify.toml @peterdimian
tsconfig.json @peterdimian
tailwind.config.js @peterdimian
vite.config.ts @peterdimian

# Database and Backend - Require backend expertise
server/ @peterdimian
netlify/functions/ @peterdimian
scripts/ @peterdimian
database/ @peterdimian

# Frontend Core - Require frontend expertise  
client/src/components/ @peterdimian
client/src/pages/ @peterdimian
client/src/hooks/ @peterdimian
client/src/lib/ @peterdimian

# Documentation - Require documentation review
README.md @peterdimian
docs/ @peterdimian
*.md @peterdimian

# Sensitive Directories - Multiple reviewers required
config/ @peterdimian
secrets/ @peterdimian
keys/ @peterdimian

# Deployment Files - Require DevOps review
.github/workflows/ @peterdimian
docker/ @peterdimian
k8s/ @peterdimian
terraform/ @peterdimian

# Legal and Compliance
LICENSE @peterdimian
SECURITY.md @peterdimian
PRIVACY.md @peterdimian
TERMS.md @peterdimian
