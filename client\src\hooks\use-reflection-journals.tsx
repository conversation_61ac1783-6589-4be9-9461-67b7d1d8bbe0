import { useMutation, useQuery } from "@tanstack/react-query";
import { apiRequest, queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { ReflectionJournal } from "@shared/schema";

export function useReflectionJournals() {
  const { toast } = useToast();
  
  const {
    data: journals,
    isLoading,
    error,
  } = useQuery<ReflectionJournal[]>({
    queryKey: ["/api/pathcoach/reflection-journals"],
    staleTime: 300000, // 5 minutes
  });
  
  const createJournalMutation = useMutation({
    mutationFn: async (journalData: { 
      prompt: string; 
      entry: string;
    }) => {
      const res = await apiRequest("POST", "/api/pathcoach/reflection-journals", journalData);
      return await res.json();
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Your journal entry has been saved.",
      });
      // Invalidate the journals query to refetch data
      queryClient.invalidateQueries({ queryKey: ["/api/pathcoach/reflection-journals"] });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to save journal entry: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  return {
    journals,
    isLoading,
    error,
    createJournalMutation,
  };
}