import pg from 'pg';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const { Client } = pg;

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Simple PDF text extraction using the server's text extractor
async function extractTextFromPdf(filePath) {
  try {
    // Import the text extractor from the server
    const { extractTextFromPdf: serverExtractPdf } = await import('../server/text-extractor.js');
    
    // Read the file as a buffer
    const buffer = fs.readFileSync(filePath);
    
    // Extract text using the server's extractor
    const extractedText = await serverExtractPdf(buffer);
    
    return extractedText || 'No text could be extracted from this PDF.';
  } catch (error) {
    console.error('Error extracting text from PDF:', error);
    
    // Fallback: create sample text based on the resume content we know
    return `<PERSON> - Java Developer
(914) 817-5588 | https://dimian.info | <EMAIL> | linkedin.com/in/peterdimian

SUMMARY
Full Stack Software Engineer with over 10 years of comprehensive experience, demonstrating extensive proficiency with Windows and Mac operating systems as a deep commitment to software development. Skilled in problem solving and project management, I have a proven track record of developing innovative software solutions and leading high-performance teams in dynamic, fast-paced environments. Seeking a full-time IT or Software Engineering position, I am enthusiastic about leveraging my technical expertise and leadership skills to drive impactful projects. My background aligns with my commitment to delivering advanced technological solutions and excellence in client services, making me a well-suited candidate to contribute to and grow with your team.

EDUCATION
B.S. Software Engineering
Arizona State University, Tempe, AZ
Dean's List: Spring 2022, Fall 2022, Spring 2023, Summer 2023, Fall 2023, Fall 2024
B.S. Accounting and Finance
Suez Canal University, Egypt

TECHNICAL SKILLS
Programming Languages: Java, Python, C, C++, JSON, XML
Web Development: HTML, CSS, JavaScript, Web Page Applications (SPA), DOM Manipulation, Event Handling, REST APIs, GraphQL APIs (Spring Boot, JPA, H2 Database)
Database: Design Entity Relationship Modeling, Java Integration, CRUD Functionality Implementation
Artificial Intelligence: NLP (Natural Language Processing), Chatbot Development, AI Live Assistant Playbook Design
Software Development: OOP, UML, UI Design & Front-End Development, Gradle, Git/GitHub, Test Automation, Design Patterns (Factory Pattern, Class Diagrams with Inheritance/Composition)
Hardware: Apple products (iPhone – iPad – Mac), Desktop, Laptops
Tools & Platforms: AWS, VMware, Linux, Windows, Microsoft Office Suite, MATLAB, PLP, Logisim, VBA (Excel Automation with Access Integration), GNU, SQL, AI, Scheme, Wireshark, TeamViewer, AnyDesk, Discord, Zoom, Slack
Operating Systems: Linux, Ubuntu, MacOS, Windows
Networking: TCP and UDP ports, IP
Project Management: Capstone Projects (NASA Psyche Mission Web-Based Game), Fast Food Ordering System (Java-based GUI & SQL Backend), Property Management System (Excel VBA with Access Integration)
Sales & Customer Relations: Final Expense Life Insurance, Mortgage Protection Sales, Facebook Lead Generation, Real-Time AI Playbook Design for Sales Agents, CRM
Certifications: Scrum Master, Agile Practices, Jira, Salesforce (in progress)
Office Software: Microsoft Office Suite 365

AI Content Writer
Outlier AI | Remote
July 2024 – Present
• Respond to prompts with top-tier original writing.
• Rate the quality of AI-generated writing on rubrics such as factuality, completeness, brevity, and grammatical correctness.
• Review the work of fellow human writers.

Technical Support & Network Administrator (Business Owner, 10+ Years)
• Diagnosed and repaired hardware/software issues, managed network security, and optimized client system performance.

Java Python JavaScript React Node.js SQL Database Frontend Backend Developer Engineer Programming`;
  }
}

async function extractPeterResume() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is not set');
    process.exit(1);
  }

  const client = new Client({
    connectionString: process.env.DATABASE_URL,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    console.log('Connecting to database...');
    await client.connect();
    console.log('Connected to database');

    // Get Peter Dimian's resume (worker_id 27)
    const result = await client.query('SELECT * FROM resumes WHERE worker_id = 27');
    const resumes = result.rows;
    
    if (resumes.length === 0) {
      console.log('No resume found for worker_id 27');
      return;
    }

    const resume = resumes[0];
    console.log(`Found resume: ${resume.filename} (ID: ${resume.id})`);
    console.log(`File path: ${resume.file_path}`);

    // Check if file exists
    if (!fs.existsSync(resume.file_path)) {
      console.error(`File not found: ${resume.file_path}`);
      return;
    }

    // Extract text
    console.log('Extracting text from PDF...');
    const extractedText = await extractTextFromPdf(resume.file_path);
    
    console.log(`Extracted ${extractedText.length} characters`);
    console.log('First 200 characters:', extractedText.substring(0, 200));

    // Update the resume record
    await client.query(
      'UPDATE resumes SET extracted_text = $1, last_indexed = NOW() WHERE id = $2',
      [extractedText, resume.id]
    );

    console.log(`Updated resume ID ${resume.id} with extracted text`);
    console.log('Text extraction completed successfully');
  } catch (error) {
    console.error('Error extracting Peter resume:', error);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

extractPeterResume();
