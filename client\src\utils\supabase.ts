// Supabase has been replaced with Neon database
// This file is kept for backward compatibility but no longer initializes Supabase

console.log('Supabase client disabled - using Neon database instead');

// Create a mock supabase object to prevent errors
const supabase = {
  auth: {
    getSession: () => Promise.resolve({ data: null, error: new Error('Supabase disabled') })
  },
  supabaseUrl: 'disabled',
  supabaseKey: 'disabled'
};

// We don't need to spread the supabase object as it breaks the methods
// Just add the URL and key as properties
Object.defineProperties(supabase, {
  supabaseUrl: {
    value: supabaseUrl,
    writable: false,
    enumerable: true
  },
  supabaseKey: {
    value: supabaseKey,
    writable: false,
    enumerable: true
  }
});

export default supabase;
