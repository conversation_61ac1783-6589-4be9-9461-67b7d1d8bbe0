import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useLocation } from "wouter";
import { Helmet } from "react-helmet-async";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Loader2, Search, FileText, CheckCircle, XCircle, AlertCircle, Filter, ArrowUpDown } from "lucide-react";
import ResumeViewModal from "@/components/resume-view-modal";
import JobDetailsModal from "@/components/job-details-modal";
import { getJobs, getResumes, getMatches } from "@/lib/supabase";

// Define interfaces for our data
interface Job {
  id: number;
  title: string;
  description: string;
  industry: string;
  location: string;
  employer_id: number;
  required_skills?: string;
  minimum_experience?: string;
}

interface Resume {
  id: number;
  worker_id: number;
  filename: string;
  file_path: string;
  file_size: number;
  file_type: string;
  upload_date: string;
  extracted_text?: string;
}

interface Worker {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface MatchScore {
  overall: number;
  skills: number;
  experience: number;
  education: number;
  industry: number;
  details: {
    matchedSkills: string[];
    missingSkills: string[];
    experienceMatch: string;
    educationMatch: string;
    industryMatch: string;
    additionalStrengths: string[];
    developmentAreas: string[];
  };
}

interface Match {
  job: Job;
  worker: Worker;
  resume: Resume;
  score: MatchScore;
}

export default function ViewMatches() {
  const { toast } = useToast();
  // Using wouter's useLocation instead of react-router-dom's useNavigate
  const [location, setLocation] = useLocation();
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  const [selectedWorker, setSelectedWorker] = useState<Worker | null>(null);
  const [showResumeModal, setShowResumeModal] = useState(false);
  const [showJobModal, setShowJobModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<"score" | "name" | "date">("score");
  const [filterByScore, setFilterByScore] = useState<number | null>(null);
  const [filterByIndustry, setFilterByIndustry] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<"all" | "jobs" | "candidates">("all");

  // Use Supabase client functions for data fetching

  // Fetch jobs
  const jobsQuery = useQuery({
    queryKey: ["jobs"],
    queryFn: async () => {
      const { data, error } = await getJobs();
      if (error) throw new Error("Failed to fetch jobs");
      return data || [];
    },
  });

  // Fetch resumes with worker information
  const resumesQuery = useQuery({
    queryKey: ["resumes"],
    queryFn: async () => {
      const { data, error } = await getResumes();
      if (error) throw new Error("Failed to fetch resumes");

      // For now, we'll use the in-memory data structure since we don't have the worker data
      // In a real implementation, you would join the resumes with worker data in Supabase
      return data?.map(resume => ({
        resume,
        worker: {
          id: resume.worker_id,
          name: "Sample Worker",
          email: "<EMAIL>",
          role: "worker"
        }
      })) || [];
    },
  });

  // Fetch match scores
  const matchesQuery = useQuery({
    queryKey: ["matches"],
    queryFn: async () => {
      const { data, error } = await getMatches();
      if (error) throw new Error("Failed to fetch matches");

      // For now, we'll use sample match data
      // In a real implementation, you would calculate match scores in Supabase
      return data?.map(match => ({
        job: jobsQuery.data?.find(job => job.id === match.job_id) || {} as Job,
        worker: {
          id: match.worker_id,
          name: "Sample Worker",
          email: "<EMAIL>",
          role: "worker"
        },
        resume: resumesQuery.data?.find(r => r.resume.worker_id === match.worker_id)?.resume || {} as Resume,
        score: {
          overall: 85,
          skills: 80,
          experience: 90,
          education: 75,
          industry: 95,
          details: {
            matchedSkills: ["JavaScript", "React", "Node.js"],
            missingSkills: ["Python", "AWS"],
            experienceMatch: "Exceeds required experience by 2 years",
            educationMatch: "Meets education requirements",
            industryMatch: "Has experience in the industry",
            additionalStrengths: ["Strong technical background", "Good communication skills"],
            developmentAreas: ["Learn Python", "Gain AWS experience"]
          }
        }
      })) || [];
    },
    enabled: jobsQuery.isSuccess && resumesQuery.isSuccess,
  });

  // Handle errors
  useEffect(() => {
    if (jobsQuery.error) {
      toast({
        title: "Error",
        description: "Failed to load jobs. Please try again.",
        variant: "destructive",
      });
    }
    if (resumesQuery.error) {
      toast({
        title: "Error",
        description: "Failed to load resumes. Please try again.",
        variant: "destructive",
      });
    }
    if (matchesQuery.error) {
      toast({
        title: "Error",
        description: "Failed to load matches. Please try again.",
        variant: "destructive",
      });
    }
  }, [jobsQuery.error, resumesQuery.error, matchesQuery.error, toast]);

  // Filter and sort matches
  const filteredMatches = matchesQuery.data
    ? matchesQuery.data
        .filter((match) => {
          // Filter by search query
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            return (
              match.job.title.toLowerCase().includes(query) ||
              match.worker.name.toLowerCase().includes(query) ||
              match.job.industry.toLowerCase().includes(query)
            );
          }
          return true;
        })
        .filter((match) => {
          // Filter by score
          if (filterByScore !== null) {
            return match.score.overall >= filterByScore;
          }
          return true;
        })
        .filter((match) => {
          // Filter by industry
          if (filterByIndustry) {
            return match.job.industry === filterByIndustry;
          }
          return true;
        })
        .filter((match) => {
          // Filter by tab
          if (activeTab === "jobs") {
            return selectedJob ? match.job.id === selectedJob.id : true;
          }
          if (activeTab === "candidates") {
            return selectedWorker ? match.worker.id === selectedWorker.id : true;
          }
          return true;
        })
        .sort((a, b) => {
          // Sort matches
          if (sortBy === "score") {
            return b.score.overall - a.score.overall;
          }
          if (sortBy === "name") {
            return a.worker.name.localeCompare(b.worker.name);
          }
          // Sort by date (assuming we have a date field)
          return new Date(b.resume.upload_date).getTime() - new Date(a.resume.upload_date).getTime();
        })
    : [];

  // Get unique industries for filtering
  const industries = jobsQuery.data
    ? Array.from(new Set(jobsQuery.data.map((job) => job.industry)))
    : [];

  // View resume handler
  const handleViewResume = (workerId: number) => {
    setSelectedWorker({ id: workerId } as Worker);
    setShowResumeModal(true);
  };

  // View job details handler
  const handleViewJob = (job: Job) => {
    setSelectedJob(job);
    setShowJobModal(true);
  };

  // Get score color based on match percentage
  const getScoreColor = (score: number) => {
    if (score >= 80) return "bg-green-500";
    if (score >= 60) return "bg-yellow-500";
    return "bg-red-500";
  };

  // Get badge variant based on match type
  const getMatchBadgeVariant = (matched: boolean) => {
    return matched ? "success" : "destructive";
  };

  return (
    <>
      <Helmet>
        <title>View Matches | PathLink</title>
      </Helmet>

      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold text-[#1C2A42] mb-6">Match Analysis</h1>

        {/* Filters and Search */}
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="Search jobs or candidates..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Select value={sortBy} onValueChange={(value) => setSortBy(value as any)}>
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <ArrowUpDown className="mr-2 h-4 w-4" />
                <span>Sort By</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="score">Match Score</SelectItem>
              <SelectItem value="name">Candidate Name</SelectItem>
              <SelectItem value="date">Resume Date</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={filterByScore?.toString() || ""}
            onValueChange={(value) => setFilterByScore(value ? parseInt(value) : null)}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>Min Score</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Scores</SelectItem>
              <SelectItem value="90">90% and above</SelectItem>
              <SelectItem value="80">80% and above</SelectItem>
              <SelectItem value="70">70% and above</SelectItem>
              <SelectItem value="60">60% and above</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={filterByIndustry || ""}
            onValueChange={(value) => setFilterByIndustry(value || null)}
          >
            <SelectTrigger className="w-[180px]">
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                <span>Industry</span>
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Industries</SelectItem>
              {industries.map((industry) => (
                <SelectItem key={industry} value={industry}>
                  {industry}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="mb-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Matches</TabsTrigger>
            <TabsTrigger value="jobs">By Job</TabsTrigger>
            <TabsTrigger value="candidates">By Candidate</TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Loading state */}
        {(jobsQuery.isLoading || resumesQuery.isLoading || matchesQuery.isLoading) && (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="w-full">
                <CardHeader className="pb-2">
                  <Skeleton className="h-6 w-1/3" />
                  <Skeleton className="h-4 w-1/4" />
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between">
                    <div className="space-y-2 w-2/3">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                    <Skeleton className="h-16 w-16 rounded-full" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* No matches found */}
        {matchesQuery.isSuccess && filteredMatches.length === 0 && (
          <Card className="w-full">
            <CardContent className="flex flex-col items-center justify-center py-10">
              <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-700">No matches found</h3>
              <p className="text-gray-500 mt-2">
                Try adjusting your filters or search criteria to see more results.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Match results */}
        {matchesQuery.isSuccess && filteredMatches.length > 0 && (
          <div className="space-y-6">
            {filteredMatches.map((match) => (
              <Card key={`${match.job.id}-${match.worker.id}`} className="w-full overflow-hidden">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-xl font-bold text-[#1C2A42]">
                        {match.worker.name} - {match.job.title}
                      </CardTitle>
                      <CardDescription>
                        Match Score: {match.score.overall}% | Industry: {match.job.industry}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewResume(match.worker.id)}
                        className="flex items-center"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Resume
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewJob(match.job)}
                        className="flex items-center"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Job Details
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Left column - Score breakdown */}
                    <div>
                      <h3 className="font-medium text-lg mb-3">Match Score Breakdown</h3>
                      <div className="space-y-4">
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm font-medium">Overall Match</span>
                            <span className="text-sm font-medium">{match.score.overall}%</span>
                          </div>
                          <Progress value={match.score.overall} className={getScoreColor(match.score.overall)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Skills Match</span>
                            <span className="text-sm">{match.score.skills}%</span>
                          </div>
                          <Progress value={match.score.skills} className={getScoreColor(match.score.skills)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Experience Match</span>
                            <span className="text-sm">{match.score.experience}%</span>
                          </div>
                          <Progress value={match.score.experience} className={getScoreColor(match.score.experience)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Education Match</span>
                            <span className="text-sm">{match.score.education}%</span>
                          </div>
                          <Progress value={match.score.education} className={getScoreColor(match.score.education)} />
                        </div>
                        <div>
                          <div className="flex justify-between mb-1">
                            <span className="text-sm">Industry Match</span>
                            <span className="text-sm">{match.score.industry}%</span>
                          </div>
                          <Progress value={match.score.industry} className={getScoreColor(match.score.industry)} />
                        </div>
                      </div>
                    </div>

                    {/* Right column - Match details */}
                    <div>
                      <h3 className="font-medium text-lg mb-3">Match Details</h3>
                      <div className="space-y-4">
                        <div>
                          <h4 className="text-sm font-medium mb-2">Skills</h4>
                          <div className="flex flex-wrap gap-2">
                            {match.score.details.matchedSkills.map((skill) => (
                              <Badge key={skill} variant="success" className="flex items-center">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                {skill}
                              </Badge>
                            ))}
                            {match.score.details.missingSkills.map((skill) => (
                              <Badge key={skill} variant="destructive" className="flex items-center">
                                <XCircle className="h-3 w-3 mr-1" />
                                {skill}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <Separator />
                        <div>
                          <h4 className="text-sm font-medium mb-2">Experience</h4>
                          <p className="text-sm">{match.score.details.experienceMatch}</p>
                        </div>
                        <Separator />
                        <div>
                          <h4 className="text-sm font-medium mb-2">Strengths & Development Areas</h4>
                          <div className="space-y-2">
                            <div>
                              <h5 className="text-xs font-medium text-green-600">Strengths:</h5>
                              <ul className="list-disc list-inside text-sm">
                                {match.score.details.additionalStrengths.map((strength, i) => (
                                  <li key={i}>{strength}</li>
                                ))}
                              </ul>
                            </div>
                            <div>
                              <h5 className="text-xs font-medium text-red-600">Development Areas:</h5>
                              <ul className="list-disc list-inside text-sm">
                                {match.score.details.developmentAreas.map((area, i) => (
                                  <li key={i}>{area}</li>
                                ))}
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="bg-gray-50 border-t">
                  <div className="w-full flex justify-between items-center">
                    <div className="text-sm text-gray-500">
                      Resume uploaded: {new Date(match.resume.upload_date).toLocaleDateString()}
                    </div>
                    <Button
                      variant="default"
                      className="bg-[#1C2A42] hover:bg-opacity-90"
                    >
                      Contact Candidate
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Resume View Modal */}
      {selectedWorker && (
        <ResumeViewModal
          open={showResumeModal}
          onClose={() => setShowResumeModal(false)}
          workerId={selectedWorker.id}
          workerName={selectedWorker.name || "Candidate"}
        />
      )}

      {/* Job Details Modal */}
      {selectedJob && (
        <JobDetailsModal
          open={showJobModal}
          onClose={() => setShowJobModal(false)}
          job={selectedJob}
        />
      )}
    </>
  );
}