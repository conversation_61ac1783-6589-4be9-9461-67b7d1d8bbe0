{"version": "5", "dialect": "pg", "id": "0000_initial_schema", "name": "initial_schema", "schema": {"tables": {"jobs": {"name": "jobs", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "salary": {"name": "salary", "type": "text", "primaryKey": false, "notNull": false}, "employer_id": {"name": "employer_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"jobs_employer_id_users_id_fk": {"name": "jobs_employer_id_users_id_fk", "tableFrom": "jobs", "tableTo": "users", "columnsFrom": ["employer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "matches": {"name": "matches", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "job_id": {"name": "job_id", "type": "integer", "primaryKey": false, "notNull": true}, "worker_id": {"name": "worker_id", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "match_score": {"name": "match_score", "type": "integer", "primaryKey": false, "notNull": true}, "match_date": {"name": "match_date", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"matches_job_id_jobs_id_fk": {"name": "matches_job_id_jobs_id_fk", "tableFrom": "matches", "tableTo": "jobs", "columnsFrom": ["job_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "matches_worker_id_users_id_fk": {"name": "matches_worker_id_users_id_fk", "tableFrom": "matches", "tableTo": "users", "columnsFrom": ["worker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "mentor_preferences": {"name": "mentor_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "worker_id": {"name": "worker_id", "type": "integer", "primaryKey": false, "notNull": true}, "industry_preference": {"name": "industry_preference", "type": "text", "primaryKey": false, "notNull": false}, "skill_level": {"name": "skill_level", "type": "text", "primaryKey": false, "notNull": false}, "communication_preference": {"name": "communication_preference", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "text", "primaryKey": false, "notNull": false}, "goals": {"name": "goals", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"mentor_preferences_worker_id_users_id_fk": {"name": "mentor_preferences_worker_id_users_id_fk", "tableFrom": "mentor_preferences", "tableTo": "users", "columnsFrom": ["worker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "resumes": {"name": "resumes", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "worker_id": {"name": "worker_id", "type": "integer", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "file_type": {"name": "file_type", "type": "text", "primaryKey": false, "notNull": true}, "upload_date": {"name": "upload_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"resumes_worker_id_users_id_fk": {"name": "resumes_worker_id_users_id_fk", "tableFrom": "resumes", "tableTo": "users", "columnsFrom": ["worker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "surplus_employees": {"name": "surplus_employees", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "previous_role": {"name": "previous_role", "type": "text", "primaryKey": false, "notNull": true}, "skills": {"name": "skills", "type": "text", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": true}, "years_experience": {"name": "years_experience", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "employer_id": {"name": "employer_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"surplus_employees_employer_id_users_id_fk": {"name": "surplus_employees_employer_id_users_id_fk", "tableFrom": "surplus_employees", "tableTo": "users", "columnsFrom": ["employer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"email_idx": {"name": "email_idx", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "worker_values": {"name": "worker_values", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "worker_id": {"name": "worker_id", "type": "integer", "primaryKey": false, "notNull": true}, "work_life_balance": {"name": "work_life_balance", "type": "integer", "primaryKey": false, "notNull": false}, "compensation": {"name": "compensation", "type": "integer", "primaryKey": false, "notNull": false}, "job_security": {"name": "job_security", "type": "integer", "primaryKey": false, "notNull": false}, "remote_work": {"name": "remote_work", "type": "integer", "primaryKey": false, "notNull": false}, "professional_growth": {"name": "professional_growth", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"worker_values_worker_id_users_id_fk": {"name": "worker_values_worker_id_users_id_fk", "tableFrom": "worker_values", "tableTo": "users", "columnsFrom": ["worker_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}}}