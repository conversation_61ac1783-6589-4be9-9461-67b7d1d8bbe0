-- Add missing fields to jobs table
ALTER TABLE jobs 
ADD COLUMN IF NOT EXISTS company TEXT,
ADD COLUMN IF NOT EXISTS salary INTEGER,
ADD COLUMN IF NOT EXISTS employment_type TEXT,
ADD COLUMN IF NOT EXISTS experience_level TEXT,
ADD COLUMN IF NOT EXISTS benefits TEXT,
ADD COLUMN IF NOT EXISTS requirements TEXT,
ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT NOW();

-- Update existing jobs to have a created_at timestamp if they don't have one
UPDATE jobs SET created_at = NOW() WHERE created_at IS NULL;

-- Make created_at NOT NULL after setting default values
ALTER TABLE jobs ALTER COLUMN created_at SET NOT NULL;
